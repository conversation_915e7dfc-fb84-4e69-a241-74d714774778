#!/usr/bin/env python3
"""
Test Volume Lookback Change
Verify that the volume lookback has been changed from 20 to 10
"""

import sys
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_volume_lookback_change():
    """Test that volume lookback has been changed to 10"""
    logger.info("🔧 TESTING VOLUME LOOKBACK CHANGE...")
    
    try:
        # Test QQE Indicator directly
        from qqe_indicator import QQEIndicator
        
        # Test default initialization
        qqe_default = QQEIndicator()
        logger.info(f"   📊 QQE Default volume_lookback: {qqe_default.volume_lookback}")
        logger.info(f"   📊 QQE Default volume_divergence_lookback: {qqe_default.volume_divergence_lookback}")
        
        if qqe_default.volume_lookback == 10:
            logger.info("   ✅ Default volume_lookback correctly set to 10")
        else:
            logger.error(f"   ❌ Default volume_lookback is {qqe_default.volume_lookback}, should be 10")
            return False
        
        # Test FixedLiveTrader initialization
        from fixed_live_trader import FixedLiveTrader
        
        trader = FixedLiveTrader(symbol="XAUUSD!")
        qqe_in_trader = trader.qqe_indicator
        
        logger.info(f"   📊 Trader QQE volume_lookback: {qqe_in_trader.volume_lookback}")
        logger.info(f"   📊 Trader QQE volume_divergence_lookback: {qqe_in_trader.volume_divergence_lookback}")
        
        if qqe_in_trader.volume_lookback == 10:
            logger.info("   ✅ Trader QQE volume_lookback correctly set to 10")
        else:
            logger.error(f"   ❌ Trader QQE volume_lookback is {qqe_in_trader.volume_lookback}, should be 10")
            return False
        
        # Test explicit initialization
        qqe_explicit = QQEIndicator(volume_lookback=10, volume_divergence_lookback=10)
        logger.info(f"   📊 Explicit QQE volume_lookback: {qqe_explicit.volume_lookback}")
        
        if qqe_explicit.volume_lookback == 10:
            logger.info("   ✅ Explicit volume_lookback correctly set to 10")
        else:
            logger.error(f"   ❌ Explicit volume_lookback is {qqe_explicit.volume_lookback}, should be 10")
            return False
        
        logger.info("✅ Volume Lookback Change Test: PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Volume Lookback Change Test: FAILED - {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run volume lookback change test"""
    logger.info("🧪 TESTING VOLUME LOOKBACK CHANGE FROM 20 TO 10")
    logger.info("=" * 60)
    
    success = test_volume_lookback_change()
    
    logger.info("\n" + "=" * 60)
    if success:
        logger.info("🎉 VOLUME LOOKBACK SUCCESSFULLY CHANGED TO 10!")
        logger.info("   📊 QQE indicator now uses 10-period volume analysis")
        logger.info("   📊 This provides more responsive volume signals")
        return True
    else:
        logger.error("❌ VOLUME LOOKBACK CHANGE FAILED!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
