#!/usr/bin/env python3
"""
Check current MT5 status and open positions
"""

import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_status():
    """Check current MT5 status"""
    try:
        if not mt5.initialize():
            logger.error("❌ Failed to initialize MT5")
            return
        
        # Get account info
        account_info = mt5.account_info()
        if account_info:
            logger.info(f"📊 Account: {account_info.login}")
            logger.info(f"💰 Balance: ${account_info.balance:.2f}")
            logger.info(f"🕐 Server Time: {datetime.now()}")
        
        # Get current positions
        positions = mt5.positions_get()
        if positions:
            logger.info(f"📈 Current Open Positions: {len(positions)}")
            for pos in positions:
                logger.info(f"   Ticket: {pos.ticket} | {pos.type_str} | Volume: {pos.volume} | Price: {pos.price_open} | Profit: ${pos.profit:.2f}")
        else:
            logger.info("📈 No open positions")
        
        # Get pending orders
        orders = mt5.orders_get()
        if orders:
            logger.info(f"📋 Pending Orders: {len(orders)}")
            for order in orders:
                logger.info(f"   Ticket: {order.ticket} | {order.type_str} | Volume: {order.volume_initial} | Price: {order.price_open}")
        else:
            logger.info("📋 No pending orders")
        
        # Check if there are any recent trades that might not be in the log
        now = datetime.now()
        recent_deals = mt5.history_deals_get(datetime(2025, 10, 1, 0, 0), now)
        
        if recent_deals:
            deals_df = pd.DataFrame(list(recent_deals), columns=recent_deals[0]._asdict().keys())
            deals_df['time'] = pd.to_datetime(deals_df['time'], unit='s')
            
            xauusd_deals = deals_df[deals_df['symbol'] == 'XAUUSD!']
            logger.info(f"🗓️ XAUUSD deals on 2025-10-01: {len(xauusd_deals)}")
            
            for _, deal in xauusd_deals.iterrows():
                entry_type = "ENTRY" if deal['entry'] == 0 else "EXIT"
                logger.info(f"   {entry_type} | Ticket: {deal['position_id']} | Time: {deal['time']} | Price: {deal['price']} | Profit: ${deal['profit']:.2f}")
        
        # Look for any deals with -4.76 profit in the last few days
        from_date = datetime(2025, 9, 28)
        all_recent_deals = mt5.history_deals_get(from_date, now)
        
        if all_recent_deals:
            all_deals_df = pd.DataFrame(list(all_recent_deals), columns=all_recent_deals[0]._asdict().keys())
            all_deals_df['time'] = pd.to_datetime(all_deals_df['time'], unit='s')
            
            target_deals = all_deals_df[
                (all_deals_df['symbol'] == 'XAUUSD!') &
                (all_deals_df['profit'] >= -4.80) &
                (all_deals_df['profit'] <= -4.70)
            ]
            
            logger.info(f"🎯 Deals with profit between -4.80 and -4.70:")
            for _, deal in target_deals.iterrows():
                logger.info(f"   Ticket: {deal['position_id']} | Time: {deal['time']} | Profit: ${deal['profit']:.2f}")
        
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    check_status()
