#!/usr/bin/env python3
"""
Diagnose Live Data Updates
Check if live data is updating properly
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime
import time

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager
from feature_engineering import FeatureEngineer

def diagnose_live_data():
    """Diagnose if live data is updating properly"""
    print("🔍 Diagnosing Live Data Updates...")
    
    # Initialize components
    mt5_manager = MT5Manager()
    feature_engineer = FeatureEngineer()
    
    # Connect to MT5
    if not mt5_manager.connect():
        print("❌ Failed to connect to MT5")
        return
    
    print("✅ Connected to MT5")
    
    # Test data retrieval multiple times
    symbol = "XAUUSD!"
    timeframe = "M5"
    
    print(f"\n📊 Testing live data updates for {symbol}...")
    print("=" * 60)
    
    previous_data = None
    previous_features = None
    
    for i in range(5):
        print(f"\n🔄 Test {i+1}/5 - {datetime.now().strftime('%H:%M:%S')}")
        
        # Get current tick
        tick = mt5_manager.get_symbol_info_tick(symbol)
        if tick:
            print(f"   Current Price: Bid={tick['bid']:.5f}, Ask={tick['ask']:.5f}")
        
        # Get latest data
        df = mt5_manager.get_latest_data(symbol, timeframe, 200)
        if df is None or len(df) < 100:
            print("   ❌ Failed to get live data")
            continue
        
        print(f"   📈 Retrieved {len(df)} bars")
        print(f"   📊 Columns: {list(df.columns)}")

        # Check if we have time column or index
        if 'time' in df.columns:
            print(f"   📅 Latest bar time: {df.iloc[-1]['time']}")
        elif hasattr(df.index, 'max'):
            print(f"   📅 Latest bar index: {df.index[-1]}")

        print(f"   💰 Latest OHLC: O={df.iloc[-1]['open']:.2f}, H={df.iloc[-1]['high']:.2f}, L={df.iloc[-1]['low']:.2f}, C={df.iloc[-1]['close']:.2f}")
        
        # Check if data changed
        if previous_data is not None:
            # Compare last 5 bars (OHLC only)
            current_last_5 = df.tail(5)[['open', 'high', 'low', 'close']].values
            previous_last_5 = previous_data.tail(5)[['open', 'high', 'low', 'close']].values

            if np.array_equal(current_last_5, previous_last_5):
                print("   ⚠️  Data UNCHANGED from previous check")
            else:
                print("   ✅ Data UPDATED since previous check")
        
        # Calculate features
        try:
            features_df = feature_engineer.create_technical_indicators(df)
            if len(features_df) > 0:
                # Get latest features
                latest_features = features_df.iloc[-1]
                
                # Show key features
                key_features = ['vwap', 'bb_upper', 'bb_lower', 'rsi', 'macd']
                print("   🎯 Key Features:")
                for feature in key_features:
                    if feature in latest_features:
                        print(f"      {feature}: {latest_features[feature]:.6f}")
                
                # Check if features changed
                if previous_features is not None:
                    feature_changes = 0
                    for feature in key_features:
                        if feature in latest_features and feature in previous_features:
                            if abs(latest_features[feature] - previous_features[feature]) > 0.000001:
                                feature_changes += 1
                    
                    if feature_changes == 0:
                        print("   ⚠️  Features UNCHANGED from previous check")
                    else:
                        print(f"   ✅ {feature_changes}/{len(key_features)} features CHANGED")
                
                previous_features = latest_features.copy()
            else:
                print("   ❌ Failed to calculate features")
                
        except Exception as e:
            print(f"   ❌ Error calculating features: {e}")
        
        previous_data = df.copy()
        
        if i < 4:  # Don't wait after last iteration
            print("   ⏳ Waiting 60 seconds...")
            time.sleep(60)
    
    print(f"\n📋 Diagnosis Summary:")
    print("=" * 60)
    
    # Final check - get current market status
    tick = mt5_manager.get_symbol_info_tick(symbol)
    if tick:
        current_time = datetime.now()
        market_time = datetime.fromtimestamp(tick['time'])
        time_diff = (current_time - market_time).total_seconds()
        
        print(f"🕐 Current Time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 Last Tick Time: {market_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  Time Difference: {time_diff:.0f} seconds")
        
        if time_diff > 300:  # 5 minutes
            print("⚠️  WARNING: Market data might be stale (>5 minutes old)")
        elif time_diff > 60:  # 1 minute
            print("⚠️  CAUTION: Market data is somewhat old (>1 minute)")
        else:
            print("✅ Market data is fresh (<1 minute old)")
    
    # Disconnect
    mt5_manager.disconnect()
    print("\n✅ Diagnosis completed!")

def main():
    """Main function"""
    print("🚀 Live Data Diagnosis Tool")
    print("This will check if live data is updating properly")
    print("Testing will take about 5 minutes...")
    print()
    
    diagnose_live_data()

if __name__ == "__main__":
    main()
