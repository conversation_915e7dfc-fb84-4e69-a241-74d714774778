#!/usr/bin/env python3
"""
Test the new relative change-based signal generation
"""

import sys
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_relative_change_signals():
    """Test the new relative change-based signal generation"""
    print("📈 TESTING RELATIVE CHANGE-BASED SIGNALS")
    print("=" * 55)
    
    try:
        # Initialize trader
        trader = FixedLiveTrader()
        
        print("1️⃣ NEW SIGNAL GENERATION LOGIC")
        print("-" * 40)
        
        print("❌ OLD LOGIC (Absolute Thresholds):")
        print("• BUY when strength > +15%")
        print("• SELL when strength < -15%")
        print("• Problem: Ignores momentum and direction changes")
        print("")
        print("✅ NEW LOGIC (Relative Changes):")
        print("• BUY when strength increases by ≥15%")
        print("• SELL when strength decreases by ≥15%")
        print("• Benefit: Captures momentum shifts and direction changes")
        
        print("\n2️⃣ SIGNAL GENERATION EXAMPLES")
        print("-" * 40)
        
        examples = [
            # (prev_strength, curr_strength, change, expected_signal, description)
            (10.0, 25.0, +15.0, "BUY", "Strength increased by 15%"),
            (5.0, 22.0, +17.0, "BUY", "Strong bullish momentum shift"),
            (-10.0, 8.0, +18.0, "BUY", "Bearish to bullish reversal"),
            (20.0, 5.0, -15.0, "SELL", "Strength decreased by 15%"),
            (15.0, -2.0, -17.0, "SELL", "Strong bearish momentum shift"),
            (-5.0, -22.0, -17.0, "SELL", "Bearish momentum acceleration"),
            (10.0, 20.0, +10.0, "NONE", "Insufficient change (+10% < 15%)"),
            (-5.0, 5.0, +10.0, "NONE", "Insufficient change (+10% < 15%)"),
            (25.0, 15.0, -10.0, "NONE", "Insufficient change (-10% > -15%)"),
        ]
        
        print("Previous | Current | Change  | Signal | Description")
        print("-" * 65)
        
        for prev, curr, change, expected, desc in examples:
            signal = "BUY" if change >= 15 else "SELL" if change <= -15 else "NONE"
            status = "✅" if signal == expected else "❌"
            print(f"{prev:8.1f} | {curr:7.1f} | {change:+7.1f} | {signal:6s} {status} | {desc}")
        
        print("\n3️⃣ ADVANTAGES OF RELATIVE CHANGE SIGNALS")
        print("-" * 50)
        
        print("✅ MOMENTUM DETECTION:")
        print("• Captures acceleration in bullish/bearish momentum")
        print("• Detects trend reversals and momentum shifts")
        print("• More responsive to market dynamics")
        print("")
        print("✅ DIRECTION CHANGES:")
        print("• From bearish (-10%) to bullish (+8%) = +18% change → BUY")
        print("• From bullish (+15%) to bearish (-2%) = -17% change → SELL")
        print("• Captures sentiment shifts better than absolute levels")
        print("")
        print("✅ ADAPTIVE THRESHOLDS:")
        print("• Works in different market conditions")
        print("• Not dependent on absolute strength levels")
        print("• Better suited for varying volatility environments")
        
        print("\n4️⃣ COMPARISON WITH OLD SYSTEM")
        print("-" * 40)
        
        comparison_cases = [
            # (prev, curr, old_signal, new_signal, scenario)
            (-5.0, 8.0, "NONE", "BUY", "Reversal detection"),
            (25.0, 20.0, "BUY", "NONE", "Avoid false signals"),
            (10.0, 30.0, "BUY", "BUY", "Strong momentum"),
            (-20.0, -10.0, "SELL", "BUY", "Momentum shift"),
        ]
        
        print("Previous | Current | Old Signal | New Signal | Scenario")
        print("-" * 60)
        
        for prev, curr, old_sig, new_sig, scenario in comparison_cases:
            # Old logic
            old_signal = "BUY" if curr > 15 else "SELL" if curr < -15 else "NONE"
            # New logic
            change = curr - prev
            new_signal = "BUY" if change >= 15 else "SELL" if change <= -15 else "NONE"
            
            old_ok = "✅" if old_signal == old_sig else "❌"
            new_ok = "✅" if new_signal == new_sig else "❌"
            
            print(f"{prev:8.1f} | {curr:7.1f} | {old_signal:10s} {old_ok} | {new_signal:10s} {new_ok} | {scenario}")
        
        print("\n5️⃣ EXPECTED BEHAVIOR CHANGES")
        print("-" * 40)
        
        print("📊 SIGNAL FREQUENCY:")
        print("• May generate fewer signals initially")
        print("• Requires previous data point for comparison")
        print("• First iteration will have no signal")
        print("")
        print("📈 SIGNAL QUALITY:")
        print("• Better momentum detection")
        print("• Fewer false signals in ranging markets")
        print("• More responsive to trend changes")
        print("")
        print("⚡ RESPONSIVENESS:")
        print("• Captures sudden momentum shifts")
        print("• Detects trend reversals faster")
        print("• Better suited for dynamic markets")
        
        print("\n6️⃣ SYSTEM INTEGRATION")
        print("-" * 30)
        
        print("✅ COMPATIBLE WITH EXISTING FEATURES:")
        print("• BB filtering still works (RANGING markets)")
        print("• Sensitive closing still at zero crossing")
        print("• Regime detection unchanged")
        print("• Risk management unchanged")
        print("")
        print("📝 LOGGING ENHANCEMENTS:")
        print("• Shows previous → current strength")
        print("• Displays change magnitude")
        print("• Explains signal reasoning")
        
        print("\n7️⃣ CONFIDENCE CALCULATION")
        print("-" * 35)
        
        print("NEW CONFIDENCE FORMULA:")
        print("• Based on change magnitude, not absolute strength")
        print("• confidence = min(abs(change) / 100, 1.0)")
        print("• Larger changes = Higher confidence")
        print("")
        print("EXAMPLES:")
        print("• +15% change → 0.15 confidence")
        print("• +25% change → 0.25 confidence")
        print("• +50% change → 0.50 confidence")
        print("• +100% change → 1.00 confidence (max)")
        
        print(f"\n✅ RELATIVE CHANGE SIGNALS TEST COMPLETE")
        print("=" * 55)
        print("📈 Signal generation now based on momentum changes")
        print("🎯 BUY: Strength increases by ≥15%")
        print("🎯 SELL: Strength decreases by ≥15%")
        print("⚡ More responsive to market dynamics")
        print("🚀 Better trend reversal and momentum detection")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_relative_change_signals()
