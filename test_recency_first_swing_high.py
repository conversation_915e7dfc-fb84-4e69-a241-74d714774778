#!/usr/bin/env python3
"""
Test the recency-first swing high detection approach
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_recency_first_swing_high():
    """Test that the algorithm prioritizes the most recent swing high"""
    print("🧪 TESTING RECENCY-FIRST SWING HIGH SELECTION")
    print("=" * 60)
    
    # Create trader instance
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test Scenario: Multiple swing highs where we want the MOST RECENT, not the highest
    print("\n📊 SCENARIO: Recent Swing High vs Older Higher Swing High")
    dates = pd.date_range(start='2024-01-01', periods=20, freq='5min')
    
    # Create data that matches your XAUUSD situation:
    # - Older higher swing high at 4218.12 (14 candles ago)
    # - More recent swing high at 4211.96 (5-6 candles ago) - SHOULD BE SELECTED
    
    highs = [
        4200.0, 4202.0, 4204.0, 4206.0, 4208.0,   # Rising
        4210.0, 4218.12, 4216.0, 4214.0, 4212.0,  # OLD HIGHER swing high at index 6 (4218.12)
        4210.0, 4208.0, 4206.0, 4204.0, 4211.96,  # Recent swing high at index 14 (4211.96)
        4210.0, 4208.0, 4206.0, 4204.0, 4202.0    # Decline after recent high
    ]
    
    lows = [
        4197.0, 4199.0, 4201.0, 4203.0, 4205.0,   # Rising
        4207.0, 4215.12, 4213.0, 4211.0, 4209.0,  # OLD HIGHER swing high
        4207.0, 4205.0, 4203.0, 4201.0, 4208.96,  # RECENT swing high at index 14
        4207.0, 4205.0, 4203.0, 4201.0, 4199.0    # Decline
    ]
    
    closes = [(h + l) / 2 for h, l in zip(highs, lows)]
    opens = closes.copy()
    
    # Add ATR column
    atr_values = [2.5] * 20  # Typical XAUUSD ATR
    
    df = pd.DataFrame({
        'time': dates,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'tick_volume': [100] * 20,
        'atr': atr_values
    }).set_index('time')
    
    print("📈 DATA ANALYSIS:")
    print("Swing High Candidates:")
    print(f"  📊 Older Higher High: 4218.12 (index 6, ~13 candles ago)")
    print(f"  📊 Recent Lower High: 4211.96 (index 14, ~5 candles ago)")
    print(f"  📊 Price Difference: {4218.12 - 4211.96:.2f} points")
    print()
    
    print("Expected Logic (RECENCY-FIRST):")
    print(f"  🎯 Should select: 4211.96 (most recent swing high)")
    print(f"  🎯 Should NOT select: 4218.12 (older, even though higher)")
    print()
    
    print("Last 8 candles:")
    for i in range(8):
        idx = -(8-i)
        candle = df.iloc[idx]
        marker = ""
        if len(df) + idx == 14:
            marker = " ← Expected Recent High (4211.96)"
        elif len(df) + idx == 6:
            marker = " ← Older Higher High (4218.12)"
        print(f"  {len(df) + idx:2d}: H={candle['high']:8.2f} L={candle['low']:8.2f}{marker}")
    
    # Run swing detection
    print(f"\n🔍 RUNNING RECENCY-FIRST SWING DETECTION:")
    swing_points = trader.find_recent_swing_points(df)
    
    print(f"\n📋 RESULTS:")
    success = True
    
    if swing_points['recent_high']:
        detected_high = swing_points['recent_high']
        expected_high = 4211.96  # Recent swing high
        older_high = 4218.12     # Older higher swing high
        candles_ago = swing_points['recent_high_candles_ago']
        
        print(f"✅ Detected Swing High: {detected_high:.2f}")
        print(f"   📅 Candles ago: {candles_ago}")
        
        if abs(detected_high - expected_high) < 0.01:
            print(f"   🎉 CORRECT: Selected most recent swing high (4211.96)")
            print(f"   ✅ Properly prioritized recency over absolute price")
            success = True
        elif abs(detected_high - older_high) < 0.01:
            print(f"   ❌ INCORRECT: Selected older higher swing high (4218.12)")
            print(f"   ⚠️  Should prioritize recent swing high (4211.96)")
            success = False
        else:
            print(f"   ❓ UNEXPECTED: Selected {detected_high:.2f}")
            success = False
    else:
        print("❌ No swing high detected")
        success = False
    
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}: Recency-first swing high selection test")
    print("=" * 60)
    
    if success:
        print("🎉 The algorithm correctly prioritizes RECENT swing highs!")
        print("📊 It selects the most recent valid swing high, not just the highest price")
        print("🔍 This should fix your XAUUSD issue: 4211.96 instead of 4218.12")
    else:
        print("⚠️  The algorithm still needs adjustment")
        print("🔧 The recency-first logic may need refinement for swing highs")
    
    return success

if __name__ == "__main__":
    test_recency_first_swing_high()
