#!/usr/bin/env python3
"""
Debug consecutive candle detection
"""

import sys
import pandas as pd
import numpy as np

sys.path.append('src')
from enhanced_regime_detector import EnhancedRegimeDetector

def test_consecutive_detection():
    """Test consecutive candle detection with known data"""
    print("🔍 DEBUGGING CONSECUTIVE CANDLE DETECTION")
    print("=" * 50)
    
    # Create perfect consecutive bullish candles
    dates = pd.date_range(start='2024-01-01', periods=20, freq='5T')
    
    # Create 8 consecutive bullish candles
    closes = [2000.0]  # Starting price
    for i in range(1, 20):
        if i <= 8:  # First 8 candles are bullish
            closes.append(closes[-1] + 2.0)  # Each candle closes 2 points higher
        else:  # Rest are random
            closes.append(closes[-1] + np.random.uniform(-1, 1))
    
    print("Test closes:", closes[:10])
    print("Expected: 8 consecutive bullish candles")
    
    # Create OHLC data
    opens = [closes[0]] + closes[:-1]  # Open = previous close
    highs = [max(o, c) + 0.5 for o, c in zip(opens, closes)]
    lows = [min(o, c) - 0.5 for o, c in zip(opens, closes)]
    
    df = pd.DataFrame({
        'datetime': dates,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': [1000] * 20
    })
    df.set_index('datetime', inplace=True)
    
    print("\nFirst 10 candles:")
    for i in range(10):
        direction = "UP" if closes[i] > (opens[i] if i == 0 else closes[i-1]) else "DOWN"
        print(f"Candle {i}: Close={closes[i]:.1f}, Direction={direction}")
    
    # Test the detector
    detector = EnhancedRegimeDetector("XAUUSD!", "M5")
    
    # Test consecutive counting directly
    consecutive, direction = detector._count_consecutive_direction(np.array(closes), 10)
    print(f"\nConsecutive count: {consecutive}")
    print(f"Direction (True=up): {direction}")

    # Manual debug of consecutive logic
    print("\nManual consecutive check:")
    closes_array = np.array(closes)
    print(f"Last close: {closes_array[-1]}")
    print(f"Second last close: {closes_array[-2]}")
    print(f"Direction: {closes_array[-1] > closes_array[-2]}")

    manual_consecutive = 1
    manual_direction = closes_array[-1] > closes_array[-2]

    for i in range(2, min(11, len(closes_array))):
        current_dir = closes_array[-i] > closes_array[-(i+1)]
        print(f"  Checking closes[{-i}]={closes_array[-i]:.1f} vs closes[{-(i+1)}]={closes_array[-(i+1)]:.1f}, dir={current_dir}")
        if current_dir == manual_direction:
            manual_consecutive += 1
            print(f"    Match! Consecutive now: {manual_consecutive}")
        else:
            print(f"    No match, breaking")
            break

    print(f"Manual consecutive result: {manual_consecutive}")
    
    # Test momentum analysis
    momentum_result = detector._analyze_momentum_strength(df)
    print(f"\nMomentum analysis result: {momentum_result}")
    
    # Expected: Should detect 8 consecutive bullish candles and give 10 points

def test_trend_strength():
    """Test trend strength calculation"""
    print("\n🔍 DEBUGGING TREND STRENGTH CALCULATION")
    print("=" * 50)
    
    # Create strong uptrend data
    closes = []
    base_price = 2000
    for i in range(20):
        # Strong upward trend: 5% total gain with 90% up moves
        trend_component = (i / 19) * base_price * 0.05  # 5% gain over 20 candles
        noise = np.random.uniform(-0.5, 0.5)  # Small noise
        closes.append(base_price + trend_component + noise)
    
    print("Trend test closes (first 10):", [f"{c:.1f}" for c in closes[:10]])
    print("Trend test closes (last 10):", [f"{c:.1f}" for c in closes[-10:]])
    
    detector = EnhancedRegimeDetector("XAUUSD!", "M5")
    
    # Test trend strength calculation
    trend_strength = detector._calculate_trend_strength(
        np.array(closes), np.array(closes), np.array(closes)
    )
    print(f"\nTrend strength result: {trend_strength}")
    
    # Calculate price change manually
    price_change = (closes[-1] - closes[0]) / closes[0] * 100
    print(f"Manual price change: {price_change:.2f}%")

if __name__ == "__main__":
    test_consecutive_detection()
    test_trend_strength()
