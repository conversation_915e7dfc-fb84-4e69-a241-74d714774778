#!/usr/bin/env python3
"""
Test script to verify MT5 connection to correct terminal
"""

import sys
import os
from datetime import datetime

# Add src to path
sys.path.append('src')
from mt5_integration import MT5<PERSON>anager

def test_mt5_connection():
    """Test MT5 connection to correct terminal"""
    print("🧪 MT5 CONNECTION TEST")
    print("=" * 50)
    
    # Check if MT5 terminal exists at specified path
    mt5_path = r"C:\Program Files\MetaTrader 5\terminal64.exe"
    print(f"🔍 Checking MT5 terminal path: {mt5_path}")
    
    if os.path.exists(mt5_path):
        print("   ✅ MT5 terminal found at correct path")
    else:
        print("   ❌ MT5 terminal NOT found at specified path")
        print("   📁 Please verify MetaTrader 5 installation location")
        return False
    
    # Check for wrong MT5 installation
    wrong_mt5_path = r"C:\Program Files\MetaTrader 5 2\terminal64.exe"
    print(f"🔍 Checking wrong MT5 path: {wrong_mt5_path}")
    
    if os.path.exists(wrong_mt5_path):
        print("   ⚠️  Wrong MT5 terminal found (this was causing the issue)")
    else:
        print("   ✅ Wrong MT5 terminal not found")
    
    print("\n🔌 Testing MT5 Connection...")
    
    # Test connection
    try:
        mt5_manager = MT5Manager("XAUUSD!")
        
        print("   📡 Attempting to connect to MT5...")
        success = mt5_manager.connect()
        
        if success:
            print("   ✅ Successfully connected to MT5!")
            
            # Get account info
            account_info = mt5_manager.get_account_info()
            if account_info:
                print(f"   📊 Account: {account_info.get('login', 'N/A')}")
                print(f"   💰 Balance: ${account_info.get('balance', 'N/A')}")
                print(f"   🏢 Company: {account_info.get('company', 'N/A')}")
                print(f"   🌐 Server: {account_info.get('server', 'N/A')}")
            
            # Test symbol info
            symbol_info = mt5_manager.get_symbol_info_tick("XAUUSD!")
            if symbol_info:
                print(f"   📈 XAUUSD! Bid: {symbol_info.get('bid', 'N/A')}")
                print(f"   📉 XAUUSD! Ask: {symbol_info.get('ask', 'N/A')}")
                print(f"   🕐 Last Update: {datetime.fromtimestamp(symbol_info.get('time', 0))}")
            
            # Test positions
            positions = mt5_manager.get_positions("XAUUSD!")
            print(f"   📊 Open XAUUSD! positions: {len(positions)}")
            
            if positions:
                for i, pos in enumerate(positions):
                    print(f"      Position {i+1}: {pos.get('type_str', 'N/A')} "
                          f"Volume: {pos.get('volume', 'N/A')} "
                          f"Price: {pos.get('price_open', 'N/A')}")
            
            # Disconnect
            mt5_manager.disconnect()
            print("   🔌 Disconnected from MT5")
            
            return True
            
        else:
            print("   ❌ Failed to connect to MT5")
            return False
            
    except Exception as e:
        print(f"   ❌ Connection error: {e}")
        return False

def main():
    """Run MT5 connection test"""
    print("🚀 TESTING CORRECT MT5 TERMINAL CONNECTION")
    print("=" * 60)
    print(f"⏰ Test Time: {datetime.now()}")
    print()
    
    # Test MT5 Connection
    connection_success = test_mt5_connection()
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 30)
    print(f"✅ MT5 Connection: {'PASS' if connection_success else 'FAIL'}")
    
    if connection_success:
        print("\n🎉 CONNECTION TEST PASSED!")
        print("✅ MT5 is now connecting to the correct terminal")
        print("✅ Your pending orders should now work properly")
    else:
        print("\n❌ CONNECTION TEST FAILED")
        print("🔧 Make sure MetaTrader 5 is running and logged in")

if __name__ == "__main__":
    main()
