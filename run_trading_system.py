#!/usr/bin/env python3
"""
Multi-Symbol Trading System Runner
Easily run the trading system with different symbols
"""

import sys
import os

def show_usage():
    """Show usage instructions"""
    print("🚀 MULTI-SYMBOL TRADING SYSTEM")
    print("=" * 50)
    print("📊 Supported Symbols:")
    print("   • XAUUSD! (Gold) - Contract Size: 100 ounces")
    print("   • EURUSD! (Euro) - Contract Size: 100,000 units") 
    print("   • BTCUSD (Bitcoin) - Contract Size: 1 BTC")
    print()
    print("💡 Usage:")
    print("   python run_trading_system.py [SYMBOL]")
    print()
    print("📈 Examples:")
    print("   python run_trading_system.py BTCUSD")
    print("   python run_trading_system.py EURUSD")
    print("   python run_trading_system.py XAUUSD")
    print("   python run_trading_system.py          # Defaults to XAUUSD")
    print()
    print("🔧 Features:")
    print("   • 4% risk per trade")
    print("   • 1.5 ATR stop loss")
    print("   • Symbol-specific position sizing")
    print("   • QQE + Candle Strength signals")
    print("   • Regime-based trading logic")
    print("=" * 50)

def main():
    """Main function"""
    # Default symbol
    symbol = "XAUUSD!"
    
    # Check for command line argument
    if len(sys.argv) > 1:
        arg = sys.argv[1].upper()
        
        if arg in ['-H', '--HELP', 'HELP']:
            show_usage()
            return
            
        # Map common inputs to correct symbol format
        if arg == "BTCUSD":
            symbol = "BTCUSD"
        elif arg == "EURUSD":
            symbol = "EURUSD!"
        elif arg == "XAUUSD":
            symbol = "XAUUSD!"
        else:
            print(f"⚠️  Unknown symbol: {arg}")
            print("🔍 Supported symbols: BTCUSD, EURUSD, XAUUSD")
            show_usage()
            return
    
    print(f"🎯 Starting trading system with {symbol}")
    print("⚠️  Press Ctrl+C to stop the system")
    print()
    
    # Import and run the trading system
    try:
        from fixed_live_trader import FixedLiveTrader
        
        # Create and start trader
        trader = FixedLiveTrader(symbol=symbol)
        trader.start_trading()
        
    except KeyboardInterrupt:
        print("\n🛑 Trading system stopped by user")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you're running from the correct directory")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
