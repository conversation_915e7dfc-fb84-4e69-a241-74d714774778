#!/usr/bin/env python3
"""
Analyze XAU_5m_data.csv to understand data structure and prepare for LSTM training
"""

import pandas as pd
from datetime import datetime, timedelta
import numpy as np

def analyze_xau_data():
    """Analyze the XAU 5m data file"""
    print("Loading XAU 5m data...")
    
    # Load the data
    df = pd.read_csv('data/XAU_5m_data.csv')
    df['datetime'] = pd.to_datetime(df['datetime'])
    
    # Get data info
    print('=== DATA OVERVIEW ===')
    print(f'Total records: {len(df):,}')
    print(f'Date range: {df["datetime"].min()} to {df["datetime"].max()}')
    print(f'Data span: {(df["datetime"].max() - df["datetime"].min()).days} days')
    
    # Check recent data (last 12 months)
    twelve_months_ago = df['datetime'].max() - timedelta(days=365)
    six_months_ago = df['datetime'].max() - timedelta(days=180)
    
    recent_12m = df[df['datetime'] >= twelve_months_ago]
    recent_6m = df[df['datetime'] >= six_months_ago]
    
    print(f'\n=== RECENT DATA ===')
    print(f'Last 12 months: {len(recent_12m):,} records ({twelve_months_ago.date()} to {df["datetime"].max().date()})')
    print(f'Last 6 months: {len(recent_6m):,} records ({six_months_ago.date()} to {df["datetime"].max().date()})')
    
    # Check data quality
    print(f'\n=== DATA QUALITY ===')
    print(f'Missing values: {df.isnull().sum().sum()}')
    print(f'Duplicate timestamps: {df["datetime"].duplicated().sum()}')
    
    # Check for gaps in data
    df_sorted = df.sort_values('datetime')
    time_diffs = df_sorted['datetime'].diff()
    expected_diff = timedelta(minutes=5)
    gaps = time_diffs[time_diffs > expected_diff]
    
    print(f'Data gaps (>5min): {len(gaps)}')
    if len(gaps) > 0:
        print(f'Largest gap: {gaps.max()}')
    
    # Show recent sample
    print(f'\n=== RECENT DATA SAMPLE ===')
    print(recent_6m.tail(10))
    
    # Basic statistics
    print(f'\n=== PRICE STATISTICS (Last 6 months) ===')
    print(recent_6m[['open', 'high', 'low', 'close', 'volume']].describe())
    
    return recent_12m, recent_6m

if __name__ == "__main__":
    recent_12m, recent_6m = analyze_xau_data()
