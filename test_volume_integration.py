#!/usr/bin/env python3
"""
Comprehensive Test Script for Tick Volume Integration
Tests all volume enhancements to ensure correct implementation
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')

# Import the enhanced components
from qqe_indicator import QQEIndicator
from fixed_live_trader import FixedLiveTrader, FixedFeatureEngineer, RegimeDetector

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_volume_integration.log')
    ]
)
logger = logging.getLogger(__name__)

def create_test_data():
    """Create synthetic OHLCV data for testing"""
    logger.info("📊 Creating synthetic test data...")
    
    # Create 100 periods of test data
    np.random.seed(42)  # For reproducible results
    periods = 100
    
    # Base price around 2000 (like XAUUSD)
    base_price = 2000.0
    prices = []
    volumes = []
    
    current_price = base_price
    for i in range(periods):
        # Random price movement
        change = np.random.normal(0, 0.5)  # 0.5% average movement
        current_price += change
        
        # Create OHLC from current price
        high = current_price + abs(np.random.normal(0, 0.2))
        low = current_price - abs(np.random.normal(0, 0.2))
        open_price = current_price + np.random.normal(0, 0.1)
        close = current_price
        
        # Create volume with some patterns
        base_volume = 1000
        if i > 50:  # Create volume divergence scenario
            if change > 0:  # Price up
                volume = base_volume * np.random.uniform(0.5, 0.8)  # Volume down (bearish divergence)
            else:  # Price down
                volume = base_volume * np.random.uniform(1.2, 1.8)  # Volume up (bullish divergence)
        else:
            # Normal volume correlation
            volume = base_volume * (1 + abs(change) * 2) * np.random.uniform(0.8, 1.2)
        
        prices.append([open_price, high, low, close])
        volumes.append(volume)
    
    # Create DataFrame
    df = pd.DataFrame(prices, columns=['open', 'high', 'low', 'close'])
    df['volume'] = volumes
    df['datetime'] = pd.date_range(start='2024-01-01', periods=periods, freq='5T')
    df.set_index('datetime', inplace=True)
    
    logger.info(f"✅ Created {len(df)} periods of test data")
    logger.info(f"   Price range: {df['close'].min():.2f} - {df['close'].max():.2f}")
    logger.info(f"   Volume range: {df['volume'].min():.0f} - {df['volume'].max():.0f}")
    
    return df

def test_qqe_volume_indicators():
    """Test QQE indicator volume analysis"""
    logger.info("\n🔍 TESTING QQE VOLUME INDICATORS...")
    
    try:
        # Create QQE indicator - FIXED: Match user's TradingView settings
        qqe = QQEIndicator(
            rsi_period=7,  # USER'S TRADINGVIEW SETTING: 7
            rsi_smoothing=5,
            qqe_factor=1.0,  # USER'S TRADINGVIEW SETTING: 1
            threshold=10,
            volume_lookback=20,
            volume_divergence_lookback=10
        )
        
        # Get test data
        df = create_test_data()
        
        # Test volume indicators calculation
        df_with_volume = qqe.calculate_volume_indicators(df)
        
        # Check if volume indicators were added
        expected_columns = [
            'volume_sma', 'volume_ratio', 'volume_momentum', 
            'volume_weighted_momentum', 'volume_trend', 'volume_strength'
        ]
        
        for col in expected_columns:
            if col in df_with_volume.columns:
                logger.info(f"   ✅ {col}: Added successfully")
                logger.info(f"      Latest value: {df_with_volume[col].iloc[-1]}")
            else:
                logger.error(f"   ❌ {col}: Missing!")
                return False
        
        # Test divergence detection
        df_with_divergence = qqe.detect_volume_divergence(df_with_volume)
        
        divergence_columns = [
            'price_direction', 'volume_direction', 'volume_divergence',
            'divergence_strength', 'divergence_type'
        ]
        
        for col in divergence_columns:
            if col in df_with_divergence.columns:
                logger.info(f"   ✅ {col}: Added successfully")
            else:
                logger.error(f"   ❌ {col}: Missing!")
                return False
        
        # Check for divergence detection
        divergence_count = df_with_divergence['volume_divergence'].sum()
        logger.info(f"   📊 Divergence periods detected: {divergence_count}")
        
        # Show divergence types
        divergence_types = df_with_divergence['divergence_type'].value_counts()
        logger.info(f"   📊 Divergence types: {dict(divergence_types)}")
        
        logger.info("✅ QQE Volume Indicators Test: PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ QQE Volume Indicators Test: FAILED - {e}")
        return False

def test_qqe_signal_generation():
    """Test QQE signal generation with volume enhancement"""
    logger.info("\n🎯 TESTING QQE SIGNAL GENERATION WITH VOLUME...")
    
    try:
        # Create QQE indicator
        qqe = QQEIndicator()
        
        # Get test data
        df = create_test_data()
        
        # Calculate QQE bands and signals
        df_with_qqe = qqe.calculate_qqe_bands(df)
        
        # Check if QQE columns were added
        qqe_columns = [
            'rsi', 'rsi_ma', 'longband', 'shortband', 'trend', 'fast_atr_rsi_tl',
            'qqe_long_count', 'qqe_short_count', 'qqe_signal', 'qqe_signal_strength',
            'qqe_volume_confirmation', 'qqe_divergence_filter'
        ]
        
        for col in qqe_columns:
            if col in df_with_qqe.columns:
                logger.info(f"   ✅ {col}: Added successfully")
            else:
                logger.error(f"   ❌ {col}: Missing!")
                return False
        
        # Test QQE analysis
        qqe_analysis = qqe.get_qqe_analysis(df_with_qqe)
        
        logger.info("   📊 QQE Analysis Results:")
        for key, value in qqe_analysis.items():
            if isinstance(value, float):
                logger.info(f"      {key}: {value:.3f}")
            else:
                logger.info(f"      {key}: {value}")
        
        # Check volume-specific fields
        volume_fields = ['volume_ratio', 'volume_strength', 'volume_confirmation', 'divergence_type', 'divergence_filter']
        for field in volume_fields:
            if field in qqe_analysis:
                logger.info(f"   ✅ Volume field {field}: {qqe_analysis[field]}")
            else:
                logger.error(f"   ❌ Volume field {field}: Missing!")
                return False
        
        logger.info("✅ QQE Signal Generation Test: PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ QQE Signal Generation Test: FAILED - {e}")
        return False

def test_regime_detection_volume():
    """Test regime detection with volume factors"""
    logger.info("\n🏛️ TESTING REGIME DETECTION WITH VOLUME...")
    
    try:
        # Create feature engineer and regime detector
        feature_engineer = FixedFeatureEngineer()
        regime_detector = RegimeDetector()
        
        # Get test data
        df = create_test_data()
        
        # Create technical indicators
        df_with_features = feature_engineer.create_technical_indicators(df)
        
        # Check if volume features were added
        volume_features = ['volume_sma', 'volume_ratio', 'volume_sma_short', 'volume_trend', 'volume_momentum', 'volume_volatility']
        
        for feature in volume_features:
            if feature in df_with_features.columns:
                logger.info(f"   ✅ Volume feature {feature}: Added successfully")
                logger.info(f"      Latest value: {df_with_features[feature].iloc[-1]}")
            else:
                logger.error(f"   ❌ Volume feature {feature}: Missing!")
                return False
        
        # Calculate regime indicators
        df_with_regime = regime_detector.calculate_regime_indicators(df_with_features)
        
        # Test regime detection
        regime, confidence, details, trend_direction, accurate_trend_direction = regime_detector.detect_regime(df_with_regime)
        
        logger.info(f"   📊 Regime Detection Results:")
        logger.info(f"      Regime: {regime}")
        logger.info(f"      Confidence: {confidence:.3f}")
        logger.info(f"      Trend Direction: {accurate_trend_direction}")
        
        # Check if volume factors are in reasoning
        reasoning = details.get('reasoning', [])
        volume_reasoning_found = False
        for reason in reasoning:
            if 'Volume' in reason:
                logger.info(f"   ✅ Volume reasoning: {reason}")
                volume_reasoning_found = True
        
        if not volume_reasoning_found:
            logger.error("   ❌ No volume reasoning found in regime detection!")
            return False
        
        # Check updated max score
        trending_score = details.get('trending_score', 0)
        ranging_score = details.get('ranging_score', 0)
        max_score = max(trending_score, ranging_score)
        
        logger.info(f"   📊 Scoring: Trending {trending_score:.1f}, Ranging {ranging_score:.1f}")
        logger.info(f"   📊 Max possible score should be 19.5 (was 17.5)")
        
        logger.info("✅ Regime Detection Volume Test: PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Regime Detection Volume Test: FAILED - {e}")
        return False

def test_full_system_integration():
    """Test full system integration"""
    logger.info("\n🚀 TESTING FULL SYSTEM INTEGRATION...")
    
    try:
        # Create FixedLiveTrader instance
        trader = FixedLiveTrader(symbol="XAUUSD!")
        
        logger.info("   ✅ FixedLiveTrader created successfully")
        
        # Check if QQE indicator has volume parameters
        qqe = trader.qqe_indicator
        if hasattr(qqe, 'volume_lookback') and hasattr(qqe, 'volume_divergence_lookback'):
            logger.info(f"   ✅ QQE volume parameters: lookback={qqe.volume_lookback}, divergence={qqe.volume_divergence_lookback}")
        else:
            logger.error("   ❌ QQE volume parameters missing!")
            return False
        
        # Test volume confirmation method
        if hasattr(qqe, '_calculate_volume_confirmation'):
            test_confirmation = qqe._calculate_volume_confirmation(1.5, 'HIGH')
            logger.info(f"   ✅ Volume confirmation method works: {test_confirmation:.2f}")
        else:
            logger.error("   ❌ Volume confirmation method missing!")
            return False
        
        logger.info("✅ Full System Integration Test: PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Full System Integration Test: FAILED - {e}")
        return False

def run_live_system_test():
    """Test the actual live system with real data"""
    logger.info("\n🔴 TESTING LIVE SYSTEM WITH REAL DATA...")
    
    try:
        # Create trader instance
        trader = FixedLiveTrader(symbol="XAUUSD!")
        
        # Test getting live prediction (this will test all components)
        logger.info("   🔄 Getting live prediction...")
        result = trader.get_live_prediction()
        
        if result and len(result) >= 12:
            signal, confidence, details, atr, regime, logic, regime_details, candle_strength, qqe_analysis, features_df, half_size, swing_factor = result
            
            logger.info("   ✅ Live prediction successful!")
            logger.info(f"      Signal: {signal}")
            logger.info(f"      Confidence: {confidence:.3f}")
            logger.info(f"      Regime: {regime}")
            logger.info(f"      Details: {details}")
            
            # Check if volume information is in details
            if 'Vol:' in details:
                logger.info("   ✅ Volume information found in details")
            else:
                logger.error("   ❌ Volume information missing from details!")
                return False
            
            # Check QQE analysis for volume fields
            volume_fields = ['volume_ratio', 'volume_strength', 'volume_confirmation', 'divergence_type']
            for field in volume_fields:
                if field in qqe_analysis:
                    logger.info(f"   ✅ QQE analysis has {field}: {qqe_analysis[field]}")
                else:
                    logger.error(f"   ❌ QQE analysis missing {field}!")
                    return False
            
            logger.info("✅ Live System Test: PASSED")
            return True
        else:
            logger.error("   ❌ Live prediction failed or returned incomplete data!")
            return False
            
    except Exception as e:
        logger.error(f"❌ Live System Test: FAILED - {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run all tests"""
    logger.info("🧪 STARTING COMPREHENSIVE TICK VOLUME INTEGRATION TESTS")
    logger.info("=" * 80)
    
    tests = [
        ("QQE Volume Indicators", test_qqe_volume_indicators),
        ("QQE Signal Generation", test_qqe_signal_generation),
        ("Regime Detection Volume", test_regime_detection_volume),
        ("Full System Integration", test_full_system_integration),
        ("Live System Test", run_live_system_test)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            logger.error(f"❌ {test_name}: FAILED with exception - {e}")
    
    logger.info("\n" + "=" * 80)
    logger.info("🏁 TEST SUMMARY")
    logger.info(f"   ✅ Passed: {passed}")
    logger.info(f"   ❌ Failed: {failed}")
    logger.info(f"   📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        logger.info("🎉 ALL TESTS PASSED! Volume integration is working correctly.")
        return True
    else:
        logger.error(f"⚠️ {failed} tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
