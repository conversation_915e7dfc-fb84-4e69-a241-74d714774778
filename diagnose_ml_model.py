#!/usr/bin/env python3
"""
Diagnostic script to investigate why ML model only generates BUY signals
"""

import sys
import pandas as pd
import numpy as np
import joblib
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager
from fixed_live_trader import FixedFeatureEngineer, RegimeDetector

def diagnose_ml_model():
    """Diagnose ML model feature values and predictions"""
    print("🔍 DIAGNOSING ML MODEL...")
    
    # Load model components
    try:
        model = joblib.load('models_fixed/xgboost_model.pkl')
        scaler = joblib.load('models_fixed/feature_scaler.pkl')
        selected_features = joblib.load('models_fixed/selected_features.pkl')
        
        print(f"✅ Model loaded successfully")
        print(f"📊 Selected features ({len(selected_features)}):")
        for i, feature in enumerate(selected_features):
            print(f"   {i+1:2d}. {feature}")
        print()
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return
    
    # Initialize components
    mt5_manager = MT5Manager()
    feature_engineer = FixedFeatureEngineer()
    regime_detector = RegimeDetector()
    
    # Connect to MT5
    if not mt5_manager.connect():
        print("❌ Cannot connect to MT5")
        return
    
    print("✅ Connected to MT5")
    
    # Get recent data
    try:
        df = mt5_manager.get_latest_data("XAUUSD!", "M5", 200)
        if df is None or len(df) < 100:
            print("❌ Cannot get sufficient data")
            return
        
        print(f"📈 Got {len(df)} candles")
        
        # Create features
        features_df = feature_engineer.create_technical_indicators(df)
        features_df = regime_detector.calculate_regime_indicators(features_df)
        
        # Get last 10 predictions to see the pattern
        print("\n🎯 LAST 10 ML PREDICTIONS:")
        print("=" * 80)
        
        for i in range(-10, 0):
            try:
                # Get features for this candle
                latest_features = features_df[selected_features].iloc[i]
                latest_features = latest_features.fillna(latest_features.median())

                # CRITICAL FIX: Convert absolute price features to relative features
                current_price = features_df['close'].iloc[i]
                price_features = [
                    'lowest_low_20', 'lowest_low_10', 'highest_high_10', 'highest_high_5',
                    'lowest_low_5', 'sma_50', 'highest_high_20', 'bb_upper', 'bb_lower',
                    'sma_10', 'sma_5', 'sma_20', 'bb_middle'
                ]

                # Convert absolute prices to relative ratios
                for feature in price_features:
                    if feature in latest_features.index:
                        latest_features[feature] = latest_features[feature] / current_price

                # Scale features
                features_scaled = scaler.transform(latest_features.values.reshape(1, -1))
                
                # Make prediction
                pred_proba = model.predict_proba(features_scaled)[0, 1]
                ml_confidence = abs(pred_proba - 0.5) * 2
                
                if pred_proba > 0.5:
                    raw_signal = "BUY"
                else:
                    raw_signal = "SELL"
                
                # Get timestamp
                timestamp = features_df.index[i].strftime("%H:%M")
                close_price = features_df['close'].iloc[i]
                
                print(f"{timestamp} | Close: {close_price:7.2f} | Prob: {pred_proba:.3f} | Signal: {raw_signal:4s} | Conf: {ml_confidence:.3f}")
                
            except Exception as e:
                print(f"Error processing candle {i}: {e}")
        
        # Analyze feature values for latest candle
        print(f"\n📊 FEATURE VALUES (Latest Candle):")
        print("=" * 80)
        
        latest_features = features_df[selected_features].iloc[-1]
        latest_features = latest_features.fillna(latest_features.median())

        # CRITICAL FIX: Convert absolute price features to relative features
        current_price = features_df['close'].iloc[-1]
        price_features = [
            'lowest_low_20', 'lowest_low_10', 'highest_high_10', 'highest_high_5',
            'lowest_low_5', 'sma_50', 'highest_high_20', 'bb_upper', 'bb_lower',
            'sma_10', 'sma_5', 'sma_20', 'bb_middle'
        ]

        # Convert absolute prices to relative ratios
        for feature in price_features:
            if feature in latest_features.index:
                latest_features[feature] = latest_features[feature] / current_price
        
        for feature, value in latest_features.items():
            print(f"{feature:25s}: {value:10.6f}")
        
        # Check for any obvious issues
        print(f"\n🔍 FEATURE ANALYSIS:")
        print("=" * 80)
        
        # Check for NaN values
        nan_count = latest_features.isna().sum()
        print(f"NaN values: {nan_count}")
        
        # Check for extreme values
        extreme_features = []
        for feature, value in latest_features.items():
            if abs(value) > 1000:
                extreme_features.append((feature, value))
        
        if extreme_features:
            print("Extreme values (>1000):")
            for feature, value in extreme_features:
                print(f"  {feature}: {value}")
        else:
            print("No extreme values detected")
        
        # Check feature distribution over last 50 candles
        print(f"\n📈 FEATURE TRENDS (Last 50 candles):")
        print("=" * 80)
        
        recent_features = features_df[selected_features].tail(50)
        
        for feature in selected_features[:5]:  # Show first 5 features
            values = recent_features[feature].dropna()
            if len(values) > 0:
                print(f"{feature:25s}: Min={values.min():8.4f} | Max={values.max():8.4f} | Mean={values.mean():8.4f} | Std={values.std():8.4f}")
        
        # Final prediction analysis
        print(f"\n🎯 PREDICTION ANALYSIS:")
        print("=" * 80)
        
        latest_features_scaled = scaler.transform(latest_features.values.reshape(1, -1))
        pred_proba = model.predict_proba(latest_features_scaled)[0, 1]
        
        print(f"Raw probability: {pred_proba:.6f}")
        print(f"Threshold: 0.5")
        print(f"Distance from threshold: {pred_proba - 0.5:+.6f}")
        print(f"Signal: {'BUY' if pred_proba > 0.5 else 'SELL'}")
        print(f"Confidence: {abs(pred_proba - 0.5) * 2:.3f}")
        
        # Check if model is biased
        recent_probas = []
        for i in range(-50, 0):
            try:
                features = features_df[selected_features].iloc[i].fillna(features_df[selected_features].iloc[i].median())

                # Apply the same price normalization fix
                current_price = features_df['close'].iloc[i]
                for feature in price_features:
                    if feature in features.index:
                        features[feature] = features[feature] / current_price

                features_scaled = scaler.transform(features.values.reshape(1, -1))
                prob = model.predict_proba(features_scaled)[0, 1]
                recent_probas.append(prob)
            except:
                continue
        
        if recent_probas:
            buy_signals = sum(1 for p in recent_probas if p > 0.5)
            sell_signals = len(recent_probas) - buy_signals
            
            print(f"\nLast 50 predictions:")
            print(f"BUY signals: {buy_signals} ({buy_signals/len(recent_probas)*100:.1f}%)")
            print(f"SELL signals: {sell_signals} ({sell_signals/len(recent_probas)*100:.1f}%)")
            print(f"Average probability: {np.mean(recent_probas):.3f}")
            
            if buy_signals > 45:  # More than 90% BUY signals
                print("⚠️  WARNING: Model heavily biased towards BUY signals!")
            elif sell_signals > 45:  # More than 90% SELL signals
                print("⚠️  WARNING: Model heavily biased towards SELL signals!")
            else:
                print("✅ Model appears balanced")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        mt5_manager.disconnect()

if __name__ == "__main__":
    diagnose_ml_model()
