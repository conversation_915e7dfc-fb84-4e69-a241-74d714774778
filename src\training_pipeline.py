"""
Training and Validation Pipeline
Handles model training, validation, and performance evaluation
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.metrics import precision_score, recall_score, f1_score, roc_auc_score
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
from pathlib import Path
import json

from config.config import *
from src.lstm_model import LSTMTradingModel
from src.data_preprocessing import DataPreprocessor

# Set up logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

class TrainingPipeline:
    """
    Complete training and validation pipeline for LSTM trading model
    """
    
    def __init__(self):
        self.model = None
        self.preprocessor = DataPreprocessor()
        self.training_results = {}
        self.validation_results = {}
        
    def train_model(self, data_dict: Dict) -> Dict:
        """
        Train LSTM model with prepared data
        
        Args:
            data_dict: Dictionary containing prepared datasets
            
        Returns:
            Dictionary with training results
        """
        try:
            logger.info("Starting model training pipeline...")
            
            # Extract data
            X_train, y_train = data_dict['train']
            X_val, y_val = data_dict['validation']
            X_test, y_test = data_dict['test']
            feature_columns = data_dict['feature_columns']
            class_weights = data_dict['class_weights']
            data_info = data_dict['data_info']
            
            logger.info(f"Training data shape: {X_train.shape}")
            logger.info(f"Validation data shape: {X_val.shape}")
            logger.info(f"Test data shape: {X_test.shape}")
            
            # Initialize model
            input_shape = (X_train.shape[1], X_train.shape[2])  # (sequence_length, num_features)
            num_classes = data_info['num_classes']
            
            self.model = LSTMTradingModel(input_shape, num_classes)
            
            # Build model
            model = self.model.build_model()
            if model is None:
                logger.error("Failed to build model")
                return {}
            
            # Train model
            history = self.model.train(X_train, y_train, X_val, y_val, class_weights)
            
            if not history:
                logger.error("Training failed")
                return {}
            
            # Evaluate on test set
            test_results = self.evaluate_model(X_test, y_test, "Test")
            
            # Evaluate on validation set
            val_results = self.evaluate_model(X_val, y_val, "Validation")
            
            # Evaluate on training set (to check for overfitting)
            train_results = self.evaluate_model(X_train, y_train, "Training")
            
            # Compile results
            results = {
                'training_history': history,
                'test_results': test_results,
                'validation_results': val_results,
                'training_results': train_results,
                'model_info': self.model.get_model_summary(),
                'data_info': data_info,
                'feature_columns': feature_columns
            }
            
            # Save results
            self._save_training_results(results)
            
            # Check for overfitting
            overfitting_analysis = self._analyze_overfitting(history, train_results, val_results)
            results['overfitting_analysis'] = overfitting_analysis
            
            logger.info("Model training pipeline completed successfully")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in training pipeline: {e}")
            return {}
    
    def evaluate_model(self, X: np.ndarray, y: np.ndarray, dataset_name: str = "") -> Dict:
        """
        Evaluate model performance
        
        Args:
            X: Input features
            y: True labels
            dataset_name: Name of dataset being evaluated
            
        Returns:
            Dictionary with evaluation metrics
        """
        try:
            if self.model is None or self.model.model is None:
                logger.error("Model not trained")
                return {}
            
            logger.info(f"Evaluating model on {dataset_name} set...")
            
            # Get predictions
            y_pred = self.model.predict(X)
            y_pred_proba = self.model.predict_proba(X)
            
            # Calculate metrics
            accuracy = accuracy_score(y, y_pred)
            precision = precision_score(y, y_pred, average='weighted', zero_division=0)
            recall = recall_score(y, y_pred, average='weighted', zero_division=0)
            f1 = f1_score(y, y_pred, average='weighted', zero_division=0)
            
            # Classification report
            class_report = classification_report(y, y_pred, output_dict=True, zero_division=0)
            
            # Confusion matrix
            conf_matrix = confusion_matrix(y, y_pred)
            
            # ROC AUC for binary classification
            roc_auc = None
            if len(np.unique(y)) == 2:
                try:
                    roc_auc = roc_auc_score(y, y_pred_proba[:, 1] if y_pred_proba.shape[1] > 1 else y_pred_proba)
                except:
                    roc_auc = None
            
            results = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'roc_auc': roc_auc,
                'classification_report': class_report,
                'confusion_matrix': conf_matrix.tolist(),
                'predictions': y_pred.tolist(),
                'true_labels': y.tolist()
            }
            
            logger.info(f"{dataset_name} Results:")
            logger.info(f"Accuracy: {accuracy:.4f}")
            logger.info(f"Precision: {precision:.4f}")
            logger.info(f"Recall: {recall:.4f}")
            logger.info(f"F1-Score: {f1:.4f}")
            if roc_auc:
                logger.info(f"ROC AUC: {roc_auc:.4f}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error evaluating model: {e}")
            return {}
    
    def _analyze_overfitting(self, history: Dict, train_results: Dict, val_results: Dict) -> Dict:
        """
        Analyze model for overfitting
        
        Args:
            history: Training history
            train_results: Training set results
            val_results: Validation set results
            
        Returns:
            Dictionary with overfitting analysis
        """
        try:
            analysis = {
                'is_overfitting': False,
                'is_underfitting': False,
                'recommendations': []
            }
            
            # Check training vs validation loss
            train_loss = history.get('loss', [])
            val_loss = history.get('val_loss', [])
            
            if train_loss and val_loss:
                final_train_loss = train_loss[-1]
                final_val_loss = val_loss[-1]
                
                # Overfitting indicators
                if final_val_loss > final_train_loss * 1.2:
                    analysis['is_overfitting'] = True
                    analysis['recommendations'].append("Model is overfitting. Consider reducing model complexity or adding more regularization.")
                
                # Check if validation loss is increasing while training loss decreases
                if len(val_loss) > 10:
                    recent_val_trend = np.mean(val_loss[-5:]) - np.mean(val_loss[-10:-5])
                    recent_train_trend = np.mean(train_loss[-5:]) - np.mean(train_loss[-10:-5])
                    
                    if recent_val_trend > 0 and recent_train_trend < 0:
                        analysis['is_overfitting'] = True
                        analysis['recommendations'].append("Validation loss increasing while training loss decreasing - clear overfitting.")
            
            # Check accuracy gap
            train_acc = train_results.get('accuracy', 0)
            val_acc = val_results.get('accuracy', 0)
            
            if train_acc - val_acc > 0.1:  # 10% accuracy gap
                analysis['is_overfitting'] = True
                analysis['recommendations'].append("Large accuracy gap between training and validation sets.")
            
            # Underfitting indicators
            if train_acc < 0.6 and val_acc < 0.6:
                analysis['is_underfitting'] = True
                analysis['recommendations'].append("Low accuracy on both sets suggests underfitting. Consider increasing model complexity.")
            
            # Good fit indicators
            if not analysis['is_overfitting'] and not analysis['is_underfitting']:
                if abs(train_acc - val_acc) < 0.05:
                    analysis['recommendations'].append("Model appears to have good fit with minimal overfitting.")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing overfitting: {e}")
            return {'is_overfitting': False, 'is_underfitting': False, 'recommendations': []}
    
    def cross_validate(self, df: pd.DataFrame, n_splits: int = CROSS_VALIDATION_FOLDS) -> Dict:
        """
        Perform walk-forward cross-validation
        
        Args:
            df: DataFrame with features and targets
            n_splits: Number of cross-validation splits
            
        Returns:
            Dictionary with cross-validation results
        """
        try:
            logger.info(f"Starting {n_splits}-fold walk-forward cross-validation...")
            
            # Create walk-forward splits
            splits = self.preprocessor.create_walk_forward_splits(df, n_splits)
            
            cv_results = {
                'fold_results': [],
                'mean_accuracy': 0,
                'std_accuracy': 0,
                'mean_f1': 0,
                'std_f1': 0
            }
            
            accuracies = []
            f1_scores = []
            
            for fold, (train_idx, test_idx) in enumerate(splits):
                logger.info(f"Training fold {fold + 1}/{n_splits}")
                
                # Split data
                train_data = df.iloc[train_idx]
                test_data = df.iloc[test_idx]
                
                # Prepare data for this fold
                fold_data = self.preprocessor.prepare_dataset(train_data)
                if not fold_data:
                    logger.error(f"Failed to prepare data for fold {fold + 1}")
                    continue
                
                # Train model for this fold
                fold_results = self.train_model(fold_data)
                
                if fold_results:
                    test_acc = fold_results['test_results']['accuracy']
                    test_f1 = fold_results['test_results']['f1_score']
                    
                    accuracies.append(test_acc)
                    f1_scores.append(test_f1)
                    
                    cv_results['fold_results'].append({
                        'fold': fold + 1,
                        'accuracy': test_acc,
                        'f1_score': test_f1,
                        'results': fold_results
                    })
                    
                    logger.info(f"Fold {fold + 1} - Accuracy: {test_acc:.4f}, F1: {test_f1:.4f}")
            
            # Calculate summary statistics
            if accuracies:
                cv_results['mean_accuracy'] = np.mean(accuracies)
                cv_results['std_accuracy'] = np.std(accuracies)
                cv_results['mean_f1'] = np.mean(f1_scores)
                cv_results['std_f1'] = np.std(f1_scores)
                
                logger.info(f"Cross-validation completed:")
                logger.info(f"Mean Accuracy: {cv_results['mean_accuracy']:.4f} ± {cv_results['std_accuracy']:.4f}")
                logger.info(f"Mean F1-Score: {cv_results['mean_f1']:.4f} ± {cv_results['std_f1']:.4f}")
            
            return cv_results
            
        except Exception as e:
            logger.error(f"Error in cross-validation: {e}")
            return {}
    
    def _save_training_results(self, results: Dict):
        """
        Save training results to files
        
        Args:
            results: Training results dictionary
        """
        try:
            # Save results as JSON
            results_file = REPORTS_DIR / "training_results.json"
            
            # Convert numpy arrays to lists for JSON serialization
            json_results = self._convert_numpy_to_list(results.copy())
            
            with open(results_file, 'w') as f:
                json.dump(json_results, f, indent=2, default=str)
            
            logger.info(f"Training results saved to {results_file}")
            
            # Save model
            model_file = MODELS_DIR / "lstm_trading_model.h5"
            if self.model and self.model.model:
                self.model.save_model(str(model_file))
            
            # Save preprocessor
            preprocessor_file = MODELS_DIR / "preprocessor.joblib"
            joblib.dump(self.preprocessor, preprocessor_file)
            logger.info(f"Preprocessor saved to {preprocessor_file}")
            
        except Exception as e:
            logger.error(f"Error saving training results: {e}")
    
    def _convert_numpy_to_list(self, obj):
        """Convert numpy arrays to lists for JSON serialization"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: self._convert_numpy_to_list(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_numpy_to_list(item) for item in obj]
        else:
            return obj
