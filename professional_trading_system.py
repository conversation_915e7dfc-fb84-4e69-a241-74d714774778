#!/usr/bin/env python3
"""
Professional Rule-Based Trading System
No ML predictions - Uses proven technical analysis and risk management
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager

class TechnicalAnalyzer:
    """Calculate technical indicators"""
    
    @staticmethod
    def calculate_ema(data, period):
        """Calculate Exponential Moving Average"""
        return data.ewm(span=period).mean()
    
    @staticmethod
    def calculate_rsi(data, period=14):
        """Calculate RSI"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    @staticmethod
    def calculate_atr(high, low, close, period=14):
        """Calculate Average True Range"""
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window=period).mean()
    
    @staticmethod
    def calculate_bollinger_bands(data, period=20, std_dev=2):
        """Calculate Bollinger Bands"""
        sma = data.rolling(window=period).mean()
        std = data.rolling(window=period).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower

class TrendAnalyzer:
    """Analyze market trend and strength"""
    
    def __init__(self):
        self.tech = TechnicalAnalyzer()
    
    def get_trend_direction(self, df):
        """Determine trend direction using EMAs"""
        df['ema_20'] = self.tech.calculate_ema(df['close'], 20)
        df['ema_50'] = self.tech.calculate_ema(df['close'], 50)
        
        # Trend direction
        df['trend'] = np.where(df['ema_20'] > df['ema_50'], 1,  # Uptrend
                              np.where(df['ema_20'] < df['ema_50'], -1, 0))  # Downtrend
        
        # Trend strength (distance between EMAs)
        df['trend_strength'] = abs(df['ema_20'] - df['ema_50']) / df['close']
        
        return df
    
    def get_support_resistance(self, df, lookback=20):
        """Identify support and resistance levels"""
        df['swing_high'] = df['high'].rolling(window=lookback, center=True).max()
        df['swing_low'] = df['low'].rolling(window=lookback, center=True).min()
        
        # Current price relative to swing levels
        df['above_resistance'] = df['close'] > df['swing_high'].shift(1)
        df['below_support'] = df['close'] < df['swing_low'].shift(1)
        
        return df

class SignalGenerator:
    """Generate trading signals based on rules"""
    
    def __init__(self):
        self.tech = TechnicalAnalyzer()
        self.trend = TrendAnalyzer()
    
    def generate_signals(self, df):
        """Generate buy/sell signals"""
        # Calculate all indicators
        df = self.trend.get_trend_direction(df)
        df = self.trend.get_support_resistance(df)
        
        df['rsi'] = self.tech.calculate_rsi(df['close'])
        df['atr'] = self.tech.calculate_atr(df['high'], df['low'], df['close'])
        
        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = self.tech.calculate_bollinger_bands(df['close'])
        df['bb_upper'] = bb_upper
        df['bb_middle'] = bb_middle
        df['bb_lower'] = bb_lower
        
        # Price position in Bollinger Bands
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # Generate signals
        df['signal'] = 0
        df['signal_strength'] = 0.0
        
        # LONG SIGNALS - Higher quality signals
        long_conditions = (
            (df['trend'] == 1) &  # Uptrend
            (df['trend_strength'] > 0.0005) &  # Minimum trend strength
            (df['rsi'] > 40) & (df['rsi'] < 60) &  # RSI in neutral zone
            (df['close'] > df['ema_20']) &  # Price above short EMA
            (df['close'] > df['ema_50']) &  # Price above long EMA too
            (df['bb_position'] > 0.4) & (df['bb_position'] < 0.6)  # Middle of BB
        )

        # SHORT SIGNALS - Higher quality signals
        short_conditions = (
            (df['trend'] == -1) &  # Downtrend
            (df['trend_strength'] > 0.0005) &  # Minimum trend strength
            (df['rsi'] > 40) & (df['rsi'] < 60) &  # RSI in neutral zone
            (df['close'] < df['ema_20']) &  # Price below short EMA
            (df['close'] < df['ema_50']) &  # Price below long EMA too
            (df['bb_position'] > 0.4) & (df['bb_position'] < 0.6)  # Middle of BB
        )
        
        df.loc[long_conditions, 'signal'] = 1
        df.loc[short_conditions, 'signal'] = -1
        
        # Calculate signal strength
        df.loc[df['signal'] != 0, 'signal_strength'] = (
            df['trend_strength'] * 100 +  # Trend strength
            abs(50 - df['rsi']) / 50 * 0.5 +  # RSI distance from neutral
            (1 - abs(df['bb_position'] - 0.5) * 2) * 0.3  # BB position
        )
        
        return df

class RiskManager:
    """Manage position sizing and risk"""
    
    def __init__(self, account_balance, risk_percent=4.0, atr_multiplier=1.5):
        self.account_balance = account_balance
        self.risk_percent = risk_percent
        self.atr_multiplier = atr_multiplier
    
    def calculate_position_size(self, entry_price, atr_value):
        """Calculate position size based on ATR stop loss"""
        risk_amount = self.account_balance * (self.risk_percent / 100)
        stop_distance = atr_value * self.atr_multiplier
        
        # Position size = Risk Amount / Stop Distance
        position_size = risk_amount / stop_distance
        
        # Convert to lots (assuming 1 lot = 100 units for XAUUSD)
        lot_size = position_size / 100
        
        # Round to 2 decimal places and ensure minimum
        lot_size = max(0.01, round(lot_size, 2))
        
        return lot_size
    
    def calculate_stops_targets(self, entry_price, atr_value, signal_direction):
        """Calculate stop loss and take profit levels"""
        stop_distance = atr_value * self.atr_multiplier
        target_distance = atr_value * 2.0  # Risk:Reward 1:1.33 (better ratio)

        if signal_direction == 1:  # Long
            stop_loss = entry_price - stop_distance
            take_profit = entry_price + target_distance
        else:  # Short
            stop_loss = entry_price + stop_distance
            take_profit = entry_price - target_distance

        return stop_loss, take_profit

class ProfessionalTradingSystem:
    """Main trading system"""
    
    def __init__(self):
        self.mt5_manager = MT5Manager()
        self.signal_generator = SignalGenerator()
        self.risk_manager = None
        self.last_signal_time = None
        self.min_signal_interval = 300  # 5 minutes between signals
    
    def initialize(self):
        """Initialize the trading system"""
        print("🚀 PROFESSIONAL RULE-BASED TRADING SYSTEM")
        print("=" * 60)
        print("✅ No ML predictions - Pure technical analysis")
        print("✅ Proven risk management principles")
        print("✅ Multiple confirmation signals")
        print("=" * 60)
        
        if not self.mt5_manager.connect():
            return False
        
        # Get account info
        account_info = self.mt5_manager.get_account_info()
        if account_info:
            balance = account_info['balance']
            self.risk_manager = RiskManager(balance)
            print(f"💰 Account Balance: ${balance:.2f}")
            print(f"🎯 Risk per trade: 4% (${balance * 0.04:.2f})")
            return True
        
        return False
    
    def analyze_market(self):
        """Analyze current market conditions"""
        # Get recent data
        df = self.mt5_manager.get_latest_data("XAUUSD!", "M5", 200)
        if df is None or len(df) < 100:
            return None, None
        
        # Generate signals
        df = self.signal_generator.generate_signals(df)
        
        # Get latest signal
        latest = df.iloc[-1]
        
        # Check if we have a valid signal
        if latest['signal'] == 0:
            return None, df
        
        # Check minimum time between signals
        current_time = datetime.now()
        if (self.last_signal_time and 
            (current_time - self.last_signal_time).seconds < self.min_signal_interval):
            return None, df
        
        return latest, df
    
    def execute_trade(self, signal_data):
        """Execute trade based on signal"""
        try:
            # Get current price
            tick = self.mt5_manager.get_symbol_info_tick("XAUUSD!")
            if not tick:
                return False
            
            entry_price = tick['ask'] if signal_data['signal'] == 1 else tick['bid']
            atr_value = signal_data['atr']
            
            # Calculate position size and levels
            lot_size = self.risk_manager.calculate_position_size(entry_price, atr_value)
            stop_loss, take_profit = self.risk_manager.calculate_stops_targets(
                entry_price, atr_value, signal_data['signal']
            )
            
            # Determine order type
            order_type = "buy" if signal_data['signal'] == 1 else "sell"
            
            print(f"\n🎯 TRADE SIGNAL DETECTED!")
            print(f"   Direction: {'LONG' if signal_data['signal'] == 1 else 'SHORT'}")
            print(f"   Entry Price: {entry_price:.2f}")
            print(f"   Position Size: {lot_size:.2f} lots")
            print(f"   Stop Loss: {stop_loss:.2f}")
            print(f"   Take Profit: {take_profit:.2f}")
            print(f"   Signal Strength: {signal_data['signal_strength']:.2f}")
            print(f"   ATR: {atr_value:.2f}")
            
            # Execute the trade
            success = self.mt5_manager.place_order(
                symbol="XAUUSD!",
                order_type=order_type,
                lot_size=lot_size,
                price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                comment=f"RuleBased-{signal_data['signal_strength']:.1f}"
            )
            
            if success:
                self.last_signal_time = datetime.now()
                print("✅ Trade executed successfully!")
                return True
            else:
                print("❌ Trade execution failed!")
                return False
                
        except Exception as e:
            print(f"❌ Error executing trade: {e}")
            return False
    
    def run_live_trading(self):
        """Run live trading system"""
        if not self.initialize():
            print("❌ Failed to initialize trading system")
            return
        
        print("\n🔄 Starting live trading...")
        print("Press Ctrl+C to stop")
        
        try:
            while True:
                # Analyze market
                signal, market_data = self.analyze_market()
                
                if signal is not None:
                    # We have a signal - execute trade
                    self.execute_trade(signal)
                else:
                    # No signal - show market status
                    if market_data is not None:
                        latest = market_data.iloc[-1]
                        print(f"\n📊 Market Analysis ({datetime.now().strftime('%H:%M:%S')})")
                        print(f"   Price: {latest['close']:.2f}")
                        print(f"   Trend: {'UP' if latest['trend'] == 1 else 'DOWN' if latest['trend'] == -1 else 'SIDEWAYS'}")
                        print(f"   RSI: {latest['rsi']:.1f}")
                        print(f"   Signal: None (waiting for setup)")
                
                # Wait 5 minutes before next analysis
                import time
                time.sleep(300)
                
        except KeyboardInterrupt:
            print("\n🛑 Trading stopped by user")
        except Exception as e:
            print(f"\n❌ Error in live trading: {e}")
        finally:
            self.mt5_manager.disconnect()

def main():
    """Main function"""
    system = ProfessionalTradingSystem()
    system.run_live_trading()

if __name__ == "__main__":
    main()
