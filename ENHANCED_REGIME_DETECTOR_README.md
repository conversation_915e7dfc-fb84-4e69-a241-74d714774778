# 🎯 Enhanced Regime Detection System

## Overview

The Enhanced Regime Detection System is a comprehensive 3-tier market regime analysis tool designed specifically for XAUUSD M5 trading. It implements 15 different indicators across three tiers to provide accurate regime classification with confidence scoring.

## 🏗️ System Architecture

### **TIER 1: LEADING INDICATORS (60% weight) - PURE PRICE ACTION** 🔥

1. **Swing Structure (12 points)** - MOST IMPORTANT
   - Tracks Higher Highs + Higher Lows (HH+HL) for uptrends
   - Tracks Lower Highs + Lower Lows (LH+LL) for downtrends
   - Identifies bouncing ranges and oscillating patterns

2. **Momentum Strength (10 points)**
   - Counts consecutive candle directions
   - Detects zigzag patterns for ranging markets

3. **Candle Body Analysis (8 points)**
   - Analyzes body-to-range ratios
   - Identifies strong directional vs. indecision candles

4. **Breakout Detection (8 points)**
   - Real-time breakout identification
   - Detects rejections and false breakouts

5. **ATR Expansion/Contraction (8 points)**
   - Leading volatility indicator
   - Detects Bollinger Band squeezes

6. **Range Definition (6 points)**
   - Measures price behavior within defined ranges
   - Counts support/resistance touches

7. **Price Oscillation (8 points)**
   - Measures back-and-forth movement
   - Counts moving average crosses

### **TIER 2: CONFIRMATION INDICATORS (30% weight)** ✅

8. **Multi-timeframe Alignment (10 points)**
   - Checks 5M, 15M, and 1H alignment
   - Provides confidence boost for aligned signals

9. **RSI Behavior (6 points)**
   - Analyzes RSI patterns and divergences
   - Trending vs. ranging RSI behavior

10. **EMA Separation (6 points)**
    - Uses 8/21/50 EMA system
    - Measures separation and alignment

11. **Bollinger Band Behavior (4 points)**
    - Band riding and squeeze detection
    - %B position analysis

12. **MACD (4 points)**
    - MACD strength and divergence analysis
    - Histogram expansion/contraction

### **TIER 3: CONTEXT FILTERS (10% weight)** 🎛️

13. **Session Analysis (3 points)**
    - London: Trending bias (highest volatility)
    - NY: Trending bias (follow-through moves)
    - Asian: Ranging bias (low volatility consolidation)

14. **Time-based Filters (2 points)**
    - Regime duration checks
    - Rapid flipping detection

15. **Regime Persistence (3 points)**
    - Sticky filter to prevent whipsaws
    - Previous regime bonus

## 🎯 Decision Thresholds

**Total Points: 98**

- **STRONG_TRENDING**: ≥50 points (51%) + 18-point difference + Swing Structure agreement
- **STRONG_RANGING**: ≥50 points (51%) + 18-point difference + Range/Oscillation agreement  
- **TRANSITIONAL**: <18-point difference OR conflicting Tier 1 signals OR max score <45

## 🚀 Usage Examples

### Basic Usage

```python
from enhanced_regime_detector import EnhancedRegimeDetector

# Initialize detector
detector = EnhancedRegimeDetector("XAUUSD!", "M5")

# Analyze regime
regime, confidence, details = detector.detect_regime(df)

print(f"Regime: {regime}")
print(f"Confidence: {confidence:.2f}%")
print(f"Trending Score: {details['trending_score']}/98")
print(f"Ranging Score: {details['ranging_score']}/98")
```

### With Live MT5 Data

```python
from mt5_integration import MT5Manager
from enhanced_regime_detector import EnhancedRegimeDetector

# Connect to MT5
mt5_manager = MT5Manager()
mt5_manager.connect()

# Get data
df = mt5_manager.get_latest_data("XAUUSD!", "M5", 200)

# Analyze
detector = EnhancedRegimeDetector("XAUUSD!", "M5")
regime, confidence, details = detector.detect_regime(df)
```

## 📊 Output Interpretation

### Regime Types

- **STRONG_TRENDING**: High confidence trending market - follow trends
- **STRONG_RANGING**: High confidence ranging market - trade ranges
- **TRENDING**: Moderate trending - cautious trend following
- **RANGING**: Moderate ranging - range trading opportunities
- **TRANSITIONAL**: Conflicting signals - AVOID TRADING

### Confidence Levels

- **80%+**: High confidence - normal position sizes
- **60-80%**: Medium confidence - standard trading
- **<60%**: Low confidence - reduce position sizes

## 🔧 Key Features

### Economic Calendar Integration
- Parses economic calendar with timezone conversion
- Amsterdam +2 timezone to system time conversion
- High-impact news filtering

### Real-time Processing
- Proper timezone handling
- MT5 integration for live data
- Latency handling and data consistency

### Advanced Analytics
- 15 comprehensive indicators
- Multi-timeframe analysis
- Session-based filtering
- Persistence tracking

## 📈 Trading Applications

### For Trending Markets
- Follow trend direction
- Use momentum strategies
- Wider stop losses
- Trail stops aggressively

### For Ranging Markets
- Trade range boundaries
- Use mean reversion strategies
- Tighter stop losses
- Take profits at support/resistance

### For Transitional Markets
- **AVOID TRADING**
- Wait for clear regime establishment
- High risk environment

## 🛠️ Technical Requirements

- Python 3.7+
- pandas, numpy
- pytz (timezone handling)
- MT5 integration (MetaTrader 5)
- Economic calendar file

## 📁 File Structure

```
enhanced_regime_detector.py     # Main implementation
test_enhanced_regime_live.py    # Live data testing
economic_calendar.txt           # Economic events data
ENHANCED_REGIME_DETECTOR_README.md  # This documentation
```

## 🔍 Testing

Run the test script to verify functionality:

```bash
python enhanced_regime_detector.py        # Basic test with sample data
python test_enhanced_regime_live.py       # Test with live MT5 data
```

## ⚠️ Important Notes

1. **Minimum Data**: Requires at least 50 candles for accurate analysis
2. **Timezone Handling**: Economic calendar times are converted from Amsterdam +2
3. **Session Times**: All times in GMT for consistency
4. **Risk Management**: Always use proper risk management regardless of regime
5. **Backtesting**: Test thoroughly before live trading

## 🎯 Performance Optimization

The system is designed for:
- **Accuracy**: 15 comprehensive indicators
- **Speed**: Efficient calculations
- **Reliability**: Error handling and fallbacks
- **Flexibility**: Configurable parameters

## 📞 Support

For questions or issues:
1. Check the test scripts for usage examples
2. Review the detailed logging output
3. Verify MT5 connection and data availability
4. Ensure economic calendar file is present

---

**🚀 Ready for live trading with comprehensive regime detection!** 🎉
