#!/usr/bin/env python3
"""
Test 100-Period Regime Regression Channel Implementation

This test verifies that the new 100-period regression channel is correctly:
1. Calculated in the feature engineering
2. Used in regime detection scoring
3. Properly weighted in the scoring system
4. Logged correctly
"""

import pandas as pd
import numpy as np
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import RegimeDetector, FixedFeatureEngineer

def create_test_data(trend_type="flat", periods=150):
    """Create test data with specific trend characteristics"""
    np.random.seed(42)  # For reproducible results
    
    if trend_type == "flat":
        # Flat trend - price oscillates around a mean
        base_price = 2000.0
        prices = [base_price]
        for i in range(periods - 1):
            # Small random walk with mean reversion
            change = np.random.normal(0, 0.5) - (prices[-1] - base_price) * 0.01
            prices.append(prices[-1] + change)
    
    elif trend_type == "strong_uptrend":
        # Strong uptrend - consistent upward movement
        base_price = 2000.0
        prices = [base_price]
        for i in range(periods - 1):
            # Strong upward trend with some noise
            trend_component = 2.0  # Strong upward movement
            noise = np.random.normal(0, 0.8)
            prices.append(prices[-1] + trend_component + noise)
    
    elif trend_type == "moderate_downtrend":
        # Moderate downtrend
        base_price = 2000.0
        prices = [base_price]
        for i in range(periods - 1):
            # Moderate downward trend with noise
            trend_component = -1.0  # Moderate downward movement
            noise = np.random.normal(0, 0.6)
            prices.append(prices[-1] + trend_component + noise)
    
    elif trend_type == "weak_uptrend":
        # Weak uptrend
        base_price = 2000.0
        prices = [base_price]
        for i in range(periods - 1):
            # Weak upward trend with noise
            trend_component = 0.3  # Weak upward movement
            noise = np.random.normal(0, 0.4)
            prices.append(prices[-1] + trend_component + noise)
    
    # Create OHLC data
    df = pd.DataFrame({
        'close': prices,
        'high': [p + abs(np.random.normal(0, 0.3)) for p in prices],
        'low': [p - abs(np.random.normal(0, 0.3)) for p in prices],
        'open': [p + np.random.normal(0, 0.2) for p in prices],
        'volume': [1000 + np.random.randint(-100, 100) for _ in prices]
    })
    
    # Ensure high >= close >= low and high >= open >= low
    for i in range(len(df)):
        df.loc[i, 'high'] = max(df.loc[i, 'high'], df.loc[i, 'close'], df.loc[i, 'open'])
        df.loc[i, 'low'] = min(df.loc[i, 'low'], df.loc[i, 'close'], df.loc[i, 'open'])
    
    return df

def test_100_period_regression_calculation():
    """Test that 100-period regression is calculated correctly"""
    print("🧪 TEST 1: 100-Period Regression Calculation")

    # Create test data
    df = create_test_data("strong_uptrend", 150)

    # Initialize components
    feature_engineer = FixedFeatureEngineer()
    regime_detector = RegimeDetector()

    # First create basic technical indicators
    df_with_indicators = feature_engineer.create_technical_indicators(df)

    # Then calculate regime indicators
    df_with_features = regime_detector.calculate_regime_indicators(df_with_indicators)
    
    # Check if 100-period regression columns exist
    required_columns = [
        'regression_line_regime',
        'regression_upper_regime', 
        'regression_lower_regime',
        'regression_position_regime',
        'regression_slope_regime',
        'regression_slope_regime_abs',
        'regression_trend_regime'
    ]
    
    missing_columns = [col for col in required_columns if col not in df_with_features.columns]
    if missing_columns:
        print(f"❌ FAILED: Missing columns: {missing_columns}")
        return False
    
    # Check that values are calculated (not all NaN)
    latest = df_with_features.iloc[-1]
    regime_slope = latest['regression_slope_regime']
    
    if pd.isna(regime_slope):
        print(f"❌ FAILED: 100-period regression slope is NaN")
        return False
    
    print(f"✅ PASSED: 100-period regression slope = {regime_slope:+.6f}% per period")
    print(f"   Trend: {latest['regression_trend_regime']}")
    print(f"   Slope (abs): {latest['regression_slope_regime_abs']:.6f}")
    
    return True

def test_regime_scoring_integration():
    """Test that 100-period regression is integrated into regime scoring"""
    print("\n🧪 TEST 2: Regime Scoring Integration")
    
    # Test different trend scenarios
    test_scenarios = [
        ("flat", "Should contribute to RANGING score"),
        ("strong_uptrend", "Should contribute to TRENDING score"),
        ("moderate_downtrend", "Should contribute to TRENDING score"),
        ("weak_uptrend", "Should contribute to RANGING score (weak trend)")
    ]
    
    regime_detector = RegimeDetector()
    feature_engineer = FixedFeatureEngineer()
    
    for trend_type, expected_behavior in test_scenarios:
        print(f"\n📊 Testing {trend_type}: {expected_behavior}")
        
        # Create test data
        df = create_test_data(trend_type, 150)
        df_with_indicators = feature_engineer.create_technical_indicators(df)
        df_with_features = regime_detector.calculate_regime_indicators(df_with_indicators)
        
        # Get regime detection
        regime, confidence, details, trend_dir, accurate_trend = regime_detector.detect_regime(df_with_features)
        
        # Check if 100-period regression data is in details
        regime_regression_100 = details.get('regime_regression_100', {})
        if not regime_regression_100:
            print(f"❌ FAILED: No 100-period regression data in regime details")
            return False
        
        slope = regime_regression_100.get('slope', 0)
        strength = regime_regression_100.get('strength', 'UNKNOWN')
        trend = regime_regression_100.get('trend', 'UNKNOWN')
        
        print(f"   100-Period Regression: {slope:+.6f}% per period ({strength}) → {trend}")
        print(f"   Regime: {regime} (confidence: {confidence:.1%})")
        print(f"   Trending Score: {details.get('trending_score', 0):.1f}")
        print(f"   Ranging Score: {details.get('ranging_score', 0):.1f}")
        
        # Check reasoning contains 100-period regression
        reasoning = details.get('reasoning', [])
        regime_channel_reasoning = [r for r in reasoning if "100-Period Regime Channel" in r]
        
        if not regime_channel_reasoning:
            print(f"❌ FAILED: No 100-period regime channel reasoning found")
            return False
        
        print(f"   Reasoning: {regime_channel_reasoning[0]}")
    
    print(f"✅ PASSED: 100-period regression integrated into regime scoring")
    return True

def test_scoring_system_balance():
    """Test that the scoring system is properly balanced with the new weight"""
    print("\n🧪 TEST 3: Scoring System Balance")
    
    regime_detector = RegimeDetector()
    feature_engineer = FixedFeatureEngineer()
    
    # Create extreme scenarios to test max scores
    scenarios = [
        ("strong_uptrend", "Should achieve high trending score"),
        ("flat", "Should achieve high ranging score")
    ]
    
    for trend_type, description in scenarios:
        print(f"\n📊 Testing {trend_type}: {description}")
        
        df = create_test_data(trend_type, 150)
        df_with_indicators = feature_engineer.create_technical_indicators(df)
        df_with_features = regime_detector.calculate_regime_indicators(df_with_indicators)
        
        regime, confidence, details, trend_dir, accurate_trend = regime_detector.detect_regime(df_with_features)
        
        trending_score = details.get('trending_score', 0)
        ranging_score = details.get('ranging_score', 0)
        max_score = max(trending_score, ranging_score)
        
        print(f"   Trending Score: {trending_score:.1f}")
        print(f"   Ranging Score: {ranging_score:.1f}")
        print(f"   Max Score: {max_score:.1f}")
        print(f"   Confidence: {confidence:.1%}")
        
        # Check that scores are reasonable (not too high or too low)
        if max_score > 25:  # Updated max possible is 22.5, so 25 is reasonable upper bound
            print(f"⚠️  WARNING: Score seems too high ({max_score:.1f})")
        elif max_score < 1:
            print(f"⚠️  WARNING: Score seems too low ({max_score:.1f})")
        else:
            print(f"✅ Score within reasonable range")
    
    print(f"✅ PASSED: Scoring system appears balanced")
    return True

def main():
    """Run all tests"""
    print("🚀 Testing 100-Period Regime Regression Channel Implementation")
    print("=" * 70)
    
    tests = [
        test_100_period_regression_calculation,
        test_regime_scoring_integration,
        test_scoring_system_balance
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test failed")
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 70)
    print(f"🎯 RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! 100-period regime regression channel is working correctly!")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
