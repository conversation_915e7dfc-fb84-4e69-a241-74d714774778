#!/usr/bin/env python3
"""
Test the enhanced features: Trailing Stop + Trend Direction Filtering
"""

import pandas as pd
import numpy as np

def test_trend_direction_logic():
    """Test the accurate trend direction detection"""
    print("🧪 TESTING ACCURATE TREND DIRECTION DETECTION")
    print("=" * 60)
    
    # Simulate different market scenarios
    scenarios = [
        {
            'name': 'Strong Uptrend',
            'ema_slope': 0.0020,  # UP
            'price_5_ago': 2650,
            'price_10_ago': 2640,
            'current_price': 2670,
            'sma_20_now': 2665,
            'sma_20_3_ago': 2655,
            'expected_direction': 'UP'
        },
        {
            'name': 'Strong Downtrend',
            'ema_slope': -0.0018,  # DOWN
            'price_5_ago': 2670,
            'price_10_ago': 2680,
            'current_price': 2650,
            'sma_20_now': 2655,
            'sma_20_3_ago': 2665,
            'expected_direction': 'DOWN'
        },
        {
            'name': 'Mixed Signals (Choppy)',
            'ema_slope': 0.0005,  # UP (weak)
            'price_5_ago': 2665,
            'price_10_ago': 2670,  # DOWN
            'current_price': 2660,
            'sma_20_now': 2658,
            'sma_20_3_ago': 2662,  # DOWN
            'expected_direction': 'DOWN'  # 1 UP, 3 DOWN = DOWN
        }
    ]
    
    for scenario in scenarios:
        print(f"\n🔍 {scenario['name']}:")
        
        # Simulate the trend detection logic
        ema_trend = 'UP' if scenario['ema_slope'] > 0 else 'DOWN'
        price_trend_5 = 'UP' if scenario['current_price'] > scenario['price_5_ago'] else 'DOWN'
        price_trend_10 = 'UP' if scenario['current_price'] > scenario['price_10_ago'] else 'DOWN'
        ma_trend = 'UP' if scenario['sma_20_now'] > scenario['sma_20_3_ago'] else 'DOWN'
        
        # Count votes
        trend_up_votes = sum([
            ema_trend == 'UP',
            price_trend_5 == 'UP',
            price_trend_10 == 'UP',
            ma_trend == 'UP'
        ])
        
        accurate_trend_direction = 'UP' if trend_up_votes >= 3 else 'DOWN'
        
        print(f"   EMA Slope: {scenario['ema_slope']:.4f} → {ema_trend}")
        print(f"   Price vs 5 periods ago: {scenario['current_price']} vs {scenario['price_5_ago']} → {price_trend_5}")
        print(f"   Price vs 10 periods ago: {scenario['current_price']} vs {scenario['price_10_ago']} → {price_trend_10}")
        print(f"   SMA Trend: {scenario['sma_20_now']} vs {scenario['sma_20_3_ago']} → {ma_trend}")
        print(f"   UP Votes: {trend_up_votes}/4")
        print(f"   Final Direction: {accurate_trend_direction} (Expected: {scenario['expected_direction']})")
        print(f"   ✅ {'PASS' if accurate_trend_direction == scenario['expected_direction'] else 'FAIL'}")

def test_trend_filtering_logic():
    """Test the trend direction filtering in trending markets"""
    print("\n\n🧪 TESTING TREND DIRECTION FILTERING")
    print("=" * 60)
    
    test_cases = [
        {
            'name': 'Uptrend + Reversed BUY Signal',
            'regime': 'TRENDING',
            'ml_signal': 'SELL',  # Original ML signal
            'reversed_signal': 'BUY',  # Reversed signal
            'trend_direction': 'UP',
            'expected_final_signal': 'BUY',
            'expected_logic': 'REVERSE_MODEL_UP_TREND'
        },
        {
            'name': 'Downtrend + Reversed SELL Signal',
            'regime': 'TRENDING',
            'ml_signal': 'BUY',  # Original ML signal
            'reversed_signal': 'SELL',  # Reversed signal
            'trend_direction': 'DOWN',
            'expected_final_signal': 'SELL',
            'expected_logic': 'REVERSE_MODEL_DOWN_TREND'
        },
        {
            'name': 'Uptrend + Reversed SELL Signal (CONFLICT)',
            'regime': 'TRENDING',
            'ml_signal': 'BUY',  # Original ML signal
            'reversed_signal': 'SELL',  # Reversed signal
            'trend_direction': 'UP',
            'expected_final_signal': None,
            'expected_logic': 'NO_TRADE_TREND_CONFLICT(UP_vs_SELL)'
        },
        {
            'name': 'Downtrend + Reversed BUY Signal (CONFLICT)',
            'regime': 'TRENDING',
            'ml_signal': 'SELL',  # Original ML signal
            'reversed_signal': 'BUY',  # Reversed signal
            'trend_direction': 'DOWN',
            'expected_final_signal': None,
            'expected_logic': 'NO_TRADE_TREND_CONFLICT(DOWN_vs_BUY)'
        },
        {
            'name': 'Ranging Market (No Filtering)',
            'regime': 'RANGING',
            'ml_signal': 'BUY',  # Original ML signal
            'reversed_signal': None,  # No reversal in ranging
            'trend_direction': 'UP',  # Irrelevant in ranging
            'expected_final_signal': 'BUY',
            'expected_logic': 'FOLLOW_MODEL'
        }
    ]
    
    for case in test_cases:
        print(f"\n🔍 {case['name']}:")
        
        # Simulate the enhanced regime logic
        if case['regime'] == "RANGING":
            final_signal = case['ml_signal']
            logic = "FOLLOW_MODEL"
        elif case['regime'] == "TRENDING":
            reversed_signal = case['reversed_signal']
            trend_direction = case['trend_direction']
            
            # Check if reversed signal aligns with trend direction
            if trend_direction == "UP" and reversed_signal == "BUY":
                final_signal = reversed_signal
                logic = "REVERSE_MODEL_UP_TREND"
            elif trend_direction == "DOWN" and reversed_signal == "SELL":
                final_signal = reversed_signal
                logic = "REVERSE_MODEL_DOWN_TREND"
            else:
                final_signal = None
                logic = f"NO_TRADE_TREND_CONFLICT({trend_direction}_vs_{reversed_signal})"
        else:
            final_signal = None
            logic = "NO_TRADE"
        
        print(f"   Regime: {case['regime']}")
        print(f"   ML Signal: {case['ml_signal']}")
        print(f"   Reversed Signal: {case['reversed_signal']}")
        print(f"   Trend Direction: {case['trend_direction']}")
        print(f"   Final Signal: {final_signal} (Expected: {case['expected_final_signal']})")
        print(f"   Logic: {logic} (Expected: {case['expected_logic']})")
        
        signal_match = final_signal == case['expected_final_signal']
        logic_match = logic == case['expected_logic']
        print(f"   ✅ Signal: {'PASS' if signal_match else 'FAIL'}")
        print(f"   ✅ Logic: {'PASS' if logic_match else 'FAIL'}")

def test_trailing_stop_logic():
    """Test the trailing stop calculation logic"""
    print("\n\n🧪 TESTING TRAILING STOP LOGIC")
    print("=" * 60)
    
    scenarios = [
        {
            'name': 'BUY Position - 1.5 ATR Profit',
            'position_type': 'BUY',
            'entry_price': 2650.00,
            'current_price': 2665.00,  # +15 points
            'atr_value': 10.0,  # 15/10 = 1.5 ATR profit
            'initial_sl': 2640.00,  # Entry - 1 ATR
            'current_sl': 2640.00,
            'profit_atr_count': 0,
            'expected_new_sl': 2650.00,  # Move up by 1 ATR
            'expected_profit_count': 1,
            'should_update': True
        },
        {
            'name': 'BUY Position - 0.8 ATR Profit (No Update)',
            'position_type': 'BUY',
            'entry_price': 2650.00,
            'current_price': 2658.00,  # +8 points
            'atr_value': 10.0,  # 8/10 = 0.8 ATR profit
            'initial_sl': 2640.00,
            'current_sl': 2640.00,
            'profit_atr_count': 0,
            'expected_new_sl': 2640.00,  # No change
            'expected_profit_count': 0,
            'should_update': False
        },
        {
            'name': 'SELL Position - 1.2 ATR Profit',
            'position_type': 'SELL',
            'entry_price': 2650.00,
            'current_price': 2638.00,  # -12 points (profit for SELL)
            'atr_value': 10.0,  # 12/10 = 1.2 ATR profit
            'initial_sl': 2660.00,  # Entry + 1 ATR
            'current_sl': 2660.00,
            'profit_atr_count': 0,
            'expected_new_sl': 2650.00,  # Move down by 1 ATR
            'expected_profit_count': 1,
            'should_update': True
        },
        {
            'name': 'BUY Position - Already Trailed Once',
            'position_type': 'BUY',
            'entry_price': 2650.00,
            'current_price': 2675.00,  # +25 points
            'atr_value': 10.0,  # 25/10 = 2.5 ATR profit
            'initial_sl': 2640.00,
            'current_sl': 2650.00,  # Already moved up once
            'profit_atr_count': 1,  # Already counted 1 ATR
            'expected_new_sl': 2660.00,  # Move up by another ATR
            'expected_profit_count': 2,
            'should_update': True
        }
    ]
    
    for scenario in scenarios:
        print(f"\n🔍 {scenario['name']}:")
        
        # Simulate trailing stop logic
        position_type = scenario['position_type']
        entry_price = scenario['entry_price']
        current_price = scenario['current_price']
        atr_value = scenario['atr_value']
        current_sl = scenario['current_sl']
        profit_atr_count = scenario['profit_atr_count']
        
        if position_type == 'BUY':
            profit_points = current_price - entry_price
            profit_atr = profit_points / atr_value
            
            if profit_atr >= (profit_atr_count + 1):
                new_sl = current_sl + atr_value
                new_profit_count = profit_atr_count + 1
                should_update = True
            else:
                new_sl = current_sl
                new_profit_count = profit_atr_count
                should_update = False
        
        else:  # SELL
            profit_points = entry_price - current_price
            profit_atr = profit_points / atr_value
            
            if profit_atr >= (profit_atr_count + 1):
                new_sl = current_sl - atr_value
                new_profit_count = profit_atr_count + 1
                should_update = True
            else:
                new_sl = current_sl
                new_profit_count = profit_atr_count
                should_update = False
        
        print(f"   Position: {position_type} @ {entry_price}")
        print(f"   Current Price: {current_price}")
        print(f"   Profit Points: {profit_points:.1f}")
        print(f"   Profit ATR: {profit_atr:.2f}")
        print(f"   Current SL: {current_sl}")
        print(f"   New SL: {new_sl} (Expected: {scenario['expected_new_sl']})")
        print(f"   Profit Count: {new_profit_count} (Expected: {scenario['expected_profit_count']})")
        print(f"   Should Update: {should_update} (Expected: {scenario['should_update']})")
        
        sl_match = new_sl == scenario['expected_new_sl']
        count_match = new_profit_count == scenario['expected_profit_count']
        update_match = should_update == scenario['should_update']
        
        print(f"   ✅ SL: {'PASS' if sl_match else 'FAIL'}")
        print(f"   ✅ Count: {'PASS' if count_match else 'FAIL'}")
        print(f"   ✅ Update: {'PASS' if update_match else 'FAIL'}")

def show_enhancement_summary():
    """Show summary of all enhancements"""
    print("\n\n✅ ENHANCEMENT SUMMARY")
    print("=" * 60)
    print("🎯 TRAILING STOP FEATURE:")
    print("   ✅ Moves stop loss every 1 ATR in profit")
    print("   ✅ Protects profits while allowing for continued gains")
    print("   ✅ Automatically initialized when position opens")
    print("   ✅ Cleared when position closes")
    print()
    print("🎯 ACCURATE TREND DIRECTION:")
    print("   ✅ Uses 4 indicators for consensus (EMA, Price 5, Price 10, SMA)")
    print("   ✅ Requires 3/4 agreement for direction")
    print("   ✅ More reliable than single indicator")
    print()
    print("🎯 TREND DIRECTION FILTERING:")
    print("   ✅ Only trades reversed signals that align with trend")
    print("   ✅ Prevents counter-trend trades in strong trends")
    print("   ✅ Reduces false signals and improves win rate")
    print()
    print("🚀 EXPECTED BENEFITS:")
    print("   • Higher win rate in trending markets")
    print("   • Better profit protection with trailing stops")
    print("   • Reduced drawdowns from counter-trend trades")
    print("   • More sophisticated regime-based logic")

def main():
    """Main function"""
    test_trend_direction_logic()
    test_trend_filtering_logic()
    test_trailing_stop_logic()
    show_enhancement_summary()

if __name__ == "__main__":
    main()
