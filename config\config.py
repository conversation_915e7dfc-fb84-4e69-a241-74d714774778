"""
Configuration file for XAUUSD LSTM Trading System
"""
import os
from pathlib import Path

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent
DATA_DIR = PROJECT_ROOT / "data"
MODELS_DIR = PROJECT_ROOT / "models"
REPORTS_DIR = PROJECT_ROOT / "reports"
LOGS_DIR = PROJECT_ROOT / "logs"

# Data configuration
SYMBOL = "XAUUSD!"
TIMEFRAME = "M5"
DATA_FILE = DATA_DIR / "XAU_5m_data.csv"
UPDATED_DATA_FILE = DATA_DIR / "XAU_5m_updated.csv"

# MT5 Configuration
MT5_LOGIN = None  # Will be set from environment or user input
MT5_PASSWORD = None  # Will be set from environment or user input
MT5_SERVER = None  # Will be set from environment or user input

# Trading Configuration
RISK_PERCENT = 4.0  # Risk 4% of account balance per trade
ATR_MULTIPLIER = 1.5  # Stop loss multiplier for ATR
MIN_ACCOUNT_BALANCE = 1000  # Minimum account balance to trade

# Model Configuration
SEQUENCE_LENGTH = 60  # Number of time steps to look back
PREDICTION_HORIZON = 1  # Number of steps to predict ahead
TRAIN_SPLIT = 0.7  # 70% for training
VALIDATION_SPLIT = 0.15  # 15% for validation
TEST_SPLIT = 0.15  # 15% for testing

# Feature Engineering
TECHNICAL_INDICATORS = {
    'ATR_PERIOD': 14,
    'RSI_PERIOD': 14,
    'MACD_FAST': 12,
    'MACD_SLOW': 26,
    'MACD_SIGNAL': 9,
    'BB_PERIOD': 20,
    'BB_STD': 2,
    'EMA_FAST': 9,
    'EMA_SLOW': 21,
    'STOCH_K': 14,
    'STOCH_D': 3,
    'ADX_PERIOD': 14,
    'CCI_PERIOD': 20,
    'WILLIAMS_R_PERIOD': 14
}

# LSTM Model Configuration
MODEL_CONFIG = {
    'lstm_units': [128, 64, 32],
    'dropout_rate': 0.3,
    'recurrent_dropout': 0.2,
    'dense_units': [32, 16],
    'learning_rate': 0.001,
    'batch_size': 64,
    'epochs': 100,
    'early_stopping_patience': 15,
    'reduce_lr_patience': 10,
    'reduce_lr_factor': 0.5
}

# Trading Signals
SIGNAL_THRESHOLD = 0.6  # Minimum confidence for trading signals
TREND_THRESHOLD = 0.7  # Threshold for trend detection
RANGE_THRESHOLD = 0.3  # Threshold for range detection

# Logging Configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Data Processing
MAX_DATA_YEARS = 5  # Use last 5 years of data for training
MIN_DATA_POINTS = 10000  # Minimum data points required
OUTLIER_THRESHOLD = 3  # Standard deviations for outlier detection

# Model Evaluation
CROSS_VALIDATION_FOLDS = 5
WALK_FORWARD_STEPS = 252  # Approximately 1 year of trading days

# Live Trading
UPDATE_FREQUENCY = 300  # Update every 5 minutes (300 seconds)
MAX_POSITIONS = 1  # Maximum number of open positions
SLIPPAGE_POINTS = 2  # Expected slippage in points

# Create directories if they don't exist
for directory in [DATA_DIR, MODELS_DIR, REPORTS_DIR, LOGS_DIR]:
    directory.mkdir(exist_ok=True)
