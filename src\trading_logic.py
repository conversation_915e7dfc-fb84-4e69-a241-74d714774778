"""
Trading Logic Implementation
Handles trading decisions based on model predictions and risk management
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
from enum import Enum

from config.config import *
from src.mt5_integration import MT5Manager

# Set up logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

class SignalType(Enum):
    HOLD = 0
    BUY = 1
    SELL = 2

class TradingDecision:
    """Represents a trading decision"""
    def __init__(self, signal: SignalType, confidence: float, entry_price: float,
                 stop_loss: float, take_profit: Optional[float] = None,
                 position_size: float = 0.0, reason: str = ""):
        self.signal = signal
        self.confidence = confidence
        self.entry_price = entry_price
        self.stop_loss = stop_loss
        self.take_profit = take_profit
        self.position_size = position_size
        self.reason = reason
        self.timestamp = datetime.now()

class TradingEngine:
    """
    Main trading engine that makes trading decisions based on model predictions
    """
    
    def __init__(self, mt5_manager: MT5Manager):
        self.mt5_manager = mt5_manager
        self.current_position = None
        self.trade_history = []
        self.last_signal = SignalType.HOLD
        self.last_signal_time = None
        self.consecutive_signals = 0
        
    def process_prediction(self, prediction_result: Dict, market_data: Dict) -> Optional[TradingDecision]:
        """
        Process model prediction and make trading decision
        
        Args:
            prediction_result: Model prediction result
            market_data: Current market data
            
        Returns:
            TradingDecision or None
        """
        try:
            logger.info("Processing prediction for trading decision...")
            
            # Extract prediction information
            prediction_class = prediction_result.get('prediction_class', 0)
            confidence = prediction_result.get('confidence', 0.0)
            probabilities = prediction_result.get('prediction_probabilities', [])
            signal_strength = prediction_result.get('signal_strength', 'WEAK')
            market_context = prediction_result.get('market_context', {})
            
            # Current market data
            current_price = market_data.get('close', 0.0)
            current_atr = market_data.get('atr', 0.0)
            
            if current_price == 0 or current_atr == 0:
                logger.error("Invalid market data for trading decision")
                return None
            
            # Convert prediction to signal
            signal = SignalType(prediction_class)
            
            # Check if we should act on this signal
            if not self._should_act_on_signal(signal, confidence, signal_strength, market_context):
                logger.info(f"Signal {signal.name} filtered out. Confidence: {confidence:.3f}, Strength: {signal_strength}")
                return None
            
            # Calculate position size based on risk management
            account_info = self.mt5_manager.get_account_info()
            if not account_info:
                logger.error("Cannot get account info for position sizing")
                return None
            
            # Create trading decision
            decision = self._create_trading_decision(
                signal, confidence, current_price, current_atr, account_info, market_context
            )
            
            if decision:
                logger.info(f"Trading decision created: {decision.signal.name} at {decision.entry_price:.5f}")
                logger.info(f"Stop Loss: {decision.stop_loss:.5f}, Position Size: {decision.position_size:.2f}")
                logger.info(f"Reason: {decision.reason}")
            
            return decision
            
        except Exception as e:
            logger.error(f"Error processing prediction: {e}")
            return None
    
    def _should_act_on_signal(self, signal: SignalType, confidence: float, 
                            signal_strength: str, market_context: Dict) -> bool:
        """
        Determine if we should act on the trading signal
        
        Args:
            signal: Trading signal
            confidence: Model confidence
            signal_strength: Signal strength classification
            market_context: Market context information
            
        Returns:
            bool: True if should act on signal
        """
        try:
            # Don't trade on HOLD signals
            if signal == SignalType.HOLD:
                return False
            
            # Minimum confidence threshold
            if confidence < SIGNAL_THRESHOLD:
                logger.debug(f"Signal confidence {confidence:.3f} below threshold {SIGNAL_THRESHOLD}")
                return False
            
            # Signal strength filter
            if signal_strength == "NO_SIGNAL" or signal_strength == "WEAK":
                logger.debug(f"Signal strength {signal_strength} too weak")
                return False
            
            # Check if we already have a position in the same direction
            if self.current_position:
                current_direction = 'BUY' if self.current_position['type'] == 'BUY' else 'SELL'
                signal_direction = 'BUY' if signal == SignalType.BUY else 'SELL'
                
                if current_direction == signal_direction:
                    logger.debug("Already have position in same direction")
                    return False
            
            # Market regime filter
            market_regime = market_context.get('market_regime', 'UNKNOWN')
            trend_direction = market_context.get('trend_direction', 'UNKNOWN')
            
            # In ranging markets, be more conservative
            if market_regime == 'RANGING' and signal_strength != 'STRONG':
                logger.debug("Ranging market detected, requiring strong signals")
                return False
            
            # Trend following logic
            if market_regime == 'TRENDING':
                if signal == SignalType.BUY and trend_direction == 'DOWN':
                    if confidence < 0.8:  # Higher threshold for counter-trend trades
                        logger.debug("Counter-trend BUY signal requires higher confidence")
                        return False
                elif signal == SignalType.SELL and trend_direction == 'UP':
                    if confidence < 0.8:  # Higher threshold for counter-trend trades
                        logger.debug("Counter-trend SELL signal requires higher confidence")
                        return False
            
            # RSI filter for extreme conditions
            rsi_level = market_context.get('rsi_level')
            if rsi_level:
                if signal == SignalType.BUY and rsi_level > 80:
                    logger.debug("RSI overbought, filtering BUY signal")
                    return False
                elif signal == SignalType.SELL and rsi_level < 20:
                    logger.debug("RSI oversold, filtering SELL signal")
                    return False
            
            # Time-based filters (avoid trading during low liquidity periods)
            current_hour = datetime.now().hour
            if current_hour < 6 or current_hour > 22:  # Avoid Asian session low liquidity
                if signal_strength != 'STRONG':
                    logger.debug("Low liquidity period, requiring strong signals")
                    return False
            
            # Consecutive signal filter (avoid overtrading)
            if (self.last_signal == signal and 
                self.last_signal_time and 
                (datetime.now() - self.last_signal_time).total_seconds() < 3600):  # 1 hour
                self.consecutive_signals += 1
                if self.consecutive_signals > 3:
                    logger.debug("Too many consecutive signals, filtering")
                    return False
            else:
                self.consecutive_signals = 1
            
            self.last_signal = signal
            self.last_signal_time = datetime.now()
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking signal validity: {e}")
            return False
    
    def _create_trading_decision(self, signal: SignalType, confidence: float,
                               current_price: float, current_atr: float,
                               account_info: Dict, market_context: Dict) -> Optional[TradingDecision]:
        """
        Create a trading decision with proper risk management
        
        Args:
            signal: Trading signal
            confidence: Model confidence
            current_price: Current market price
            current_atr: Current ATR value
            account_info: Account information
            market_context: Market context
            
        Returns:
            TradingDecision or None
        """
        try:
            # Calculate stop loss based on ATR
            atr_stop_distance = current_atr * ATR_MULTIPLIER
            
            if signal == SignalType.BUY:
                entry_price = current_price  # Market order
                stop_loss = entry_price - atr_stop_distance
                take_profit = entry_price + (atr_stop_distance * 2)  # 2:1 RR ratio
            elif signal == SignalType.SELL:
                entry_price = current_price  # Market order
                stop_loss = entry_price + atr_stop_distance
                take_profit = entry_price - (atr_stop_distance * 2)  # 2:1 RR ratio
            else:
                return None
            
            # Calculate position size based on risk management
            account_balance = account_info.get('balance', 0)
            risk_amount = account_balance * (RISK_PERCENT / 100)
            
            # Calculate position size
            position_size = self.mt5_manager.calculate_position_size(
                risk_amount, entry_price, stop_loss
            )
            
            if position_size <= 0:
                logger.error("Invalid position size calculated")
                return None
            
            # Check minimum account balance
            if account_balance < MIN_ACCOUNT_BALANCE:
                logger.error(f"Account balance {account_balance} below minimum {MIN_ACCOUNT_BALANCE}")
                return None
            
            # Create reason string
            reason_parts = [
                f"Model confidence: {confidence:.3f}",
                f"Market regime: {market_context.get('market_regime', 'UNKNOWN')}",
                f"Trend: {market_context.get('trend_direction', 'UNKNOWN')}",
                f"ATR: {current_atr:.5f}"
            ]
            reason = "; ".join(reason_parts)
            
            # Create trading decision
            decision = TradingDecision(
                signal=signal,
                confidence=confidence,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                position_size=position_size,
                reason=reason
            )
            
            return decision
            
        except Exception as e:
            logger.error(f"Error creating trading decision: {e}")
            return None
    
    def execute_decision(self, decision: TradingDecision) -> bool:
        """
        Execute trading decision
        
        Args:
            decision: TradingDecision to execute
            
        Returns:
            bool: True if execution successful
        """
        try:
            logger.info(f"Executing trading decision: {decision.signal.name}")
            
            # Check if we need to close existing position first
            if self.current_position:
                if not self._close_current_position():
                    logger.error("Failed to close current position")
                    return False
            
            # Execute new order
            action = 'BUY' if decision.signal == SignalType.BUY else 'SELL'
            
            order_result = self.mt5_manager.send_order(
                action=action,
                volume=decision.position_size,
                price=None,  # Market order
                sl=decision.stop_loss,
                tp=decision.take_profit,
                comment=f"LSTM_{action}_{decision.confidence:.3f}"
            )
            
            if order_result:
                # Update current position
                self.current_position = {
                    'ticket': order_result.get('order'),
                    'type': action,
                    'volume': decision.position_size,
                    'entry_price': order_result.get('price', decision.entry_price),
                    'stop_loss': decision.stop_loss,
                    'take_profit': decision.take_profit,
                    'timestamp': datetime.now(),
                    'decision': decision
                }
                
                # Add to trade history
                self.trade_history.append({
                    'timestamp': datetime.now(),
                    'action': 'OPEN',
                    'signal': decision.signal.name,
                    'entry_price': order_result.get('price', decision.entry_price),
                    'position_size': decision.position_size,
                    'stop_loss': decision.stop_loss,
                    'take_profit': decision.take_profit,
                    'confidence': decision.confidence,
                    'reason': decision.reason
                })
                
                logger.info(f"Order executed successfully. Ticket: {order_result.get('order')}")
                return True
            else:
                logger.error("Failed to execute order")
                return False
                
        except Exception as e:
            logger.error(f"Error executing trading decision: {e}")
            return False
    
    def _close_current_position(self) -> bool:
        """Close current open position"""
        try:
            if not self.current_position:
                return True
            
            ticket = self.current_position.get('ticket')
            if not ticket:
                logger.error("No ticket found for current position")
                return False
            
            success = self.mt5_manager.close_position(ticket)
            
            if success:
                # Add to trade history
                self.trade_history.append({
                    'timestamp': datetime.now(),
                    'action': 'CLOSE',
                    'ticket': ticket,
                    'type': self.current_position.get('type'),
                    'reason': 'New signal received'
                })
                
                self.current_position = None
                logger.info(f"Position {ticket} closed successfully")
                return True
            else:
                logger.error(f"Failed to close position {ticket}")
                return False
                
        except Exception as e:
            logger.error(f"Error closing current position: {e}")
            return False
    
    def check_position_management(self, market_data: Dict) -> bool:
        """
        Check if current position needs management (trailing stop, etc.)
        
        Args:
            market_data: Current market data
            
        Returns:
            bool: True if position management successful
        """
        try:
            if not self.current_position:
                return True
            
            current_price = market_data.get('close', 0.0)
            if current_price == 0:
                return False
            
            # Get current position info from MT5
            positions = self.mt5_manager.get_positions()
            current_mt5_position = None
            
            for pos in positions:
                if pos['ticket'] == self.current_position['ticket']:
                    current_mt5_position = pos
                    break
            
            if not current_mt5_position:
                # Position was closed externally
                logger.info("Position was closed externally")
                self.current_position = None
                return True
            
            # Check for trailing stop logic (simplified)
            entry_price = self.current_position['entry_price']
            position_type = self.current_position['type']
            current_atr = market_data.get('atr', 0.0)
            
            if current_atr > 0:
                # Calculate new trailing stop
                trailing_distance = current_atr * ATR_MULTIPLIER
                
                if position_type == 'BUY':
                    new_stop = current_price - trailing_distance
                    if new_stop > self.current_position['stop_loss']:
                        # Update stop loss (would need MT5 modification function)
                        logger.info(f"Trailing stop updated for BUY position: {new_stop:.5f}")
                        self.current_position['stop_loss'] = new_stop
                else:  # SELL
                    new_stop = current_price + trailing_distance
                    if new_stop < self.current_position['stop_loss']:
                        # Update stop loss (would need MT5 modification function)
                        logger.info(f"Trailing stop updated for SELL position: {new_stop:.5f}")
                        self.current_position['stop_loss'] = new_stop
            
            return True
            
        except Exception as e:
            logger.error(f"Error in position management: {e}")
            return False
    
    def get_current_position_info(self) -> Optional[Dict]:
        """Get current position information"""
        return self.current_position.copy() if self.current_position else None
    
    def get_trade_history(self) -> List[Dict]:
        """Get trade history"""
        return self.trade_history.copy()
    
    def get_trading_statistics(self) -> Dict:
        """Get trading performance statistics"""
        try:
            if not self.trade_history:
                return {}
            
            open_trades = [t for t in self.trade_history if t['action'] == 'OPEN']
            close_trades = [t for t in self.trade_history if t['action'] == 'CLOSE']
            
            stats = {
                'total_trades': len(open_trades),
                'closed_trades': len(close_trades),
                'open_positions': len(open_trades) - len(close_trades),
                'avg_confidence': np.mean([t['confidence'] for t in open_trades if 'confidence' in t]),
                'signal_distribution': {}
            }
            
            # Signal distribution
            for trade in open_trades:
                signal = trade.get('signal', 'UNKNOWN')
                stats['signal_distribution'][signal] = stats['signal_distribution'].get(signal, 0) + 1
            
            return stats
            
        except Exception as e:
            logger.error(f"Error calculating trading statistics: {e}")
            return {}
