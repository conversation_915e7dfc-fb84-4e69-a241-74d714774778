#!/usr/bin/env python3
"""
Test QQE Integration with Fixed Live Trader
"""

import sys
import os
sys.path.append('src')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Import the QQE indicator
from qqe_indicator import QQEIndicator

def create_sample_data():
    """Create sample OHLC data for testing"""
    np.random.seed(42)
    
    # Create 200 periods of sample data
    dates = pd.date_range(start='2024-01-01', periods=200, freq='5T')
    
    # Generate realistic price movement
    base_price = 2000.0
    prices = []
    current_price = base_price
    
    for i in range(200):
        # Random walk with some trend
        change = np.random.normal(0, 2.0)  # Random change
        if i > 100:  # Add some trend in the second half
            change += 0.5
        
        current_price += change
        prices.append(current_price)
    
    # Create OHLC from prices
    df = pd.DataFrame(index=dates)
    df['close'] = prices
    
    # Generate realistic OHLC
    df['open'] = df['close'].shift(1).fillna(df['close'].iloc[0])
    df['high'] = df[['open', 'close']].max(axis=1) + np.random.uniform(0, 1, len(df))
    df['low'] = df[['open', 'close']].min(axis=1) - np.random.uniform(0, 1, len(df))
    
    return df

def test_qqe_indicator():
    """Test QQE indicator functionality"""
    print("🧪 Testing QQE Indicator Integration")
    print("=" * 50)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Create QQE indicator - FIXED: Match user's TradingView settings
    qqe = QQEIndicator(
        rsi_period=7,  # USER'S TRADINGVIEW SETTING: 7
        rsi_smoothing=5,
        qqe_factor=1.0,  # USER'S TRADINGVIEW SETTING: 1
        threshold=10
    )
    
    # Create sample data
    df = create_sample_data()
    print(f"📊 Created sample data: {len(df)} periods")
    print(f"   Price range: {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    # Calculate QQE indicators
    print("\n🔄 Calculating QQE bands...")
    df = qqe.calculate_qqe_bands(df)
    
    print("🔄 Generating QQE signals...")
    df = qqe.generate_qqe_signals(df)
    
    # Get analysis
    qqe_analysis = qqe.get_qqe_analysis(df)
    
    print("\n📈 QQE Analysis Results:")
    print(f"   RSI: {qqe_analysis.get('rsi', 0):.1f}")
    print(f"   RSI MA: {qqe_analysis.get('rsi_ma', 0):.1f}")
    print(f"   Fast ATR RSI TL: {qqe_analysis.get('fast_atr_rsi_tl', 0):.1f}")
    print(f"   QQE Trend: {int(qqe_analysis.get('trend', 0)):+d}")
    print(f"   QQE Signal: {int(qqe_analysis.get('qqe_signal', 0)):+d}")
    print(f"   QQE Signal Strength: {qqe_analysis.get('qqe_signal_strength', 0):.3f}")
    
    # Count signals
    long_signals = (df['qqe_signal'] == 1).sum()
    short_signals = (df['qqe_signal'] == -1).sum()
    
    print(f"\n📊 Signal Statistics:")
    print(f"   Long signals: {long_signals}")
    print(f"   Short signals: {short_signals}")
    print(f"   Total signals: {long_signals + short_signals}")
    
    # Show recent signals
    recent_signals = df[df['qqe_signal'] != 0].tail(5)
    if len(recent_signals) > 0:
        print(f"\n🎯 Recent Signals:")
        for idx, row in recent_signals.iterrows():
            signal_type = "LONG" if row['qqe_signal'] == 1 else "SHORT"
            print(f"   {idx.strftime('%Y-%m-%d %H:%M')}: {signal_type} (strength: {row['qqe_signal_strength']:.3f})")
    else:
        print(f"\n🎯 No recent signals found")
    
    # Test voting system simulation
    print(f"\n🗳️  Testing Voting System:")
    
    # Simulate candle strength values
    candle_strengths = [30, -20, 5, -45, 60]
    
    for candle_strength in candle_strengths:
        qqe_signal = qqe_analysis.get('qqe_signal', 0)
        qqe_strength = qqe_analysis.get('qqe_signal_strength', 0)
        
        # Simulate voting logic
        if qqe_signal == 1:  # QQE Long
            if candle_strength > 0:
                result = "BUY (QQE + Candle Agree)"
                confidence = min((qqe_strength * 0.7) + (abs(candle_strength) / 100 * 0.3), 1.0)
            elif abs(candle_strength) < 20:
                result = "BUY (QQE + Candle Neutral)"
                confidence = qqe_strength * 0.8
            else:
                result = "NO SIGNAL (QQE + Candle Disagree)"
                confidence = 0.0
        elif qqe_signal == -1:  # QQE Short
            if candle_strength < 0:
                result = "SELL (QQE + Candle Agree)"
                confidence = min((qqe_strength * 0.7) + (abs(candle_strength) / 100 * 0.3), 1.0)
            elif abs(candle_strength) < 20:
                result = "SELL (QQE + Candle Neutral)"
                confidence = qqe_strength * 0.8
            else:
                result = "NO SIGNAL (QQE + Candle Disagree)"
                confidence = 0.0
        else:  # No QQE signal
            if abs(candle_strength) >= 50:
                result = f"{'BUY' if candle_strength > 0 else 'SELL'} (Candle Only)"
                confidence = min(abs(candle_strength) / 100 * 0.6, 1.0)
            else:
                result = "NO SIGNAL (Both Neutral)"
                confidence = 0.0
        
        print(f"   Candle: {candle_strength:+3d}% | QQE: {int(qqe_signal):+d} ({qqe_strength:.3f}) → {result} (conf: {confidence:.3f})")
    
    print(f"\n✅ QQE Integration Test Complete!")
    return True

if __name__ == "__main__":
    try:
        test_qqe_indicator()
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
