#!/usr/bin/env python3

import pandas as pd
from fixed_live_trader import FixedLiveTrader

def create_test_data(price, ema_10, ema_20, reg_l, reg_u, reg_ls, reg_us, atr):
    """Create test DataFrame with specified values (need at least 2 rows)"""
    data = {
        'close': [price - 1, price],  # Two rows
        'high': [price + 4, price + 5],
        'low': [price - 6, price - 5],
        'ema_10': [ema_10, ema_10],
        'ema_20': [ema_20, ema_20],
        'regression_lower': [reg_l, reg_l],
        'regression_upper': [reg_u, reg_u],
        'regression_lower_short': [reg_ls, reg_ls],
        'regression_upper_short': [reg_us, reg_us],
        'atr': [atr, atr]
    }
    return pd.DataFrame(data)

# Initialize trader
trader = FixedLiveTrader("XAUUSD!")

print("🧪 DEBUGGING EMA TEST-AND-BOUNCE")
print("=" * 50)

# Test Case: EMA test-and-bounce
print("📊 EMA test-and-bounce scenario")

df = create_test_data(
    price=4363.06,    # Close exactly at EMAs
    ema_10=4363.06,   # At close
    ema_20=4363.06,   # At close
    reg_l=4346.38,    # Far below
    reg_u=4374.72,    # Far above
    reg_ls=4359.58,   # Below close
    reg_us=4370.45,   # Above close
    atr=9.88          # From your log
)

# Create candle that tested below EMAs but closed back at EMAs
current_candle = {
    'high': 4369.40,   # From your log
    'low': 4350.25,    # From your log - tested 12.81 points below EMAs!
    'close': 4363.06   # From your log - closed exactly at EMAs
}

print(f"Current Price: {4363.06}")
print(f"EMA10/20: {4363.06}")
print(f"Candle Low: {4350.25}")
print(f"Candle High: {4369.40}")
print(f"Tolerance: {9.88 * 0.05}")

# Manual check of conditions
level = 4363.06
current_price = 4363.06
current_candle_low = 4350.25
current_candle_high = 4369.40
tolerance = 9.88 * 0.05  # 0.494

print(f"\nCondition checks:")
print(f"close_to_ema_distance = abs({current_price} - {level}) = {abs(current_price - level)}")
print(f"close_to_ema_distance <= tolerance? {abs(current_price - level)} <= {tolerance} = {abs(current_price - level) <= tolerance}")

if abs(current_price - level) <= tolerance:
    print(f"\nBUY test-and-bounce check:")
    print(f"current_candle_low < level - tolerance? {current_candle_low} < {level - tolerance} = {current_candle_low < level - tolerance}")
    print(f"current_price >= level - tolerance? {current_price} >= {level - tolerance} = {current_price >= level - tolerance}")
    
    buy_pattern = (current_candle_low < level - tolerance and current_price >= level - tolerance)
    sell_pattern = (current_candle_high > level + tolerance and current_price <= level + tolerance)

    if buy_pattern:
        print("✅ BUY test-and-bounce pattern detected!")
    else:
        print("❌ BUY test-and-bounce pattern NOT detected")

    print(f"\nSELL test-and-rejection check:")
    print(f"current_candle_high > level + tolerance? {current_candle_high} > {level + tolerance} = {current_candle_high > level + tolerance}")
    print(f"current_price <= level + tolerance? {current_price} <= {level + tolerance} = {current_price <= level + tolerance}")

    if sell_pattern:
        print("✅ SELL test-and-rejection pattern detected!")
    else:
        print("❌ SELL test-and-rejection pattern NOT detected")

    # Calculate test distances
    buy_test_distance = abs(current_candle_low - level) if current_candle_low < level - tolerance else 0
    sell_test_distance = abs(current_candle_high - level) if current_candle_high > level + tolerance else 0

    print(f"\nTest distances:")
    print(f"BUY test distance: {buy_test_distance:.2f} (candle low {current_candle_low} vs EMA {level})")
    print(f"SELL test distance: {sell_test_distance:.2f} (candle high {current_candle_high} vs EMA {level})")

    if buy_pattern and sell_pattern:
        if buy_test_distance >= sell_test_distance:
            print(f"✅ Stronger BUY test ({buy_test_distance:.2f} >= {sell_test_distance:.2f}) - EMA should be SUPPORT")
        else:
            print(f"✅ Stronger SELL test ({sell_test_distance:.2f} > {buy_test_distance:.2f}) - EMA should be RESISTANCE")
    elif buy_pattern:
        print("✅ Only BUY pattern - EMA should be SUPPORT")
    elif sell_pattern:
        print("✅ Only SELL pattern - EMA should be RESISTANCE")

print(f"\nNormal classification check:")
print(f"level < current_price? {level} < {current_price} = {level < current_price}")
print(f"level > current_price? {level} > {current_price} = {level > current_price}")

confluence = trader._get_support_resistance_confluence(df, 4363.06, "UP", current_candle)
print(f"\nResult: BuyConf={confluence['buy_confluence']:.3f}, SellConf={confluence['sell_confluence']:.3f}")

# Print debug info
debug_info = getattr(trader, '_last_confluence_debug', 'No debug info')
print(f"Debug: {debug_info}")

# Expected result
print(f"\n🎯 EXPECTED:")
print(f"- EMA should be added as SUPPORT only (stronger BUY test: 12.81 vs 6.34)")
print(f"- BuyConf should be 1.000 (candle low exactly at support level)")
print(f"- SellConf should be 0.000 (no resistance levels near candle high)")
