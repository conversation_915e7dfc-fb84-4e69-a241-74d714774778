#!/usr/bin/env python3
"""
AI Model System - Deep Learning for Financial Time Series
Proper neural network with advanced regularization and dropout
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager

# Deep Learning imports
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, regularizers, callbacks
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score, classification_report
import joblib

# Set random seeds for reproducibility
tf.random.set_seed(42)
np.random.seed(42)

class AITradingModel:
    def __init__(self):
        self.mt5_manager = MT5Manager()
        self.scaler = RobustScaler()
        self.model = None
        self.sequence_length = 60  # 60 time steps (5 hours of 5-min data)
        self.features = ['open', 'high', 'low', 'close', 'volume']
        
    def get_financial_data(self, bars=10000):
        """Get comprehensive financial data"""
        print("📊 Getting financial data for AI model...")
        
        if not self.mt5_manager.connect():
            return None
        
        df = self.mt5_manager.get_latest_data("XAUUSD!", "M5", bars)
        if df is None or len(df) < 5000:
            return None
        
        print(f"✅ Retrieved {len(df)} bars")
        print(f"📅 Date range: {df.index[0]} to {df.index[-1]}")
        print(f"💰 Price range: {df['close'].min():.2f} to {df['close'].max():.2f}")
        
        return df
    
    def create_ai_features(self, df):
        """Create features optimized for neural networks"""
        df = df.copy()
        
        # Normalize prices to returns (neural networks work better with stationary data)
        df['return_1'] = df['close'].pct_change(1)
        df['return_5'] = df['close'].pct_change(5)
        
        # Price ratios (scale-invariant)
        df['hl_ratio'] = (df['high'] - df['low']) / df['close']
        df['oc_ratio'] = (df['close'] - df['open']) / df['open']
        
        # Moving averages (normalized)
        for period in [5, 10, 20, 50]:
            ma = df['close'].rolling(period).mean()
            df[f'ma_{period}_ratio'] = (df['close'] - ma) / ma
        
        # Volatility features
        df['volatility_5'] = df['close'].rolling(5).std() / df['close']
        df['volatility_20'] = df['close'].rolling(20).std() / df['close']
        
        # Volume features (if available)
        if 'volume' in df.columns and df['volume'].sum() > 0:
            df['volume_ma'] = df['volume'].rolling(20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
        else:
            df['volume_ratio'] = 1.0  # Default if no volume data
        
        # Technical indicators (normalized)
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi_norm'] = (df['rsi'] - 50) / 50  # Normalize to [-1, 1]
        
        # MACD (normalized)
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = (exp1 - exp2) / df['close']
        
        # Select features for AI model
        ai_features = [
            'return_1', 'return_5', 'hl_ratio', 'oc_ratio',
            'ma_5_ratio', 'ma_10_ratio', 'ma_20_ratio', 'ma_50_ratio',
            'volatility_5', 'volatility_20', 'volume_ratio',
            'rsi_norm', 'macd'
        ]
        
        # Remove any features with all NaN
        available_features = []
        for feature in ai_features:
            if feature in df.columns and not df[feature].isna().all():
                available_features.append(feature)
        
        print(f"✅ Created {len(available_features)} AI features")
        return df[available_features + ['close']]
    
    def create_sequences_and_targets(self, df):
        """Create sequences for LSTM and targets"""
        print("🎯 Creating sequences and targets...")
        
        # Create forward-looking targets (5 periods ahead)
        df['future_return'] = df['close'].shift(-5) / df['close'] - 1
        
        # Use dynamic thresholds based on volatility
        volatility = df['future_return'].rolling(500).std()
        up_threshold = volatility * 0.6
        down_threshold = -volatility * 0.6
        
        # Create targets
        df['target'] = np.where(df['future_return'] > up_threshold, 1,
                               np.where(df['future_return'] < down_threshold, 0, np.nan))
        
        # Remove rows with NaN targets or insufficient history
        clean_df = df.dropna().iloc[self.sequence_length:].copy()
        
        print(f"📊 Clean data: {len(clean_df)} samples")
        print(f"🎯 Target distribution:")
        print(f"   Up: {sum(clean_df['target'] == 1)} ({sum(clean_df['target'] == 1)/len(clean_df)*100:.1f}%)")
        print(f"   Down: {sum(clean_df['target'] == 0)} ({sum(clean_df['target'] == 0)/len(clean_df)*100:.1f}%)")
        
        if len(clean_df) < 1000:
            print("❌ Insufficient clean data")
            return None, None
        
        # Prepare features (exclude target and close)
        feature_cols = [col for col in clean_df.columns if col not in ['target', 'close', 'future_return']]
        
        # Create sequences
        X_sequences = []
        y_targets = []
        
        for i in range(len(clean_df)):
            if i < self.sequence_length:
                continue
                
            # Get sequence of features
            start_idx = i - self.sequence_length
            end_idx = i
            
            sequence = clean_df[feature_cols].iloc[start_idx:end_idx].values
            target = clean_df['target'].iloc[i]
            
            X_sequences.append(sequence)
            y_targets.append(target)
        
        X = np.array(X_sequences)
        y = np.array(y_targets)
        
        print(f"✅ Created sequences: X shape {X.shape}, y shape {y.shape}")
        
        return X, y
    
    def build_ai_model(self, input_shape):
        """Build advanced neural network with regularization"""
        print("🧠 Building AI model...")
        
        model = keras.Sequential([
            # Input layer
            layers.Input(shape=input_shape),
            
            # LSTM layers with dropout and recurrent dropout
            layers.LSTM(64, return_sequences=True, dropout=0.3, recurrent_dropout=0.3),
            layers.BatchNormalization(),
            
            layers.LSTM(32, return_sequences=True, dropout=0.3, recurrent_dropout=0.3),
            layers.BatchNormalization(),
            
            layers.LSTM(16, dropout=0.3, recurrent_dropout=0.3),
            layers.BatchNormalization(),
            
            # Dense layers with strong regularization
            layers.Dense(32, activation='relu', 
                        kernel_regularizer=regularizers.l1_l2(l1=0.01, l2=0.01)),
            layers.Dropout(0.5),
            
            layers.Dense(16, activation='relu',
                        kernel_regularizer=regularizers.l1_l2(l1=0.01, l2=0.01)),
            layers.Dropout(0.4),
            
            layers.Dense(8, activation='relu',
                        kernel_regularizer=regularizers.l1_l2(l1=0.01, l2=0.01)),
            layers.Dropout(0.3),
            
            # Output layer
            layers.Dense(1, activation='sigmoid')
        ])
        
        # Compile with advanced optimizer
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001, clipnorm=1.0),
            loss='binary_crossentropy',
            metrics=['accuracy', 'AUC']
        )
        
        print("✅ AI model built successfully")
        print(f"📊 Model parameters: {model.count_params():,}")
        
        return model
    
    def train_ai_model(self, X, y):
        """Train AI model with advanced callbacks"""
        print("🚀 Training AI model...")
        
        # Scale the features
        X_scaled = np.zeros_like(X)
        for i in range(X.shape[2]):  # Scale each feature
            feature_data = X[:, :, i].reshape(-1, 1)
            self.scaler.fit(feature_data)
            X_scaled[:, :, i] = self.scaler.transform(feature_data).reshape(X.shape[0], X.shape[1])
        
        # Split data with temporal order preserved
        split_idx = int(len(X_scaled) * 0.8)
        X_train, X_val = X_scaled[:split_idx], X_scaled[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        print(f"📊 Training set: {len(X_train)} samples")
        print(f"📊 Validation set: {len(X_val)} samples")
        
        # Build model
        self.model = self.build_ai_model((X.shape[1], X.shape[2]))
        
        # Advanced callbacks for overfitting prevention
        callbacks_list = [
            # Early stopping with patience
            callbacks.EarlyStopping(
                monitor='val_auc',
                patience=15,
                restore_best_weights=True,
                mode='max'
            ),
            
            # Reduce learning rate on plateau
            callbacks.ReduceLROnPlateau(
                monitor='val_auc',
                factor=0.5,
                patience=8,
                min_lr=1e-6,
                mode='max'
            ),
            
            # Model checkpoint
            callbacks.ModelCheckpoint(
                'models_ai/best_model.h5',
                monitor='val_auc',
                save_best_only=True,
                mode='max'
            )
        ]
        
        # Train model
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=100,
            batch_size=32,
            callbacks=callbacks_list,
            verbose=1
        )
        
        # Evaluate final performance
        train_pred = self.model.predict(X_train)
        val_pred = self.model.predict(X_val)
        
        train_auc = roc_auc_score(y_train, train_pred)
        val_auc = roc_auc_score(y_val, val_pred)
        gap = train_auc - val_auc
        
        print(f"\n📊 Final AI Model Performance:")
        print(f"   Training AUC: {train_auc:.4f}")
        print(f"   Validation AUC: {val_auc:.4f}")
        print(f"   Overfitting Gap: {gap:.4f}")
        
        if gap > 0.05:
            print(f"⚠️  Warning: Some overfitting detected")
        else:
            print(f"✅ Good generalization achieved")
        
        return val_auc > 0.55 and gap < 0.08
    
    def test_ai_predictions(self):
        """Test AI model predictions"""
        print("\n🔍 Testing AI predictions...")
        
        predictions = []
        
        for i in range(3):
            # Get live data
            df = self.mt5_manager.get_latest_data("XAUUSD!", "M5", 200)
            if df is None:
                continue
            
            # Create features
            features_df = self.create_ai_features(df)
            
            # Get latest sequence
            if len(features_df) < self.sequence_length:
                continue
            
            feature_cols = [col for col in features_df.columns if col != 'close']
            latest_sequence = features_df[feature_cols].tail(self.sequence_length).values
            
            # Scale sequence
            sequence_scaled = np.zeros_like(latest_sequence)
            for j in range(latest_sequence.shape[1]):
                feature_data = latest_sequence[:, j].reshape(-1, 1)
                sequence_scaled[:, j] = self.scaler.transform(feature_data).reshape(-1)
            
            # Make prediction
            sequence_input = sequence_scaled.reshape(1, self.sequence_length, -1)
            pred_proba = self.model.predict(sequence_input, verbose=0)[0, 0]
            confidence = abs(pred_proba - 0.5) * 2
            
            predictions.append(pred_proba)
            print(f"   Test {i+1}: Prob={pred_proba:.4f}, Conf={confidence:.4f}")
            
            if i < 2:
                import time
                time.sleep(5)
        
        unique_preds = len(set([round(p, 3) for p in predictions]))
        print(f"   Unique predictions: {unique_preds}")
        
        return len(predictions) >= 2 and unique_preds >= 2
    
    def save_ai_model(self):
        """Save AI model and components"""
        os.makedirs('models_ai', exist_ok=True)
        
        # Save model
        self.model.save('models_ai/ai_trading_model.h5')
        
        # Save scaler and metadata
        joblib.dump(self.scaler, 'models_ai/scaler.pkl')
        
        metadata = {
            'sequence_length': self.sequence_length,
            'features': self.features
        }
        joblib.dump(metadata, 'models_ai/metadata.pkl')
        
        print("💾 AI model saved successfully!")
    
    def run_ai_system_build(self):
        """Run complete AI system build"""
        print("🚀 AI TRADING MODEL SYSTEM")
        print("=" * 60)
        print("Deep Learning with Advanced Regularization")
        print("=" * 60)
        
        # Get data
        df = self.get_financial_data(10000)
        if df is None:
            return False
        
        # Create AI features
        features_df = self.create_ai_features(df)
        
        # Create sequences and targets
        X, y = self.create_sequences_and_targets(features_df)
        if X is None:
            return False
        
        # Train AI model
        if not self.train_ai_model(X, y):
            print("❌ AI model training failed")
            return False
        
        # Test predictions
        if not self.test_ai_predictions():
            print("❌ AI prediction test failed")
            return False
        
        # Save model
        self.save_ai_model()
        
        print("\n🎉 AI TRADING MODEL READY!")
        print("Advanced neural network with proper regularization")
        
        self.mt5_manager.disconnect()
        return True

def main():
    """Main function"""
    print("🧠 AI TRADING MODEL SYSTEM")
    print("Deep Learning for Financial Time Series")
    print()
    
    ai_system = AITradingModel()
    success = ai_system.run_ai_system_build()
    
    if success:
        print("\n✅ AI trading model ready for deployment!")
    else:
        print("\n❌ AI model build failed")

if __name__ == "__main__":
    main()
