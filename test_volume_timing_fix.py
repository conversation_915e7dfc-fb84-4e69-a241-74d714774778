#!/usr/bin/env python3
"""
Test Volume Timing Fix
Verify that volume analysis now uses only closed candles, matching QQE timing
"""

import sys
import pandas as pd
import numpy as np
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_timing_test_data():
    """Create test data to verify volume timing"""
    logger.info("📊 Creating timing test data...")
    
    # Create 15 periods of test data
    periods = 15
    data = []
    
    for i in range(periods):
        # Create realistic price and volume data
        price = 2000 + i * 0.5 + np.random.uniform(-0.2, 0.2)
        
        # Create volume pattern where current forming candle has very different volume
        if i == periods - 1:  # Current forming candle
            volume = 5000  # Very high volume (should be ignored)
        else:  # Closed candles
            volume = 1000 + i * 50  # Gradual increase
        
        # Create OHLC
        high = price + 0.3
        low = price - 0.3
        open_price = price + np.random.uniform(-0.1, 0.1)
        close = price
        
        data.append({
            'datetime': pd.Timestamp('2024-01-01') + pd.Timedelta(minutes=5*i),
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('datetime', inplace=True)
    
    logger.info(f"✅ Created {len(df)} periods")
    logger.info(f"   Closed candles (0-13): Volume 1000-1650")
    logger.info(f"   Current forming (14): Volume 5000 (should be ignored)")
    
    return df

def test_volume_timing_consistency():
    """Test that volume analysis uses only closed candles"""
    logger.info("\n🔧 TESTING VOLUME TIMING CONSISTENCY...")
    
    try:
        from qqe_indicator import QQEIndicator
        
        # Create QQE indicator
        qqe = QQEIndicator(volume_lookback=10, volume_divergence_lookback=10)
        
        # Get test data
        df = create_timing_test_data()
        
        logger.info(f"\n   📊 RAW DATA CHECK:")
        logger.info(f"      Last closed candle volume: {df.iloc[-2]['volume']}")
        logger.info(f"      Current forming candle volume: {df.iloc[-1]['volume']}")
        
        # Calculate volume indicators
        df_with_volume = qqe.calculate_volume_indicators(df)
        
        # Check volume analysis results
        logger.info(f"\n   📊 VOLUME ANALYSIS RESULTS:")
        
        # Check last closed candle values
        last_closed_volume_sma = df_with_volume.iloc[-2]['volume_sma']
        last_closed_volume_ratio = df_with_volume.iloc[-2]['volume_ratio']
        
        # Check current forming candle values (should be same as last closed)
        current_forming_volume_sma = df_with_volume.iloc[-1]['volume_sma']
        current_forming_volume_ratio = df_with_volume.iloc[-1]['volume_ratio']
        
        logger.info(f"      Last closed candle:")
        logger.info(f"        Volume SMA: {last_closed_volume_sma:.2f}")
        logger.info(f"        Volume Ratio: {last_closed_volume_ratio:.2f}")
        
        logger.info(f"      Current forming candle:")
        logger.info(f"        Volume SMA: {current_forming_volume_sma:.2f}")
        logger.info(f"        Volume Ratio: {current_forming_volume_ratio:.2f}")
        
        # Test 1: Volume SMA should be same for both (based on closed candles only)
        if abs(last_closed_volume_sma - current_forming_volume_sma) < 0.01:
            logger.info("      ✅ CORRECT: Volume SMA same for both (based on closed candles)")
        else:
            logger.error("      ❌ WRONG: Volume SMA different (forming candle affecting calculation)")
            return False
        
        # Test 2: Volume ratio should be same for both (based on closed candles only)
        if abs(last_closed_volume_ratio - current_forming_volume_ratio) < 0.01:
            logger.info("      ✅ CORRECT: Volume ratio same for both (based on closed candles)")
        else:
            logger.error("      ❌ WRONG: Volume ratio different (forming candle affecting calculation)")
            return False
        
        # Test 3: If we calculated volume ratio using forming candle, it would be very different
        forming_candle_volume = df.iloc[-1]['volume']  # 5000
        if forming_candle_volume > 0:
            theoretical_wrong_ratio = forming_candle_volume / last_closed_volume_sma
            logger.info(f"      📊 If forming candle was used: ratio would be {theoretical_wrong_ratio:.2f}")
            logger.info(f"      📊 Actual ratio (correct): {current_forming_volume_ratio:.2f}")
            
            if theoretical_wrong_ratio != current_forming_volume_ratio:
                logger.info("      ✅ CORRECT: Forming candle volume ignored as expected")
            else:
                logger.error("      ❌ WRONG: Forming candle volume being used!")
                return False
        
        logger.info("\n✅ Volume Timing Consistency Test: PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Volume Timing Consistency Test: FAILED - {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

def test_divergence_timing_consistency():
    """Test that divergence detection uses only closed candles"""
    logger.info("\n🔧 TESTING DIVERGENCE TIMING CONSISTENCY...")
    
    try:
        from qqe_indicator import QQEIndicator
        
        # Create QQE indicator
        qqe = QQEIndicator(volume_lookback=10, volume_divergence_lookback=10)
        
        # Get test data
        df = create_timing_test_data()
        
        # Calculate QQE with volume analysis
        df_with_qqe = qqe.calculate_qqe_bands(df)
        
        # Check divergence analysis
        last_closed_divergence = df_with_qqe.iloc[-2]['divergence_type']
        current_forming_divergence = df_with_qqe.iloc[-1]['divergence_type']
        
        logger.info(f"   📊 DIVERGENCE ANALYSIS:")
        logger.info(f"      Last closed candle divergence: {last_closed_divergence}")
        logger.info(f"      Current forming candle divergence: {current_forming_divergence}")
        
        # Test: Divergence should be same for both (based on closed candles only)
        if last_closed_divergence == current_forming_divergence:
            logger.info("      ✅ CORRECT: Divergence same for both (based on closed candles)")
        else:
            logger.error("      ❌ WRONG: Divergence different (forming candle affecting calculation)")
            return False
        
        logger.info("\n✅ Divergence Timing Consistency Test: PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Divergence Timing Consistency Test: FAILED - {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

def test_qqe_analysis_timing():
    """Test that QQE analysis uses last closed candle"""
    logger.info("\n🔧 TESTING QQE ANALYSIS TIMING...")
    
    try:
        from qqe_indicator import QQEIndicator
        
        # Create QQE indicator
        qqe = QQEIndicator()
        
        # Get test data
        df = create_timing_test_data()
        
        # Calculate QQE
        df_with_qqe = qqe.calculate_qqe_bands(df)
        
        # Get QQE analysis
        qqe_analysis = qqe.get_qqe_analysis(df_with_qqe)
        
        logger.info(f"   📊 QQE ANALYSIS TIMING:")
        logger.info(f"      Analysis uses: Last closed candle (index -2)")
        logger.info(f"      Volume ratio from analysis: {qqe_analysis.get('volume_ratio', 'N/A')}")
        logger.info(f"      Divergence type from analysis: {qqe_analysis.get('divergence_type', 'N/A')}")
        
        # Verify analysis uses last closed candle data
        last_closed_volume_ratio = df_with_qqe.iloc[-2]['volume_ratio']
        analysis_volume_ratio = qqe_analysis.get('volume_ratio', 0)
        
        if abs(last_closed_volume_ratio - analysis_volume_ratio) < 0.01:
            logger.info("      ✅ CORRECT: QQE analysis uses last closed candle data")
        else:
            logger.error("      ❌ WRONG: QQE analysis not using last closed candle data")
            return False
        
        logger.info("\n✅ QQE Analysis Timing Test: PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ QQE Analysis Timing Test: FAILED - {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run volume timing tests"""
    logger.info("🧪 TESTING VOLUME TIMING FIX")
    logger.info("=" * 70)
    
    tests = [
        ("Volume Timing Consistency", test_volume_timing_consistency),
        ("Divergence Timing Consistency", test_divergence_timing_consistency),
        ("QQE Analysis Timing", test_qqe_analysis_timing)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            logger.error(f"❌ {test_name}: FAILED with exception - {e}")
    
    logger.info("\n" + "=" * 70)
    logger.info("🏁 VOLUME TIMING TEST SUMMARY")
    logger.info(f"   ✅ Passed: {passed}")
    logger.info(f"   ❌ Failed: {failed}")
    logger.info(f"   📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        logger.info("🎉 ALL TIMING TESTS PASSED!")
        logger.info("   Volume analysis now correctly uses only closed candles")
        logger.info("   This matches QQE signal timing for consistent analysis")
        return True
    else:
        logger.error(f"⚠️ {failed} tests failed. Volume timing issues remain.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
