"""
Model Evaluation and Reporting Module
Generates comprehensive reports and visualizations for model performance
"""
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path
import json
from sklearn.metrics import confusion_matrix, classification_report
from datetime import datetime

from config.config import *

# Set up logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

# Set plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class ModelEvaluator:
    """
    Comprehensive model evaluation and reporting
    """
    
    def __init__(self):
        self.results = {}
        self.figures = {}
        
    def generate_comprehensive_report(self, training_results: Dict, 
                                    test_data: Optional[pd.DataFrame] = None) -> Dict:
        """
        Generate comprehensive evaluation report
        
        Args:
            training_results: Results from training pipeline
            test_data: Optional test data for additional analysis
            
        Returns:
            Dictionary with all evaluation results
        """
        try:
            logger.info("Generating comprehensive model evaluation report...")
            
            report = {
                'model_performance': self._analyze_model_performance(training_results),
                'training_analysis': self._analyze_training_process(training_results),
                'overfitting_analysis': training_results.get('overfitting_analysis', {}),
                'feature_importance': self._analyze_feature_importance(training_results),
                'trading_simulation': self._simulate_trading_performance(training_results, test_data),
                'visualizations': self._create_visualizations(training_results),
                'recommendations': self._generate_recommendations(training_results)
            }
            
            # Save report
            self._save_report(report)
            
            logger.info("Comprehensive evaluation report generated successfully")
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating comprehensive report: {e}")
            return {}
    
    def _analyze_model_performance(self, results: Dict) -> Dict:
        """Analyze overall model performance"""
        try:
            performance = {
                'summary': {},
                'detailed_metrics': {},
                'class_performance': {}
            }
            
            # Extract results for different datasets
            train_results = results.get('training_results', {})
            val_results = results.get('validation_results', {})
            test_results = results.get('test_results', {})
            
            # Summary metrics
            performance['summary'] = {
                'train_accuracy': train_results.get('accuracy', 0),
                'validation_accuracy': val_results.get('accuracy', 0),
                'test_accuracy': test_results.get('accuracy', 0),
                'train_f1': train_results.get('f1_score', 0),
                'validation_f1': val_results.get('f1_score', 0),
                'test_f1': test_results.get('f1_score', 0)
            }
            
            # Detailed metrics for test set
            if test_results:
                performance['detailed_metrics'] = {
                    'accuracy': test_results.get('accuracy', 0),
                    'precision': test_results.get('precision', 0),
                    'recall': test_results.get('recall', 0),
                    'f1_score': test_results.get('f1_score', 0),
                    'roc_auc': test_results.get('roc_auc', None)
                }
                
                # Class-wise performance
                class_report = test_results.get('classification_report', {})
                if class_report:
                    performance['class_performance'] = {
                        str(k): v for k, v in class_report.items() 
                        if isinstance(v, dict) and k not in ['accuracy', 'macro avg', 'weighted avg']
                    }
            
            return performance
            
        except Exception as e:
            logger.error(f"Error analyzing model performance: {e}")
            return {}
    
    def _analyze_training_process(self, results: Dict) -> Dict:
        """Analyze the training process"""
        try:
            training_analysis = {
                'convergence': {},
                'stability': {},
                'efficiency': {}
            }
            
            history = results.get('training_history', {})
            
            if history:
                # Convergence analysis
                train_loss = history.get('loss', [])
                val_loss = history.get('val_loss', [])
                
                if train_loss and val_loss:
                    training_analysis['convergence'] = {
                        'epochs_trained': len(train_loss),
                        'final_train_loss': train_loss[-1],
                        'final_val_loss': val_loss[-1],
                        'best_val_loss': min(val_loss),
                        'best_epoch': val_loss.index(min(val_loss)) + 1,
                        'early_stopped': len(train_loss) < MODEL_CONFIG['epochs']
                    }
                
                # Training stability
                if len(train_loss) > 10:
                    recent_train_std = np.std(train_loss[-10:])
                    recent_val_std = np.std(val_loss[-10:]) if len(val_loss) > 10 else 0
                    
                    training_analysis['stability'] = {
                        'train_loss_stability': recent_train_std,
                        'val_loss_stability': recent_val_std,
                        'stable_training': recent_train_std < 0.01 and recent_val_std < 0.01
                    }
            
            return training_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing training process: {e}")
            return {}
    
    def _analyze_feature_importance(self, results: Dict) -> Dict:
        """Analyze feature importance (simplified for LSTM)"""
        try:
            feature_analysis = {
                'num_features': len(results.get('feature_columns', [])),
                'feature_categories': self._categorize_features(results.get('feature_columns', [])),
                'recommendations': []
            }
            
            # Categorize features
            features = results.get('feature_columns', [])
            categories = {
                'price_indicators': [f for f in features if any(x in f.lower() for x in ['ema', 'sma', 'bb', 'price'])],
                'momentum_indicators': [f for f in features if any(x in f.lower() for x in ['rsi', 'macd', 'stoch', 'momentum'])],
                'volatility_indicators': [f for f in features if any(x in f.lower() for x in ['atr', 'volatility', 'range'])],
                'volume_indicators': [f for f in features if 'volume' in f.lower()],
                'time_features': [f for f in features if any(x in f.lower() for x in ['hour', 'day', 'session'])],
                'market_structure': [f for f in features if any(x in f.lower() for x in ['adx', 'trend', 'regime', 'fractal'])]
            }
            
            feature_analysis['feature_categories'] = {k: len(v) for k, v in categories.items()}
            
            # Recommendations based on feature distribution
            total_features = len(features)
            if total_features > 100:
                feature_analysis['recommendations'].append("Consider feature selection to reduce dimensionality")
            elif total_features < 20:
                feature_analysis['recommendations'].append("Consider adding more features for better performance")
            
            return feature_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing feature importance: {e}")
            return {}
    
    def _categorize_features(self, features: List[str]) -> Dict:
        """Categorize features by type"""
        categories = {
            'price_based': 0,
            'momentum': 0,
            'volatility': 0,
            'volume': 0,
            'time': 0,
            'other': 0
        }
        
        for feature in features:
            feature_lower = feature.lower()
            if any(x in feature_lower for x in ['price', 'ema', 'sma', 'bb', 'vwap']):
                categories['price_based'] += 1
            elif any(x in feature_lower for x in ['rsi', 'macd', 'stoch', 'momentum', 'roc']):
                categories['momentum'] += 1
            elif any(x in feature_lower for x in ['atr', 'volatility', 'range', 'true_range']):
                categories['volatility'] += 1
            elif 'volume' in feature_lower:
                categories['volume'] += 1
            elif any(x in feature_lower for x in ['hour', 'day', 'session', 'time']):
                categories['time'] += 1
            else:
                categories['other'] += 1
        
        return categories
    
    def _simulate_trading_performance(self, results: Dict, test_data: Optional[pd.DataFrame] = None) -> Dict:
        """Simulate trading performance based on model predictions"""
        try:
            simulation = {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0,
                'total_return': 0,
                'max_drawdown': 0,
                'sharpe_ratio': 0,
                'profit_factor': 0
            }
            
            test_results = results.get('test_results', {})
            predictions = test_results.get('predictions', [])
            true_labels = test_results.get('true_labels', [])
            
            if not predictions or not true_labels:
                logger.warning("No predictions available for trading simulation")
                return simulation
            
            # Simple simulation assuming:
            # 0 = Hold, 1 = Buy, 2 = Sell
            # Calculate returns based on correct predictions
            
            trades = []
            for pred, actual in zip(predictions, true_labels):
                if pred != 0:  # Only count actual trading signals
                    trade_result = 1 if pred == actual else -1  # Win/Loss
                    trades.append(trade_result)
            
            if trades:
                simulation['total_trades'] = len(trades)
                simulation['winning_trades'] = sum(1 for t in trades if t > 0)
                simulation['losing_trades'] = sum(1 for t in trades if t < 0)
                simulation['win_rate'] = simulation['winning_trades'] / simulation['total_trades']
                
                # Simplified return calculation
                # Assume 1% return per winning trade, -1% per losing trade
                returns = [0.01 if t > 0 else -0.01 for t in trades]
                simulation['total_return'] = sum(returns)
                
                # Calculate max drawdown
                cumulative_returns = np.cumsum(returns)
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdowns = cumulative_returns - running_max
                simulation['max_drawdown'] = abs(min(drawdowns)) if len(drawdowns) > 0 else 0
                
                # Sharpe ratio (simplified)
                if len(returns) > 1:
                    simulation['sharpe_ratio'] = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
                
                # Profit factor
                gross_profit = sum(r for r in returns if r > 0)
                gross_loss = abs(sum(r for r in returns if r < 0))
                simulation['profit_factor'] = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            return simulation
            
        except Exception as e:
            logger.error(f"Error simulating trading performance: {e}")
            return {}
    
    def _create_visualizations(self, results: Dict) -> Dict:
        """Create visualization plots"""
        try:
            visualizations = {}
            
            # Training history plots
            history = results.get('training_history', {})
            if history:
                visualizations['training_curves'] = self._plot_training_curves(history)
                visualizations['loss_curves'] = self._plot_loss_curves(history)
            
            # Confusion matrix
            test_results = results.get('test_results', {})
            if test_results.get('confusion_matrix'):
                visualizations['confusion_matrix'] = self._plot_confusion_matrix(test_results)
            
            # Performance comparison
            visualizations['performance_comparison'] = self._plot_performance_comparison(results)
            
            return visualizations
            
        except Exception as e:
            logger.error(f"Error creating visualizations: {e}")
            return {}
    
    def _plot_training_curves(self, history: Dict) -> str:
        """Plot training curves"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Training History', fontsize=16)
            
            # Loss curves
            if 'loss' in history and 'val_loss' in history:
                axes[0, 0].plot(history['loss'], label='Training Loss')
                axes[0, 0].plot(history['val_loss'], label='Validation Loss')
                axes[0, 0].set_title('Model Loss')
                axes[0, 0].set_xlabel('Epoch')
                axes[0, 0].set_ylabel('Loss')
                axes[0, 0].legend()
                axes[0, 0].grid(True)
            
            # Accuracy curves
            if 'accuracy' in history and 'val_accuracy' in history:
                axes[0, 1].plot(history['accuracy'], label='Training Accuracy')
                axes[0, 1].plot(history['val_accuracy'], label='Validation Accuracy')
                axes[0, 1].set_title('Model Accuracy')
                axes[0, 1].set_xlabel('Epoch')
                axes[0, 1].set_ylabel('Accuracy')
                axes[0, 1].legend()
                axes[0, 1].grid(True)
            
            # Learning rate (if available)
            if 'lr' in history:
                axes[1, 0].plot(history['lr'])
                axes[1, 0].set_title('Learning Rate')
                axes[1, 0].set_xlabel('Epoch')
                axes[1, 0].set_ylabel('Learning Rate')
                axes[1, 0].set_yscale('log')
                axes[1, 0].grid(True)
            
            # Loss difference
            if 'loss' in history and 'val_loss' in history:
                loss_diff = np.array(history['val_loss']) - np.array(history['loss'])
                axes[1, 1].plot(loss_diff)
                axes[1, 1].set_title('Validation - Training Loss')
                axes[1, 1].set_xlabel('Epoch')
                axes[1, 1].set_ylabel('Loss Difference')
                axes[1, 1].axhline(y=0, color='r', linestyle='--', alpha=0.7)
                axes[1, 1].grid(True)
            
            plt.tight_layout()
            
            # Save plot
            plot_path = REPORTS_DIR / "training_curves.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(plot_path)
            
        except Exception as e:
            logger.error(f"Error plotting training curves: {e}")
            return ""
    
    def _plot_confusion_matrix(self, test_results: Dict) -> str:
        """Plot confusion matrix"""
        try:
            conf_matrix = np.array(test_results['confusion_matrix'])
            
            plt.figure(figsize=(8, 6))
            sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues',
                       xticklabels=['Hold', 'Buy', 'Sell'],
                       yticklabels=['Hold', 'Buy', 'Sell'])
            plt.title('Confusion Matrix')
            plt.xlabel('Predicted')
            plt.ylabel('Actual')
            
            # Save plot
            plot_path = REPORTS_DIR / "confusion_matrix.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(plot_path)
            
        except Exception as e:
            logger.error(f"Error plotting confusion matrix: {e}")
            return ""
    
    def _plot_performance_comparison(self, results: Dict) -> str:
        """Plot performance comparison across datasets"""
        try:
            train_results = results.get('training_results', {})
            val_results = results.get('validation_results', {})
            test_results = results.get('test_results', {})
            
            metrics = ['accuracy', 'precision', 'recall', 'f1_score']
            datasets = ['Training', 'Validation', 'Test']
            
            data = []
            for dataset, result_dict in zip(datasets, [train_results, val_results, test_results]):
                for metric in metrics:
                    if metric in result_dict:
                        data.append({
                            'Dataset': dataset,
                            'Metric': metric.replace('_', ' ').title(),
                            'Value': result_dict[metric]
                        })
            
            if data:
                df = pd.DataFrame(data)
                
                plt.figure(figsize=(12, 6))
                sns.barplot(data=df, x='Metric', y='Value', hue='Dataset')
                plt.title('Model Performance Comparison')
                plt.ylabel('Score')
                plt.xticks(rotation=45)
                plt.legend(title='Dataset')
                plt.grid(True, alpha=0.3)
                
                # Save plot
                plot_path = REPORTS_DIR / "performance_comparison.png"
                plt.savefig(plot_path, dpi=300, bbox_inches='tight')
                plt.close()
                
                return str(plot_path)
            
            return ""
            
        except Exception as e:
            logger.error(f"Error plotting performance comparison: {e}")
            return ""
    
    def _generate_recommendations(self, results: Dict) -> List[str]:
        """Generate recommendations based on results"""
        try:
            recommendations = []
            
            # Performance-based recommendations
            test_results = results.get('test_results', {})
            test_accuracy = test_results.get('accuracy', 0)
            
            if test_accuracy < 0.6:
                recommendations.append("Model accuracy is below 60%. Consider improving features or model architecture.")
            elif test_accuracy > 0.8:
                recommendations.append("Excellent model performance! Consider deploying for live trading.")
            
            # Overfitting recommendations
            overfitting_analysis = results.get('overfitting_analysis', {})
            if overfitting_analysis.get('is_overfitting'):
                recommendations.extend(overfitting_analysis.get('recommendations', []))
            
            # Feature recommendations
            feature_analysis = results.get('feature_importance', {})
            if feature_analysis:
                recommendations.extend(feature_analysis.get('recommendations', []))
            
            # Trading simulation recommendations
            simulation = results.get('trading_simulation', {})
            win_rate = simulation.get('win_rate', 0)
            
            if win_rate < 0.5:
                recommendations.append("Win rate below 50%. Consider adjusting trading thresholds or improving model.")
            elif win_rate > 0.6:
                recommendations.append("Good win rate achieved. Monitor live performance carefully.")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return []
    
    def _save_report(self, report: Dict):
        """Save evaluation report"""
        try:
            # Save as JSON
            report_file = REPORTS_DIR / f"evaluation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            # Convert numpy arrays to lists for JSON serialization
            json_report = self._convert_numpy_to_list(report.copy())
            
            with open(report_file, 'w') as f:
                json.dump(json_report, f, indent=2, default=str)
            
            logger.info(f"Evaluation report saved to {report_file}")
            
        except Exception as e:
            logger.error(f"Error saving evaluation report: {e}")
    
    def _convert_numpy_to_list(self, obj):
        """Convert numpy arrays to lists for JSON serialization"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: self._convert_numpy_to_list(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_numpy_to_list(item) for item in obj]
        else:
            return obj
