#!/usr/bin/env python3
"""
Data Preparation Pipeline for LSTM Training
Uses identical feature engineering as fixed_live_trader.py
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add src to path for imports
sys.path.append('src')
sys.path.append('.')

# Import the exact same classes from fixed_live_trader.py
from fixed_live_trader import FixedFeatureEngineer, RegimeDetector

class LSTMDataPreparer:
    """Prepare data for LSTM training using identical feature engineering"""
    
    def __init__(self):
        self.feature_engineer = FixedFeatureEngineer()
        self.regime_detector = RegimeDetector()
        
    def load_recent_data(self, months=12):
        """Load last N months of data from XAU_5m_data.csv"""
        print(f"Loading last {months} months of XAU data...")
        
        # Load data
        df = pd.read_csv('data/XAU_5m_data.csv')
        df['datetime'] = pd.to_datetime(df['datetime'])
        
        # Filter to recent data
        cutoff_date = df['datetime'].max() - timedelta(days=months * 30)
        recent_df = df[df['datetime'] >= cutoff_date].copy()
        
        # Sort by datetime
        recent_df = recent_df.sort_values('datetime').reset_index(drop=True)
        
        print(f"Loaded {len(recent_df):,} records from {recent_df['datetime'].min().date()} to {recent_df['datetime'].max().date()}")
        
        return recent_df
    
    def prepare_features(self, df):
        """Apply identical feature engineering as live system"""
        print("Applying feature engineering...")
        
        # Apply technical indicators (same as live system)
        features_df = self.feature_engineer.create_technical_indicators(df)
        
        # Add regime indicators (same as live system)
        features_df = self.regime_detector.calculate_regime_indicators(features_df)
        
        # Add candlestick position analysis (same as live system)
        features_df = self.regime_detector.calculate_candle_position(features_df)
        
        print(f"Features created: {len(features_df.columns)} columns")
        
        return features_df
    
    def create_targets(self, df, prediction_steps=[1, 3, 5]):
        """Create PERCENTAGE-BASED prediction targets for LSTM training"""
        print(f"Creating PERCENTAGE-BASED prediction targets for steps: {prediction_steps}")

        targets_df = df.copy()

        # PERCENTAGE-BASED Price targets (relative to current close)
        for step in prediction_steps:
            current_close = df['close']
            future_open = df['open'].shift(-step)
            future_high = df['high'].shift(-step)
            future_low = df['low'].shift(-step)
            future_close = df['close'].shift(-step)

            # Percentage changes from current close (more robust)
            targets_df[f'target_open_pct_{step}'] = (future_open - current_close) / current_close
            targets_df[f'target_high_pct_{step}'] = (future_high - current_close) / current_close
            targets_df[f'target_low_pct_{step}'] = (future_low - current_close) / current_close
            targets_df[f'target_close_pct_{step}'] = (future_close - current_close) / current_close

            # Price direction (1 for up, 0 for down)
            targets_df[f'target_direction_{step}'] = (future_close > current_close).astype(int)

            # Keep the existing change percentage (same as close_pct)
            targets_df[f'target_change_pct_{step}'] = (future_close - current_close) / current_close
        
        # Regime targets (detect regime for next N steps)
        for step in prediction_steps:
            # Shift regime detection results
            if 'regime' in df.columns:
                targets_df[f'target_regime_{step}'] = df['regime'].shift(-step)
            
            # Bull/bear strength targets
            if 'bullish_strength' in df.columns:
                targets_df[f'target_bull_strength_{step}'] = df['bullish_strength'].shift(-step)
                targets_df[f'target_bear_strength_{step}'] = df['bearish_strength'].shift(-step)
        
        print(f"Targets created for {len(prediction_steps)} prediction steps")
        
        return targets_df
    
    def get_feature_columns(self, df):
        """Get list of feature columns (exclude datetime, targets, etc.)"""
        exclude_patterns = [
            'datetime', 'target_', 'regime', 'bullish_strength', 'bearish_strength',
            'dominant_bias', 'net_strength', 'candles_analyzed'
        ]

        feature_cols = []
        for col in df.columns:
            if not any(pattern in col for pattern in exclude_patterns):
                # Include both numeric and categorical features
                if df[col].dtype in ['float64', 'int64', 'bool', 'object']:
                    # Convert categorical to numeric for LSTM
                    if df[col].dtype == 'object':
                        # Handle trend direction and other categorical features
                        if col in ['trend_direction', 'accurate_trend_direction']:
                            continue  # Skip these for now, will encode separately
                        elif col in ['rsi_momentum', 'ema_fast_trend', 'ema_slow_trend', 'ema_cross_trend',
                                   'price_trend_3', 'price_trend_5', 'price_trend_8', 'ma_trend']:
                            feature_cols.append(col)
                    elif df[col].dtype == 'bool':
                        feature_cols.append(col)
                    else:
                        feature_cols.append(col)

        return feature_cols

    def encode_categorical_features(self, df):
        """Encode categorical features for LSTM training"""
        df_encoded = df.copy()

        # Encode UP/DOWN features as 1/0
        categorical_cols = ['rsi_momentum', 'ema_fast_trend', 'ema_slow_trend', 'ema_cross_trend',
                           'price_trend_3', 'price_trend_5', 'price_trend_8', 'ma_trend']

        for col in categorical_cols:
            if col in df_encoded.columns:
                df_encoded[col] = (df_encoded[col] == 'UP').astype(int)

        # Encode trend direction (BUY=1, SELL=0)
        if 'trend_direction' in df_encoded.columns:
            df_encoded['trend_direction_encoded'] = (df_encoded['trend_direction'] == 'BUY').astype(int)

        if 'accurate_trend_direction' in df_encoded.columns:
            df_encoded['accurate_trend_direction_encoded'] = (df_encoded['accurate_trend_direction'] == 'UP').astype(int)

        return df_encoded
    
    def prepare_lstm_dataset(self, months=12, sequence_length=60, prediction_steps=[1, 3, 5]):
        """Complete pipeline to prepare LSTM training dataset"""
        print("=== LSTM Dataset Preparation ===")
        
        # Step 1: Load recent data
        df = self.load_recent_data(months)
        
        # Step 2: Apply feature engineering
        features_df = self.prepare_features(df)

        # Step 2.5: Encode categorical features
        features_df = self.encode_categorical_features(features_df)

        # Step 3: Create targets
        targets_df = self.create_targets(features_df, prediction_steps)

        # Step 4: Get feature columns
        feature_cols = self.get_feature_columns(targets_df)
        print(f"Selected {len(feature_cols)} feature columns")
        
        # Step 5: Remove rows with NaN values
        clean_df = targets_df.dropna()
        print(f"Clean dataset: {len(clean_df):,} rows (removed {len(targets_df) - len(clean_df):,} NaN rows)")
        
        # Step 6: Save prepared dataset
        output_file = f'data/lstm_training_data_{months}m.csv'
        clean_df.to_csv(output_file, index=False)
        print(f"Saved prepared dataset to: {output_file}")
        
        # Step 7: Save feature column list
        feature_file = f'data/feature_columns_{months}m.txt'
        with open(feature_file, 'w') as f:
            for col in feature_cols:
                f.write(f"{col}\n")
        print(f"Saved feature columns to: {feature_file}")
        
        return clean_df, feature_cols

if __name__ == "__main__":
    # Prepare 12-month dataset
    preparer = LSTMDataPreparer()
    df, features = preparer.prepare_lstm_dataset(months=12)
    
    print(f"\n=== DATASET SUMMARY ===")
    print(f"Total rows: {len(df):,}")
    print(f"Features: {len(features)}")
    print(f"Date range: {df['datetime'].min()} to {df['datetime'].max()}")
