#!/usr/bin/env python3
"""
Test the corrected swing point detection logic with mock data
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def create_test_data():
    """Create test data with known swing points including recent candle scenarios"""

    # Create test data with clear swing points
    dates = pd.date_range(start='2024-01-01', periods=25, freq='5min')

    # Create price data where the MOST RECENT CLOSED candle is a swing point
    highs = [
        2000.0, 2001.0, 2002.0, 2003.0, 2004.0,  # Rising to setup
        2005.0, 2015.0, 2005.0, 2004.0, 2003.0,  # CLEAR swing high at index 6 (2015.0)
        2002.0, 2001.0, 2000.0, 1999.0, 1990.0,  # Falling to setup
        1995.0, 2000.0, 2001.0, 2002.0, 2003.0,  # CLEAR swing low at index 14 (1990.0)
        2004.0, 2005.0, 2006.0, 2007.0, 2020.0   # Rising to NEW HIGH at most recent (index 24)
    ]

    # Create corresponding lows that make sense
    lows = []
    for i, h in enumerate(highs):
        if i == 6:  # Swing high candle
            lows.append(h - 5.0)  # Higher low for swing high candle
        elif i == 14:  # Swing low candle
            lows.append(h - 10.0)  # Much lower low for swing low candle
        elif i == 24:  # Most recent candle - potential swing high
            lows.append(h - 4.0)  # Higher low for potential swing high
        else:
            lows.append(h - 3.0)  # Normal spread

    closes = [h - 1.5 for h in highs]  # Closes between high and low
    opens = closes.copy()

    df = pd.DataFrame({
        'time': dates,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'tick_volume': [100] * 25
    })

    df.set_index('time', inplace=True)

    return df

def create_test_data_recent_low():
    """Create test data where most recent closed candle is a swing low"""

    dates = pd.date_range(start='2024-01-01', periods=15, freq='5min')

    # Create scenario where most recent candle is a swing low
    highs = [
        2010.0, 2009.0, 2008.0, 2007.0, 2006.0,  # Falling trend
        2005.0, 2004.0, 2003.0, 2002.0, 2001.0,  # Continue falling
        2000.0, 1999.0, 1998.0, 1997.0, 1990.0   # Most recent is swing low
    ]

    lows = [h - 3.0 for h in highs]  # Normal spread
    lows[-1] = highs[-1] - 8.0  # Make most recent candle have a much lower low

    closes = [h - 1.5 for h in highs]
    opens = closes.copy()

    df = pd.DataFrame({
        'time': dates,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'tick_volume': [100] * 15
    })

    df.set_index('time', inplace=True)

    return df

def test_swing_detection():
    """Test the swing point detection with mock data"""
    print("🧪 TESTING IMPROVED SWING POINT DETECTION")
    print("=" * 60)

    # Test 1: Data with most recent candle as potential swing high
    print("\n📈 TEST 1: Most Recent Candle as Potential Swing High")
    df = create_test_data()

    print("📊 TEST DATA (last 10 candles):")
    for i in range(10):
        idx = -(10-i)
        candle = df.iloc[idx]
        marker = ""
        if idx == -1:
            marker = " ← Most Recent (Potential Swing HIGH 2020.0)"
        print(f"  {len(df) + idx:2d}: H={candle['high']:7.1f} L={candle['low']:7.1f} C={candle['close']:7.1f}{marker}")

    # Create trader instance
    trader = FixedLiveTrader("XAUUSD!")

    # Test the swing detection
    print(f"\n🔍 RUNNING SWING DETECTION:")
    swing_points = trader.find_recent_swing_points(df)

    print(f"\n📋 RESULTS:")
    print(f"Swing points available: {swing_points['swing_points_available']}")

    if swing_points['recent_high']:
        print(f"✅ Recent Swing High: {swing_points['recent_high']:.1f}")
        print(f"   📍 Distance from current: {swing_points['recent_high_distance']:.1f}")
        print(f"   📅 Candles ago: {swing_points['recent_high_candles_ago']}")
    else:
        print("❌ No recent swing high found")

    if swing_points['recent_low']:
        print(f"✅ Recent Swing Low: {swing_points['recent_low']:.1f}")
        print(f"   📍 Distance from current: {swing_points['recent_low_distance']:.1f}")
        print(f"   📅 Candles ago: {swing_points['recent_low_candles_ago']}")
    else:
        print("❌ No recent swing low found")

    # Test 2: Data with most recent candle as potential swing low
    print("\n📉 TEST 2: Most Recent Candle as Potential Swing Low")
    df_low = create_test_data_recent_low()

    print("📊 TEST DATA (last 5 candles):")
    for i in range(5):
        idx = -(5-i)
        candle = df_low.iloc[idx]
        marker = ""
        if idx == -1:
            marker = " ← Most Recent (Potential Swing LOW 1990.0)"
        print(f"  {len(df_low) + idx:2d}: H={candle['high']:7.1f} L={candle['low']:7.1f} C={candle['close']:7.1f}{marker}")

    print(f"\n🔍 RUNNING SWING DETECTION:")
    swing_points_low = trader.find_recent_swing_points(df_low)

    print(f"\n📋 RESULTS:")
    if swing_points_low['recent_low']:
        print(f"✅ Recent Swing Low: {swing_points_low['recent_low']:.1f}")
        print(f"   📍 Distance from current: {swing_points_low['recent_low_distance']:.1f}")
        print(f"   📅 Candles ago: {swing_points_low['recent_low_candles_ago']}")
    else:
        print("❌ No recent swing low found")

    return swing_points

def test_debug_script_logic():
    """Test the corrected debug script logic"""
    print(f"\n🧪 TESTING DEBUG SCRIPT LOGIC")
    print("=" * 40)
    
    # Test with simple 3-candle pattern
    # Swing high: 2000, 2010, 2005 (middle is highest)
    h1, h2, h3 = 2005.0, 2010.0, 2000.0  # h2 is highest
    l1, l2, l3 = 2003.0, 2008.0, 1998.0  # l3 is lowest
    
    print(f"Test candles: H1={h1} H2={h2} H3={h3}")
    print(f"              L1={l1} L2={l2} L3={l3}")
    
    # Test swing high detection (corrected logic)
    print(f"\n🔍 SWING HIGH CHECK:")
    print(f"Condition: H2 > H3 AND H2 > H1")
    print(f"H2 ({h2}) > H3 ({h3}) = {h2 > h3}")
    print(f"H2 ({h2}) > H1 ({h1}) = {h2 > h1}")
    high_found = h2 > h3 and h2 > h1
    print(f"Both conditions met: {high_found}")
    if high_found:
        print(f"✅ SWING HIGH FOUND: H2 = {h2}")
    
    # Test swing low detection (corrected logic)
    print(f"\n🔍 SWING LOW CHECK:")
    print(f"Condition: L2 < L3 AND L2 < L1")
    print(f"L2 ({l2}) < L3 ({l3}) = {l2 < l3}")
    print(f"L2 ({l2}) < L1 ({l1}) = {l2 < l1}")
    low_found = l2 < l3 and l2 < l1
    print(f"Both conditions met: {low_found}")
    if low_found:
        print(f"✅ SWING LOW FOUND: L2 = {l2}")
    else:
        print(f"❌ No swing low found (L3 is the actual low)")

if __name__ == "__main__":
    try:
        # Test the main implementation
        swing_points = test_swing_detection()
        
        # Test the debug script logic
        test_debug_script_logic()
        
        print(f"\n✅ SWING DETECTION TESTS COMPLETE")
        print("=" * 60)
        
        if swing_points['swing_points_available']:
            print("🎉 SUCCESS: Swing points detected correctly!")
        else:
            print("⚠️  WARNING: No swing points detected - check implementation")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
