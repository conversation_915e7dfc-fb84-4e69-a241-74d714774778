#!/usr/bin/env python3
"""
Test feature engineering consistency between training data and live system
"""

import pandas as pd
import numpy as np
import sys
sys.path.append('.')

from fixed_live_trader import FixedFeatureEngineer, RegimeDetector

def test_feature_consistency():
    """Test that training features match live system features exactly"""
    print("=== FEATURE CONSISTENCY TEST ===")
    
    # Load a small sample of raw data
    df = pd.read_csv('data/XAU_5m_data.csv')
    df['datetime'] = pd.to_datetime(df['datetime'])
    
    # Take last 200 rows for testing
    test_df = df.tail(200).copy().reset_index(drop=True)
    
    print(f"Testing with {len(test_df)} rows")
    
    # Initialize feature engineering classes
    feature_engineer = FixedFeatureEngineer()
    regime_detector = RegimeDetector()
    
    # Apply feature engineering (same as live system)
    print("Applying feature engineering...")
    features_df = feature_engineer.create_technical_indicators(test_df)
    features_df = regime_detector.calculate_regime_indicators(features_df)
    features_df = regime_detector.calculate_candle_position(features_df)
    
    # Load training data features for comparison
    training_features = []
    with open('data/feature_columns_12m.txt', 'r') as f:
        training_features = [line.strip() for line in f if line.strip()]
    
    print(f"Training features: {len(training_features)}")
    print(f"Live system features: {len(features_df.columns)}")
    
    # Check which features are in both
    live_features = list(features_df.columns)
    
    # Features in training but not in live
    missing_in_live = set(training_features) - set(live_features)
    if missing_in_live:
        print(f"\n❌ Features in training but missing in live: {missing_in_live}")
    
    # Features in live but not in training
    missing_in_training = set(live_features) - set(training_features) - {'datetime'}
    if missing_in_training:
        print(f"\n⚠️ Features in live but missing in training: {missing_in_training}")
    
    # Common features
    common_features = set(training_features) & set(live_features)
    print(f"\n✅ Common features: {len(common_features)}")
    
    # Test a few key features for value consistency
    print("\n=== VALUE CONSISTENCY TEST ===")
    key_features = ['rsi', 'atr', 'bb_position', 'ema_fast_slope', 'trend_strength']
    
    for feature in key_features:
        if feature in features_df.columns:
            values = features_df[feature].dropna()
            if len(values) > 0:
                print(f"{feature}: min={values.min():.6f}, max={values.max():.6f}, mean={values.mean():.6f}")
            else:
                print(f"{feature}: No valid values")
        else:
            print(f"{feature}: Missing from live features")
    
    # Check for any infinite or NaN values
    print("\n=== DATA QUALITY CHECK ===")
    for col in features_df.select_dtypes(include=[np.number]).columns:
        inf_count = np.isinf(features_df[col]).sum()
        nan_count = features_df[col].isna().sum()
        if inf_count > 0 or nan_count > 0:
            print(f"{col}: {inf_count} infinite, {nan_count} NaN values")
    
    print("\n✅ Feature consistency test completed!")
    
    return features_df, training_features

if __name__ == "__main__":
    live_features, training_features = test_feature_consistency()
