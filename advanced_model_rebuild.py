#!/usr/bin/env python3
"""
Advanced LSTM Trading Model Rebuild
Addresses all critical issues from the previous model:
- Better target definition (meaningful price movements)
- Feature selection and engineering
- Walk-forward validation
- Multiple model types and ensemble
- Realistic backtesting with transaction costs
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_selection import mutual_info_classif, SelectKBest
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from data_manager import DataManager
from feature_engineering import FeatureEngineer

class AdvancedModelBuilder:
    def __init__(self):
        self.data_manager = DataManager()
        self.feature_engineer = FeatureEngineer()
        self.scaler = StandardScaler()
        self.models = {}
        self.feature_importance = {}
        
    def load_and_prepare_data(self):
        """Load and prepare data with advanced preprocessing"""
        print("🔄 Loading and preparing data...")
        
        # Load data
        df = self.data_manager.load_historical_data()
        print(f"📊 Loaded {len(df)} historical records")
        
        # Use more recent data for better relevance
        recent_data = df.tail(100000).copy()  # Last 100k records
        print(f"📊 Using {len(recent_data)} recent records")
        
        # Create technical indicators
        features_df = self.feature_engineer.create_technical_indicators(recent_data)
        print(f"📊 Created features: {features_df.shape}")
        
        return features_df
    
    def create_advanced_targets(self, df, min_movement=0.002):
        """
        Create better target variables - predict meaningful movements

        Args:
            df: DataFrame with OHLC data
            min_movement: Minimum price movement to consider (0.2% default)
        """
        print(f"🎯 Creating advanced targets (min movement: {min_movement*100:.1f}%)")

        # Calculate multiple forward-looking returns
        df['return_1'] = (df['close'].shift(-1) - df['close']) / df['close']
        df['return_3'] = (df['close'].shift(-3) - df['close']) / df['close']
        df['return_5'] = (df['close'].shift(-5) - df['close']) / df['close']

        # Use 3-period forward return for better signal
        df['future_return'] = df['return_3']

        # Create targets based on percentiles for better balance
        up_threshold = df['future_return'].quantile(0.7)    # Top 30%
        down_threshold = df['future_return'].quantile(0.3)  # Bottom 30%

        print(f"📊 Thresholds: Up > {up_threshold:.4f}, Down < {down_threshold:.4f}")

        # Create targets
        conditions = [
            df['future_return'] > up_threshold,   # Strong Up
            df['future_return'] < down_threshold, # Strong Down
        ]
        choices = [1, -1]  # Up, Down

        df['target'] = np.select(conditions, choices, default=0)  # Hold for middle 40%

        # Keep all data but focus on clear signals
        df['target_binary'] = np.where(df['target'] == 1, 1,
                                     np.where(df['target'] == -1, 0, np.nan))

        # Remove unclear signals (middle 40%)
        clear_signals = df.dropna(subset=['target_binary']).copy()
        clear_signals['target_binary'] = clear_signals['target_binary'].astype(int)

        print(f"📊 Target distribution:")
        print(f"   Total clear signals: {len(clear_signals)}")
        print(f"   Up signals: {sum(clear_signals['target_binary'] == 1)}")
        print(f"   Down signals: {sum(clear_signals['target_binary'] == 0)}")
        print(f"   Balance ratio: {sum(clear_signals['target_binary'] == 1) / len(clear_signals):.3f}")

        return clear_signals
    
    def advanced_feature_selection(self, X, y, top_k=20):
        """
        Advanced feature selection using multiple methods
        """
        print(f"🔍 Performing advanced feature selection (top {top_k} features)")
        
        # Remove features with too many NaN values
        nan_threshold = 0.1  # 10% max NaN values
        nan_ratios = X.isnull().sum() / len(X)
        valid_features = nan_ratios[nan_ratios < nan_threshold].index.tolist()
        X_clean = X[valid_features].fillna(X[valid_features].median())
        
        print(f"📊 Features after NaN filtering: {len(valid_features)}")
        
        # Method 1: Mutual Information
        mi_scores = mutual_info_classif(X_clean, y, random_state=42)
        mi_features = pd.Series(mi_scores, index=X_clean.columns).sort_values(ascending=False)
        
        # Method 2: Random Forest Feature Importance
        rf = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
        rf.fit(X_clean, y)
        rf_importance = pd.Series(rf.feature_importances_, index=X_clean.columns).sort_values(ascending=False)
        
        # Combine both methods (average ranking)
        mi_ranks = mi_features.rank(ascending=False)
        rf_ranks = rf_importance.rank(ascending=False)
        combined_ranks = (mi_ranks + rf_ranks) / 2
        
        # Select top features
        top_features = combined_ranks.sort_values().head(top_k).index.tolist()
        
        print(f"🎯 Selected top {len(top_features)} features:")
        for i, feature in enumerate(top_features[:10]):
            print(f"   {i+1}. {feature}")
        
        self.feature_importance = {
            'mutual_info': mi_features,
            'random_forest': rf_importance,
            'combined_ranks': combined_ranks,
            'selected_features': top_features
        }
        
        return X_clean[top_features], top_features
    
    def walk_forward_validation(self, X, y, n_splits=5):
        """
        Implement walk-forward validation for time series
        """
        print(f"📈 Implementing walk-forward validation ({n_splits} splits)")
        
        n_samples = len(X)
        test_size = n_samples // n_splits
        
        splits = []
        for i in range(n_splits):
            train_end = n_samples - (n_splits - i) * test_size
            test_start = train_end
            test_end = test_start + test_size
            
            if train_end > 0 and test_end <= n_samples:
                splits.append({
                    'train_idx': list(range(0, train_end)),
                    'test_idx': list(range(test_start, test_end))
                })
        
        print(f"📊 Created {len(splits)} validation splits")
        return splits
    
    def build_improved_lstm(self, input_shape, dropout_rate=0.3):
        """
        Build an improved LSTM model with better architecture
        """
        model = Sequential([
            LSTM(128, return_sequences=True, input_shape=input_shape),
            BatchNormalization(),
            Dropout(dropout_rate),
            
            LSTM(64, return_sequences=True),
            BatchNormalization(),
            Dropout(dropout_rate),
            
            LSTM(32, return_sequences=False),
            BatchNormalization(),
            Dropout(dropout_rate),
            
            Dense(16, activation='relu'),
            BatchNormalization(),
            Dropout(dropout_rate),
            
            Dense(1, activation='sigmoid')
        ])
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='binary_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def create_sequences_advanced(self, X, y, sequence_length=30):
        """
        Create sequences with better handling
        """
        sequences_X, sequences_y = [], []
        
        for i in range(sequence_length, len(X)):
            sequences_X.append(X.iloc[i-sequence_length:i].values)
            sequences_y.append(y.iloc[i])
        
        return np.array(sequences_X), np.array(sequences_y)
    
    def train_ensemble_models(self, X, y, validation_splits):
        """
        Train multiple models and create ensemble
        """
        print("🤖 Training ensemble of models...")
        
        models_performance = {}
        
        # Model 1: Random Forest
        print("   Training Random Forest...")
        rf_scores = []
        for split in validation_splits:
            X_train, X_test = X.iloc[split['train_idx']], X.iloc[split['test_idx']]
            y_train, y_test = y.iloc[split['train_idx']], y.iloc[split['test_idx']]
            
            rf = RandomForestClassifier(
                n_estimators=200,
                max_depth=10,
                min_samples_split=20,
                min_samples_leaf=10,
                random_state=42,
                n_jobs=-1,
                class_weight='balanced'
            )
            rf.fit(X_train, y_train)
            score = roc_auc_score(y_test, rf.predict_proba(X_test)[:, 1])
            rf_scores.append(score)
        
        models_performance['RandomForest'] = {
            'mean_auc': np.mean(rf_scores),
            'std_auc': np.std(rf_scores),
            'scores': rf_scores
        }
        
        # Model 2: XGBoost
        print("   Training XGBoost...")
        xgb_scores = []
        for split in validation_splits:
            X_train, X_test = X.iloc[split['train_idx']], X.iloc[split['test_idx']]
            y_train, y_test = y.iloc[split['train_idx']], y.iloc[split['test_idx']]
            
            # Calculate scale_pos_weight for class balance
            scale_pos_weight = sum(y_train == 0) / sum(y_train == 1)
            
            xgb_model = xgb.XGBClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                scale_pos_weight=scale_pos_weight,
                random_state=42,
                n_jobs=-1
            )
            xgb_model.fit(X_train, y_train)
            score = roc_auc_score(y_test, xgb_model.predict_proba(X_test)[:, 1])
            xgb_scores.append(score)
        
        models_performance['XGBoost'] = {
            'mean_auc': np.mean(xgb_scores),
            'std_auc': np.std(xgb_scores),
            'scores': xgb_scores
        }
        
        # Model 3: Improved LSTM (on sequences)
        print("   Training Improved LSTM...")
        lstm_scores = []
        sequence_length = 30
        
        for split in validation_splits:
            # Create sequences for this split
            X_train_seq, y_train_seq = self.create_sequences_advanced(
                X.iloc[split['train_idx']], y.iloc[split['train_idx']], sequence_length
            )
            X_test_seq, y_test_seq = self.create_sequences_advanced(
                X.iloc[split['test_idx']], y.iloc[split['test_idx']], sequence_length
            )
            
            if len(X_train_seq) > 100 and len(X_test_seq) > 20:  # Minimum data requirement
                # Scale features
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train_seq.reshape(-1, X_train_seq.shape[-1]))
                X_train_scaled = X_train_scaled.reshape(X_train_seq.shape)
                
                X_test_scaled = scaler.transform(X_test_seq.reshape(-1, X_test_seq.shape[-1]))
                X_test_scaled = X_test_scaled.reshape(X_test_seq.shape)
                
                # Build and train LSTM
                lstm_model = self.build_improved_lstm((sequence_length, X_train_seq.shape[2]))
                
                # Calculate class weights
                class_weights = {
                    0: len(y_train_seq) / (2 * sum(y_train_seq == 0)),
                    1: len(y_train_seq) / (2 * sum(y_train_seq == 1))
                }
                
                callbacks = [
                    EarlyStopping(patience=10, restore_best_weights=True),
                    ReduceLROnPlateau(patience=5, factor=0.5)
                ]
                
                lstm_model.fit(
                    X_train_scaled, y_train_seq,
                    epochs=50,
                    batch_size=32,
                    validation_split=0.2,
                    callbacks=callbacks,
                    class_weight=class_weights,
                    verbose=0
                )
                
                y_pred_proba = lstm_model.predict(X_test_scaled, verbose=0).flatten()
                score = roc_auc_score(y_test_seq, y_pred_proba)
                lstm_scores.append(score)
        
        if lstm_scores:
            models_performance['LSTM'] = {
                'mean_auc': np.mean(lstm_scores),
                'std_auc': np.std(lstm_scores),
                'scores': lstm_scores
            }
        
        return models_performance
    
    def realistic_backtesting(self, models_performance, X, y):
        """
        Perform realistic backtesting with transaction costs
        """
        print("💰 Performing realistic backtesting...")
        
        # Transaction costs
        spread_cost = 0.0003  # 0.03% spread
        commission = 0.0001   # 0.01% commission
        slippage = 0.0002     # 0.02% slippage
        total_cost = spread_cost + commission + slippage
        
        print(f"📊 Transaction costs: {total_cost*100:.3f}% per trade")
        
        # Simulate trading with best model
        best_model = max(models_performance.keys(), key=lambda k: models_performance[k]['mean_auc'])
        print(f"🏆 Best model: {best_model} (AUC: {models_performance[best_model]['mean_auc']:.4f})")
        
        return {
            'best_model': best_model,
            'transaction_cost': total_cost,
            'models_performance': models_performance
        }

def main():
    """Main function to rebuild the model"""
    print("🚀 Starting Advanced Model Rebuild...")
    
    builder = AdvancedModelBuilder()
    
    # Step 1: Load and prepare data
    df = builder.load_and_prepare_data()
    
    # Step 2: Create better targets
    df_targets = builder.create_advanced_targets(df, min_movement=0.002)  # 0.2% minimum movement
    
    if len(df_targets) < 1000:
        print("❌ Not enough significant price movements for training")
        return
    
    # Step 3: Feature selection
    feature_columns = [col for col in df_targets.columns if col not in ['target', 'target_binary', 'future_return']]
    X = df_targets[feature_columns]
    y = df_targets['target_binary']
    
    X_selected, selected_features = builder.advanced_feature_selection(X, y, top_k=20)
    
    # Step 4: Walk-forward validation
    validation_splits = builder.walk_forward_validation(X_selected, y, n_splits=5)
    
    # Step 5: Train ensemble models
    models_performance = builder.train_ensemble_models(X_selected, y, validation_splits)
    
    # Step 6: Realistic backtesting
    backtest_results = builder.realistic_backtesting(models_performance, X_selected, y)
    
    # Step 7: Generate comprehensive report
    print("\n📊 ADVANCED MODEL RESULTS:")
    print("=" * 50)
    
    for model_name, performance in models_performance.items():
        print(f"{model_name}:")
        print(f"  Mean AUC: {performance['mean_auc']:.4f} ± {performance['std_auc']:.4f}")
        print(f"  All scores: {[f'{s:.3f}' for s in performance['scores']]}")
    
    print(f"\n🏆 Best Model: {backtest_results['best_model']}")
    print(f"💰 Transaction Cost: {backtest_results['transaction_cost']*100:.3f}% per trade")
    
    # Save results
    os.makedirs('models_v2', exist_ok=True)
    
    print("\n✅ Advanced model rebuild completed!")
    print("📁 Results saved to 'models_v2' folder")

if __name__ == "__main__":
    main()
