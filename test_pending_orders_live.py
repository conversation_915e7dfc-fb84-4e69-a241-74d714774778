#!/usr/bin/env python3
"""
Live test script to verify pending order functionality with actual MT5 connection
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager
from fixed_live_trader import FixedLiveTrader

def test_mt5_connection():
    """Test MT5 connection and basic functionality"""
    print("🔌 Testing MT5 Connection...")
    
    mt5_manager = MT5Manager("XAUUSD!")
    
    if not mt5_manager.connect():
        print("   ❌ Failed to connect to MT5")
        return False
    
    print("   ✅ Connected to MT5 successfully")
    
    # Get current price
    tick = mt5_manager.get_symbol_info_tick("XAUUSD!")
    if tick:
        print(f"   📊 Current XAUUSD! price: Bid={tick['bid']}, Ask={tick['ask']}")
    else:
        print("   ⚠️ Could not get current price")
    
    # Test pip size calculation
    pip_size = mt5_manager.get_pip_size("XAUUSD!")
    print(f"   📏 Pip size for XAUUSD!: {pip_size}")
    
    mt5_manager.disconnect()
    return True

def test_pending_order_placement():
    """Test actual pending order placement"""
    print("\n📋 Testing Pending Order Placement...")
    
    trader = FixedLiveTrader("XAUUSD!")
    
    if not trader.mt5_manager.connect():
        print("   ❌ Failed to connect to MT5")
        return False
    
    print("   ✅ Connected to MT5")
    
    # Get current price to create realistic test orders
    tick = trader.mt5_manager.get_symbol_info_tick("XAUUSD!")
    if not tick:
        print("   ❌ Could not get current price")
        trader.mt5_manager.disconnect()
        return False
    
    current_price = tick['ask']
    print(f"   📊 Current price: {current_price}")
    
    # Create test confirmation candle data based on current price
    confirmation_candle_data = {
        'high': current_price + 2.0,  # 2 points above current
        'low': current_price - 2.0,   # 2 points below current
        'close': current_price
    }
    
    print(f"   📊 Test candle: High={confirmation_candle_data['high']}, Low={confirmation_candle_data['low']}")
    
    # Test BUY STOP order (should be placed above current price)
    print("\n🟢 Testing BUY STOP order:")
    try:
        result = trader.place_pending_order_from_confirmation_candle(
            'BUY', confirmation_candle_data, 0.01, 1.0
        )
        if result:
            print(f"   ✅ BUY STOP order placed successfully!")
            print(f"   📋 Order details logged in system")
        else:
            print("   ❌ Failed to place BUY STOP order")
    except Exception as e:
        print(f"   ❌ Error placing BUY STOP order: {e}")
    
    # Test SELL STOP order (should be placed below current price)
    print("\n🔴 Testing SELL STOP order:")
    try:
        result = trader.place_pending_order_from_confirmation_candle(
            'SELL', confirmation_candle_data, 0.01, 1.0
        )
        if result:
            print(f"   ✅ SELL STOP order placed successfully!")
            print(f"   📋 Order details logged in system")
        else:
            print("   ❌ Failed to place SELL STOP order")
    except Exception as e:
        print(f"   ❌ Error placing SELL STOP order: {e}")
    
    # Show pending orders in system
    print(f"\n📋 Pending orders in system: {len(trader.pending_orders)}")
    for i, order in enumerate(trader.pending_orders):
        print(f"   {i+1}. Type: {order['type']}, Price: {order['price']}, Ticket: {order.get('ticket', 'N/A')}")
    
    trader.mt5_manager.disconnect()
    return True

def test_order_management():
    """Test order management functions"""
    print("\n🔧 Testing Order Management...")
    
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test expired order checking
    print("   🕐 Testing expired order checking...")
    try:
        trader.check_and_remove_expired_pending_orders()
        print("   ✅ Expired order check completed")
    except Exception as e:
        print(f"   ❌ Error in expired order check: {e}")
    
    # Test filled order checking
    print("   📈 Testing filled order checking...")
    try:
        trader.check_pending_orders_filled()
        print("   ✅ Filled order check completed")
    except Exception as e:
        print(f"   ❌ Error in filled order check: {e}")

def main():
    """Run all live pending order tests"""
    print("🧪 LIVE PENDING ORDER TEST")
    print("=" * 50)
    print("⚠️  WARNING: This test will attempt to place actual pending orders on MT5!")
    print("⚠️  Make sure you're connected to a demo account!")
    print("=" * 50)
    
    # Test 1: MT5 connection
    if not test_mt5_connection():
        print("❌ MT5 connection failed - aborting tests")
        return
    
    # Test 2: Pending order placement
    if not test_pending_order_placement():
        print("❌ Pending order placement failed")
        return
    
    # Test 3: Order management
    test_order_management()
    
    print("\n📊 SUMMARY")
    print("=" * 30)
    print("✅ Live pending order tests completed!")
    print("🎯 Key fixes applied:")
    print("   • Shortened comment from 'Pending SELL from candle confirmation' to 'PSELL'")
    print("   • Fixed numpy float64 -> Python float conversion")
    print("   • Added parameter validation")
    print("   • Added symbol validation and selection")
    print("   • Added retry logic with different filling types")
    print("   • Added better error reporting")
    
    print("\n📝 If orders still fail, check:")
    print("   • Account has sufficient margin")
    print("   • Symbol is available for trading")
    print("   • Order prices are valid (not too close to current price)")
    print("   • Broker allows pending orders for this symbol")

if __name__ == "__main__":
    main()
