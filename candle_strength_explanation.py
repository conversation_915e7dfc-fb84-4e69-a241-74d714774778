#!/usr/bin/env python3
"""
Comprehensive explanation of Candle Strength calculation logic
"""

import sys
import warnings
import pandas as pd
import numpy as np
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def explain_candle_strength_logic():
    """Detailed explanation of how candle strength is calculated"""
    print("📊 CANDLE STRENGTH CALCULATION LOGIC")
    print("=" * 60)
    
    print("\n🎯 OVERVIEW:")
    print("Candle strength measures the recent bullish vs bearish momentum")
    print("by analyzing the last 8 candles and their characteristics.")
    print("")
    
    print("📈 STEP-BY-STEP CALCULATION:")
    print("-" * 40)
    
    print("\n1️⃣ DATA COLLECTION:")
    print("   • Analyzes the last 8 candles (lookback=8)")
    print("   • Uses OHLC data: Open, High, Low, Close")
    print("   • Includes ATR (Average True Range) for normalization")
    
    print("\n2️⃣ INDIVIDUAL CANDLE ANALYSIS:")
    print("   For each candle, calculate:")
    print("   • Candle Range = High - Low")
    print("   • Candle Body = |Close - Open|")
    print("   • Body-to-Range Ratio = Body / Range")
    print("   • Bullish/Bearish Classification:")
    print("     - Bullish: Close > Open (green candle)")
    print("     - Bearish: Close < Open (red candle)")
    
    print("\n3️⃣ STRENGTH WEIGHTING:")
    print("   Each candle gets a strength weight based on:")
    print("   • Body Size relative to ATR: (Body / ATR)")
    print("   • Body-to-Range Ratio (how much of the range is body)")
    print("   • Formula: Weight = (Body/ATR) × (Body/Range)")
    print("   ")
    print("   This means:")
    print("   • Larger bodies = Higher weight")
    print("   • Bodies that fill more of the range = Higher weight")
    print("   • Small wicks = Higher weight")
    print("   • Large wicks = Lower weight")
    
    print("\n4️⃣ BULLISH vs BEARISH STRENGTH:")
    print("   • Bullish Strength = Sum of (Bullish candles × Their weights)")
    print("   • Bearish Strength = Sum of (Bearish candles × Their weights)")
    
    print("\n5️⃣ NORMALIZATION:")
    print("   • Total Strength = Bullish Strength + Bearish Strength")
    print("   • Bullish % = Bullish Strength / Total Strength")
    print("   • Bearish % = Bearish Strength / Total Strength")
    print("   • Net Strength = Bullish % - Bearish %")
    print("   ")
    print("   Result: Net Strength ranges from -1.0 to +1.0")
    print("   • +1.0 = 100% bullish (all candles bullish)")
    print("   • -1.0 = 100% bearish (all candles bearish)")
    print("   •  0.0 = Perfectly balanced")
    
    print("\n6️⃣ PERCENTAGE CONVERSION:")
    print("   • Final Candle Strength % = Net Strength × 100")
    print("   • Example: Net Strength 0.234 = +23.4%")
    print("   • Example: Net Strength -0.456 = -45.6%")
    
    print("\n🔍 PRACTICAL EXAMPLES:")
    print("-" * 30)
    
    examples = [
        ("All 8 candles bullish, large bodies", "+100%", "Maximum bullish"),
        ("All 8 candles bearish, large bodies", "-100%", "Maximum bearish"),
        ("6 bullish, 2 bearish, equal sizes", "+50%", "Strong bullish bias"),
        ("4 bullish, 4 bearish, equal sizes", "0%", "Perfectly balanced"),
        ("2 bullish, 6 bearish, equal sizes", "-50%", "Strong bearish bias"),
        ("Mixed candles, small bodies", "±5%", "Weak/neutral strength")
    ]
    
    for scenario, strength, description in examples:
        print(f"   {scenario:35s} → {strength:6s} ({description})")
    
    print("\n⚖️ QUALITY FACTORS:")
    print("-" * 25)
    
    print("Higher strength when:")
    print("✅ Large candle bodies (strong moves)")
    print("✅ Bodies fill most of the range (decisive moves)")
    print("✅ Consistent direction (all bullish or bearish)")
    print("✅ Recent candles (last 8 are most important)")
    print("")
    print("Lower strength when:")
    print("❌ Small candle bodies (weak moves)")
    print("❌ Large wicks (indecision)")
    print("❌ Mixed directions (choppy market)")
    print("❌ Doji candles (open ≈ close)")
    
    print("\n🎯 TRADING SIGNAL LOGIC:")
    print("-" * 35)
    
    print("Current System Thresholds:")
    print("• BUY Signal: Candle Strength > +15%")
    print("• SELL Signal: Candle Strength < -15%")
    print("• NO Signal: Between -15% and +15%")
    print("• Sensitive Closing: BUY closes <0%, SELL closes >0%")
    
    print("\n📊 REAL-WORLD INTERPRETATION:")
    print("-" * 40)
    
    interpretations = [
        ("+50% to +100%", "Very strong bullish momentum"),
        ("+15% to +50%", "Moderate bullish momentum → BUY"),
        ("+0% to +15%", "Weak bullish bias → NO SIGNAL"),
        ("-15% to +0%", "Weak bearish bias → NO SIGNAL"),
        ("-50% to -15%", "Moderate bearish momentum → SELL"),
        ("-100% to -50%", "Very strong bearish momentum")
    ]
    
    for range_str, meaning in interpretations:
        print(f"   {range_str:15s}: {meaning}")
    
    # Live example
    try:
        trader = FixedLiveTrader()
        if trader.mt5_manager.connect():
            print(f"\n🔴 LIVE EXAMPLE:")
            print("-" * 20)
            
            result = trader.get_live_prediction()
            if result:
                _, _, _, _, _, _, _, candle_strength = result
                net_strength = candle_strength['net_strength']
                percentage = net_strength * 100
                
                print(f"Current Candle Strength: {percentage:+.1f}%")
                print(f"Net Strength (raw): {net_strength:+.3f}")
                print(f"Bullish Strength: {candle_strength['bullish_strength']:.3f}")
                print(f"Bearish Strength: {candle_strength['bearish_strength']:.3f}")
                print(f"Dominant Bias: {candle_strength['dominant_bias']}")
                print(f"Candles Analyzed: {candle_strength['candles_analyzed']}")
                
                # Interpretation
                if percentage > 15:
                    signal = "BUY"
                    interpretation = "Bullish momentum detected"
                elif percentage < -15:
                    signal = "SELL"
                    interpretation = "Bearish momentum detected"
                else:
                    signal = "NONE"
                    interpretation = "Neutral/weak momentum"
                
                print(f"Signal Generated: {signal}")
                print(f"Interpretation: {interpretation}")
            
            trader.mt5_manager.disconnect()
    except:
        print("\n⚠️  Could not connect for live example")
    
    print(f"\n🧮 MATHEMATICAL FORMULA:")
    print("-" * 30)
    
    print("For each candle i in last 8 candles:")
    print("   Weight[i] = (Body[i] / ATR) × (Body[i] / Range[i])")
    print("   ")
    print("Bullish_Sum = Σ(Weight[i] × IsBullish[i])")
    print("Bearish_Sum = Σ(Weight[i] × IsBearish[i])")
    print("Total = Bullish_Sum + Bearish_Sum")
    print("   ")
    print("Bullish_Norm = Bullish_Sum / Total")
    print("Bearish_Norm = Bearish_Sum / Total")
    print("Net_Strength = Bullish_Norm - Bearish_Norm")
    print("   ")
    print("Candle_Strength_% = Net_Strength × 100")
    
    print(f"\n✅ CANDLE STRENGTH LOGIC EXPLAINED!")
    print("=" * 60)
    print("🎯 Measures recent 8-candle bullish vs bearish momentum")
    print("⚖️  Weights candles by body size and decisiveness")
    print("📊 Normalizes to -100% to +100% scale")
    print("🚀 Used for ±15% BUY/SELL signal generation")
    print("🔄 Used for 0% sensitive closing logic")

if __name__ == "__main__":
    explain_candle_strength_logic()
