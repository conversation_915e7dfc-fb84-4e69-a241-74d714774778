#!/usr/bin/env python3
"""
Simplified LSTM training with binary classification
"""

import pandas as pd
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, accuracy_score
import sys
import os

# Add src to path
sys.path.append('src')

from data_manager import DataManager
from feature_engineering import FeatureEngineer

def create_sequences(X, y, sequence_length=20):
    """Create sequences for LSTM"""
    X_seq, y_seq = [], []
    
    for i in range(sequence_length, len(X)):
        X_seq.append(X[i-sequence_length:i])
        y_seq.append(y[i])
    
    return np.array(X_seq), np.array(y_seq)

def create_simple_lstm(input_shape):
    """Create a simple LSTM model"""
    model = Sequential([
        LSTM(64, return_sequences=True, input_shape=input_shape),
        Dropout(0.2),
        BatchNormalization(),
        
        LSTM(32, return_sequences=False),
        Dropout(0.2),
        BatchNormalization(),
        
        Dense(16, activation='relu'),
        Dropout(0.2),
        
        Dense(1, activation='sigmoid')  # Binary classification
    ])
    
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='binary_crossentropy',
        metrics=['accuracy']
    )
    
    return model

def simple_lstm_train():
    """Train LSTM with simplified approach"""
    
    print("🚀 Simple LSTM Training...")
    
    # Load data
    data_manager = DataManager()
    df = data_manager.load_historical_data()
    
    # Use recent data only (last 50k records for faster training)
    df_recent = df.tail(50000)
    print(f"📊 Using recent data: {df_recent.shape}")
    
    # Create features
    feature_engineer = FeatureEngineer()
    df_features = feature_engineer.create_technical_indicators(df_recent)
    
    # Create simple binary target
    df_features['future_return'] = df_features['close'].shift(-1) / df_features['close'] - 1
    df_features['target'] = (df_features['future_return'] > 0).astype(int)
    
    # Remove NaN rows
    df_clean = df_features.dropna()
    print(f"📊 Clean data shape: {df_clean.shape}")
    
    # Select top features based on Random Forest results
    top_features = [
        'fractal_up', 'fractal_down', 'pivot_low', 'pivot_high',
        'upper_shadow', 'lower_shadow', 'williams_r', 'price_vs_ema_fast',
        'channel_position', 'roc_5', 'rsi', 'macd', 'bb_position',
        'atr_normalized', 'volume_ratio', 'stoch_k', 'cci'
    ]
    
    # Use available features
    available_features = [col for col in top_features if col in df_clean.columns]
    print(f"📈 Using {len(available_features)} top features: {available_features}")
    
    # Prepare data
    X = df_clean[available_features].values
    y = df_clean['target'].values
    
    # Scale features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Create sequences
    sequence_length = 20  # Shorter sequence
    X_seq, y_seq = create_sequences(X_scaled, y, sequence_length)
    
    print(f"📊 Sequence data shape: X={X_seq.shape}, y={y_seq.shape}")
    
    # Split data temporally
    split_idx = int(len(X_seq) * 0.8)
    X_train, X_test = X_seq[:split_idx], X_seq[split_idx:]
    y_train, y_test = y_seq[:split_idx], y_seq[split_idx:]
    
    print(f"📊 Train: {X_train.shape}, Test: {X_test.shape}")
    print(f"🎯 Train target distribution: {np.bincount(y_train)}")
    print(f"🎯 Test target distribution: {np.bincount(y_test)}")
    
    # Create model
    model = create_simple_lstm((sequence_length, len(available_features)))
    print("🧠 Model created:")
    model.summary()
    
    # Callbacks
    callbacks = [
        EarlyStopping(patience=10, restore_best_weights=True),
        ReduceLROnPlateau(patience=5, factor=0.5, min_lr=1e-6)
    ]
    
    # Train model
    print("🏋️ Training LSTM...")
    history = model.fit(
        X_train, y_train,
        validation_data=(X_test, y_test),
        epochs=50,
        batch_size=64,
        callbacks=callbacks,
        verbose=1
    )
    
    # Evaluate
    train_pred = (model.predict(X_train) > 0.5).astype(int).flatten()
    test_pred = (model.predict(X_test) > 0.5).astype(int).flatten()
    
    train_acc = accuracy_score(y_train, train_pred)
    test_acc = accuracy_score(y_test, test_pred)
    
    print(f"\n📊 Final Results:")
    print(f"📊 Train Accuracy: {train_acc:.4f}")
    print(f"📊 Test Accuracy: {test_acc:.4f}")
    
    print("\n📋 Test Classification Report:")
    print(classification_report(y_test, test_pred))
    
    # Save model
    model.save('models/simple_lstm_model.h5')
    print("💾 Model saved to models/simple_lstm_model.h5")
    
    # Check if model is learning
    if test_acc > 0.52:
        print("✅ LSTM is learning! Accuracy > 52%")
        return True, model, scaler
    else:
        print("❌ LSTM is not learning well. Accuracy <= 52%")
        return False, model, scaler

if __name__ == "__main__":
    try:
        success, model, scaler = simple_lstm_train()
        if success:
            print("\n🎉 Simple LSTM training successful!")
            print("💡 Ready for live trading implementation.")
        else:
            print("\n⚠️ LSTM needs further tuning.")
    except Exception as e:
        print(f"❌ Error during LSTM training: {e}")
        import traceback
        traceback.print_exc()
