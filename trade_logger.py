#!/usr/bin/env python3
"""
Comprehensive Trade Logger for Regime-Based Trading System
Logs all trade executions and results with detailed analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
from typing import Optional, Dict, List
import MetaTrader5 as mt5

class TradeLogger:
    """
    Comprehensive trade logging system that tracks:
    - Trade execution details (regime, trend, ML decision, final decision)
    - Trade results (profit/loss, maximum favorable excursion)
    - Performance metrics
    """
    
    def __init__(self, csv_file: str = "trade_log.csv"):
        self.csv_file = csv_file
        self.logger = logging.getLogger(__name__)
        self.current_trade_id = None
        
        # Initialize CSV file with headers if it doesn't exist
        self.initialize_csv()
    
    def initialize_csv(self):
        """Initialize CSV file with proper headers"""
        if not os.path.exists(self.csv_file):
            headers = [
                'trade_id', 'timestamp', 'symbol', 'direction', 'volume', 'entry_price', 'stop_loss',
                'regime', 'regime_confidence', 'trend_direction', 'ml_signal', 'ml_confidence', 
                'final_decision', 'logic', 'atr_value',
                # Results (filled when trade closes)
                'exit_timestamp', 'exit_price', 'exit_reason', 'profit_loss_pips', 'profit_loss_usd',
                'max_favorable_pips', 'max_adverse_pips', 'duration_minutes', 'trailing_stops_count',
                'win_loss', 'profit_factor'
            ]
            
            df = pd.DataFrame(columns=headers)
            df.to_csv(self.csv_file, index=False)
            self.logger.info(f"✅ Initialized trade log: {self.csv_file}")
    
    def log_trade_execution(self, trade_data: Dict) -> str:
        """
        Log a new trade execution
        
        Args:
            trade_data: Dictionary containing trade execution details
            
        Returns:
            trade_id: Unique identifier for this trade
        """
        try:
            # Generate unique trade ID
            trade_id = f"T{datetime.now().strftime('%Y%m%d_%H%M%S')}_{trade_data['ticket']}"
            self.current_trade_id = trade_id
            
            # Prepare trade execution data
            execution_data = {
                'trade_id': trade_id,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'symbol': trade_data['symbol'],
                'direction': trade_data['direction'],
                'volume': trade_data['volume'],
                'entry_price': trade_data['entry_price'],
                'stop_loss': trade_data['stop_loss'],
                'regime': trade_data['regime'],
                'regime_confidence': trade_data['regime_confidence'],
                'trend_direction': trade_data['trend_direction'],
                'ml_signal': trade_data['ml_signal'],
                'ml_confidence': trade_data['ml_confidence'],
                'final_decision': trade_data['final_decision'],
                'logic': trade_data['logic'],
                'atr_value': trade_data['atr_value'],
                # Results (will be filled when trade closes)
                'exit_timestamp': None,
                'exit_price': None,
                'exit_reason': None,
                'profit_loss_pips': None,
                'profit_loss_usd': None,
                'max_favorable_pips': None,
                'max_adverse_pips': None,
                'duration_minutes': None,
                'trailing_stops_count': 0,
                'win_loss': None,
                'profit_factor': None
            }
            
            # Append to CSV
            df = pd.DataFrame([execution_data])
            df.to_csv(self.csv_file, mode='a', header=False, index=False)
            
            self.logger.info(f"📝 TRADE LOGGED: {trade_id}")
            self.logger.info(f"   Direction: {trade_data['direction']} | Volume: {trade_data['volume']}")
            self.logger.info(f"   Entry: {trade_data['entry_price']} | SL: {trade_data['stop_loss']}")
            self.logger.info(f"   Regime: {trade_data['regime']} ({trade_data['regime_confidence']:.2f})")
            self.logger.info(f"   Logic: {trade_data['logic']}")
            
            return trade_id
            
        except Exception as e:
            self.logger.error(f"❌ Error logging trade execution: {e}")
            return None
    
    def update_trailing_stop(self, trade_id: str):
        """Update trailing stop count for a trade"""
        try:
            df = pd.read_csv(self.csv_file)
            mask = df['trade_id'] == trade_id
            if mask.any():
                df.loc[mask, 'trailing_stops_count'] += 1
                df.to_csv(self.csv_file, index=False)
                self.logger.info(f"📝 Updated trailing stop count for {trade_id}")
        except Exception as e:
            self.logger.error(f"❌ Error updating trailing stop: {e}")
    
    def log_trade_result(self, trade_id: str, result_data: Dict):
        """
        Log trade result when position closes
        
        Args:
            trade_id: Trade identifier
            result_data: Dictionary containing trade result details
        """
        try:
            # Read current CSV
            df = pd.read_csv(self.csv_file)
            
            # Find the trade to update
            mask = df['trade_id'] == trade_id
            if not mask.any():
                self.logger.error(f"❌ Trade {trade_id} not found in log")
                return
            
            # Calculate trade metrics
            entry_price = df.loc[mask, 'entry_price'].iloc[0]
            direction = df.loc[mask, 'direction'].iloc[0]
            volume = df.loc[mask, 'volume'].iloc[0]
            entry_time = pd.to_datetime(df.loc[mask, 'timestamp'].iloc[0])
            exit_time = datetime.now()
            
            # Calculate profit/loss in pips and USD
            if direction == 'BUY':
                profit_loss_pips = (result_data['exit_price'] - entry_price) * 10  # For XAUUSD, 1 pip = 0.1
            else:  # SELL
                profit_loss_pips = (entry_price - result_data['exit_price']) * 10
            
            profit_loss_usd = profit_loss_pips * volume * 10  # XAUUSD: 1 pip = $10 per lot
            
            # Determine win/loss
            win_loss = 'WIN' if profit_loss_pips > 0 else 'LOSS' if profit_loss_pips < 0 else 'BREAKEVEN'
            
            # Calculate duration
            duration_minutes = (exit_time - entry_time).total_seconds() / 60
            
            # Update the trade record
            df.loc[mask, 'exit_timestamp'] = exit_time.strftime('%Y-%m-%d %H:%M:%S')
            df.loc[mask, 'exit_price'] = result_data['exit_price']
            df.loc[mask, 'exit_reason'] = result_data['exit_reason']
            df.loc[mask, 'profit_loss_pips'] = round(profit_loss_pips, 1)
            df.loc[mask, 'profit_loss_usd'] = round(profit_loss_usd, 2)
            df.loc[mask, 'max_favorable_pips'] = result_data.get('max_favorable_pips', 0)
            df.loc[mask, 'max_adverse_pips'] = result_data.get('max_adverse_pips', 0)
            df.loc[mask, 'duration_minutes'] = round(duration_minutes, 1)
            df.loc[mask, 'win_loss'] = win_loss
            
            # Calculate profit factor (for this trade)
            if profit_loss_pips > 0:
                df.loc[mask, 'profit_factor'] = abs(profit_loss_pips) / max(abs(result_data.get('max_adverse_pips', 1)), 1)
            else:
                df.loc[mask, 'profit_factor'] = 0
            
            # Save updated CSV
            df.to_csv(self.csv_file, index=False)
            
            self.logger.info(f"📝 TRADE RESULT LOGGED: {trade_id}")
            self.logger.info(f"   Exit: {result_data['exit_price']} | Reason: {result_data['exit_reason']}")
            self.logger.info(f"   P&L: {profit_loss_pips:.1f} pips (${profit_loss_usd:.2f})")
            self.logger.info(f"   Duration: {duration_minutes:.1f} minutes | Result: {win_loss}")
            
        except Exception as e:
            self.logger.error(f"❌ Error logging trade result: {e}")
    
    def calculate_max_excursion(self, ticket: int, entry_price: float, direction: str, 
                               entry_time: datetime, exit_time: datetime) -> Dict:
        """
        Calculate maximum favorable and adverse excursion for a trade
        
        Args:
            ticket: MT5 ticket number
            entry_price: Entry price
            direction: BUY or SELL
            entry_time: Trade entry time
            exit_time: Trade exit time
            
        Returns:
            Dictionary with max_favorable_pips and max_adverse_pips
        """
        try:
            # Get historical data for the trade period
            if not mt5.initialize():
                return {'max_favorable_pips': 0, 'max_adverse_pips': 0}
            
            # Get M1 data for precise analysis
            rates = mt5.copy_rates_range("XAUUSD!", mt5.TIMEFRAME_M1, entry_time, exit_time)
            
            if rates is None or len(rates) == 0:
                return {'max_favorable_pips': 0, 'max_adverse_pips': 0}
            
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            
            max_favorable = 0
            max_adverse = 0
            
            for _, row in df.iterrows():
                high = row['high']
                low = row['low']
                
                if direction == 'BUY':
                    # For BUY: favorable = price going up, adverse = price going down
                    favorable = (high - entry_price) * 10  # Convert to pips
                    adverse = (entry_price - low) * 10
                else:  # SELL
                    # For SELL: favorable = price going down, adverse = price going up
                    favorable = (entry_price - low) * 10
                    adverse = (high - entry_price) * 10
                
                max_favorable = max(max_favorable, favorable)
                max_adverse = max(max_adverse, adverse)
            
            return {
                'max_favorable_pips': round(max_favorable, 1),
                'max_adverse_pips': round(max_adverse, 1)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error calculating max excursion: {e}")
            return {'max_favorable_pips': 0, 'max_adverse_pips': 0}
    
    def get_trade_summary(self) -> Dict:
        """Get summary statistics of all trades"""
        try:
            if not os.path.exists(self.csv_file):
                return {}
            
            df = pd.read_csv(self.csv_file)
            completed_trades = df[df['exit_timestamp'].notna()]
            
            if len(completed_trades) == 0:
                return {'total_trades': 0}
            
            wins = completed_trades[completed_trades['win_loss'] == 'WIN']
            losses = completed_trades[completed_trades['win_loss'] == 'LOSS']
            
            summary = {
                'total_trades': len(completed_trades),
                'wins': len(wins),
                'losses': len(losses),
                'win_rate': len(wins) / len(completed_trades) * 100 if len(completed_trades) > 0 else 0,
                'total_pips': completed_trades['profit_loss_pips'].sum(),
                'total_usd': completed_trades['profit_loss_usd'].sum(),
                'avg_win_pips': wins['profit_loss_pips'].mean() if len(wins) > 0 else 0,
                'avg_loss_pips': losses['profit_loss_pips'].mean() if len(losses) > 0 else 0,
                'avg_duration': completed_trades['duration_minutes'].mean(),
                'max_favorable_avg': completed_trades['max_favorable_pips'].mean(),
                'max_adverse_avg': completed_trades['max_adverse_pips'].mean()
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"❌ Error getting trade summary: {e}")
            return {}
    
    def print_summary(self):
        """Print trade summary to console"""
        summary = self.get_trade_summary()
        
        if summary.get('total_trades', 0) == 0:
            print("📊 No completed trades yet")
            return
        
        print("\n📊 TRADE SUMMARY")
        print("=" * 50)
        print(f"Total Trades: {summary['total_trades']}")
        print(f"Wins: {summary['wins']} | Losses: {summary['losses']}")
        print(f"Win Rate: {summary['win_rate']:.1f}%")
        print(f"Total P&L: {summary['total_pips']:.1f} pips (${summary['total_usd']:.2f})")
        print(f"Avg Win: {summary['avg_win_pips']:.1f} pips")
        print(f"Avg Loss: {summary['avg_loss_pips']:.1f} pips")
        print(f"Avg Duration: {summary['avg_duration']:.1f} minutes")
        print(f"Avg Max Favorable: {summary['max_favorable_avg']:.1f} pips")
        print(f"Avg Max Adverse: {summary['max_adverse_avg']:.1f} pips")
        print("=" * 50)
