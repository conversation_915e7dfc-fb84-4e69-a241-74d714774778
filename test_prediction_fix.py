#!/usr/bin/env python3
"""
Test script to verify the get_live_prediction fix
"""

import sys
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_prediction_fix():
    """Test that get_live_prediction works without errors"""
    print("🧪 TESTING PREDICTION FIX...")
    
    try:
        # Initialize trader
        trader = FixedLiveTrader()
        
        # Connect to MT5
        if not trader.mt5_manager.connect():
            print("❌ Cannot connect to MT5")
            return
        
        print("✅ Connected to MT5")
        
        # Load model (should work with disabled model)
        if not trader.load_model():
            print("❌ Model loading failed")
            return
        
        print("✅ Model loading successful (disabled)")
        
        # Test get_live_prediction
        print("🎯 Testing get_live_prediction...")
        
        result = trader.get_live_prediction()
        
        if result is None:
            print("❌ get_live_prediction returned None")
            return
        
        if len(result) != 8:
            print(f"❌ get_live_prediction returned {len(result)} items, expected 8")
            return
        
        signal, confidence, details, atr_value, regime, logic, regime_details, candle_strength = result
        
        print("✅ get_live_prediction successful!")
        print(f"   Signal: {signal}")
        print(f"   Confidence: {confidence}")
        print(f"   Details: {details}")
        print(f"   ATR: {atr_value}")
        print(f"   Regime: {regime}")
        print(f"   Logic: {logic}")
        print(f"   Regime Details: {type(regime_details)}")
        print(f"   Candle Strength: {type(candle_strength)}")
        
        # Test multiple calls to ensure stability
        print("\n🔄 Testing multiple calls...")
        for i in range(3):
            result = trader.get_live_prediction()
            if result is None:
                print(f"❌ Call {i+1} failed")
                return
            signal, confidence, details, atr_value, regime, logic, regime_details, candle_strength = result
            print(f"   Call {i+1}: {signal} | {confidence:.3f} | {regime}")
        
        print("\n✅ ALL TESTS PASSED! Prediction fix is working correctly.")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            trader.mt5_manager.disconnect()
        except:
            pass

if __name__ == "__main__":
    test_prediction_fix()
