"""
Live Trading System Integration
Main system that coordinates all components for live trading
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional
import logging
import time
import threading
from datetime import datetime, timedelta
import json
import signal
import sys

from config.config import *
from src.mt5_integration import MT5Manager
from src.live_data_processor import LiveDataProcessor
from src.trading_logic import TradingEngine, SignalType
from src.risk_management import RiskManager

# Set up logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT,
    handlers=[
        logging.FileHandler(LOGS_DIR / "live_trading.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LiveTradingSystem:
    """
    Main live trading system that coordinates all components
    """
    
    def __init__(self):
        self.mt5_manager = MT5Manager()
        self.data_processor = LiveDataProcessor()
        self.trading_engine = None
        self.risk_manager = RiskManager()
        
        self.is_running = False
        self.system_status = {
            'start_time': None,
            'last_update': None,
            'total_predictions': 0,
            'total_trades': 0,
            'system_health': 'UNKNOWN'
        }
        
        # Performance tracking
        self.performance_log = []
        self.error_count = 0
        self.last_error_time = None
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def initialize(self, model_path: str, preprocessor_path: str,
                  mt5_login: Optional[int] = None, mt5_password: Optional[str] = None,
                  mt5_server: Optional[str] = None) -> bool:
        """
        Initialize the live trading system
        
        Args:
            model_path: Path to trained LSTM model
            preprocessor_path: Path to fitted preprocessor
            mt5_login: MT5 login
            mt5_password: MT5 password
            mt5_server: MT5 server
            
        Returns:
            bool: True if initialization successful
        """
        try:
            logger.info("Initializing Live Trading System...")
            
            # Initialize data processor (includes MT5 connection and model loading)
            if not self.data_processor.initialize(model_path, preprocessor_path, 
                                                 mt5_login, mt5_password, mt5_server):
                logger.error("Failed to initialize data processor")
                return False
            
            # Initialize trading engine
            self.trading_engine = TradingEngine(self.data_processor.mt5_manager)
            
            # Validate system components
            if not self._validate_system_components():
                logger.error("System component validation failed")
                return False
            
            # Initialize performance tracking
            self._initialize_performance_tracking()
            
            logger.info("Live Trading System initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing live trading system: {e}")
            return False
    
    def start_trading(self) -> bool:
        """
        Start live trading
        
        Returns:
            bool: True if started successfully
        """
        try:
            if self.is_running:
                logger.warning("Trading system already running")
                return True
            
            logger.info("Starting live trading system...")
            
            # Final pre-trading checks
            if not self._pre_trading_checks():
                logger.error("Pre-trading checks failed")
                return False
            
            # Start data processing
            self.data_processor.start_live_processing()
            
            # Start main trading loop
            self.is_running = True
            self.system_status['start_time'] = datetime.now()
            
            # Start trading thread
            trading_thread = threading.Thread(target=self._main_trading_loop, daemon=True)
            trading_thread.start()
            
            # Start monitoring thread
            monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            monitoring_thread.start()
            
            logger.info("Live trading system started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting live trading system: {e}")
            self.is_running = False
            return False
    
    def stop_trading(self):
        """Stop live trading system"""
        try:
            logger.info("Stopping live trading system...")
            
            self.is_running = False
            
            # Stop data processing
            self.data_processor.stop_live_processing()
            
            # Close any open positions (optional - depends on strategy)
            if self.trading_engine and self.trading_engine.current_position:
                logger.info("Closing open position before shutdown...")
                self.trading_engine._close_current_position()
            
            # Save final performance report
            self._save_performance_report()
            
            # Cleanup
            self.data_processor.cleanup()
            
            logger.info("Live trading system stopped")
            
        except Exception as e:
            logger.error(f"Error stopping live trading system: {e}")
    
    def _main_trading_loop(self):
        """Main trading loop"""
        try:
            logger.info("Main trading loop started")
            
            while self.is_running:
                try:
                    # Check system health
                    if not self._check_system_health():
                        logger.warning("System health check failed, pausing trading")
                        time.sleep(60)  # Wait 1 minute before retrying
                        continue
                    
                    # Get latest prediction
                    prediction = self.data_processor.get_latest_prediction()
                    
                    if prediction:
                        self.system_status['total_predictions'] += 1
                        self.system_status['last_update'] = datetime.now()
                        
                        logger.info(f"New prediction received: {prediction}")
                        
                        # Get current market data
                        market_data = self.data_processor.get_current_market_data()
                        
                        if market_data:
                            # Process prediction through trading engine
                            decision = self.trading_engine.process_prediction(prediction, market_data)
                            
                            if decision:
                                # Validate with risk manager
                                account_info = self.mt5_manager.get_account_info()
                                
                                if account_info:
                                    is_valid, reason = self.risk_manager.validate_trade_risk(
                                        account_info, decision.position_size,
                                        decision.entry_price, decision.stop_loss
                                    )
                                    
                                    if is_valid:
                                        # Execute trade
                                        if self.trading_engine.execute_decision(decision):
                                            self.system_status['total_trades'] += 1
                                            logger.info(f"Trade executed successfully: {decision.signal.name}")
                                            
                                            # Update risk manager
                                            trade_info = {
                                                'type': decision.signal.name,
                                                'entry_price': decision.entry_price,
                                                'position_size': decision.position_size,
                                                'confidence': decision.confidence
                                            }
                                            self.risk_manager.update_trade_history(trade_info)
                                        else:
                                            logger.error("Failed to execute trade")
                                    else:
                                        logger.info(f"Trade rejected by risk manager: {reason}")
                                else:
                                    logger.error("Failed to get account info")
                            else:
                                logger.debug("No trading decision generated")
                        else:
                            logger.warning("No market data available")
                    
                    # Check position management
                    if self.trading_engine.current_position:
                        market_data = self.data_processor.get_current_market_data()
                        if market_data:
                            self.trading_engine.check_position_management(market_data)
                    
                    # Update performance tracking
                    self._update_performance_tracking()
                    
                    # Sleep before next iteration
                    time.sleep(10)  # Check every 10 seconds
                    
                except Exception as e:
                    logger.error(f"Error in main trading loop: {e}")
                    self.error_count += 1
                    self.last_error_time = datetime.now()
                    time.sleep(30)  # Wait 30 seconds on error
            
            logger.info("Main trading loop stopped")
            
        except Exception as e:
            logger.error(f"Fatal error in main trading loop: {e}")
            self.is_running = False
    
    def _monitoring_loop(self):
        """System monitoring loop"""
        try:
            logger.info("Monitoring loop started")
            
            while self.is_running:
                try:
                    # Check emergency stop conditions
                    account_info = self.mt5_manager.get_account_info()
                    if account_info:
                        if self.risk_manager.check_emergency_stop(account_info):
                            logger.critical("Emergency stop triggered!")
                            self.stop_trading()
                            break
                    
                    # Log system status periodically
                    if datetime.now().minute % 15 == 0:  # Every 15 minutes
                        self._log_system_status()
                    
                    # Save performance data periodically
                    if datetime.now().minute % 30 == 0:  # Every 30 minutes
                        self._save_performance_data()
                    
                    time.sleep(60)  # Check every minute
                    
                except Exception as e:
                    logger.error(f"Error in monitoring loop: {e}")
                    time.sleep(60)
            
            logger.info("Monitoring loop stopped")
            
        except Exception as e:
            logger.error(f"Fatal error in monitoring loop: {e}")
    
    def _validate_system_components(self) -> bool:
        """Validate all system components are working"""
        try:
            # Check MT5 connection
            if not self.data_processor.mt5_manager.connected:
                logger.error("MT5 not connected")
                return False
            
            # Check model is loaded
            if not self.data_processor.model or not self.data_processor.model.model:
                logger.error("LSTM model not loaded")
                return False
            
            # Check account info
            account_info = self.data_processor.mt5_manager.get_account_info()
            if not account_info:
                logger.error("Cannot get account info")
                return False
            
            # Check minimum balance
            if account_info.get('balance', 0) < MIN_ACCOUNT_BALANCE:
                logger.error(f"Account balance below minimum: {account_info.get('balance', 0)}")
                return False
            
            logger.info("System component validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Error validating system components: {e}")
            return False
    
    def _pre_trading_checks(self) -> bool:
        """Perform pre-trading checks"""
        try:
            # Check market is open
            if not self.data_processor.is_market_open():
                logger.warning("Market is closed")
                return False
            
            # Check system status
            system_status = self.data_processor.get_system_status()
            if not system_status.get('mt5_connected', False):
                logger.error("MT5 not connected")
                return False
            
            # Check data buffer
            if system_status.get('data_buffer_size', 0) < SEQUENCE_LENGTH:
                logger.error("Insufficient data buffer")
                return False
            
            logger.info("Pre-trading checks passed")
            return True
            
        except Exception as e:
            logger.error(f"Error in pre-trading checks: {e}")
            return False
    
    def _check_system_health(self) -> bool:
        """Check overall system health"""
        try:
            # Check error rate
            if self.error_count > 10 and self.last_error_time:
                time_since_error = (datetime.now() - self.last_error_time).total_seconds()
                if time_since_error < 300:  # 5 minutes
                    logger.warning("High error rate detected")
                    return False
            
            # Check data processor status
            system_status = self.data_processor.get_system_status()
            if not system_status.get('is_running', False):
                logger.warning("Data processor not running")
                return False
            
            # Check last update time
            last_update = system_status.get('last_update')
            if last_update:
                time_since_update = (datetime.now() - last_update).total_seconds()
                if time_since_update > 600:  # 10 minutes
                    logger.warning("No data updates for 10 minutes")
                    return False
            
            self.system_status['system_health'] = 'HEALTHY'
            return True
            
        except Exception as e:
            logger.error(f"Error checking system health: {e}")
            self.system_status['system_health'] = 'UNHEALTHY'
            return False
    
    def _initialize_performance_tracking(self):
        """Initialize performance tracking"""
        try:
            self.performance_log = []
            self.error_count = 0
            logger.info("Performance tracking initialized")
            
        except Exception as e:
            logger.error(f"Error initializing performance tracking: {e}")
    
    def _update_performance_tracking(self):
        """Update performance tracking data"""
        try:
            account_info = self.mt5_manager.get_account_info()
            if account_info:
                performance_record = {
                    'timestamp': datetime.now(),
                    'balance': account_info.get('balance', 0),
                    'equity': account_info.get('equity', 0),
                    'margin': account_info.get('margin', 0),
                    'free_margin': account_info.get('free_margin', 0),
                    'total_predictions': self.system_status['total_predictions'],
                    'total_trades': self.system_status['total_trades'],
                    'error_count': self.error_count
                }
                
                self.performance_log.append(performance_record)
                
                # Keep only last 1000 records
                if len(self.performance_log) > 1000:
                    self.performance_log = self.performance_log[-1000:]
            
        except Exception as e:
            logger.error(f"Error updating performance tracking: {e}")
    
    def _log_system_status(self):
        """Log current system status"""
        try:
            account_info = self.mt5_manager.get_account_info()
            system_status = self.data_processor.get_system_status()
            
            logger.info("=== SYSTEM STATUS ===")
            logger.info(f"Running time: {datetime.now() - self.system_status['start_time']}")
            logger.info(f"Total predictions: {self.system_status['total_predictions']}")
            logger.info(f"Total trades: {self.system_status['total_trades']}")
            logger.info(f"System health: {self.system_status['system_health']}")
            
            if account_info:
                logger.info(f"Account balance: {account_info.get('balance', 0):.2f}")
                logger.info(f"Account equity: {account_info.get('equity', 0):.2f}")
                logger.info(f"Free margin: {account_info.get('free_margin', 0):.2f}")
            
            logger.info(f"Data buffer size: {system_status.get('data_buffer_size', 0)}")
            logger.info(f"Last data update: {system_status.get('last_update', 'Never')}")
            logger.info("=====================")
            
        except Exception as e:
            logger.error(f"Error logging system status: {e}")
    
    def _save_performance_data(self):
        """Save performance data to file"""
        try:
            if self.performance_log:
                performance_file = REPORTS_DIR / f"performance_log_{datetime.now().strftime('%Y%m%d')}.json"
                
                # Convert datetime objects to strings for JSON serialization
                json_data = []
                for record in self.performance_log:
                    json_record = record.copy()
                    json_record['timestamp'] = record['timestamp'].isoformat()
                    json_data.append(json_record)
                
                with open(performance_file, 'w') as f:
                    json.dump(json_data, f, indent=2)
                
                logger.debug(f"Performance data saved to {performance_file}")
            
        except Exception as e:
            logger.error(f"Error saving performance data: {e}")
    
    def _save_performance_report(self):
        """Save final performance report"""
        try:
            if self.trading_engine:
                trading_stats = self.trading_engine.get_trading_statistics()
                
                account_info = self.mt5_manager.get_account_info()
                risk_metrics = self.risk_manager.get_risk_metrics(account_info) if account_info else None
                
                report = {
                    'session_summary': {
                        'start_time': self.system_status['start_time'].isoformat() if self.system_status['start_time'] else None,
                        'end_time': datetime.now().isoformat(),
                        'total_predictions': self.system_status['total_predictions'],
                        'total_trades': self.system_status['total_trades'],
                        'error_count': self.error_count
                    },
                    'trading_statistics': trading_stats,
                    'risk_metrics': risk_metrics.__dict__ if risk_metrics else {},
                    'final_account_info': account_info
                }
                
                report_file = REPORTS_DIR / f"trading_session_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                
                with open(report_file, 'w') as f:
                    json.dump(report, f, indent=2, default=str)
                
                logger.info(f"Final performance report saved to {report_file}")
            
        except Exception as e:
            logger.error(f"Error saving performance report: {e}")
    
    def _signal_handler(self, signum, frame):
        """Handle system signals for graceful shutdown"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop_trading()
        sys.exit(0)
    
    def get_system_status(self) -> Dict:
        """Get current system status"""
        try:
            status = self.system_status.copy()
            status['is_running'] = self.is_running
            status['error_count'] = self.error_count
            status['last_error_time'] = self.last_error_time
            
            # Add component status
            if self.data_processor:
                status['data_processor_status'] = self.data_processor.get_system_status()
            
            if self.trading_engine:
                status['current_position'] = self.trading_engine.get_current_position_info()
                status['trading_stats'] = self.trading_engine.get_trading_statistics()
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {}
