#!/usr/bin/env python3
"""
Test the MAX SCORE FIX - Verify exactly 9 points maximum
"""

def test_max_score_scenarios():
    """Test all possible maximum score scenarios"""
    print("🧪 TESTING MAX SCORE FIX - VERIFY EXACTLY 9 POINTS")
    print("=" * 60)
    
    # Test all possible BB width scenarios for max score
    bb_scenarios = [
        {'name': 'BB Squeeze (0-20th)', 'bb_pct': 0.15, 'expected_bb_points': 2, 'goes_to': 'ranging'},
        {'name': 'B<PERSON>rrow (20-35th)', 'bb_pct': 0.30, 'expected_bb_points': 1, 'goes_to': 'ranging'},
        {'name': 'B<PERSON> Neutral (35-40th)', 'bb_pct': 0.38, 'expected_bb_points': 0, 'goes_to': 'none'},
        {'name': 'BB Medium (40-70th)', 'bb_pct': 0.55, 'expected_bb_points': 1, 'goes_to': 'trending'},
        {'name': 'B<PERSON> <PERSON> (70-100th)', 'bb_pct': 0.80, 'expected_bb_points': 2, 'goes_to': 'trending'},
    ]
    
    for scenario in bb_scenarios:
        print(f"\n🔍 {scenario['name']}:")
        
        # Simulate MAXIMUM trending scenario
        trending_score = 0
        ranging_score = 0
        
        # ATR: Maximum trending
        trending_score += 3  # ATR > 70th percentile
        
        # Slope: Maximum trending
        trending_score += 3  # Slope > 0.0015
        
        # BB Width: Test this scenario
        bb_width_pct = scenario['bb_pct']
        bb_squeeze = bb_width_pct < 0.20
        if bb_squeeze:
            ranging_score += 2
        elif bb_width_pct > 0.70:
            trending_score += 2
        elif bb_width_pct > 0.40:
            trending_score += 1  # FIXED: Full point (was 0.5)
        elif bb_width_pct < 0.35:  # bb_width_threshold
            ranging_score += 1
        
        # Volatility: Maximum trending
        trending_score += 1  # Vol > 60th percentile
        
        total_score = trending_score + ranging_score
        
        print(f"   BB Percentile: {bb_width_pct:.2f}")
        print(f"   BB Points: {scenario['expected_bb_points']} → {scenario['goes_to']}")
        print(f"   Trending Score: {trending_score}")
        print(f"   Ranging Score: {ranging_score}")
        print(f"   Total Score: {total_score}")
        
        # Check if max is exactly 9
        if scenario['goes_to'] == 'trending':
            expected_max = 9  # 3+3+BB_points+1
            print(f"   Expected Max: {expected_max}")
            print(f"   ✅ {'PASS' if total_score <= 9 else 'FAIL - EXCEEDS 9!'}")
        elif scenario['goes_to'] == 'ranging':
            # This would be a mixed scenario, not pure max
            print(f"   Mixed scenario (not pure max)")
        else:
            expected_max = 7  # 3+3+0+1
            print(f"   Expected Max: {expected_max}")
            print(f"   ✅ {'PASS' if total_score == expected_max else 'FAIL'}")

def test_pure_max_scenarios():
    """Test pure maximum scenarios for both trending and ranging"""
    print("\n\n🧪 TESTING PURE MAXIMUM SCENARIOS")
    print("=" * 60)
    
    scenarios = [
        {
            'name': 'PURE MAX TRENDING',
            'atr_pct': 0.85,      # +3 trending
            'slope_abs': 0.0020,  # +3 trending
            'bb_width_pct': 0.80, # +2 trending (wide bands)
            'vol_pct': 0.70,      # +1 trending
            'expected_trending': 9,
            'expected_ranging': 0
        },
        {
            'name': 'PURE MAX RANGING',
            'atr_pct': 0.15,      # +3 ranging
            'slope_abs': 0.0003,  # +3 ranging
            'bb_width_pct': 0.15, # +2 ranging (squeeze)
            'vol_pct': 0.30,      # +1 ranging
            'expected_trending': 0,
            'expected_ranging': 9
        },
        {
            'name': 'MAX TRENDING (Medium BB)',
            'atr_pct': 0.85,      # +3 trending
            'slope_abs': 0.0020,  # +3 trending
            'bb_width_pct': 0.55, # +1 trending (medium bands)
            'vol_pct': 0.70,      # +1 trending
            'expected_trending': 8,
            'expected_ranging': 0
        }
    ]
    
    for scenario in scenarios:
        print(f"\n🔍 {scenario['name']}:")
        
        trending_score = 0
        ranging_score = 0
        
        # ATR Analysis
        atr_pct = scenario['atr_pct']
        if atr_pct > 0.70:
            trending_score += 3
        elif atr_pct < 0.30:
            ranging_score += 3
        else:
            if atr_pct > 0.5:
                trending_score += 1
            else:
                ranging_score += 1
        
        # EMA Slope Analysis
        slope_abs = scenario['slope_abs']
        if slope_abs > 0.0015:
            trending_score += 3
        elif slope_abs < 0.0005:
            ranging_score += 3
        else:
            mid_threshold = (0.0015 + 0.0005) / 2
            if slope_abs > mid_threshold:
                trending_score += 1
            else:
                ranging_score += 1
        
        # FIXED: BB Width Analysis
        bb_width_pct = scenario['bb_width_pct']
        bb_squeeze = bb_width_pct < 0.20
        if bb_squeeze:
            ranging_score += 2
        elif bb_width_pct > 0.70:
            trending_score += 2
        elif bb_width_pct > 0.40:
            trending_score += 1  # FIXED: Full point
        elif bb_width_pct < 0.35:
            ranging_score += 1
        
        # Volatility Analysis
        vol_pct = scenario['vol_pct']
        if vol_pct > 0.60:
            trending_score += 1
        else:
            ranging_score += 1
        
        total_score = trending_score + ranging_score
        max_score = max(trending_score, ranging_score)
        confidence = max_score / 9  # Max possible is exactly 9
        
        print(f"   Trending Score: {trending_score} (Expected: {scenario['expected_trending']})")
        print(f"   Ranging Score: {ranging_score} (Expected: {scenario['expected_ranging']})")
        print(f"   Total Score: {total_score}")
        print(f"   Max Score: {max_score}")
        print(f"   Confidence: {confidence:.3f}")
        
        # Verify expectations
        trending_match = trending_score == scenario['expected_trending']
        ranging_match = ranging_score == scenario['expected_ranging']
        max_is_9_or_less = max_score <= 9
        
        print(f"   ✅ Trending: {'PASS' if trending_match else 'FAIL'}")
        print(f"   ✅ Ranging: {'PASS' if ranging_match else 'FAIL'}")
        print(f"   ✅ Max ≤ 9: {'PASS' if max_is_9_or_less else 'FAIL - EXCEEDS 9!'}")

def show_final_bb_distribution():
    """Show the final BB width distribution"""
    print("\n\n📊 FINAL BB WIDTH DISTRIBUTION")
    print("=" * 60)
    
    bb_ranges = [
        {'range': '0-20th percentile', 'points': '+2 ranging', 'description': 'BB Squeeze (tight bands)'},
        {'range': '20-35th percentile', 'points': '+1 ranging', 'description': 'Narrow bands'},
        {'range': '35-40th percentile', 'points': '0 points', 'description': 'Neutral zone (5% of data)'},
        {'range': '40-70th percentile', 'points': '+1 trending', 'description': 'Medium bands (FIXED: full point)'},
        {'range': '70-100th percentile', 'points': '+2 trending', 'description': 'Wide bands'},
    ]
    
    for bb_range in bb_ranges:
        print(f"{bb_range['range']:20} → {bb_range['points']:12} | {bb_range['description']}")
    
    print(f"\n✅ CONFIRMED:")
    print(f"   • Maximum possible score: EXACTLY 9 points")
    print(f"   • No fractional points (0.5) anywhere")
    print(f"   • Consistent confidence calculation")
    print(f"   • Only 5% neutral zone (35-40th percentile)")

def main():
    """Main function"""
    test_max_score_scenarios()
    test_pure_max_scenarios()
    show_final_bb_distribution()

if __name__ == "__main__":
    main()
