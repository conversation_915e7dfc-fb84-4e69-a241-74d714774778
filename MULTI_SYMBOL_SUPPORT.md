# Multi-Symbol Trading System Support

## Overview
The trading system has been successfully modified to support multiple trading pairs while maintaining all existing configurations unchanged. The system now supports **XAUUSD!**, **EURUSD!**, and **BTCUSD** with proper symbol-specific position sizing.

## ✅ Changes Made

### 1. Symbol-Specific Position Sizing
- **XAUUSD!**: 1 lot = 100 ounces (unchanged)
- **EURUSD!**: 1 lot = 100,000 units (standard forex)
- **BTCUSD**: 1 lot = 1 BTC (crypto)

### 2. Modified Files
- `fixed_live_trader.py`: Added multi-symbol support with symbol parameter
- `src/mt5_integration.py`: Updated to use configurable symbol instead of hardcoded XAUUSD
- Created test scripts and utilities

### 3. Key Features
- **Backward Compatible**: XAUUSD still works as default
- **No Config Changes**: All existing configurations preserved
- **Proper Data Feeding**: Each symbol gets its own market data
- **Correct Position Sizing**: Risk management adapted for each symbol's contract size

## 🚀 How to Use

### Method 1: Command Line Arguments
```bash
# Run with BTCUSD
python fixed_live_trader.py BTCUSD

# Run with EURUSD
python fixed_live_trader.py EURUSD

# Run with XAUUSD (default)
python fixed_live_trader.py XAUUSD
python fixed_live_trader.py
```

### Method 2: Using the Runner Script
```bash
# Easy-to-use runner with help
python run_trading_system.py BTCUSD
python run_trading_system.py EURUSD
python run_trading_system.py --help
```

### Method 3: Direct Python Import
```python
from fixed_live_trader import FixedLiveTrader

# Create trader for specific symbol
trader = FixedLiveTrader(symbol="BTCUSD")
trader.start_trading()
```

## 🧪 Testing

### Test All Symbols (Position Sizing)
```bash
python test_all_symbols.py
```

### Test BTCUSD Specifically
```bash
python test_btcusd.py
```

## ✅ Verification Results

### Position Sizing Test Results
- **XAUUSD!**: ✅ Correct (Contract size: 100)
- **EURUSD!**: ✅ Correct (Contract size: 100,000)  
- **BTCUSD**: ✅ Correct (Contract size: 1)

### BTCUSD Live Test Results
- ✅ System connects to MT5 with BTCUSD symbol
- ✅ Displays "Symbol: BTCUSD, Spread: 31800" (not XAUUSD)
- ✅ Processes Bitcoin price data (~122,519 range)
- ✅ Position sizing calculates correctly for BTC contract size
- ✅ No contamination from old XAUUSD data

## 📊 Position Sizing Examples

### Example: $10,000 Account, 4% Risk, 100 ATR

| Symbol | Contract Size | Risk per Lot | Calculated Lot Size |
|--------|---------------|--------------|-------------------|
| XAUUSD! | 100 ounces | $10,000 | 0.04 lots |
| EURUSD! | 100,000 units | $10,000,000 | 0.01 lots |
| BTCUSD | 1 BTC | $100 | 4.0 lots |

## 🔧 Technical Details

### Symbol Configuration
```python
SYMBOL_SPECS = {
    "XAUUSD!": {"contract_size": 100, "name": "Gold"},
    "EURUSD!": {"contract_size": 100000, "name": "Euro"},
    "BTCUSD": {"contract_size": 1, "name": "Bitcoin"}
}
```

### Position Size Formula
```python
risk_amount = balance * (risk_percent / 100)  # 4% of account
stop_distance = atr_value * 1.0               # 1 ATR stop loss
risk_per_lot = stop_distance * contract_size  # Symbol-specific
lot_size = risk_amount / risk_per_lot         # Final calculation
```

## 🎯 Key Benefits

1. **No Configuration Changes**: All existing configs preserved
2. **Proper Risk Management**: Each symbol uses correct contract size
3. **Clean Data Separation**: No cross-contamination between symbols
4. **Easy Symbol Switching**: Simple command-line interface
5. **Backward Compatible**: Existing XAUUSD usage unchanged

## ⚠️ Important Notes

- **MT5 Symbol Names**: Use exact MT5 symbol names (XAUUSD!, EURUSD!, BTCUSD)
- **Market Hours**: Ensure the symbol is actively trading when running
- **Spread Costs**: Different symbols have different spreads (BTCUSD: ~318 points)
- **Risk Management**: 4% risk per trade applies to all symbols

## 🚀 Ready to Trade

The system is now ready to trade BTCUSD and EURUSD in addition to XAUUSD. All position sizing is correctly calculated for each symbol's contract specifications, and the system properly feeds the correct market data for each trading pair.

**Test Command for BTCUSD:**
```bash
python test_btcusd.py
```

**Live Trading Command for BTCUSD:**
```bash
python fixed_live_trader.py BTCUSD
```
