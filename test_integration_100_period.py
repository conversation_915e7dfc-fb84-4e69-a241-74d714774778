#!/usr/bin/env python3
"""
Integration Test for 100-Period Regime Regression Channel

This test verifies that the 100-period regime regression channel works correctly
in the full trading system context, including:
1. Feature engineering pipeline
2. Regime detection integration
3. Logging and analysis output
4. Scoring system balance
"""

import pandas as pd
import numpy as np
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader

def create_realistic_market_data(periods=200):
    """Create realistic market data with mixed trend phases"""
    np.random.seed(42)
    
    base_price = 2000.0
    prices = [base_price]
    
    # Create different market phases
    phase_length = periods // 4
    
    for i in range(periods - 1):
        phase = i // phase_length
        
        if phase == 0:  # Ranging phase
            change = np.random.normal(0, 0.3) - (prices[-1] - base_price) * 0.02
        elif phase == 1:  # Uptrending phase
            change = np.random.normal(1.5, 0.8)
        elif phase == 2:  # Ranging phase again
            change = np.random.normal(0, 0.4) - (prices[-1] - (base_price + 75)) * 0.015
        else:  # Downtrending phase
            change = np.random.normal(-1.2, 0.7)
        
        prices.append(max(prices[-1] + change, 100))  # Prevent negative prices
    
    # Create OHLC data
    df = pd.DataFrame({
        'close': prices,
        'high': [p + abs(np.random.normal(0, 0.5)) for p in prices],
        'low': [p - abs(np.random.normal(0, 0.5)) for p in prices],
        'open': [p + np.random.normal(0, 0.3) for p in prices],
        'volume': [1000 + np.random.randint(-100, 100) for _ in prices]
    })
    
    # Ensure OHLC consistency
    for i in range(len(df)):
        df.loc[i, 'high'] = max(df.loc[i, 'high'], df.loc[i, 'close'], df.loc[i, 'open'])
        df.loc[i, 'low'] = min(df.loc[i, 'low'], df.loc[i, 'close'], df.loc[i, 'open'])
    
    return df

def test_full_integration():
    """Test the full integration of 100-period regime regression"""
    print("🧪 INTEGRATION TEST: 100-Period Regime Regression in Full Trading System")
    print("=" * 80)
    
    # Create realistic market data
    df = create_realistic_market_data(200)
    
    # Initialize the full trading system
    trader = FixedLiveTrader(symbol="XAUUSD!")
    
    # Process the data through the full pipeline
    print("📊 Processing market data through full trading pipeline...")
    
    # Create technical indicators
    df_with_indicators = trader.feature_engineer.create_technical_indicators(df)
    print(f"✅ Technical indicators created: {len(df_with_indicators.columns)} columns")
    
    # Calculate regime indicators (includes 100-period regression)
    df_with_regime = trader.regime_detector.calculate_regime_indicators(df_with_indicators)
    print(f"✅ Regime indicators calculated: {len(df_with_regime.columns)} columns")
    
    # Check that 100-period regression columns exist
    regime_columns = [
        'regression_line_regime',
        'regression_upper_regime', 
        'regression_lower_regime',
        'regression_position_regime',
        'regression_slope_regime',
        'regression_slope_regime_abs',
        'regression_trend_regime'
    ]
    
    missing_columns = [col for col in regime_columns if col not in df_with_regime.columns]
    if missing_columns:
        print(f"❌ FAILED: Missing 100-period regression columns: {missing_columns}")
        return False
    
    print(f"✅ All 100-period regression columns present")
    
    # Test regime detection with 100-period data
    regime, confidence, details, trend_dir, accurate_trend = trader.regime_detector.detect_regime(df_with_regime)
    
    print(f"\n📈 REGIME DETECTION RESULTS:")
    print(f"   Regime: {regime}")
    print(f"   Confidence: {confidence:.1%}")
    print(f"   Trend Direction: {accurate_trend}")
    
    # Check that 100-period regression data is in details
    regime_regression_100 = details.get('regime_regression_100', {})
    if not regime_regression_100:
        print(f"❌ FAILED: No 100-period regression data in regime details")
        return False
    
    slope = regime_regression_100.get('slope', 0)
    strength = regime_regression_100.get('strength', 'UNKNOWN')
    trend = regime_regression_100.get('trend', 'UNKNOWN')
    
    print(f"\n🎯 100-PERIOD REGIME REGRESSION:")
    print(f"   Slope: {slope:+.6f}% per period")
    print(f"   Strength: {strength}")
    print(f"   Trend: {trend}")
    
    # Check scoring contribution
    reasoning = details.get('reasoning', [])
    regime_channel_reasoning = [r for r in reasoning if "100-Period Regime Channel" in r]
    
    if not regime_channel_reasoning:
        print(f"❌ FAILED: No 100-period regime channel reasoning found")
        return False
    
    print(f"   Contribution: {regime_channel_reasoning[0]}")
    
    # Check scoring balance
    trending_score = details.get('trending_score', 0)
    ranging_score = details.get('ranging_score', 0)
    
    print(f"\n⚖️  SCORING BALANCE:")
    print(f"   Trending Score: {trending_score:.1f}")
    print(f"   Ranging Score: {ranging_score:.1f}")
    print(f"   Score Difference: {abs(trending_score - ranging_score):.1f}")
    
    # Verify scores are reasonable
    max_score = max(trending_score, ranging_score)
    if max_score > 25:
        print(f"⚠️  WARNING: Score seems too high ({max_score:.1f})")
    elif max_score < 1:
        print(f"⚠️  WARNING: Score seems too low ({max_score:.1f})")
    else:
        print(f"✅ Scores within reasonable range")
    
    # Test different market phases
    print(f"\n📊 TESTING DIFFERENT MARKET PHASES:")
    
    # Test last 50 candles (should be downtrend phase)
    df_downtrend = df_with_regime.tail(50)
    regime_down, conf_down, details_down, _, _ = trader.regime_detector.detect_regime(df_downtrend)
    regime_100_down = details_down.get('regime_regression_100', {})
    
    print(f"   Downtrend Phase: {regime_down} ({conf_down:.1%})")
    print(f"   100-Period: {regime_100_down.get('slope', 0):+.6f}% ({regime_100_down.get('strength', 'N/A')})")
    
    # Test middle section (should be ranging/uptrend)
    df_middle = df_with_regime.iloc[75:125]
    regime_mid, conf_mid, details_mid, _, _ = trader.regime_detector.detect_regime(df_middle)
    regime_100_mid = details_mid.get('regime_regression_100', {})
    
    print(f"   Middle Phase: {regime_mid} ({conf_mid:.1%})")
    print(f"   100-Period: {regime_100_mid.get('slope', 0):+.6f}% ({regime_100_mid.get('strength', 'N/A')})")
    
    print(f"\n🎉 INTEGRATION TEST PASSED!")
    print(f"✅ 100-period regime regression channel is fully integrated")
    print(f"✅ Scoring system is balanced and working correctly")
    print(f"✅ Different market phases are detected appropriately")
    
    return True

def main():
    """Run the integration test"""
    try:
        success = test_full_integration()
        
        if success:
            print("\n" + "=" * 80)
            print("🚀 SUCCESS: 100-Period Regime Regression Channel is ready for live trading!")
            return True
        else:
            print("\n" + "=" * 80)
            print("❌ FAILED: Integration test failed")
            return False
            
    except Exception as e:
        print(f"\n❌ INTEGRATION TEST FAILED with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
