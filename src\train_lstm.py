#!/usr/bin/env python3
"""
Train Multi-target LSTM Model
Complete training pipeline with validation and evaluation
"""

import numpy as np
import json
import os
from datetime import datetime
import sys

# Try to import matplotlib, skip plotting if not available
try:
    import matplotlib.pyplot as plt
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    print("Matplotlib not available, skipping plots")

# Add src to path
sys.path.append('.')
from src.multi_target_lstm import MultiTargetLSTM

class LSTMTrainer:
    """Complete LSTM training pipeline"""
    
    def __init__(self, data_dir='data/lstm_sequences'):
        self.data_dir = data_dir
        self.model = None
        self.metadata = None
        
    def load_data(self):
        """Load prepared sequences and metadata"""
        print("Loading training data...")
        
        # Load metadata
        with open(f'{self.data_dir}/metadata.json', 'r') as f:
            self.metadata = json.load(f)
        
        # Load sequences
        X_train = np.load(f'{self.data_dir}/X_train.npy')
        y_train = np.load(f'{self.data_dir}/y_train.npy')
        X_val = np.load(f'{self.data_dir}/X_val.npy')
        y_val = np.load(f'{self.data_dir}/y_val.npy')
        X_test = np.load(f'{self.data_dir}/X_test.npy')
        y_test = np.load(f'{self.data_dir}/y_test.npy')
        
        print(f"Data loaded:")
        print(f"  Train: X{X_train.shape}, y{y_train.shape}")
        print(f"  Val:   X{X_val.shape}, y{y_val.shape}")
        print(f"  Test:  X{X_test.shape}, y{y_test.shape}")
        
        return (X_train, y_train), (X_val, y_val), (X_test, y_test)
    
    def create_model(self):
        """Create LSTM model from metadata"""
        self.model = MultiTargetLSTM(
            sequence_length=self.metadata['sequence_length'],
            n_features=self.metadata['n_features'],
            n_targets=self.metadata['n_targets']
        )
        
        return self.model
    
    def train_model(self, train_data, val_data, epochs=50, batch_size=32):
        """Train the model"""
        X_train, y_train = train_data
        X_val, y_val = val_data
        
        print(f"Training model for {epochs} epochs with batch size {batch_size}")
        
        # Train model
        history = self.model.train_model(
            X_train, y_train, 
            X_val, y_val,
            epochs=epochs,
            batch_size=batch_size
        )
        
        return history
    
    def evaluate_model(self, test_data):
        """Evaluate trained model"""
        X_test, y_test = test_data
        
        print("Evaluating model on test data...")
        
        results, predictions = self.model.evaluate_model(X_test, y_test)
        
        return results, predictions
    
    def plot_training_history(self, history, save_path='models/training_history.png'):
        """Plot training history"""
        if not PLOTTING_AVAILABLE:
            print("Matplotlib not available, skipping training history plot")
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('LSTM Training History', fontsize=16)
        
        # Total loss
        axes[0, 0].plot(history.history['loss'], label='Train Loss')
        axes[0, 0].plot(history.history['val_loss'], label='Val Loss')
        axes[0, 0].set_title('Total Loss')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # Direction accuracy
        if 'direction_predictions_accuracy' in history.history:
            axes[0, 1].plot(history.history['direction_predictions_accuracy'], label='Train Acc')
            axes[0, 1].plot(history.history['val_direction_predictions_accuracy'], label='Val Acc')
            axes[0, 1].set_title('Direction Accuracy')
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].set_ylabel('Accuracy')
            axes[0, 1].legend()
            axes[0, 1].grid(True)
        
        # Price MAE
        if 'price_predictions_mae' in history.history:
            axes[1, 0].plot(history.history['price_predictions_mae'], label='Train MAE')
            axes[1, 0].plot(history.history['val_price_predictions_mae'], label='Val MAE')
            axes[1, 0].set_title('Price MAE')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('MAE')
            axes[1, 0].legend()
            axes[1, 0].grid(True)
        
        # Change MAE
        if 'change_predictions_mae' in history.history:
            axes[1, 1].plot(history.history['change_predictions_mae'], label='Train MAE')
            axes[1, 1].plot(history.history['val_change_predictions_mae'], label='Val MAE')
            axes[1, 1].set_title('Change % MAE')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('MAE')
            axes[1, 1].legend()
            axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Training history plot saved to {save_path}")
    
    def save_training_results(self, results, save_path='models/training_results.json'):
        """Save training results"""
        training_results = {
            'timestamp': datetime.now().isoformat(),
            'model_config': {
                'sequence_length': self.metadata['sequence_length'],
                'n_features': self.metadata['n_features'],
                'n_targets': self.metadata['n_targets']
            },
            'data_info': {
                'train_samples': self.metadata['train_samples'],
                'val_samples': self.metadata['val_samples'],
                'test_samples': self.metadata['test_samples']
            },
            'evaluation_results': results
        }
        
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        with open(save_path, 'w') as f:
            json.dump(training_results, f, indent=2)
        
        print(f"Training results saved to {save_path}")
    
    def complete_training_pipeline(self, epochs=50, batch_size=32):
        """Complete training pipeline"""
        print("=== LSTM TRAINING PIPELINE ===")
        
        # Load data
        train_data, val_data, test_data = self.load_data()
        
        # Create model
        self.create_model()
        
        # Train model
        history = self.train_model(train_data, val_data, epochs, batch_size)
        
        # Evaluate model
        results, predictions = self.evaluate_model(test_data)
        
        # Save model
        self.model.save_model('models/lstm_market_predictor.h5')
        
        # Plot training history
        self.plot_training_history(history)
        
        # Save results
        self.save_training_results(results)
        
        print("✅ Training pipeline completed!")
        
        return history, results, predictions

def main():
    """Main training function"""
    trainer = LSTMTrainer()
    
    # Run complete training pipeline
    history, results, predictions = trainer.complete_training_pipeline(
        epochs=10,  # Start with 10 epochs for testing
        batch_size=32
    )
    
    print("\n=== FINAL RESULTS ===")
    for metric, value in results.items():
        print(f"{metric}: {value:.6f}")

if __name__ == "__main__":
    main()
