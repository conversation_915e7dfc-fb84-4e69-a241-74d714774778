#!/usr/bin/env python3
"""
Test script to verify timing with broker delay
"""

import sys
import time
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_timing_with_delay():
    """Test the timing logic with the new 4-second delay"""
    print("🕐 Testing Timing Logic with Broker Delay...")
    
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test the wait_for_candle_close method
    print("\n📊 Testing candle close timing:")
    
    current_time = datetime.now()
    print(f"   Current time: {current_time.strftime('%H:%M:%S')}")
    
    wait_seconds, next_candle_close = trader.wait_for_candle_close()
    print(f"   Next candle closes at: {next_candle_close.strftime('%H:%M:%S')}")
    print(f"   Wait time: {wait_seconds:.1f} seconds")
    
    # Test boundary detection
    seconds_since_hour = current_time.minute * 60 + current_time.second
    is_at_5min_boundary = (seconds_since_hour % 300) <= 10
    
    print(f"\n🎯 Boundary Detection:")
    print(f"   Seconds since hour start: {seconds_since_hour}")
    print(f"   Is at 5-minute boundary: {is_at_5min_boundary}")
    print(f"   Boundary tolerance: ±10 seconds")
    
    # Show what the new timing flow will look like
    print(f"\n⏰ New Timing Flow:")
    print(f"   1. Detect candle close time (e.g., 14:25:00)")
    print(f"   2. Log: '🎯 CANDLE CLOSE ANALYSIS: Running at 14:25:XX'")
    print(f"   3. Log: '⏳ Waiting 4 seconds for broker to process candle data...'")
    print(f"   4. Sleep for 4 seconds")
    print(f"   5. Start analysis with fresh candle data")
    
    print(f"\n✅ Benefits of 4-second delay:")
    print(f"   • Ensures broker has processed complete candle data")
    print(f"   • Prevents analysis on incomplete/forming candles")
    print(f"   • Gives time for price feeds to stabilize")
    print(f"   • Reduces risk of stale data issues")

def simulate_timing_sequence():
    """Simulate what the timing sequence will look like"""
    print(f"\n🎬 Simulating Timing Sequence...")
    
    # Simulate detecting candle close
    current_time = datetime.now()
    print(f"   {current_time.strftime('%H:%M:%S')} - 🎯 CANDLE CLOSE ANALYSIS: Running at candle close")
    
    # Simulate the delay
    print(f"   {current_time.strftime('%H:%M:%S')} - ⏳ Waiting 4 seconds for broker to process candle data...")
    
    # Show what happens during the delay
    for i in range(1, 5):
        time.sleep(1)
        delay_time = current_time + timedelta(seconds=i)
        print(f"   {delay_time.strftime('%H:%M:%S')} - ⏳ Delay {i}/4 seconds...")
    
    # Simulate analysis start
    analysis_start = current_time + timedelta(seconds=4)
    print(f"   {analysis_start.strftime('%H:%M:%S')} - 🚀 Starting analysis with fresh candle data!")
    
    print(f"\n✅ Timing sequence completed!")

def main():
    """Run timing tests with delay"""
    print("🧪 TIMING WITH BROKER DELAY TEST")
    print("=" * 50)
    
    # Test 1: Timing logic
    test_timing_with_delay()
    
    # Test 2: Simulate sequence
    simulate_timing_sequence()
    
    print("\n📊 SUMMARY")
    print("=" * 30)
    print("✅ Timing with broker delay implemented!")
    print("🎯 Key changes:")
    print("   • Added 4-second delay after detecting candle close")
    print("   • Delay ensures broker has processed candle data")
    print("   • Analysis now runs at XX:X5:04 instead of XX:X5:00")
    print("   • Reduces risk of incomplete candle data")
    
    print("\n📝 Expected log sequence:")
    print("   🎯 CANDLE CLOSE ANALYSIS: Running at 14:25:00")
    print("   ⏳ Waiting 4 seconds for broker to process candle data...")
    print("   [4 second delay]")
    print("   🚀 Analysis starts with complete candle data")

if __name__ == "__main__":
    main()
