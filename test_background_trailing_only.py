#!/usr/bin/env python3
"""
Background Trailing Only Test

Tests that:
1. Main loop trailing is disabled (no more warnings)
2. Background trailing monitor starts and runs independently every 10 seconds
3. Background monitor handles all trailing stops and partial closes
"""

import pandas as pd
import sys
import os
import time
import threading
from datetime import datetime
from unittest.mock import Mock, patch

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader

def test_background_trailing_only():
    """Test that only background trailing works, main loop trailing is disabled"""
    
    print("🧪 Background Trailing Only Test")
    print("=" * 50)
    
    # Create test trader
    trader = FixedLiveTrader("XAUUSD!")
    
    test_results = []
    
    # Test 1: Main Loop Trailing Disabled
    print(f"\n📊 Test 1: Main Loop Trailing Disabled")
    print("-" * 40)
    
    # Set up position and trailing data
    trader.current_position = {
        'type': 'BUY',
        'ticket': 12345,
        'time': datetime.now(),
        'volume': 0.10,
        'remaining_volume': 0.10,
        'price': 4300.0,
        'sl': 4298.5,
        'take_profit': 0.0,
        'original_sl': 4298.5
    }
    
    trader.trailing_stop_data = {
        'initial_sl': 4298.5,
        'current_sl': 4298.5,
        'original_sl_distance': 1.50,
        'profit_sl_count': 0
    }
    
    # Mock MT5 manager
    mock_tick = {'bid': 4301.0, 'ask': 4301.5}
    
    # Test the main loop section that was disabled
    # We need to track calls from the main thread vs background thread
    main_thread_calls = []

    def track_update_trailing_stop(*args, **kwargs):
        current_thread = threading.current_thread().name
        main_thread_calls.append(current_thread)
        return False  # Don't actually do trailing

    with patch.object(trader.mt5_manager, 'get_symbol_info_tick', return_value=mock_tick), \
         patch.object(trader, 'update_trailing_stop', side_effect=track_update_trailing_stop):

        # Simulate the main loop logic (this should NOT call update_trailing_stop anymore)
        has_position = True
        if has_position and trader.current_position:
            trader.logger.info(f"📊 POSITION STATUS: {trader.current_position['type']} position active - Background trailing monitor handling updates")
            # Ensure background monitor is running
            if not (trader.trailing_monitor_thread and trader.trailing_monitor_thread.is_alive()):
                trader.logger.warning("⚠️ Background trailing monitor not running - restarting")
                trader.start_real_time_trailing_monitor()
                time.sleep(1)  # Give background thread time to make its call

    # Check if any calls came from the main thread
    main_thread_call_count = sum(1 for thread_name in main_thread_calls if thread_name == 'MainThread')
    background_thread_call_count = sum(1 for thread_name in main_thread_calls if thread_name == 'TrailingStopMonitor')

    print(f"  Main thread calls: {main_thread_call_count}")
    print(f"  Background thread calls: {background_thread_call_count}")
    print(f"  Main loop update_trailing_stop called: {'❌ YES (BAD)' if main_thread_call_count > 0 else '✅ NO (GOOD)'}")
    print(f"  Expected: Main loop should NOT call update_trailing_stop")
    
    test1_passed = main_thread_call_count == 0
    test_results.append(("Main Loop Trailing Disabled", test1_passed))
    
    # Test 2: Background Monitor Independence
    print(f"\n📊 Test 2: Background Monitor Independence")
    print("-" * 40)
    
    # Stop any existing monitor
    if trader.trailing_monitor_thread and trader.trailing_monitor_thread.is_alive():
        trader.stop_real_time_trailing_monitor()
        time.sleep(1)
    
    print("🔍 Starting background monitor...")
    
    # Mock MT5 calls for the background monitor
    mock_positions = [{
        'ticket': 12345,
        'symbol': 'XAUUSD!',
        'type': 0,  # BUY
        'volume': 0.10,
        'price_open': 4300.0,
        'sl': 4298.5,
        'tp': 0.0,
        'profit': 15.0,
        'time': 1234567890
    }]

    # Mock the MT5 manager to be "connected"
    trader.mt5_manager.connected = True

    with patch.object(trader.mt5_manager, 'get_symbol_info_tick', return_value=mock_tick), \
         patch.object(trader.mt5_manager, 'get_positions', return_value=mock_positions), \
         patch.object(trader.mt5_manager, 'modify_position', return_value=True):
        
        # Start the monitor
        trader.start_real_time_trailing_monitor()
        time.sleep(1)  # Give it time to start
        
        monitor_started = trader.trailing_monitor_active and trader.trailing_monitor_thread is not None
        monitor_alive = trader.trailing_monitor_thread.is_alive() if trader.trailing_monitor_thread else False
        
        print(f"  Monitor Active: {'✅ YES' if monitor_started else '❌ NO'}")
        print(f"  Thread Alive: {'✅ YES' if monitor_alive else '❌ NO'}")
        print(f"  Thread Name: {trader.trailing_monitor_thread.name if trader.trailing_monitor_thread else 'None'}")
        
        # Let it run for a few cycles to verify independence
        print("🔍 Letting monitor run for 12 seconds (should complete at least 1 cycle)...")
        time.sleep(12)  # More than one 10-second cycle
        
        # Check if it's still running
        still_alive = trader.trailing_monitor_thread.is_alive() if trader.trailing_monitor_thread else False
        print(f"  Still Running After 12s: {'✅ YES' if still_alive else '❌ NO'}")
        
        # Stop the monitor
        trader.stop_real_time_trailing_monitor()
        time.sleep(1)
        
        monitor_stopped = not trader.trailing_monitor_active
        print(f"  Monitor Stopped: {'✅ YES' if monitor_stopped else '❌ NO'}")
    
    test2_passed = monitor_started and monitor_alive and still_alive and monitor_stopped
    test_results.append(("Background Monitor Independence", test2_passed))
    
    # Test 3: Monitor Auto-Restart Logic
    print(f"\n📊 Test 3: Monitor Auto-Restart Logic")
    print("-" * 40)
    
    # Simulate the main loop detecting a dead monitor and restarting it
    trader.trailing_monitor_thread = None  # Simulate dead thread
    trader.trailing_monitor_active = False
    
    print("🔍 Testing auto-restart logic...")
    
    # This simulates the main loop check
    has_position = True
    if has_position and trader.current_position:
        if not (trader.trailing_monitor_thread and trader.trailing_monitor_thread.is_alive()):
            print("  Detected dead monitor - restarting...")
            trader.start_real_time_trailing_monitor()
    
    time.sleep(1)  # Give it time to start
    
    restarted = trader.trailing_monitor_active and trader.trailing_monitor_thread is not None
    restart_alive = trader.trailing_monitor_thread.is_alive() if trader.trailing_monitor_thread else False
    
    print(f"  Monitor Restarted: {'✅ YES' if restarted else '❌ NO'}")
    print(f"  Thread Alive: {'✅ YES' if restart_alive else '❌ NO'}")
    
    # Clean up
    trader.stop_real_time_trailing_monitor()
    time.sleep(1)
    
    test3_passed = restarted and restart_alive
    test_results.append(("Monitor Auto-Restart", test3_passed))
    
    # Test 4: No Warning Messages
    print(f"\n📊 Test 4: No Warning Messages from Main Loop")
    print("-" * 40)
    
    # Test that the main loop doesn't generate the old warning
    # "Cannot calculate original SL distance for trailing - invalid or zero distance"
    
    # Set up a scenario that would have caused the warning before
    trader.trailing_stop_data = None  # This would cause the warning in old code
    
    print("🔍 Testing main loop with no trailing data (would cause warning in old code)...")
    
    # Simulate main loop execution - should not call update_trailing_stop
    with patch.object(trader, 'update_trailing_stop') as mock_update_trailing:
        has_position = True
        if has_position and trader.current_position:
            trader.logger.info(f"📊 POSITION STATUS: {trader.current_position['type']} position active - Background trailing monitor handling updates")
            # The old code would have called update_trailing_stop here and generated warning
    
    print(f"  update_trailing_stop called: {'❌ YES (would cause warning)' if mock_update_trailing.called else '✅ NO (no warning)'}")
    print(f"  Expected: No calls to update_trailing_stop from main loop")
    
    test4_passed = not mock_update_trailing.called
    test_results.append(("No Warning Messages", test4_passed))
    
    # Summary
    print(f"\n🎯 Test Summary")
    print("=" * 30)
    
    passed_tests = sum(1 for _, passed in test_results if passed)
    total_tests = len(test_results)
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed Tests: {passed_tests}")
    print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
    
    for test_name, passed in test_results:
        print(f"  {test_name}: {'✅ PASSED' if passed else '❌ FAILED'}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 BACKGROUND TRAILING ONLY FIX SUCCESSFUL!")
        print("✅ Main loop trailing is completely disabled")
        print("✅ Background monitor runs independently every 10 seconds")
        print("✅ No more 'Cannot calculate original SL distance' warnings")
        print("✅ Auto-restart logic works if monitor dies")
        print("✅ Clean separation between main loop and background trailing")
        
        print(f"\n📊 SYSTEM BEHAVIOR:")
        print("  • Main loop: Handles signals, regime changes, position management")
        print("  • Background monitor: Handles ALL trailing stops and partial closes")
        print("  • Independence: Background runs every 10 seconds regardless of candle timing")
        print("  • Reliability: Auto-restart if background monitor fails")
        
    else:
        print(f"\n⚠️ SOME ISSUES REMAIN:")
        for test_name, passed in test_results:
            if not passed:
                print(f"  • {test_name} needs attention")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    try:
        success = test_background_trailing_only()
        exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
