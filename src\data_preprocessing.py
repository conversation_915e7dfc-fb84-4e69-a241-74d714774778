"""
Data Preprocessing Module
Handles dataset preparation, splitting, and balancing for LSTM training
"""
import pandas as pd
import numpy as np
from typing import Tuple, Dict, List, Optional
import logging
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.utils.class_weight import compute_class_weight
import warnings
warnings.filterwarnings('ignore')

from config.config import *
from src.feature_engineering import FeatureEngineer

# Set up logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

class DataPreprocessor:
    """
    Handles all data preprocessing for LSTM model training
    """
    
    def __init__(self):
        self.feature_engineer = FeatureEngineer()
        self.train_data = None
        self.val_data = None
        self.test_data = None
        self.class_weights = None
        
    def prepare_dataset(self, df: pd.DataFrame) -> Dict:
        """
        Complete data preparation pipeline
        
        Args:
            df: Raw OHLCV DataFrame
            
        Returns:
            Dictionary containing prepared datasets and metadata
        """
        try:
            logger.info("Starting data preparation pipeline...")
            
            # Step 1: Create technical indicators
            logger.info("Creating technical indicators...")
            df_with_features = self.feature_engineer.create_technical_indicators(df)
            
            # Step 2: Create target variables
            logger.info("Creating target variables...")
            df_with_targets = self.feature_engineer.create_target_variable(df_with_features)
            
            # Step 3: Prepare features for training
            logger.info("Preparing features for training...")
            df_processed, feature_columns = self.feature_engineer.prepare_features_for_training(df_with_targets)
            
            # Step 4: Split data temporally (no data leakage)
            logger.info("Splitting data temporally...")
            train_df, val_df, test_df = self._temporal_split(df_processed)
            
            # Step 5: Scale features
            logger.info("Scaling features...")
            train_df_scaled = self.feature_engineer.scale_features(train_df, feature_columns, fit_scaler=True)
            val_df_scaled = self.feature_engineer.scale_features(val_df, feature_columns, fit_scaler=False)
            test_df_scaled = self.feature_engineer.scale_features(test_df, feature_columns, fit_scaler=False)
            
            # Step 6: Create sequences
            logger.info("Creating sequences...")
            X_train, y_train = self.feature_engineer.create_sequences(train_df_scaled, feature_columns, 'target')
            X_val, y_val = self.feature_engineer.create_sequences(val_df_scaled, feature_columns, 'target')
            X_test, y_test = self.feature_engineer.create_sequences(test_df_scaled, feature_columns, 'target')
            
            # Step 7: Balance training dataset
            logger.info("Balancing training dataset...")
            X_train_balanced, y_train_balanced = self.feature_engineer.balance_dataset(X_train, y_train)
            
            # Step 8: Calculate class weights
            self.class_weights = self._calculate_class_weights(y_train_balanced)
            
            # Store data for later use
            self.train_data = (X_train_balanced, y_train_balanced)
            self.val_data = (X_val, y_val)
            self.test_data = (X_test, y_test)
            
            # Prepare result dictionary
            result = {
                'train': (X_train_balanced, y_train_balanced),
                'validation': (X_val, y_val),
                'test': (X_test, y_test),
                'feature_columns': feature_columns,
                'class_weights': self.class_weights,
                'data_info': {
                    'total_samples': len(df_processed),
                    'train_samples': len(X_train_balanced),
                    'val_samples': len(X_val),
                    'test_samples': len(X_test),
                    'num_features': len(feature_columns),
                    'sequence_length': SEQUENCE_LENGTH,
                    'num_classes': len(np.unique(y_train_balanced))
                }
            }
            
            logger.info("Data preparation completed successfully!")
            logger.info(f"Training samples: {len(X_train_balanced)}")
            logger.info(f"Validation samples: {len(X_val)}")
            logger.info(f"Test samples: {len(X_test)}")
            logger.info(f"Features: {len(feature_columns)}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in data preparation pipeline: {e}")
            return {}
    
    def _temporal_split(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Split data temporally to prevent data leakage
        
        Args:
            df: DataFrame to split
            
        Returns:
            Tuple of (train, validation, test) DataFrames
        """
        try:
            # Sort by datetime to ensure temporal order
            df_sorted = df.sort_index()
            
            # Calculate split indices
            total_samples = len(df_sorted)
            train_end = int(total_samples * TRAIN_SPLIT)
            val_end = int(total_samples * (TRAIN_SPLIT + VALIDATION_SPLIT))
            
            # Split data
            train_df = df_sorted.iloc[:train_end].copy()
            val_df = df_sorted.iloc[train_end:val_end].copy()
            test_df = df_sorted.iloc[val_end:].copy()
            
            logger.info(f"Temporal split - Train: {len(train_df)}, Val: {len(val_df)}, Test: {len(test_df)}")
            logger.info(f"Train period: {train_df.index.min()} to {train_df.index.max()}")
            logger.info(f"Validation period: {val_df.index.min()} to {val_df.index.max()}")
            logger.info(f"Test period: {test_df.index.min()} to {test_df.index.max()}")
            
            return train_df, val_df, test_df
            
        except Exception as e:
            logger.error(f"Error in temporal split: {e}")
            return df, pd.DataFrame(), pd.DataFrame()
    
    def _calculate_class_weights(self, y: np.ndarray) -> Dict:
        """
        Calculate class weights for imbalanced dataset
        
        Args:
            y: Target labels
            
        Returns:
            Dictionary of class weights
        """
        try:
            unique_classes = np.unique(y)
            class_weights_array = compute_class_weight(
                'balanced', 
                classes=unique_classes, 
                y=y
            )
            
            class_weights = dict(zip(unique_classes, class_weights_array))
            
            logger.info(f"Calculated class weights: {class_weights}")
            
            return class_weights
            
        except Exception as e:
            logger.error(f"Error calculating class weights: {e}")
            return {}
    
    def create_walk_forward_splits(self, df: pd.DataFrame, n_splits: int = CROSS_VALIDATION_FOLDS) -> List[Tuple]:
        """
        Create walk-forward validation splits for time series
        
        Args:
            df: DataFrame to split
            n_splits: Number of splits
            
        Returns:
            List of (train_indices, test_indices) tuples
        """
        try:
            tscv = TimeSeriesSplit(n_splits=n_splits)
            splits = []
            
            for train_idx, test_idx in tscv.split(df):
                splits.append((train_idx, test_idx))
            
            logger.info(f"Created {len(splits)} walk-forward splits")
            
            return splits
            
        except Exception as e:
            logger.error(f"Error creating walk-forward splits: {e}")
            return []
    
    def prepare_live_data(self, df: pd.DataFrame, feature_columns: List[str]) -> Optional[np.ndarray]:
        """
        Prepare live data for prediction
        
        Args:
            df: Live OHLCV data
            feature_columns: List of feature columns to use
            
        Returns:
            Prepared sequence for prediction
        """
        try:
            # Create technical indicators
            df_with_features = self.feature_engineer.create_technical_indicators(df)
            
            # Scale features using fitted scaler
            df_scaled = self.feature_engineer.scale_features(df_with_features, feature_columns, fit_scaler=False)
            
            # Get the latest sequence
            if len(df_scaled) < SEQUENCE_LENGTH:
                logger.error(f"Insufficient data for prediction. Need {SEQUENCE_LENGTH}, got {len(df_scaled)}")
                return None
            
            # Extract features for the latest sequence
            features = df_scaled[feature_columns].values
            latest_sequence = features[-SEQUENCE_LENGTH:].reshape(1, SEQUENCE_LENGTH, len(feature_columns))
            
            return latest_sequence
            
        except Exception as e:
            logger.error(f"Error preparing live data: {e}")
            return None
    
    def get_data_statistics(self) -> Dict:
        """
        Get comprehensive statistics about the prepared data
        
        Returns:
            Dictionary with data statistics
        """
        try:
            if self.train_data is None:
                logger.error("No training data available")
                return {}
            
            X_train, y_train = self.train_data
            X_val, y_val = self.val_data if self.val_data else (None, None)
            X_test, y_test = self.test_data if self.test_data else (None, None)
            
            stats = {
                'training': {
                    'samples': len(X_train),
                    'features': X_train.shape[2] if len(X_train.shape) > 2 else 0,
                    'sequence_length': X_train.shape[1] if len(X_train.shape) > 1 else 0,
                    'class_distribution': dict(zip(*np.unique(y_train, return_counts=True)))
                }
            }
            
            if X_val is not None:
                stats['validation'] = {
                    'samples': len(X_val),
                    'class_distribution': dict(zip(*np.unique(y_val, return_counts=True)))
                }
            
            if X_test is not None:
                stats['test'] = {
                    'samples': len(X_test),
                    'class_distribution': dict(zip(*np.unique(y_test, return_counts=True)))
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting data statistics: {e}")
            return {}
    
    def validate_data_quality(self, X: np.ndarray, y: np.ndarray) -> Dict:
        """
        Validate data quality and identify potential issues
        
        Args:
            X: Feature sequences
            y: Target labels
            
        Returns:
            Dictionary with validation results
        """
        try:
            validation_results = {
                'data_shape_valid': True,
                'no_nan_values': True,
                'no_infinite_values': True,
                'balanced_classes': True,
                'sufficient_samples': True,
                'issues': []
            }
            
            # Check data shapes
            if len(X.shape) != 3:
                validation_results['data_shape_valid'] = False
                validation_results['issues'].append(f"Invalid X shape: {X.shape}, expected 3D array")
            
            if len(y.shape) != 1:
                validation_results['data_shape_valid'] = False
                validation_results['issues'].append(f"Invalid y shape: {y.shape}, expected 1D array")
            
            # Check for NaN values
            if np.isnan(X).any():
                validation_results['no_nan_values'] = False
                validation_results['issues'].append("Found NaN values in features")
            
            if np.isnan(y).any():
                validation_results['no_nan_values'] = False
                validation_results['issues'].append("Found NaN values in targets")
            
            # Check for infinite values
            if np.isinf(X).any():
                validation_results['no_infinite_values'] = False
                validation_results['issues'].append("Found infinite values in features")
            
            # Check class balance
            unique_classes, counts = np.unique(y, return_counts=True)
            min_count = min(counts)
            max_count = max(counts)
            
            if max_count / min_count > 3:  # Imbalance ratio > 3:1
                validation_results['balanced_classes'] = False
                validation_results['issues'].append(f"Class imbalance detected. Ratio: {max_count/min_count:.2f}")
            
            # Check sufficient samples
            if len(X) < 1000:  # Minimum samples for reliable training
                validation_results['sufficient_samples'] = False
                validation_results['issues'].append(f"Insufficient samples: {len(X)} < 1000")
            
            # Overall validation status
            validation_results['overall_valid'] = all([
                validation_results['data_shape_valid'],
                validation_results['no_nan_values'],
                validation_results['no_infinite_values'],
                validation_results['sufficient_samples']
            ])
            
            if validation_results['overall_valid']:
                logger.info("Data quality validation passed")
            else:
                logger.warning(f"Data quality issues found: {validation_results['issues']}")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Error validating data quality: {e}")
            return {'overall_valid': False, 'issues': [str(e)]}
