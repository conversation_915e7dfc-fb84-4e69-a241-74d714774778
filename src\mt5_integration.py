"""
MetaTrader 5 Integration Module
Handles connection, data retrieval, and trading operations with MT5
"""
import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Optional, Dict, List, Tuple
import time

from config.config import *

# Set up logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

class MT5Manager:
    """
    Manages all MetaTrader 5 operations including connection, data retrieval, and trading
    """

    def __init__(self, symbol=None):
        self.connected = False
        self.account_info = None
        self.symbol_info = None
        self.symbol = symbol or SYMBOL  # Use provided symbol or default from config
        
    def connect(self, login: Optional[int] = None, password: Optional[str] = None, 
                server: Optional[str] = None) -> bool:
        """
        Connect to MetaTrader 5
        
        Args:
            login: MT5 account login
            password: MT5 account password  
            server: MT5 server name
            
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Initialize MT5 with specific terminal path to avoid connecting to wrong installation
            mt5_terminal_path = r"C:\Program Files\MetaTrader 5\terminal64.exe"
            logger.info(f"Connecting to MT5 terminal: {mt5_terminal_path}")

            if not mt5.initialize(path=mt5_terminal_path):
                logger.error(f"MT5 initialization failed: {mt5.last_error()}")
                logger.error(f"Failed to connect to: {mt5_terminal_path}")
                logger.info("Make sure MetaTrader 5 is installed in the correct path and not running")
                return False

            logger.info(f"Successfully initialized MT5 terminal: {mt5_terminal_path}")
            
            # Login if credentials provided
            if login and password and server:
                if not mt5.login(login, password, server):
                    logger.error(f"MT5 login failed: {mt5.last_error()}")
                    mt5.shutdown()
                    return False
                    
            # Get account info
            self.account_info = mt5.account_info()
            if self.account_info is None:
                logger.error("Failed to get account info")
                mt5.shutdown()
                return False
                
            # Get symbol info
            self.symbol_info = mt5.symbol_info(self.symbol)
            if self.symbol_info is None:
                logger.error(f"Failed to get symbol info for {self.symbol}")
                mt5.shutdown()
                return False

            # Enable symbol if not visible
            if not self.symbol_info.visible:
                if not mt5.symbol_select(self.symbol, True):
                    logger.error(f"Failed to select symbol {self.symbol}")
                    mt5.shutdown()
                    return False

            self.connected = True
            logger.info(f"Successfully connected to MT5. Account: {self.account_info.login}")
            logger.info(f"Account Balance: {self.account_info.balance}")
            logger.info(f"Symbol: {self.symbol}, Spread: {self.symbol_info.spread}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error connecting to MT5: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from MetaTrader 5"""
        if self.connected:
            mt5.shutdown()
            self.connected = False
            logger.info("Disconnected from MT5")
    
    def get_historical_data(self, symbol: str, timeframe: str, start_date: datetime, 
                          end_date: datetime) -> Optional[pd.DataFrame]:
        """
        Get historical data from MT5
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe (M1, M5, M15, etc.)
            start_date: Start date for data
            end_date: End date for data
            
        Returns:
            DataFrame with OHLCV data or None if failed
        """
        if not self.connected:
            logger.error("Not connected to MT5")
            return None
            
        try:
            # Convert timeframe string to MT5 constant
            timeframe_map = {
                'M1': mt5.TIMEFRAME_M1,
                'M5': mt5.TIMEFRAME_M5,
                'M15': mt5.TIMEFRAME_M15,
                'M30': mt5.TIMEFRAME_M30,
                'H1': mt5.TIMEFRAME_H1,
                'H4': mt5.TIMEFRAME_H4,
                'D1': mt5.TIMEFRAME_D1
            }
            
            mt5_timeframe = timeframe_map.get(timeframe)
            if mt5_timeframe is None:
                logger.error(f"Invalid timeframe: {timeframe}")
                return None
            
            # Get rates
            rates = mt5.copy_rates_range(symbol, mt5_timeframe, start_date, end_date)
            
            if rates is None or len(rates) == 0:
                logger.error(f"No data received for {symbol}")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(rates)
            df['datetime'] = pd.to_datetime(df['time'], unit='s')
            df = df[['datetime', 'open', 'high', 'low', 'close', 'tick_volume']]
            df.rename(columns={'tick_volume': 'volume'}, inplace=True)
            df.set_index('datetime', inplace=True)
            
            logger.info(f"Retrieved {len(df)} bars for {symbol} from {start_date} to {end_date}")
            return df
            
        except Exception as e:
            logger.error(f"Error getting historical data: {e}")
            return None
    
    def get_latest_data(self, symbol: str, timeframe: str, count: int = 1) -> Optional[pd.DataFrame]:
        """
        Get latest bars from MT5
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe
            count: Number of latest bars to get
            
        Returns:
            DataFrame with latest OHLCV data
        """
        if not self.connected:
            logger.error("Not connected to MT5")
            return None
            
        try:
            timeframe_map = {
                'M1': mt5.TIMEFRAME_M1,
                'M5': mt5.TIMEFRAME_M5,
                'M15': mt5.TIMEFRAME_M15,
                'M30': mt5.TIMEFRAME_M30,
                'H1': mt5.TIMEFRAME_H1,
                'H4': mt5.TIMEFRAME_H4,
                'D1': mt5.TIMEFRAME_D1
            }
            
            mt5_timeframe = timeframe_map.get(timeframe)
            if mt5_timeframe is None:
                logger.error(f"Invalid timeframe: {timeframe}")
                return None
            
            rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, count)
            
            if rates is None or len(rates) == 0:
                logger.error(f"No latest data received for {symbol}")
                return None
            
            df = pd.DataFrame(rates)
            df['datetime'] = pd.to_datetime(df['time'], unit='s')
            df = df[['datetime', 'open', 'high', 'low', 'close', 'tick_volume']]
            df.rename(columns={'tick_volume': 'volume'}, inplace=True)
            df.set_index('datetime', inplace=True)
            
            return df
            
        except Exception as e:
            logger.error(f"Error getting latest data: {e}")
            return None
    
    def get_account_info(self) -> Optional[Dict]:
        """Get current account information"""
        if not self.connected:
            logger.error("Not connected to MT5")
            return None
            
        try:
            account = mt5.account_info()
            if account is None:
                logger.error("Failed to get account info")
                return None
                
            return {
                'login': account.login,
                'balance': account.balance,
                'equity': account.equity,
                'margin': account.margin,
                'free_margin': account.margin_free,
                'margin_level': account.margin_level,
                'currency': account.currency
            }
            
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return None

    def get_symbol_info_tick(self, symbol: str) -> Optional[Dict]:
        """Get current tick data for symbol"""
        if not self.connected:
            logger.error("Not connected to MT5")
            return None

        try:
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                logger.error(f"Failed to get tick for {symbol}")
                return None

            return {
                'bid': tick.bid,
                'ask': tick.ask,
                'last': tick.last,
                'volume': tick.volume,
                'time': tick.time
            }

        except Exception as e:
            logger.error(f"Error getting tick data: {e}")
            return None

    def get_pip_size(self, symbol: str) -> float:
        """Get pip size for the given symbol"""
        try:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"Failed to get symbol info for {symbol}")
                return 0.0001  # Default pip size

            # Calculate pip size based on digits
            digits = symbol_info.digits
            if digits == 5 or digits == 3:
                # 5-digit or 3-digit quotes (fractional pips)
                pip_size = 10 ** -(digits - 1)
            else:
                # Standard 4-digit or 2-digit quotes
                pip_size = 10 ** -digits

            return pip_size

        except Exception as e:
            logger.error(f"Error getting pip size for {symbol}: {e}")
            return 0.0001  # Default pip size

    def place_order(self, symbol: str, order_type: str, volume: float,
                   price: float, sl: float = None, tp: float = None,
                   comment: str = "") -> Optional[int]:
        """Place a trading order (market or pending)"""
        if not self.connected:
            logger.error("Not connected to MT5")
            return None

        try:
            # Validate parameters
            if volume <= 0:
                logger.error(f"Invalid volume: {volume}")
                return None
            if price <= 0:
                logger.error(f"Invalid price: {price}")
                return None

            # Check if symbol is available
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"Symbol {symbol} not found")
                return None

            # Ensure symbol is selected for trading
            if not mt5.symbol_select(symbol, True):
                logger.error(f"Failed to select symbol {symbol}")
                return None

            # Determine MT5 order type and action
            if order_type.upper() == "BUY":
                mt5_order_type = mt5.ORDER_TYPE_BUY
                action = mt5.TRADE_ACTION_DEAL
            elif order_type.upper() == "SELL":
                mt5_order_type = mt5.ORDER_TYPE_SELL
                action = mt5.TRADE_ACTION_DEAL
            elif order_type.upper() == "BUY_STOP":
                mt5_order_type = mt5.ORDER_TYPE_BUY_STOP
                action = mt5.TRADE_ACTION_PENDING
            elif order_type.upper() == "SELL_STOP":
                mt5_order_type = mt5.ORDER_TYPE_SELL_STOP
                action = mt5.TRADE_ACTION_PENDING
            elif order_type.upper() == "BUY_LIMIT":
                mt5_order_type = mt5.ORDER_TYPE_BUY_LIMIT
                action = mt5.TRADE_ACTION_PENDING
            elif order_type.upper() == "SELL_LIMIT":
                mt5_order_type = mt5.ORDER_TYPE_SELL_LIMIT
                action = mt5.TRADE_ACTION_PENDING
            else:
                logger.error(f"Invalid order type: {order_type}")
                return None

            # Prepare order request - ensure all numeric values are standard Python types
            request = {
                "action": action,
                "symbol": symbol,
                "volume": float(volume),  # Convert to standard Python float
                "type": mt5_order_type,
                "price": float(price),    # Convert to standard Python float
                "deviation": 20,
                "magic": 234000,
                "comment": comment,
                "type_time": mt5.ORDER_TIME_GTC,
            }

            # Set appropriate filling type based on order type
            if action == mt5.TRADE_ACTION_PENDING:
                # For pending orders, try different filling types
                # First try FOK, if that fails we'll try IOC in the error handling
                request["type_filling"] = mt5.ORDER_FILLING_FOK
            else:
                # For market orders, use IOC
                request["type_filling"] = mt5.ORDER_FILLING_IOC

            # Add stop loss and take profit if provided
            if sl is not None:
                request["sl"] = float(sl)  # Convert to standard Python float
            if tp is not None:
                request["tp"] = float(tp)  # Convert to standard Python float

            # Log the request for debugging
            logger.info(f"Sending order request: {request}")

            # Send order
            result = mt5.order_send(request)

            # Check if result is valid
            if result is None:
                last_error = mt5.last_error()
                logger.error(f"Order failed: mt5.order_send returned None. Last error: {last_error}")

                # For pending orders, try with different filling type
                if action == mt5.TRADE_ACTION_PENDING:
                    logger.info("Retrying pending order with IOC filling...")
                    request["type_filling"] = mt5.ORDER_FILLING_IOC
                    result = mt5.order_send(request)

                    if result is None:
                        last_error = mt5.last_error()
                        logger.error(f"Retry failed: mt5.order_send returned None. Last error: {last_error}")
                        return None
                else:
                    return None

            # Check if result has the expected attributes
            if not hasattr(result, 'retcode'):
                logger.error(f"Order failed: Invalid result object: {result}")
                return None

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"Order failed: {result.retcode} - {result.comment}")

                # For pending orders, try with different filling type if not already tried
                if action == mt5.TRADE_ACTION_PENDING and request["type_filling"] != mt5.ORDER_FILLING_IOC:
                    logger.info("Retrying pending order with IOC filling...")
                    request["type_filling"] = mt5.ORDER_FILLING_IOC
                    result = mt5.order_send(request)

                    if result is None or result.retcode != mt5.TRADE_RETCODE_DONE:
                        if result:
                            logger.error(f"Retry failed: {result.retcode} - {result.comment}")
                        return None
                else:
                    return None

            logger.info(f"Order placed successfully: {result.order}")
            return result.order  # Return order ticket

        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return None

    def calculate_position_size(self, risk_amount: float, entry_price: float,
                              stop_loss: float) -> float:
        """
        Calculate position size based on risk amount and stop loss
        
        Args:
            risk_amount: Amount to risk in account currency
            entry_price: Entry price
            stop_loss: Stop loss price
            
        Returns:
            Position size in lots
        """
        try:
            if not self.symbol_info:
                logger.error("Symbol info not available")
                return 0.0
            
            # Calculate risk per unit
            risk_per_unit = abs(entry_price - stop_loss)
            
            # Calculate contract size and tick value
            contract_size = self.symbol_info.trade_contract_size
            tick_value = self.symbol_info.trade_tick_value
            tick_size = self.symbol_info.trade_tick_size
            
            # Calculate position size
            position_size = risk_amount / (risk_per_unit * contract_size * tick_value / tick_size)
            
            # Round to minimum lot size
            min_lot = self.symbol_info.volume_min
            lot_step = self.symbol_info.volume_step
            
            position_size = max(min_lot, round(position_size / lot_step) * lot_step)
            
            return position_size
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.0
    
    def send_order(self, action: str, volume: float, price: float = None, 
                   sl: float = None, tp: float = None, comment: str = "") -> Optional[Dict]:
        """
        Send trading order to MT5
        
        Args:
            action: 'BUY' or 'SELL'
            volume: Position size in lots
            price: Entry price (None for market order)
            sl: Stop loss price
            tp: Take profit price
            comment: Order comment
            
        Returns:
            Order result dictionary or None if failed
        """
        if not self.connected:
            logger.error("Not connected to MT5")
            return None
            
        try:
            # Determine order type
            if action.upper() == 'BUY':
                order_type = mt5.ORDER_TYPE_BUY if price is None else mt5.ORDER_TYPE_BUY_LIMIT
                if price is None:
                    price = mt5.symbol_info_tick(self.symbol).ask
            elif action.upper() == 'SELL':
                order_type = mt5.ORDER_TYPE_SELL if price is None else mt5.ORDER_TYPE_SELL_LIMIT
                if price is None:
                    price = mt5.symbol_info_tick(self.symbol).bid
            else:
                logger.error(f"Invalid action: {action}")
                return None

            # Prepare order request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": self.symbol,
                "volume": volume,
                "type": order_type,
                "price": price,
                "sl": sl,
                "tp": tp,
                "deviation": SLIPPAGE_POINTS,
                "magic": 12345,
                "comment": comment,
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # Send order
            result = mt5.order_send(request)

            # Check if result is valid
            if result is None:
                logger.error("Order failed: mt5.order_send returned None")
                return None

            # Check if result has the expected attributes
            if not hasattr(result, 'retcode'):
                logger.error(f"Order failed: Invalid result object: {result}")
                return None

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"Order failed: {result.retcode} - {result.comment}")
                return None
            
            logger.info(f"Order successful: {action} {volume} lots at {price}")
            return {
                'order': result.order,
                'deal': result.deal,
                'volume': result.volume,
                'price': result.price,
                'comment': result.comment
            }
            
        except Exception as e:
            logger.error(f"Error sending order: {e}")
            return None
    
    def get_positions(self, symbol: str = None) -> List[Dict]:
        """Get current open positions"""
        if not self.connected:
            logger.error("Not connected to MT5")
            return []

        try:
            if symbol:
                positions = mt5.positions_get(symbol=symbol)
            else:
                positions = mt5.positions_get()

            if positions is None:
                return []

            position_list = []
            for pos in positions:
                position_list.append({
                    'ticket': pos.ticket,
                    'symbol': pos.symbol,
                    'type': pos.type,  # 0 = BUY, 1 = SELL
                    'volume': pos.volume,
                    'price_open': pos.price_open,
                    'price_current': pos.price_current,
                    'sl': pos.sl,
                    'tp': pos.tp,
                    'profit': pos.profit,
                    'comment': pos.comment,
                    'time': pos.time
                })

            return position_list

        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []
    
    def close_position(self, ticket: int, symbol: str, volume: float) -> bool:
        """Close position by ticket"""
        if not self.connected:
            logger.error("Not connected to MT5")
            return False

        try:
            positions = mt5.positions_get(ticket=ticket)
            if not positions:
                logger.error(f"Position {ticket} not found")
                return False

            position = positions[0]

            # Determine close action
            if position.type == 0:  # Buy position
                order_type = mt5.ORDER_TYPE_SELL
                price = mt5.symbol_info_tick(symbol).bid
            else:  # Sell position
                order_type = mt5.ORDER_TYPE_BUY
                price = mt5.symbol_info_tick(symbol).ask

            # Close request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": order_type,
                "position": ticket,
                "price": price,
                "deviation": SLIPPAGE_POINTS,
                "magic": 12345,
                "comment": "Close by system",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            result = mt5.order_send(request)

            # Check if result is valid
            if result is None:
                logger.error("Close position failed: mt5.order_send returned None")
                return False

            # Check if result has the expected attributes
            if not hasattr(result, 'retcode'):
                logger.error(f"Close position failed: Invalid result object: {result}")
                return False

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"Close position failed: {result.retcode} - {result.comment}")
                return False
            
            logger.info(f"Position {ticket} closed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error closing position: {e}")
            return False

    def modify_position(self, ticket: int, stop_loss: float = None, take_profit: float = None) -> bool:
        """Modify position stop loss and/or take profit with comprehensive debugging"""
        if not self.connected:
            logger.error("Not connected to MT5")
            return False

        try:
            # Get position info
            positions = mt5.positions_get(ticket=ticket)
            if not positions:
                logger.error(f"Position {ticket} not found")
                return False

            position = positions[0]
            symbol = position.symbol

            # Log current position state
            logger.info(f"🔍 MODIFY POSITION DEBUG - BEFORE:")
            logger.info(f"   Ticket: {ticket}")
            logger.info(f"   Symbol: {symbol}")
            logger.info(f"   Type: {'BUY' if position.type == 0 else 'SELL'}")
            logger.info(f"   Volume: {position.volume}")
            logger.info(f"   Current SL: {position.sl}")
            logger.info(f"   Current TP: {position.tp}")
            logger.info(f"   Entry Price: {position.price_open}")

            # Get current market price for distance validation
            tick = mt5.symbol_info_tick(symbol)
            if tick:
                current_price = tick.bid if position.type == 1 else tick.ask  # Use bid for SELL, ask for BUY
                logger.info(f"   Current Price: {current_price}")

                # Calculate distance from current price to requested SL
                if stop_loss is not None:
                    sl_distance = abs(current_price - stop_loss)
                    logger.info(f"   Requested SL: {stop_loss}")
                    logger.info(f"   SL Distance: {sl_distance:.5f}")

                    # CRITICAL: Validate SL is on correct side of market
                    if position.type == 0:  # BUY position
                        if stop_loss >= current_price:
                            logger.error(f"❌ INVALID SL SIDE: BUY position SL {stop_loss:.5f} >= current price {current_price:.5f}")
                            logger.error(f"   BUY stop loss must be BELOW current price")
                            logger.error(f"   Suggested max SL: {current_price - 0.01:.5f}")
                            return False
                        else:
                            logger.info(f"✅ SL SIDE OK: BUY SL {stop_loss:.5f} < current price {current_price:.5f}")
                    else:  # SELL position (type == 1)
                        if stop_loss <= current_price:
                            logger.error(f"❌ INVALID SL SIDE: SELL position SL {stop_loss:.5f} <= current price {current_price:.5f}")
                            logger.error(f"   SELL stop loss must be ABOVE current price")
                            logger.error(f"   Suggested min SL: {current_price + 0.01:.5f}")
                            return False
                        else:
                            logger.info(f"✅ SL SIDE OK: SELL SL {stop_loss:.5f} > current price {current_price:.5f}")

                    # Get symbol info for minimum distance requirements
                    symbol_info = mt5.symbol_info(symbol)
                    if symbol_info:
                        stops_level = symbol_info.trade_stops_level
                        point = symbol_info.point
                        min_distance = stops_level * point
                        logger.info(f"   Min SL Distance Required: {min_distance:.5f} ({stops_level} points)")

                        if sl_distance < min_distance:
                            logger.error(f"❌ SL too close to market! Distance {sl_distance:.5f} < Required {min_distance:.5f}")
                            logger.error(f"   Current price: {current_price:.5f}, Requested SL: {stop_loss:.5f}")
                            logger.error(f"   MT5 requires minimum {stops_level} points ({min_distance:.5f} price units)")
                            logger.error(f"   Cannot modify position - MT5 will reject this request")
                            return False

            # Prepare modification request (NOTE: SLTP action doesn't need volume)
            request = {
                "action": mt5.TRADE_ACTION_SLTP,
                "symbol": symbol,
                "position": ticket,
                "magic": 12345,
            }
            # NOTE: Do NOT include volume in SLTP requests - it causes "Invalid stops" error

            # Add stop loss if provided
            if stop_loss is not None:
                # Round to appropriate decimal places and ensure it's a proper float
                rounded_sl = round(float(stop_loss), 5)  # 5 decimal places max for most symbols
                request["sl"] = rounded_sl
                logger.info(f"   Rounded SL: {stop_loss} → {rounded_sl}")
            else:
                request["sl"] = position.sl  # Keep existing SL

            # Add take profit if provided
            if take_profit is not None:
                request["tp"] = float(take_profit)  # Ensure it's a float
            else:
                request["tp"] = position.tp  # Keep existing TP

            logger.info(f"🔍 MODIFY REQUEST:")
            logger.info(f"   Action: TRADE_ACTION_SLTP")
            logger.info(f"   Symbol: {request['symbol']}")
            logger.info(f"   Position: {request['position']}")
            logger.info(f"   SL: {request['sl']}")
            logger.info(f"   TP: {request['tp']}")

            result = mt5.order_send(request)

            # Check if result is valid
            if result is None:
                logger.error("Modify position failed: mt5.order_send returned None")
                return False

            # Check if result has the expected attributes
            if not hasattr(result, 'retcode'):
                logger.error(f"Modify position failed: Invalid result object: {result}")
                return False

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                # Provide more specific error messages
                if result.retcode == 10025:  # No changes
                    logger.warning(f"Modify position failed: {result.retcode} - No changes (new values same as current)")
                    logger.info(f"   Current position SL: {position.sl}, TP: {position.tp}")
                    logger.info(f"   Requested SL: {request['sl']}, TP: {request['tp']}")
                else:
                    logger.error(f"Modify position failed: {result.retcode} - {result.comment}")
                    logger.error(f"   Full result: {result}")
                return False

            logger.info(f"✅ MT5 REPORTS SUCCESS: Position {ticket} modification accepted")

            # CRITICAL: Verify the modification actually took effect
            import time
            time.sleep(0.5)  # Give MT5 time to process

            # Re-query the position to verify changes
            updated_positions = mt5.positions_get(ticket=ticket)
            if updated_positions:
                updated_position = updated_positions[0]
                logger.info(f"🔍 MODIFY POSITION DEBUG - AFTER:")
                logger.info(f"   Updated SL: {updated_position.sl}")
                logger.info(f"   Updated TP: {updated_position.tp}")
                logger.info(f"   Requested SL: {request['sl']}")
                logger.info(f"   Requested TP: {request['tp']}")

                # Check if SL was actually set correctly
                if stop_loss is not None:
                    sl_diff = abs(updated_position.sl - request['sl'])
                    # Use more lenient threshold for XAUUSD (0.01 = 1 pip)
                    tolerance = 0.01  # 1 pip tolerance for XAUUSD
                    if sl_diff > tolerance:
                        # CRITICAL FIX: Don't return False if MT5 reported success
                        # Log as warning but trust MT5's success report
                        logger.warning(f"⚠️ SL VERIFICATION MISMATCH (but MT5 reported success):")
                        logger.warning(f"   Expected SL: {request['sl']}")
                        logger.warning(f"   Actual SL: {updated_position.sl}")
                        logger.warning(f"   Difference: {sl_diff} (tolerance: {tolerance})")
                        logger.info(f"🔄 Trusting MT5 success report - continuing as successful")
                    else:
                        if sl_diff > 0.00001:  # Log minor differences but don't fail
                            logger.info(f"✅ SL VERIFICATION PASSED: {updated_position.sl} (minor diff: {sl_diff:.6f})")
                        else:
                            logger.info(f"✅ SL VERIFICATION PASSED: {updated_position.sl}")

                # Check if TP was actually set correctly
                if take_profit is not None:
                    tp_diff = abs(updated_position.tp - request['tp'])
                    tolerance = 0.01  # 1 pip tolerance
                    if tp_diff > tolerance:
                        # CRITICAL FIX: Don't return False if MT5 reported success
                        # Log as warning but trust MT5's success report
                        logger.warning(f"⚠️ TP VERIFICATION MISMATCH (but MT5 reported success):")
                        logger.warning(f"   Expected TP: {request['tp']}")
                        logger.warning(f"   Actual TP: {updated_position.tp}")
                        logger.warning(f"   Difference: {tp_diff} (tolerance: {tolerance})")
                        logger.info(f"🔄 Trusting MT5 success report - continuing as successful")
                    else:
                        if tp_diff > 0.00001:  # Log minor differences but don't fail
                            logger.info(f"✅ TP VERIFICATION PASSED: {updated_position.tp} (minor diff: {tp_diff:.6f})")
                        else:
                            logger.info(f"✅ TP VERIFICATION PASSED: {updated_position.tp}")

            else:
                logger.error(f"❌ Could not re-query position {ticket} for verification")
                return False

            logger.info(f"🎉 POSITION MODIFICATION FULLY VERIFIED!")
            return True

        except Exception as e:
            logger.error(f"Error modifying position: {e}")
            return False

    def get_order_info(self, ticket: int) -> Optional[Dict]:
        """Get information about a specific order"""
        if not self.connected:
            logger.error("Not connected to MT5")
            return None

        try:
            orders = mt5.orders_get(ticket=ticket)
            if not orders:
                return None

            order = orders[0]
            return {
                'ticket': order.ticket,
                'type': order.type,
                'symbol': order.symbol,
                'volume': order.volume_initial,
                'price_open': order.price_open,
                'sl': order.sl,
                'tp': order.tp,
                'time_setup': order.time_setup,
                'comment': order.comment
            }

        except Exception as e:
            logger.error(f"Error getting order info: {e}")
            return None

    def cancel_order(self, ticket: int) -> bool:
        """Cancel a pending order"""
        if not self.connected:
            logger.error("Not connected to MT5")
            return False

        try:
            request = {
                "action": mt5.TRADE_ACTION_REMOVE,
                "order": ticket,
            }

            result = mt5.order_send(request)

            # Check if result is valid
            if result is None:
                logger.error("Cancel order failed: mt5.order_send returned None")
                return False

            # Check if result has the expected attributes
            if not hasattr(result, 'retcode'):
                logger.error(f"Cancel order failed: Invalid result object: {result}")
                return False

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"Cancel order failed: {result.retcode} - {result.comment}")
                return False

            logger.info(f"Order {ticket} cancelled successfully")
            return True

        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return False
