#!/usr/bin/env python3
"""
Test script to verify candle strength decision logic
"""

import sys
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5<PERSON>anager
from fixed_live_trader import FixedFeatureEngineer, RegimeDetector

def test_candle_strength_decision():
    """Test the new candle strength decision logic"""
    print("🧪 TESTING CANDLE STRENGTH DECISION LOGIC...")
    
    # Initialize components
    mt5_manager = MT5Manager()
    feature_engineer = FixedFeatureEngineer()
    regime_detector = RegimeDetector()
    
    # Connect to MT5
    if not mt5_manager.connect():
        print("❌ Cannot connect to MT5")
        return
    
    try:
        df = mt5_manager.get_latest_data("XAUUSD!", "M5", 100)
        if df is None or len(df) < 50:
            print("❌ Cannot get sufficient data")
            return
        
        print(f"📈 Got {len(df)} candles")
        
        # Create features
        features_df = feature_engineer.create_technical_indicators(df)
        features_df = regime_detector.calculate_regime_indicators(features_df)
        
        print(f"\n🎯 TESTING CANDLE STRENGTH DECISION:")
        print("=" * 80)
        
        # Test last 20 candles to see signal distribution
        buy_signals = 0
        sell_signals = 0
        no_signals = 0
        
        for i in range(-20, 0):
            try:
                # Calculate candle strength for this candle
                candle_strength = regime_detector.calculate_recent_candle_strength(features_df.iloc[:i+len(features_df)])
                candle_net_strength = candle_strength['net_strength'] * 100  # Convert to percentage
                
                # Apply decision logic
                if candle_net_strength > 30:
                    signal = "BUY"
                    confidence = min(abs(candle_net_strength) / 100, 1.0)
                    buy_signals += 1
                elif candle_net_strength < -30:
                    signal = "SELL"
                    confidence = min(abs(candle_net_strength) / 100, 1.0)
                    sell_signals += 1
                else:
                    signal = "NONE"
                    confidence = 0.0
                    no_signals += 1
                
                # Get timestamp and price
                timestamp = features_df.index[i].strftime("%H:%M")
                close_price = features_df['close'].iloc[i]
                
                print(f"{timestamp} | Close: {close_price:7.2f} | Strength: {candle_net_strength:+6.1f}% | Signal: {signal:4s} | Conf: {confidence:.3f}")
                
            except Exception as e:
                print(f"Error processing candle {i}: {e}")
        
        print(f"\n📊 SIGNAL DISTRIBUTION (Last 20 candles):")
        print("=" * 80)
        print(f"BUY signals:  {buy_signals:2d} ({buy_signals/20*100:5.1f}%)")
        print(f"SELL signals: {sell_signals:2d} ({sell_signals/20*100:5.1f}%)")
        print(f"NO signals:   {no_signals:2d} ({no_signals/20*100:5.1f}%)")
        
        if buy_signals > 0 and sell_signals > 0:
            print("✅ BALANCED: System generates both BUY and SELL signals!")
        elif buy_signals > 0 and sell_signals == 0:
            print("⚠️  BIAS: Only BUY signals generated")
        elif sell_signals > 0 and buy_signals == 0:
            print("⚠️  BIAS: Only SELL signals generated")
        else:
            print("⚠️  NO SIGNALS: Thresholds might be too strict")
        
        # Test current candle
        print(f"\n🎯 CURRENT CANDLE ANALYSIS:")
        print("=" * 80)
        
        current_candle_strength = regime_detector.calculate_recent_candle_strength(features_df)
        current_net_strength = current_candle_strength['net_strength'] * 100
        
        if current_net_strength > 30:
            current_signal = "BUY"
            current_confidence = min(abs(current_net_strength) / 100, 1.0)
        elif current_net_strength < -30:
            current_signal = "SELL"
            current_confidence = min(abs(current_net_strength) / 100, 1.0)
        else:
            current_signal = "NONE"
            current_confidence = 0.0
        
        current_price = features_df['close'].iloc[-1]
        current_time = features_df.index[-1].strftime("%H:%M")
        
        print(f"Time: {current_time}")
        print(f"Price: {current_price:.2f}")
        print(f"Candle Strength: {current_net_strength:+.1f}%")
        print(f"Signal: {current_signal}")
        print(f"Confidence: {current_confidence:.3f}")
        print(f"Dominant Bias: {current_candle_strength['dominant_bias']}")
        print(f"Bullish Strength: {current_candle_strength['bullish_strength']:.3f}")
        print(f"Bearish Strength: {current_candle_strength['bearish_strength']:.3f}")
        
        # Test threshold sensitivity
        print(f"\n🔍 THRESHOLD SENSITIVITY TEST:")
        print("=" * 80)
        
        thresholds = [10, 20, 30, 40, 50]
        for threshold in thresholds:
            buy_count = 0
            sell_count = 0
            none_count = 0
            
            for i in range(-20, 0):
                try:
                    candle_strength = regime_detector.calculate_recent_candle_strength(features_df.iloc[:i+len(features_df)])
                    strength_pct = candle_strength['net_strength'] * 100
                    
                    if strength_pct > threshold:
                        buy_count += 1
                    elif strength_pct < -threshold:
                        sell_count += 1
                    else:
                        none_count += 1
                except:
                    none_count += 1
            
            print(f"Threshold ±{threshold:2d}%: BUY={buy_count:2d} | SELL={sell_count:2d} | NONE={none_count:2d}")
        
        print(f"\n✅ CANDLE STRENGTH DECISION LOGIC TEST COMPLETE!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        mt5_manager.disconnect()

if __name__ == "__main__":
    test_candle_strength_decision()
