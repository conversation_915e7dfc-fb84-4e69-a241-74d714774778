#!/usr/bin/env python3
"""
Comprehensive test script to validate all the changes made to the trading system
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager
from fixed_live_trader import FixedLiveTrader

def test_timing_synchronization():
    """Test the timing synchronization logic"""
    print("⏰ Testing Timing Synchronization...")
    
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test wait_for_candle_close method
    wait_seconds, next_candle_close = trader.wait_for_candle_close()
    current_time = datetime.now()
    
    print(f"   Current time: {current_time.strftime('%H:%M:%S')}")
    print(f"   Next candle close: {next_candle_close.strftime('%H:%M:%S')}")
    print(f"   Wait time: {wait_seconds:.1f} seconds")
    
    # Verify it's a 5-minute boundary
    next_minute = next_candle_close.minute
    is_5min_boundary = next_minute % 5 == 0
    
    if is_5min_boundary:
        print("   ✅ Next candle close is on 5-minute boundary")
    else:
        print("   ❌ Next candle close is NOT on 5-minute boundary")
    
    return is_5min_boundary

def test_swing_detection():
    """Test swing point detection"""
    print("\n🔍 Testing Swing Point Detection...")
    
    trader = FixedLiveTrader("XAUUSD!")
    
    if not trader.mt5_manager.connect():
        print("   ❌ Failed to connect to MT5")
        return False
    
    df = trader.mt5_manager.get_latest_data("XAUUSD!", "M5", 30)
    if df is None or len(df) < 20:
        print("   ❌ Failed to get sufficient data")
        trader.mt5_manager.disconnect()
        return False
    
    swing_points = trader.find_recent_swing_points(df)
    trader.mt5_manager.disconnect()
    
    has_swing_points = swing_points['swing_points_available']
    if has_swing_points:
        print("   ✅ Swing point detection working")
        if swing_points['recent_high']:
            print(f"      Recent High: {swing_points['recent_high']:.5f} ({swing_points['recent_high_candles_ago']} candles ago)")
        if swing_points['recent_low']:
            print(f"      Recent Low: {swing_points['recent_low']:.5f} ({swing_points['recent_low_candles_ago']} candles ago)")
    else:
        print("   ⚠️ No swing points found (may be normal in ranging market)")
    
    return True

def test_candle_confirmation_stop():
    """Test candle confirmation trailing stop logic"""
    print("\n🎯 Testing Candle Confirmation Trailing Stop...")
    
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test the method exists and has correct parameters
    try:
        # Create mock confirmation candle data
        confirmation_candle_data = {
            'high': 4190.50,
            'low': 4188.20,
            'close': 4189.10
        }
        
        # Test that the method exists (won't actually execute without position)
        result = trader.set_candle_confirmation_trailing_stop(confirmation_candle_data, 'BUY')
        print("   ✅ Candle confirmation trailing stop method exists")
        print("   ✅ Method handles no position gracefully")
        
        # Test revert logic
        result = trader.check_candle_confirmation_stop_revert()
        print("   ✅ Candle confirmation stop revert method exists")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing candle confirmation stop: {e}")
        return False

def test_pending_orders():
    """Test pending order logic"""
    print("\n📋 Testing Pending Order Logic...")
    
    trader = FixedLiveTrader("XAUUSD!")
    
    try:
        # Test pending order methods exist
        confirmation_candle_data = {
            'high': 4190.50,
            'low': 4188.20,
            'close': 4189.10
        }
        
        # Test that methods exist (won't actually place orders without MT5 connection)
        result = trader.place_pending_order_from_confirmation_candle('BUY', confirmation_candle_data, 0.01, 1.0)
        print("   ✅ Pending order placement method exists")
        
        # Test expired order checking
        trader.check_and_remove_expired_pending_orders()
        print("   ✅ Expired order checking method exists")
        
        # Test filled order checking
        trader.check_pending_orders_filled()
        print("   ✅ Filled order checking method exists")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing pending orders: {e}")
        return False

def test_system_integration():
    """Test overall system integration"""
    print("\n🔧 Testing System Integration...")
    
    try:
        trader = FixedLiveTrader("XAUUSD!")
        
        # Test initialization
        print("   ✅ System initializes correctly")
        
        # Test that new tracking variables exist
        assert hasattr(trader, 'candle_confirmation_stop'), "Missing candle_confirmation_stop attribute"
        assert hasattr(trader, 'pending_orders'), "Missing pending_orders attribute"
        print("   ✅ New tracking variables initialized")
        
        # Test that methods exist
        required_methods = [
            'wait_for_candle_close',
            'set_candle_confirmation_trailing_stop',
            'check_candle_confirmation_stop_revert',
            'place_pending_order_from_confirmation_candle',
            'check_and_remove_expired_pending_orders',
            'check_pending_orders_filled'
        ]
        
        for method_name in required_methods:
            assert hasattr(trader, method_name), f"Missing method: {method_name}"
        
        print("   ✅ All required methods exist")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error in system integration test: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 COMPREHENSIVE TRADING SYSTEM TEST")
    print("=" * 50)
    
    results = []
    
    # Test 1: Timing synchronization
    results.append(test_timing_synchronization())
    
    # Test 2: Swing detection
    results.append(test_swing_detection())
    
    # Test 3: Candle confirmation stops
    results.append(test_candle_confirmation_stop())
    
    # Test 4: Pending orders
    results.append(test_pending_orders())
    
    # Test 5: System integration
    results.append(test_system_integration())
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System is ready for trading.")
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
    
    return passed == total

if __name__ == "__main__":
    main()
