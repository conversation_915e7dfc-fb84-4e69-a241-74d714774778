#!/usr/bin/env python3
"""
Debug EMA Approach Direction Logic

This test helps debug why the EMA test-and-bounce logic is not working as expected.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from fixed_live_trader import FixedLiveTrader

def create_test_data_with_previous(prev_price, current_price, ema_level, atr):
    """Create test DataFrame with previous and current candle data"""
    data = {
        'close': [prev_price, current_price],
        'high': [prev_price + 2, current_price + 5],
        'low': [prev_price - 2, current_price - 5],
        'ema_10': [ema_level, ema_level],
        'ema_20': [ema_level, ema_level],
        'regression_lower': [4300, 4300],
        'regression_upper': [4400, 4400],
        'regression_lower_short': [4320, 4320],
        'regression_upper_short': [4380, 4380],
        'atr': [atr, atr]
    }
    return pd.DataFrame(data)

def debug_specific_case():
    """Debug the specific failing case"""
    print("🔍 DEBUGGING SPECIFIC FAILING CASE")
    print("=" * 50)
    
    trader = FixedLiveTrader("XAUUSD!")
    
    # TEST 2 scenario that's failing
    prev_price = 4355.00  # Below EMA
    current_price = 4363.06  # At EMA
    ema_level = 4363.06
    candle_low = 4350.25  # Below EMA
    candle_high = 4369.40  # Above EMA
    atr = 10.0
    tolerance = atr * 0.05  # 0.5
    
    print(f"📊 SCENARIO DETAILS:")
    print(f"   Previous Close: {prev_price:.2f}")
    print(f"   EMA Level: {ema_level:.2f}")
    print(f"   Current Close: {current_price:.2f}")
    print(f"   Candle Low: {candle_low:.2f}")
    print(f"   Candle High: {candle_high:.2f}")
    print(f"   Tolerance: {tolerance:.2f}")
    
    # Manual logic check
    came_from_above = prev_price > ema_level + tolerance
    came_from_below = prev_price < ema_level - tolerance
    tested_below = candle_low < ema_level - tolerance
    tested_above = candle_high > ema_level + tolerance
    closed_near_ema = abs(current_price - ema_level) <= tolerance
    
    print(f"\n🧮 MANUAL LOGIC CHECK:")
    print(f"   Came from above EMA? {prev_price:.2f} > {ema_level + tolerance:.2f} = {came_from_above}")
    print(f"   Came from below EMA? {prev_price:.2f} < {ema_level - tolerance:.2f} = {came_from_below}")
    print(f"   Tested below EMA? {candle_low:.2f} < {ema_level - tolerance:.2f} = {tested_below}")
    print(f"   Tested above EMA? {candle_high:.2f} > {ema_level + tolerance:.2f} = {tested_above}")
    print(f"   Closed near EMA? |{current_price:.2f} - {ema_level:.2f}| <= {tolerance:.2f} = {closed_near_ema}")
    
    # Apply enhanced logic
    buy_pattern = (
        came_from_above and  # Must come from above
        not came_from_below and  # Must NOT come from below
        tested_below and  # Must test below
        closed_near_ema  # Must close near EMA
    )
    
    sell_pattern = (
        came_from_below and  # Must come from below
        not came_from_above and  # Must NOT come from above
        tested_above and  # Must test above
        closed_near_ema  # Must close near EMA
    )
    
    print(f"\n🎯 ENHANCED LOGIC RESULTS:")
    print(f"   BUY Pattern: {buy_pattern}")
    print(f"   SELL Pattern: {sell_pattern}")
    print(f"   Expected: BUY=False, SELL=False (came from below, tested both directions)")
    
    # Now test with actual implementation
    df = create_test_data_with_previous(prev_price, current_price, ema_level, atr)
    current_candle = {
        'high': candle_high,
        'low': candle_low,
        'close': current_price
    }
    
    confluence = trader._get_support_resistance_confluence(df, current_price, "UP", current_candle)
    
    print(f"\n🔧 ACTUAL IMPLEMENTATION RESULTS:")
    print(f"   BuyConf: {confluence['buy_confluence']:.3f}")
    print(f"   SellConf: {confluence['sell_confluence']:.3f}")
    print(f"   BUY Detected: {confluence['buy_confluence'] > 0.5}")
    print(f"   SELL Detected: {confluence['sell_confluence'] > 0.5}")
    
    # Check if there's a mismatch
    manual_buy = buy_pattern
    manual_sell = sell_pattern
    actual_buy = confluence['buy_confluence'] > 0.5
    actual_sell = confluence['sell_confluence'] > 0.5
    
    if manual_buy == actual_buy and manual_sell == actual_sell:
        print(f"   ✅ LOGIC MATCHES - Implementation is correct")
    else:
        print(f"   ❌ LOGIC MISMATCH - Implementation differs from manual logic")
        print(f"      Manual: BUY={manual_buy}, SELL={manual_sell}")
        print(f"      Actual: BUY={actual_buy}, SELL={actual_sell}")

def test_pure_patterns():
    """Test pure patterns without mixed signals"""
    print(f"\n🧪 TESTING PURE PATTERNS")
    print("=" * 50)
    
    trader = FixedLiveTrader("XAUUSD!")
    ema_level = 4363.06
    atr = 10.0
    
    test_cases = [
        {
            'name': 'Pure BUY Pattern',
            'prev_price': 4370.00,  # Above EMA
            'current_price': 4363.06,  # At EMA
            'candle_low': 4350.25,  # Below EMA (tested)
            'candle_high': 4365.00,  # Just slightly above EMA (no significant test)
            'expected_buy': True,
            'expected_sell': False
        },
        {
            'name': 'Pure SELL Pattern',
            'prev_price': 4355.00,  # Below EMA
            'current_price': 4363.06,  # At EMA
            'candle_low': 4361.00,  # Just slightly below EMA (no significant test)
            'candle_high': 4375.00,  # Above EMA (tested)
            'expected_buy': False,
            'expected_sell': True
        },
        {
            'name': 'No Pattern (from above, no test)',
            'prev_price': 4370.00,  # Above EMA
            'current_price': 4363.06,  # At EMA
            'candle_low': 4362.00,  # No significant test below
            'candle_high': 4364.00,  # No significant test above
            'expected_buy': False,
            'expected_sell': False
        }
    ]
    
    for case in test_cases:
        print(f"\n🔍 {case['name']}:")
        
        df = create_test_data_with_previous(case['prev_price'], case['current_price'], ema_level, atr)
        current_candle = {
            'high': case['candle_high'],
            'low': case['candle_low'],
            'close': case['current_price']
        }
        
        confluence = trader._get_support_resistance_confluence(df, case['current_price'], "UP", current_candle)
        
        actual_buy = confluence['buy_confluence'] > 0.5
        actual_sell = confluence['sell_confluence'] > 0.5
        
        print(f"   Expected: BUY={case['expected_buy']}, SELL={case['expected_sell']}")
        print(f"   Actual: BUY={actual_buy}, SELL={actual_sell}")
        print(f"   BuyConf: {confluence['buy_confluence']:.3f}, SellConf: {confluence['sell_confluence']:.3f}")
        
        if actual_buy == case['expected_buy'] and actual_sell == case['expected_sell']:
            print(f"   ✅ CORRECT")
        else:
            print(f"   ❌ INCORRECT")

def main():
    """Run debug tests"""
    print("🚀 EMA APPROACH DIRECTION DEBUG")
    print("=" * 50)
    
    debug_specific_case()
    test_pure_patterns()
    
    print(f"\n💡 DEBUGGING COMPLETE")
    print(f"   This helps identify where the logic diverges between manual and implementation")

if __name__ == "__main__":
    main()
