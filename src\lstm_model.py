"""
LSTM Model Architecture
Implements LSTM neural network for XAUUSD trading predictions
"""
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization, Input
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from tensorflow.keras.regularizers import l1_l2
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
import os
from pathlib import Path

from config.config import *

# Set up logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

# Set random seeds for reproducibility
tf.random.set_seed(42)
np.random.seed(42)

class LSTMTradingModel:
    """
    LSTM model for trading signal prediction
    """
    
    def __init__(self, input_shape: Tuple[int, int], num_classes: int = 3):
        """
        Initialize LSTM model
        
        Args:
            input_shape: (sequence_length, num_features)
            num_classes: Number of output classes (Hold=0, Buy=1, Sell=2)
        """
        self.input_shape = input_shape
        self.num_classes = num_classes
        self.model = None
        self.history = None
        self.callbacks = []
        
    def build_model(self) -> Model:
        """
        Build LSTM model architecture
        
        Returns:
            Compiled Keras model
        """
        try:
            logger.info("Building LSTM model architecture...")
            
            # Input layer
            inputs = Input(shape=self.input_shape, name='input_layer')
            
            # First LSTM layer
            x = LSTM(
                units=MODEL_CONFIG['lstm_units'][0],
                return_sequences=True,
                dropout=MODEL_CONFIG['dropout_rate'],
                recurrent_dropout=MODEL_CONFIG['recurrent_dropout'],
                kernel_regularizer=l1_l2(l1=0.01, l2=0.01),
                name='lstm_1'
            )(inputs)
            x = BatchNormalization(name='batch_norm_1')(x)
            
            # Second LSTM layer
            x = LSTM(
                units=MODEL_CONFIG['lstm_units'][1],
                return_sequences=True,
                dropout=MODEL_CONFIG['dropout_rate'],
                recurrent_dropout=MODEL_CONFIG['recurrent_dropout'],
                kernel_regularizer=l1_l2(l1=0.01, l2=0.01),
                name='lstm_2'
            )(x)
            x = BatchNormalization(name='batch_norm_2')(x)
            
            # Third LSTM layer (no return_sequences for final layer)
            x = LSTM(
                units=MODEL_CONFIG['lstm_units'][2],
                return_sequences=False,
                dropout=MODEL_CONFIG['dropout_rate'],
                recurrent_dropout=MODEL_CONFIG['recurrent_dropout'],
                kernel_regularizer=l1_l2(l1=0.01, l2=0.01),
                name='lstm_3'
            )(x)
            x = BatchNormalization(name='batch_norm_3')(x)
            
            # Dense layers
            x = Dense(
                units=MODEL_CONFIG['dense_units'][0],
                activation='relu',
                kernel_regularizer=l1_l2(l1=0.01, l2=0.01),
                name='dense_1'
            )(x)
            x = Dropout(MODEL_CONFIG['dropout_rate'], name='dropout_1')(x)
            x = BatchNormalization(name='batch_norm_4')(x)
            
            x = Dense(
                units=MODEL_CONFIG['dense_units'][1],
                activation='relu',
                kernel_regularizer=l1_l2(l1=0.01, l2=0.01),
                name='dense_2'
            )(x)
            x = Dropout(MODEL_CONFIG['dropout_rate'], name='dropout_2')(x)
            
            # Output layer
            if self.num_classes == 2:
                # Binary classification
                outputs = Dense(1, activation='sigmoid', name='output')(x)
                loss = 'binary_crossentropy'
                metrics = ['accuracy', 'precision', 'recall']
            else:
                # Multi-class classification
                outputs = Dense(self.num_classes, activation='softmax', name='output')(x)
                loss = 'sparse_categorical_crossentropy'
                metrics = ['accuracy', 'sparse_categorical_accuracy']
            
            # Create model
            model = Model(inputs=inputs, outputs=outputs, name='LSTM_Trading_Model')
            
            # Compile model
            optimizer = Adam(learning_rate=MODEL_CONFIG['learning_rate'])
            model.compile(
                optimizer=optimizer,
                loss=loss,
                metrics=metrics
            )
            
            # Print model summary
            model.summary()
            logger.info("Model built and compiled successfully")
            
            self.model = model
            return model
            
        except Exception as e:
            logger.error(f"Error building model: {e}")
            return None
    
    def setup_callbacks(self, model_save_path: str) -> List:
        """
        Setup training callbacks
        
        Args:
            model_save_path: Path to save best model
            
        Returns:
            List of callbacks
        """
        try:
            callbacks = []
            
            # Early stopping
            early_stopping = EarlyStopping(
                monitor='val_loss',
                patience=MODEL_CONFIG['early_stopping_patience'],
                restore_best_weights=True,
                verbose=1
            )
            callbacks.append(early_stopping)
            
            # Reduce learning rate on plateau
            reduce_lr = ReduceLROnPlateau(
                monitor='val_loss',
                factor=MODEL_CONFIG['reduce_lr_factor'],
                patience=MODEL_CONFIG['reduce_lr_patience'],
                min_lr=1e-7,
                verbose=1
            )
            callbacks.append(reduce_lr)
            
            # Model checkpoint
            checkpoint = ModelCheckpoint(
                filepath=model_save_path,
                monitor='val_loss',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            )
            callbacks.append(checkpoint)
            
            self.callbacks = callbacks
            logger.info(f"Setup {len(callbacks)} training callbacks")
            
            return callbacks
            
        except Exception as e:
            logger.error(f"Error setting up callbacks: {e}")
            return []
    
    def train(self, X_train: np.ndarray, y_train: np.ndarray,
              X_val: np.ndarray, y_val: np.ndarray,
              class_weights: Dict = None) -> Dict:
        """
        Train the LSTM model
        
        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets
            class_weights: Class weights for imbalanced data
            
        Returns:
            Training history dictionary
        """
        try:
            if self.model is None:
                logger.error("Model not built. Call build_model() first.")
                return {}
            
            logger.info("Starting model training...")
            logger.info(f"Training samples: {len(X_train)}")
            logger.info(f"Validation samples: {len(X_val)}")
            
            # Setup callbacks
            model_save_path = MODELS_DIR / "best_lstm_model.h5"
            callbacks = self.setup_callbacks(str(model_save_path))
            
            # Train model
            history = self.model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=MODEL_CONFIG['epochs'],
                batch_size=MODEL_CONFIG['batch_size'],
                callbacks=callbacks,
                class_weight=class_weights,
                verbose=1,
                shuffle=False  # Don't shuffle time series data
            )
            
            self.history = history
            logger.info("Model training completed")
            
            return history.history
            
        except Exception as e:
            logger.error(f"Error training model: {e}")
            return {}
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions using trained model
        
        Args:
            X: Input features
            
        Returns:
            Predictions array
        """
        try:
            if self.model is None:
                logger.error("Model not trained. Train model first.")
                return np.array([])
            
            predictions = self.model.predict(X, verbose=0)
            
            # Convert probabilities to class predictions for multi-class
            if self.num_classes > 2:
                predictions = np.argmax(predictions, axis=1)
            else:
                predictions = (predictions > 0.5).astype(int).flatten()
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error making predictions: {e}")
            return np.array([])
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """
        Get prediction probabilities
        
        Args:
            X: Input features
            
        Returns:
            Prediction probabilities
        """
        try:
            if self.model is None:
                logger.error("Model not trained. Train model first.")
                return np.array([])
            
            probabilities = self.model.predict(X, verbose=0)
            return probabilities
            
        except Exception as e:
            logger.error(f"Error getting prediction probabilities: {e}")
            return np.array([])
    
    def save_model(self, filepath: str):
        """
        Save trained model
        
        Args:
            filepath: Path to save model
        """
        try:
            if self.model is None:
                logger.error("No model to save")
                return
            
            self.model.save(filepath)
            logger.info(f"Model saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving model: {e}")
    
    def load_model(self, filepath: str):
        """
        Load trained model
        
        Args:
            filepath: Path to load model from
        """
        try:
            if not os.path.exists(filepath):
                logger.error(f"Model file not found: {filepath}")
                return
            
            self.model = tf.keras.models.load_model(filepath)
            logger.info(f"Model loaded from {filepath}")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
    
    def get_model_summary(self) -> Dict:
        """
        Get model architecture summary
        
        Returns:
            Dictionary with model information
        """
        try:
            if self.model is None:
                return {}
            
            summary = {
                'total_params': self.model.count_params(),
                'trainable_params': sum([tf.keras.backend.count_params(w) for w in self.model.trainable_weights]),
                'non_trainable_params': sum([tf.keras.backend.count_params(w) for w in self.model.non_trainable_weights]),
                'layers': len(self.model.layers),
                'input_shape': self.input_shape,
                'output_shape': self.model.output_shape,
                'optimizer': self.model.optimizer.__class__.__name__,
                'loss_function': self.model.loss
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting model summary: {e}")
            return {}
