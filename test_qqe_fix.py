#!/usr/bin/env python3
"""
Test script to verify QQE parameter fix
This script tests that the QQE indicator now uses the correct parameters
matching the user's TradingView settings
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Add src to path
sys.path.append('src')

# Import QQE Indicator
from qqe_indicator import QQEIndicator

def create_test_data():
    """Create test OHLCV data"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='5T')
    
    # Generate realistic price data
    np.random.seed(42)
    base_price = 2000.0
    prices = []
    current_price = base_price
    
    for i in range(100):
        # Add some trend and noise
        trend = 0.1 * np.sin(i * 0.1)  # Sine wave trend
        noise = np.random.normal(0, 2)  # Random noise
        current_price += trend + noise
        prices.append(current_price)
    
    # Create OHLC from prices
    df = pd.DataFrame(index=dates)
    df['close'] = prices
    
    # Generate realistic OHLC
    df['open'] = df['close'].shift(1).fillna(df['close'].iloc[0])
    df['high'] = df[['open', 'close']].max(axis=1) + np.random.uniform(0, 1, len(df))
    df['low'] = df[['open', 'close']].min(axis=1) - np.random.uniform(0, 1, len(df))
    df['volume'] = np.random.uniform(100, 1000, len(df))
    
    return df

def test_qqe_parameters():
    """Test QQE indicator parameters"""
    print("🧪 Testing QQE Parameter Fix")
    print("=" * 50)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Test 1: Default parameters should match user's TradingView settings
    print("\n📊 Test 1: Default Parameters")
    qqe_default = QQEIndicator()
    
    print(f"   RSI Period: {qqe_default.rsi_period} (should be 7)")
    print(f"   RSI Smoothing: {qqe_default.rsi_smoothing} (should be 5)")
    print(f"   QQE Factor: {qqe_default.qqe_factor} (should be 1.0)")
    print(f"   Threshold: {qqe_default.threshold} (should be 10)")
    
    # Verify parameters
    assert qqe_default.rsi_period == 7, f"RSI period should be 7, got {qqe_default.rsi_period}"
    assert qqe_default.rsi_smoothing == 5, f"RSI smoothing should be 5, got {qqe_default.rsi_smoothing}"
    assert qqe_default.qqe_factor == 1.0, f"QQE factor should be 1.0, got {qqe_default.qqe_factor}"
    assert qqe_default.threshold == 10, f"Threshold should be 10, got {qqe_default.threshold}"
    
    print("   ✅ All default parameters correct!")
    
    # Test 2: Explicit parameters
    print("\n📊 Test 2: Explicit Parameters")
    qqe_explicit = QQEIndicator(
        rsi_period=7,
        rsi_smoothing=5,
        qqe_factor=1.0,
        threshold=10
    )
    
    print(f"   RSI Period: {qqe_explicit.rsi_period}")
    print(f"   RSI Smoothing: {qqe_explicit.rsi_smoothing}")
    print(f"   QQE Factor: {qqe_explicit.qqe_factor}")
    print(f"   Threshold: {qqe_explicit.threshold}")
    print("   ✅ Explicit parameters set correctly!")
    
    # Test 3: Calculate QQE with test data
    print("\n📊 Test 3: QQE Calculation")
    df = create_test_data()
    
    # Calculate QQE
    df_with_qqe = qqe_default.calculate_qqe_bands(df)
    qqe_analysis = qqe_default.get_qqe_analysis(df_with_qqe)
    
    print(f"   Data points: {len(df)}")
    print(f"   Latest RSI: {qqe_analysis.get('rsi', 0):.1f}")
    print(f"   Latest RSI MA: {qqe_analysis.get('rsi_ma', 0):.1f}")
    print(f"   QQE Signal: {qqe_analysis.get('qqe_signal', 0)}")
    print(f"   QQE Trend: {qqe_analysis.get('trend', 0)}")
    print("   ✅ QQE calculation completed successfully!")
    
    print("\n🎯 SUMMARY")
    print("=" * 50)
    print("✅ QQE indicator now uses correct parameters:")
    print("   - RSI Period: 7 (matches TradingView)")
    print("   - QQE Factor: 1.0 (matches TradingView)")
    print("   - RSI Smoothing: 5 ✓")
    print("   - Threshold: 10 ✓")
    print("\n🚀 The QQE signal discrepancy should now be resolved!")
    
    return True

if __name__ == "__main__":
    try:
        test_qqe_parameters()
        print("\n✅ All tests passed!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
