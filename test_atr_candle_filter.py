#!/usr/bin/env python3
"""
Test script for ATR-based candle size filter implementation
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the main trading class
from fixed_live_trader import FixedLiveTrader

def test_candle_significance_filter():
    """Test the new is_significant_candle() function"""
    
    print("🧪 Testing ATR-Based Candle Size Filter")
    print("=" * 50)
    
    # Create a mock trader instance (we only need the method)
    class MockTrader:
        def __init__(self):
            import logging
            self.logger = logging.getLogger(__name__)
            logging.basicConfig(level=logging.INFO)
        
        # Copy the is_significant_candle method from LiveTrader
        def is_significant_candle(self, candle_data, atr_value, min_atr_percentage=0.3, min_body_percentage=0.15, signal_type="ENTRY"):
            """
            ATR-Based Candle Size Filter - Check if candle is significant enough for trading decisions
            """
            try:
                if atr_value is None or atr_value <= 0:
                    return True, "No ATR data - allowing signal", 1.0
                
                # Calculate candle metrics
                candle_range = candle_data['high'] - candle_data['low']
                body_size = abs(candle_data['close'] - candle_data['open'])
                
                # Adjust thresholds based on signal type
                if signal_type == "EXIT":
                    min_atr_percentage = 0.25
                    min_body_percentage = 0.12
                elif signal_type == "TRAILING":
                    min_atr_percentage = 0.20
                    min_body_percentage = 0.10
                
                # Calculate thresholds
                min_range_threshold = atr_value * min_atr_percentage
                min_body_threshold = atr_value * min_body_percentage
                
                # Check significance
                range_significant = candle_range >= min_range_threshold
                body_significant = body_size >= min_body_threshold
                
                # Calculate significance score
                range_score = candle_range / min_range_threshold if min_range_threshold > 0 else 1.0
                body_score = body_size / min_body_threshold if min_body_threshold > 0 else 1.0
                significance_score = (range_score + body_score) / 2.0
                
                is_significant = range_significant and body_significant
                
                # Create detailed reason
                range_atr_pct = (candle_range / atr_value * 100) if atr_value > 0 else 0
                body_atr_pct = (body_size / atr_value * 100) if atr_value > 0 else 0
                
                if is_significant:
                    reason = f"SIGNIFICANT {signal_type}: Range {range_atr_pct:.1f}% ATR (≥{min_atr_percentage*100:.1f}%), Body {body_atr_pct:.1f}% ATR (≥{min_body_percentage*100:.1f}%)"
                else:
                    reason = f"INSIGNIFICANT {signal_type}: Range {range_atr_pct:.1f}% ATR (<{min_atr_percentage*100:.1f}%), Body {body_atr_pct:.1f}% ATR (<{min_body_percentage*100:.1f}%)"
                
                return is_significant, reason, significance_score
                
            except Exception as e:
                return True, f"Error in significance check - allowing signal: {e}", 1.0
    
    trader = MockTrader()
    
    # Test cases
    atr_value = 10.0  # Example ATR value
    
    test_cases = [
        {
            'name': 'Large Significant Candle',
            'candle': {'high': 4360.0, 'low': 4350.0, 'open': 4352.0, 'close': 4358.0},
            'expected': True
        },
        {
            'name': 'Small Insignificant Candle',
            'candle': {'high': 4351.0, 'low': 4350.0, 'open': 4350.2, 'close': 4350.8},
            'expected': False
        },
        {
            'name': 'Medium Candle (Borderline)',
            'candle': {'high': 4353.0, 'low': 4350.0, 'open': 4351.0, 'close': 4352.5},
            'expected': True  # 3.0 range = 30% ATR, 1.5 body = 15% ATR
        },
        {
            'name': 'Doji Candle (Large Range, Small Body)',
            'candle': {'high': 4355.0, 'low': 4350.0, 'open': 4352.5, 'close': 4352.6},
            'expected': False  # Large range but tiny body
        }
    ]
    
    print("\n📊 Test Results:")
    print("-" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        candle = test_case['candle']
        expected = test_case['expected']
        
        # Test for ENTRY signals
        is_sig, reason, score = trader.is_significant_candle(candle, atr_value, signal_type="ENTRY")
        
        status = "✅ PASS" if is_sig == expected else "❌ FAIL"
        
        print(f"\n{i}. {test_case['name']}")
        print(f"   Candle: H:{candle['high']:.1f} L:{candle['low']:.1f} O:{candle['open']:.1f} C:{candle['close']:.1f}")
        print(f"   Range: {candle['high'] - candle['low']:.1f} ({(candle['high'] - candle['low'])/atr_value*100:.1f}% ATR)")
        print(f"   Body: {abs(candle['close'] - candle['open']):.1f} ({abs(candle['close'] - candle['open'])/atr_value*100:.1f}% ATR)")
        print(f"   Result: {is_sig} (Expected: {expected}) - {status}")
        print(f"   Reason: {reason}")
        print(f"   Score: {score:.2f}")
    
    print("\n🎯 Testing Different Signal Types:")
    print("-" * 50)
    
    # Test same candle with different signal types
    test_candle = {'high': 4352.5, 'low': 4350.0, 'open': 4351.0, 'close': 4352.0}
    
    for signal_type in ["ENTRY", "EXIT", "TRAILING"]:
        is_sig, reason, score = trader.is_significant_candle(test_candle, atr_value, signal_type=signal_type)
        print(f"\n{signal_type}: {is_sig} (Score: {score:.2f})")
        print(f"   {reason}")
    
    print("\n✅ ATR-Based Candle Filter Test Complete!")

if __name__ == "__main__":
    test_candle_significance_filter()
