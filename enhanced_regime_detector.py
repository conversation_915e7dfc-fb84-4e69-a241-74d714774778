#!/usr/bin/env python3
"""
Enhanced Regime Detection System
Implements comprehensive 3-tier regime detection with 15 indicators
Based on user specifications for XAUUSD M5 trading

TIER 1: LEADING INDICATORS (60% weight) - PURE PRICE ACTION
TIER 2: CONFIRMATION INDICATORS (30% weight)  
TIER 3: CONTEXT FILTERS (10% weight)

Total: 98 points scoring system
"""

import os
import sys
import pandas as pd
import numpy as np
import time
from datetime import datetime, timezone, timedelta
import logging
import warnings
import pytz
from typing import Dict, Tuple, Optional, List
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager

class MTFDataProvider:
    """Multi-Timeframe Data Provider for real MTF analysis"""

    def __init__(self, use_mt5: bool = True):
        self.use_mt5 = use_mt5
        self.mt5_manager = None

        if self.use_mt5:
            try:
                self.mt5_manager = MT5Manager()
                self.timeframe_map = {
                    'M1': 1,
                    'M5': 5,
                    'M15': 15,
                    'M30': 30,
                    'H1': 60,
                    'H4': 240,
                    'D1': 1440
                }
            except Exception as e:
                logging.warning(f"MT5 not available, using resampling fallback: {e}")
                self.use_mt5 = False

    def get_ohlc_data(self, symbol: str, timeframe: str, bars: int = 100) -> pd.DataFrame:
        """
        Fetch real OHLC data for specific timeframe
        """
        try:
            if self.use_mt5 and self.mt5_manager:
                # Use MT5 for real timeframe data
                return self._get_mt5_data(symbol, timeframe, bars)
            else:
                # Fallback to resampling (better than period multiplication)
                return self._get_resampled_data(symbol, timeframe, bars)

        except Exception as e:
            logging.error(f"Error fetching {timeframe} data: {e}")
            return pd.DataFrame()

    def _get_mt5_data(self, symbol: str, timeframe: str, bars: int) -> pd.DataFrame:
        """Get real timeframe data from MT5"""
        try:
            # Ensure MT5 connection
            if not self.mt5_manager.connected:
                if not self.mt5_manager.connect():
                    raise Exception("Failed to connect to MT5")

            # Get data using MT5Manager - FIXED: Use correct method name
            data = self.mt5_manager.get_latest_data(symbol, timeframe, bars)

            if data is None or len(data) == 0:
                raise Exception(f"No {timeframe} data received from MT5")

            # Data is already a DataFrame from MT5Manager
            df = data.copy()

            # Ensure required columns exist (MT5Manager returns proper format)
            required_cols = ['open', 'high', 'low', 'close']
            for col in required_cols:
                if col not in df.columns:
                    raise Exception(f"Missing required column: {col}")

            return df

        except Exception as e:
            logging.error(f"MT5 data fetch failed: {e}")
            raise

    def _get_resampled_data(self, symbol: str, timeframe: str, bars: int) -> pd.DataFrame:
        """
        Fallback: Resample 5M data to higher timeframes
        This is better than multiplying periods!
        """
        try:
            # Get 5M data first (need more bars for proper resampling)
            bars_needed = bars * self._get_multiplier(timeframe)

            if self.mt5_manager:
                data_5m = self.mt5_manager.get_latest_data(symbol, 'M5', bars_needed)
            else:
                raise Exception("No data source available")

            if data_5m is None or len(data_5m) == 0:
                raise Exception("No 5M data for resampling")

            # Data is already a DataFrame with datetime index from MT5Manager
            df_5m = data_5m.copy()

            # Resample to target timeframe
            resample_rule = self._get_resample_rule(timeframe)

            resampled = df_5m.resample(resample_rule).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last'
            }).dropna()

            # Add volume column if it exists in original data
            if 'volume' in df_5m.columns:
                resampled['volume'] = df_5m.resample(resample_rule)['volume'].sum()

            # Return last N bars (keep datetime index format like MT5Manager)
            return resampled.tail(bars)

        except Exception as e:
            logging.error(f"Resampling failed: {e}")
            return pd.DataFrame()

    def _get_multiplier(self, timeframe: str) -> int:
        """Get multiplier for bars needed"""
        multipliers = {
            '5M': 1,
            '15M': 3,
            '30M': 6,
            '1H': 12,
            '4H': 48,
            '1D': 288
        }
        return multipliers.get(timeframe, 1)

    def _get_resample_rule(self, timeframe: str) -> str:
        """Get pandas resample rule"""
        rules = {
            '5M': '5Min',
            '15M': '15Min',
            '30M': '30Min',
            '1H': '1H',
            '4H': '4H',
            '1D': '1D'
        }
        return rules.get(timeframe, '5Min')

class EconomicCalendarParser:
    """Parse and handle economic calendar with timezone conversion"""
    
    def __init__(self, calendar_file: str = "economic_calendar.txt"):
        self.calendar_file = calendar_file
        self.amsterdam_tz = pytz.timezone('Europe/Amsterdam')  # +2 timezone
        self.system_tz = pytz.timezone('UTC')  # System timezone (can be changed)
        self.events = []
        self.high_impact_currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CHF', 'CAD', 'AUD', 'NZD']
        
    def load_calendar(self) -> bool:
        """Load and parse economic calendar from Amsterdam timezone format"""
        try:
            if not os.path.exists(self.calendar_file):
                logging.warning(f"Economic calendar file not found: {self.calendar_file}")
                return False

            with open(self.calendar_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            self.events = []
            current_date = None
            current_month = None
            current_year = 2025  # Default year
            current_time = None
            current_currency = None

            # Month mapping
            month_map = {'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                        'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12}

            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue

                # Skip header and metadata lines
                if line in ['Date', 'Currency', 'Impact', 'Detail', 'Actual', 'Forecast', 'Previous', 'Graph']:
                    continue
                if line.startswith('Day ') or line == 'All' or 'IMF Meetings' in line:
                    continue

                # Parse day of week (prepare for date on next line)
                if line in ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']:
                    # Next line should be the date
                    continue

                # Parse date (e.g., "Oct 24")
                if any(month in line for month in month_map.keys()):
                    parts = line.split()
                    if len(parts) == 2 and parts[0] in month_map:
                        try:
                            current_month = month_map[parts[0]]
                            current_date = int(parts[1])
                            logging.debug(f"Parsed date: {parts[0]} {parts[1]} -> {current_month}/{current_date}")
                            continue
                        except ValueError:
                            continue

                # Parse time (e.g., "2:30pm", "11:45pm")
                if ':' in line and ('am' in line.lower() or 'pm' in line.lower()):
                    try:
                        time_str = line.lower()
                        # Extract time part
                        time_part = time_str.replace('am', '').replace('pm', '').strip()
                        if ':' in time_part:
                            hour, minute = map(int, time_part.split(':'))
                            # Convert to 24-hour format
                            if 'pm' in time_str and hour != 12:
                                hour += 12
                            elif 'am' in time_str and hour == 12:
                                hour = 0

                            current_time = (hour, minute)
                            logging.debug(f"Parsed time: {line} -> {hour}:{minute:02d}")
                            continue
                    except (ValueError, IndexError):
                        continue

                # Parse currency
                if line in self.high_impact_currencies:
                    current_currency = line
                    logging.debug(f"Parsed currency: {line}")
                    continue

                # Parse event name (when we have date, time, and currency)
                if (current_date and current_month and current_time and current_currency and
                    line not in self.high_impact_currencies and
                    not line.replace('%', '').replace('.', '').replace('-', '').replace('B', '').replace('M', '').replace('K', '').replace('T', '').replace('\t', '').isdigit()):

                    # This looks like an event name
                    try:
                        # Create datetime in Amsterdam timezone
                        event_datetime = self.amsterdam_tz.localize(
                            datetime(current_year, current_month, current_date, current_time[0], current_time[1])
                        )

                        # Determine impact level
                        impact = self._determine_impact_level(line, current_currency)

                        if impact in ['HIGH', 'MEDIUM']:  # Only store high/medium impact events
                            event = {
                                'time': event_datetime,
                                'currency': current_currency,
                                'event': line,
                                'impact': impact
                            }
                            self.events.append(event)
                            logging.debug(f"Added event: {current_currency} {line} at {event_datetime} ({impact})")

                        # Reset currency for next event (keep time and date)
                        current_currency = None

                    except Exception as e:
                        logging.debug(f"Error creating event for line '{line}': {e}")
                        continue

            # Sort events by time
            self.events.sort(key=lambda x: x['time'])

            # Filter future events only - FIXED: Use UTC for consistency
            current_time_check = datetime.now(timezone.utc)
            future_events = [e for e in self.events if e['time'].astimezone(timezone.utc) > current_time_check]

            logging.info(f"Loaded {len(self.events)} total events, {len(future_events)} future events from calendar")
            return True

        except Exception as e:
            logging.error(f"Error loading economic calendar: {e}")
            return False
    
    def _determine_impact_level(self, event_name: str, currency: str) -> str:
        """Determine impact level based on event name and currency"""
        event_lower = event_name.lower()

        # HIGH impact events
        high_impact_keywords = [
            'cpi', 'inflation', 'interest rate', 'gdp', 'employment', 'unemployment',
            'non-farm', 'payrolls', 'fomc', 'fed funds', 'ecb', 'boe', 'boj', 'rba',
            'policy rate', 'monetary policy', 'press conference', 'rate statement'
        ]

        # MEDIUM impact events
        medium_impact_keywords = [
            'pmi', 'retail sales', 'industrial production', 'trade balance',
            'consumer confidence', 'business confidence', 'manufacturing',
            'services', 'housing', 'durable goods'
        ]

        # Check for high impact
        for keyword in high_impact_keywords:
            if keyword in event_lower:
                return 'HIGH'

        # Check for medium impact
        for keyword in medium_impact_keywords:
            if keyword in event_lower:
                return 'MEDIUM'

        return 'LOW'

    def get_next_event(self) -> dict:
        """Get the next upcoming economic event"""
        try:
            # FIXED: Use UTC for consistency with session analysis
            current_time = datetime.now(timezone.utc)

            # Filter future events (convert Amsterdam events to UTC for comparison)
            future_events = [e for e in self.events if e['time'].astimezone(timezone.utc) > current_time]

            if future_events:
                # Return the earliest future event
                return min(future_events, key=lambda x: x['time'])

            return None
        except Exception as e:
            logging.error(f"Error getting next event: {e}")
            return None

    def convert_amsterdam_to_system_time(self, amsterdam_time: datetime) -> datetime:
        """Convert Amsterdam time (+2) to system time"""
        try:
            # Localize to Amsterdam timezone
            amsterdam_dt = self.amsterdam_tz.localize(amsterdam_time)
            # Convert to system timezone
            system_dt = amsterdam_dt.astimezone(self.system_tz)
            return system_dt
        except Exception as e:
            logging.error(f"Error converting timezone: {e}")
            return amsterdam_time

class EnhancedRegimeDetector:
    """
    Enhanced Regime Detection System
    Implements 3-tier scoring system with 15 indicators
    ENHANCED: Real Multi-Timeframe Analysis + Hybrid Breakout Logic
    """

    def __init__(self, symbol: str = "XAUUSD!", timeframe: str = "M5",
                 mtf_mode: bool = True, breakout_mode: str = "HYBRID"):
        self.symbol = symbol
        self.timeframe = timeframe
        self.mtf_mode = mtf_mode  # Enable real MTF analysis
        self.breakout_mode = breakout_mode  # CONSERVATIVE, AGGRESSIVE, HYBRID

        # Initialize MTF data provider
        self.mtf_provider = MTFDataProvider(use_mt5=True)

        # Initialize economic calendar
        self.economic_calendar = EconomicCalendarParser()
        self.economic_calendar.load_calendar()

        # Regime detection parameters
        self.lookback_swing = 20  # For swing structure detection
        self.lookback_momentum = 10  # For momentum analysis
        self.lookback_atr = 14  # For ATR calculations
        self.lookback_range = 50  # For range analysis

        # Session times (UTC) - FIXED: Correct trading hours
        self.sessions = {
            'ASIAN': {'start': 23, 'end': 7},     # 23:00-07:00 UTC (Sydney/Tokyo)
            'LONDON': {'start': 8, 'end': 17},    # 08:00-17:00 UTC (9 hours) - FIXED
            'NY': {'start': 13, 'end': 22}        # 13:00-22:00 UTC (9 hours) - FIXED
        }

        # MTF timeframes for analysis (MT5 format)
        self.mtf_timeframes = ['M5', 'M15', 'H1']

        # Previous regime for persistence
        self.previous_regime = None
        self.previous_confidence = 0.0
        self.regime_duration = 0

        # Regime persistence tracking
        self.regime_history = []
        self.max_history = 10

        # MTF results cache
        self.mtf_cache = {}
        self.cache_timestamp = 0
        self.cache_duration = 60  # Cache for 60 seconds

        # Logging setup
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def detect_regime(self, df: pd.DataFrame) -> Tuple[str, float, Dict]:
        """
        Main regime detection function with optional MTF analysis
        Returns: (regime, confidence, details)
        """
        if len(df) < max(self.lookback_swing, self.lookback_range):
            # Return compatible format for fixed_live_trader.py
            insufficient_details = {
                'trending_score': 0,
                'ranging_score': 0,
                'tier1_scores': {'trending': 0, 'ranging': 0},
                'tier2_scores': {'trending': 0, 'ranging': 0},
                'tier3_scores': {'trending': 0, 'ranging': 0},
                'max_possible_score': 98,
                'score_percentage': 0.0,
                'regime_regression_data': {},
                'regime_regression_100': {}
            }
            return "INSUFFICIENT_DATA", 0.0, insufficient_details

        # Use MTF detection if enabled
        if self.mtf_mode:
            return self.detect_regime_multi_timeframe()
        else:
            return self.detect_regime_single_timeframe(df)

    def detect_regime_multi_timeframe(self) -> Tuple[str, float, Dict]:
        """
        ENHANCED: Real Multi-Timeframe Regime Detection
        Runs full 98-point analysis on each timeframe
        """
        try:
            current_time = time.time()

            # Check cache first
            if (current_time - self.cache_timestamp < self.cache_duration and
                self.mtf_cache):
                self.logger.debug("Using cached MTF results")
                return self._apply_mtf_decision(self.mtf_cache)

            # Fetch data for all timeframes
            mtf_results = {}

            for tf in self.mtf_timeframes:
                try:
                    # Get real timeframe data
                    df = self.mtf_provider.get_ohlc_data(self.symbol, tf, bars=100)

                    if df is None or len(df) < 50:
                        self.logger.warning(f"Insufficient {tf} data")
                        continue

                    # Run full regime detection on this timeframe (SILENT MODE)
                    regime, confidence, details = self.detect_regime_single_timeframe(df, silent_mode=True)

                    mtf_results[tf] = {
                        'regime': regime,
                        'confidence': confidence,
                        'details': details,
                        'data_points': len(df)
                    }

                    self.logger.debug(f"{tf}: {regime} ({confidence:.1f}%)")

                except Exception as e:
                    # Enhanced exception logging for MTF detection
                    import traceback
                    self.logger.error(f"Error analyzing {tf}: {e}")
                    self.logger.error(f"Exception type: {type(e)}")

                    # Check if this is the TRANSITIONAL exception
                    if str(e) == 'TRANSITIONAL':
                        self.logger.error(f"🚨 FOUND TRANSITIONAL EXCEPTION in {tf}! Full traceback:")
                        self.logger.error(traceback.format_exc())

                        # Log MTF data conditions when exception occurs
                        self.logger.error(f"🚨 MTF DATA CONDITIONS AT EXCEPTION ({tf}):")
                        self.logger.error(f"   DataFrame shape: {df.shape if 'df' in locals() else 'N/A'}")
                        self.logger.error(f"   DataFrame columns: {list(df.columns) if 'df' in locals() else 'N/A'}")
                        self.logger.error(f"   Timeframe: {tf}")
                        self.logger.error(f"   Bars requested: {bars}")

                        if 'df' in locals() and len(df) > 0:
                            self.logger.error(f"   First 2 rows of {tf} data:")
                            self.logger.error(f"   {df.head(2).to_string()}")
                            self.logger.error(f"   Last 2 rows of {tf} data:")
                            self.logger.error(f"   {df.tail(2).to_string()}")

                    continue

            # Cache results
            self.mtf_cache = mtf_results
            self.cache_timestamp = current_time

            # Apply MTF decision logic
            return self._apply_mtf_decision(mtf_results)

        except Exception as e:
            self.logger.error(f"MTF detection failed: {e}")
            # Fallback to single timeframe with compatible format
            try:
                df_5m = self.mtf_provider.get_ohlc_data(self.symbol, 'M5', bars=100)
                if df_5m is not None and len(df_5m) > 50:
                    return self.detect_regime_single_timeframe(df_5m)
            except Exception as fallback_error:
                self.logger.error(f"MTF fallback also failed: {fallback_error}")

            # Final fallback - return insufficient data with compatible format
            insufficient_details = {
                'trending_score': 0,
                'ranging_score': 0,
                'tier1_scores': {'trending': 0, 'ranging': 0},
                'tier2_scores': {'trending': 0, 'ranging': 0},
                'tier3_scores': {'trending': 0, 'ranging': 0},
                'max_possible_score': 98,
                'score_percentage': 0.0,
                'regime_regression_data': {},
                'regime_regression_100': {}
            }
            return "INSUFFICIENT_DATA", 0.0, insufficient_details

    def _apply_mtf_decision(self, mtf_results: Dict) -> Tuple[str, float, Dict]:
        """
        Apply Multi-Timeframe filtering and decision logic
        """
        try:
            if not mtf_results:
                # Return compatible format for fixed_live_trader.py
                insufficient_details = {
                    'trending_score': 0,
                    'ranging_score': 0,
                    'tier1_scores': {'trending': 0, 'ranging': 0},
                    'tier2_scores': {'trending': 0, 'ranging': 0},
                    'tier3_scores': {'trending': 0, 'ranging': 0},
                    'max_possible_score': 98,
                    'score_percentage': 0.0,
                    'regime_regression_data': {},
                    'regime_regression_100': {}
                }
                return "INSUFFICIENT_DATA", 0.0, insufficient_details

            # Extract results
            result_5m = mtf_results.get('M5', {})
            result_15m = mtf_results.get('M15', {})
            result_1h = mtf_results.get('H1', {})

            regime_5m = result_5m.get('regime', 'UNKNOWN')
            confidence_5m = result_5m.get('confidence', 0)

            regime_15m = result_15m.get('regime', 'UNKNOWN')
            confidence_15m = result_15m.get('confidence', 0)

            regime_1h = result_1h.get('regime', 'UNKNOWN')
            confidence_1h = result_1h.get('confidence', 0)

            # Calculate MTF alignment score
            mtf_score = self._calculate_mtf_alignment_score(mtf_results)

            # Apply MTF filtering rules
            final_regime, final_confidence = self._apply_mtf_filters(
                regime_5m, confidence_5m,
                regime_15m, confidence_15m,
                regime_1h, confidence_1h,
                mtf_score
            )

            # Extract scores from 5M analysis for compatibility
            details_5m = result_5m.get('details', {})
            trending_score = details_5m.get('trending_score', 0)
            ranging_score = details_5m.get('ranging_score', 0)

            # Log detailed breakdown for MTF final decision
            tier1_scores = details_5m.get('tier1_scores', {'trending': 0, 'ranging': 0})
            tier2_scores = details_5m.get('tier2_scores', {'trending': 0, 'ranging': 0})
            tier3_scores = details_5m.get('tier3_scores', {'trending': 0, 'ranging': 0})

            self._log_detailed_score_breakdown(trending_score, ranging_score, tier1_scores, tier2_scores, tier3_scores)

            # Prepare detailed results with required keys for fixed_live_trader.py compatibility
            details = {
                # MTF specific data
                'mtf_results': mtf_results,
                'mtf_alignment_score': mtf_score,
                'final_regime': final_regime,
                'final_confidence': final_confidence,
                'decision_logic': self._get_mtf_decision_reason(
                    regime_5m, regime_15m, regime_1h, final_regime
                ),
                # Required keys for live trader compatibility
                'trending_score': trending_score,
                'ranging_score': ranging_score,
                'tier1_scores': details_5m.get('tier1_scores', {'trending': 0, 'ranging': 0}),
                'tier2_scores': details_5m.get('tier2_scores', {'trending': 0, 'ranging': 0}),
                'tier3_scores': details_5m.get('tier3_scores', {'trending': 0, 'ranging': 0}),
                'max_possible_score': 98,
                'score_percentage': max(trending_score, ranging_score) / 98 * 100 if trending_score or ranging_score else 0.0
            }

            # Add regression data for compatibility
            if details_5m:
                details.update({
                    'regime_regression_data': details_5m.get('regime_regression_data', {}),
                    'regime_regression_100': details_5m.get('regime_regression_100', {})
                })

            return final_regime, final_confidence, details

        except Exception as e:
            self.logger.error(f"MTF decision logic failed: {e}")
            # Return compatible format even for errors
            error_details = {
                'trending_score': 0,
                'ranging_score': 0,
                'tier1_scores': {'trending': 0, 'ranging': 0},
                'tier2_scores': {'trending': 0, 'ranging': 0},
                'tier3_scores': {'trending': 0, 'ranging': 0},
                'max_possible_score': 98,
                'score_percentage': 0.0,
                'regime_regression_data': {},
                'regime_regression_100': {},
                'error': str(e)
            }
            return "ERROR", 0.0, error_details

    def detect_regime_single_timeframe(self, df: pd.DataFrame, silent_mode: bool = False) -> Tuple[str, float, Dict]:
        """
        Single timeframe regime detection (original logic)
        Args:
            df: Price data DataFrame
            silent_mode: If True, suppress result logging (for MTF analysis)
        """
        try:
            start_time = time.time()

            # Calculate all technical indicators first
            df = self._calculate_technical_indicators(df)

            # TIER 1: Price Action Indicators (60 points)
            try:
                tier1_scores = self._calculate_tier1_scores(df)
            except Exception as tier1_e:
                self.logger.error(f"🚨 TIER 1 CALCULATION FAILED: {tier1_e}")
                if str(tier1_e) == 'TRANSITIONAL':
                    self.logger.error("🚨 TIER 1 is raising TRANSITIONAL exception!")
                raise tier1_e

            # TIER 2: Confirmation Indicators (30 points)
            try:
                tier2_scores = self._calculate_tier2_scores(df)
            except Exception as tier2_e:
                self.logger.error(f"🚨 TIER 2 CALCULATION FAILED: {tier2_e}")
                if str(tier2_e) == 'TRANSITIONAL':
                    self.logger.error("🚨 TIER 2 is raising TRANSITIONAL exception!")
                raise tier2_e

            # TIER 3: Context Filters (10 points)
            try:
                tier3_scores = self._calculate_tier3_scores(df)
            except Exception as tier3_e:
                self.logger.error(f"🚨 TIER 3 CALCULATION FAILED: {tier3_e}")
                if str(tier3_e) == 'TRANSITIONAL':
                    self.logger.error("🚨 TIER 3 is raising TRANSITIONAL exception!")
                raise tier3_e

            # Combine all scores
            trending_score = (tier1_scores['trending'] +
                             tier2_scores['trending'] +
                             tier3_scores['trending'])

            ranging_score = (tier1_scores['ranging'] +
                            tier2_scores['ranging'] +
                            tier3_scores['ranging'])

            # Decision logic
            regime, confidence = self._make_regime_decision(
                trending_score, ranging_score, tier1_scores, tier2_scores, tier3_scores, silent_mode
            )

            # Update persistence tracking
            self._update_persistence(regime, confidence)

            # Calculate regression data for compatibility with fixed_live_trader.py
            regression_data = self._calculate_regression_data(df)

            # Prepare detailed results
            details = {
                'trending_score': trending_score,
                'ranging_score': ranging_score,
                'tier1_scores': tier1_scores,
                'tier2_scores': tier2_scores,
                'tier3_scores': tier3_scores,
                'max_possible_score': 98,
                'score_percentage': max(trending_score, ranging_score) / 98 * 100,
                # Add regression data for fixed_live_trader.py compatibility
                'regime_regression_data': regression_data['regime_20'],
                'regime_regression_100': regression_data['regime_100']
            }

            # Performance logging
            end_time = time.time()
            detection_time = end_time - start_time
            self.logger.debug(f"Single TF regime detection completed in {detection_time:.3f}s")

            return regime, confidence, details

        except Exception as e:
            # Enhanced exception logging to trace TRANSITIONAL exception
            import traceback
            self.logger.error(f"Error in single timeframe detection: {e}")
            self.logger.error(f"Exception type: {type(e)}")
            self.logger.error(f"Exception args: {e.args}")

            # Check if this is the TRANSITIONAL exception we're looking for
            if str(e) == 'TRANSITIONAL':
                self.logger.error("🚨 FOUND TRANSITIONAL EXCEPTION! Full traceback:")
                self.logger.error(traceback.format_exc())

                # Log data conditions when exception occurs
                self.logger.error(f"🚨 DATA CONDITIONS AT EXCEPTION:")
                self.logger.error(f"   DataFrame shape: {df.shape if 'df' in locals() else 'N/A'}")
                self.logger.error(f"   DataFrame columns: {list(df.columns) if 'df' in locals() else 'N/A'}")
                self.logger.error(f"   DataFrame index type: {type(df.index) if 'df' in locals() else 'N/A'}")
                self.logger.error(f"   Silent mode: {silent_mode}")
                self.logger.error(f"   MTF mode: {self.mtf_mode}")
                self.logger.error(f"   MTF cache: {bool(self.mtf_cache)}")

                if 'df' in locals() and len(df) > 0:
                    self.logger.error(f"   Last 3 rows of data:")
                    self.logger.error(f"   {df.tail(3).to_string()}")
            else:
                self.logger.error(f"Full traceback: {traceback.format_exc()}")

            return "ERROR", 0.0, {'error': str(e)}

    # ============================================================================
    # MTF ANALYSIS HELPER METHODS
    # ============================================================================

    def _calculate_mtf_alignment_score(self, mtf_results: Dict) -> int:
        """
        Calculate MTF alignment score (10 points max)
        """
        try:
            score = 0

            regime_5m = mtf_results.get('M5', {}).get('regime', 'UNKNOWN')
            regime_15m = mtf_results.get('M15', {}).get('regime', 'UNKNOWN')
            regime_1h = mtf_results.get('H1', {}).get('regime', 'UNKNOWN')

            confidence_5m = mtf_results.get('M5', {}).get('confidence', 0)
            confidence_15m = mtf_results.get('M15', {}).get('confidence', 0)
            confidence_1h = mtf_results.get('H1', {}).get('confidence', 0)

            # 5M + 15M alignment (5 points)
            if regime_5m == regime_15m and regime_5m != 'UNKNOWN':
                if regime_5m in ['TRENDING', 'STRONG_TRENDING']:
                    score += 5
                elif regime_5m in ['RANGING', 'STRONG_RANGING']:
                    score += 4
                else:  # TRANSITIONAL
                    score += 2
            elif regime_5m == 'TRANSITIONAL' or regime_15m == 'TRANSITIONAL':
                score += 1  # Partial credit for transitional

            # 1H direction alignment (5 points)
            if regime_1h == regime_5m and regime_1h != 'UNKNOWN':
                if regime_1h in ['TRENDING', 'STRONG_TRENDING']:
                    score += 5
                elif regime_1h in ['RANGING', 'STRONG_RANGING']:
                    score += 4
                else:  # TRANSITIONAL
                    score += 2
            elif regime_1h == 'TRANSITIONAL':
                score += 1

            return min(score, 10)  # Cap at 10 points

        except Exception as e:
            self.logger.error(f"Error calculating MTF alignment: {e}")
            return 0

    def _apply_mtf_filters(self, regime_5m: str, confidence_5m: float,
                          regime_15m: str, confidence_15m: float,
                          regime_1h: str, confidence_1h: float,
                          mtf_score: int) -> Tuple[str, float]:
        """
        Apply Multi-Timeframe filtering rules
        """
        try:
            # RULE 1: If 15M + 1H both say RANGING, override 5M TRENDING
            if (regime_5m in ['TRENDING', 'STRONG_TRENDING'] and
                regime_15m in ['RANGING', 'STRONG_RANGING'] and
                regime_1h in ['RANGING', 'STRONG_RANGING'] and
                confidence_15m > 60 and confidence_1h > 60):

                self.logger.info("⚠️ MTF OVERRIDE: 5M trending but 15M+1H ranging - RANGING wins")
                return 'RANGING', confidence_5m * 0.7  # Reduce confidence

            # RULE 2: If 15M + 1H both say TRENDING, boost 5M TRENDING
            if (regime_5m in ['TRENDING', 'STRONG_TRENDING'] and
                regime_15m in ['TRENDING', 'STRONG_TRENDING'] and
                regime_1h in ['TRENDING', 'STRONG_TRENDING']):

                self.logger.info("✅ MTF BOOST: All timeframes trending - HIGH CONFIDENCE")
                return 'STRONG_TRENDING', min(confidence_5m * 1.2, 100)  # Boost confidence

            # RULE 3: If 1H is TRANSITIONAL, reduce 5M confidence
            if regime_1h == 'TRANSITIONAL':
                self.logger.info("⚠️ MTF WARNING: 1H transitional - reducing confidence")
                confidence_5m *= 0.85

            # RULE 4: If 15M conflicts with 5M, go TRANSITIONAL
            if (regime_5m in ['TRENDING', 'STRONG_TRENDING', 'RANGING', 'STRONG_RANGING'] and
                regime_15m in ['TRENDING', 'STRONG_TRENDING', 'RANGING', 'STRONG_RANGING'] and
                self._regimes_conflict(regime_5m, regime_15m) and
                confidence_15m > 70):

                self.logger.info("⚠️ MTF CONFLICT: 5M vs 15M - TRANSITIONAL")
                return 'TRANSITIONAL', 50

            # RULE 5: Apply MTF score bonus
            confidence_modifier = mtf_score * 0.5  # 0-5% bonus
            final_confidence = min(confidence_5m + confidence_modifier, 100)

            return regime_5m, final_confidence

        except Exception as e:
            self.logger.error(f"MTF filtering failed: {e}")
            return regime_5m, confidence_5m

    def _regimes_conflict(self, regime1: str, regime2: str) -> bool:
        """Check if two regimes conflict"""
        trending_regimes = ['TRENDING', 'STRONG_TRENDING']
        ranging_regimes = ['RANGING', 'STRONG_RANGING']

        return ((regime1 in trending_regimes and regime2 in ranging_regimes) or
                (regime1 in ranging_regimes and regime2 in trending_regimes))

    def _get_mtf_decision_reason(self, regime_5m: str, regime_15m: str,
                                regime_1h: str, final_regime: str) -> str:
        """Get human-readable reason for MTF decision"""
        if final_regime == 'STRONG_TRENDING':
            return f"All timeframes aligned: {regime_5m}+{regime_15m}+{regime_1h}"
        elif final_regime == 'RANGING' and regime_5m in ['TRENDING', 'STRONG_TRENDING']:
            return f"Higher TF override: 15M({regime_15m})+1H({regime_1h}) > 5M({regime_5m})"
        elif final_regime == 'TRANSITIONAL':
            return f"MTF conflict: 5M({regime_5m}) vs 15M({regime_15m})"
        else:
            return f"Standard: 5M({regime_5m}) with MTF confirmation"

    def _calculate_regression_data(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """Calculate regression data for compatibility with fixed_live_trader.py"""
        closes = df['close'].values

        # 20-candle regression (long-term)
        if len(closes) >= 20:
            recent_20 = closes[-20:]
            x_20 = np.arange(len(recent_20))
            slope_20, intercept_20 = np.polyfit(x_20, recent_20, 1)

            # Normalize slope to percentage per period
            avg_price_20 = np.mean(recent_20)
            slope_pct_20 = (slope_20 / avg_price_20) * 100

            # Determine strength
            abs_slope_20 = abs(slope_pct_20)
            if abs_slope_20 <= 0.01:
                strength_20 = "FLAT"
            elif abs_slope_20 <= 0.05:
                strength_20 = "WEAK"
            elif abs_slope_20 <= 0.15:
                strength_20 = "MODERATE"
            else:
                strength_20 = "STRONG"

            regime_20 = {
                'slope': slope_pct_20,
                'strength': strength_20,
                'source': 'Enhanced Regime Detector (20-period)'
            }
        else:
            regime_20 = {'slope': 0, 'strength': 'UNKNOWN', 'source': 'Insufficient Data'}

        # 100-candle regression (regime)
        if len(closes) >= 100:
            recent_100 = closes[-100:]
            x_100 = np.arange(len(recent_100))
            slope_100, intercept_100 = np.polyfit(x_100, recent_100, 1)

            # Normalize slope to percentage per period
            avg_price_100 = np.mean(recent_100)
            slope_pct_100 = (slope_100 / avg_price_100) * 100

            # Determine strength and trend
            abs_slope_100 = abs(slope_pct_100)
            if abs_slope_100 <= 0.01:
                strength_100 = "FLAT"
                trend_100 = "SIDEWAYS"
            elif abs_slope_100 <= 0.05:
                strength_100 = "WEAK"
                trend_100 = "UP" if slope_pct_100 > 0 else "DOWN"
            elif abs_slope_100 <= 0.15:
                strength_100 = "MODERATE"
                trend_100 = "UP" if slope_pct_100 > 0 else "DOWN"
            else:
                strength_100 = "STRONG"
                trend_100 = "UP" if slope_pct_100 > 0 else "DOWN"

            regime_100 = {
                'slope': slope_pct_100,
                'strength': strength_100,
                'trend': trend_100,
                'source': 'Enhanced Regime Detector (100-period)'
            }
        else:
            regime_100 = {'slope': 0, 'strength': 'UNKNOWN', 'trend': 'UNKNOWN', 'source': 'Insufficient Data'}

        return {
            'regime_20': regime_20,
            'regime_100': regime_100
        }

    def _calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate all required technical indicators"""
        df = df.copy()
        
        # Basic indicators
        df['atr'] = self._calculate_atr(df, self.lookback_atr)
        df['ema_8'] = df['close'].ewm(span=8).mean()
        df['ema_21'] = df['close'].ewm(span=21).mean()
        df['ema_50'] = df['close'].ewm(span=50).mean()
        
        # RSI
        df['rsi'] = self._calculate_rsi(df['close'], 14)
        
        # MACD
        macd_data = self._calculate_macd(df['close'])
        df['macd'] = macd_data['macd']
        df['macd_signal'] = macd_data['signal']
        df['macd_histogram'] = macd_data['histogram']
        
        # Bollinger Bands
        bb_data = self._calculate_bollinger_bands(df['close'], 20, 2)
        df['bb_upper'] = bb_data['upper']
        df['bb_lower'] = bb_data['lower']
        df['bb_middle'] = bb_data['middle']
        df['bb_width'] = bb_data['width']
        df['bb_percent'] = bb_data['percent_b']
        
        return df
    
    def _calculate_tier1_scores(self, df: pd.DataFrame) -> Dict[str, int]:
        """Calculate Tier 1 - Price Action Indicators (60 points)"""
        scores = {'trending': 0, 'ranging': 0}
        
        # 1. Swing Structure (12 points) - MOST IMPORTANT
        swing_score = self._analyze_swing_structure(df)
        scores[swing_score['regime']] += swing_score['points']
        
        # 2. Momentum Strength (10 points)
        momentum_score = self._analyze_momentum_strength(df)
        scores[momentum_score['regime']] += momentum_score['points']

        # 3. Candle Body Analysis (8 points)
        candle_score = self._analyze_candle_bodies(df)
        scores[candle_score['regime']] += candle_score['points']
        
        # 4. Breakout Detection (8 points)
        breakout_score = self._analyze_breakouts(df)
        scores[breakout_score['regime']] += breakout_score['points']

        # 5. ATR Expansion/Contraction (8 points)
        atr_score = self._analyze_atr_expansion(df)
        scores[atr_score['regime']] += atr_score['points']

        # 6. Range Definition (6 points)
        range_score = self._analyze_range_definition(df)
        scores[range_score['regime']] += range_score['points']

        # 7. Price Oscillation (8 points)
        oscillation_score = self._analyze_price_oscillation(df)
        scores[oscillation_score['regime']] += oscillation_score['points']
        
        return scores
    
    def _calculate_tier2_scores(self, df: pd.DataFrame) -> Dict[str, int]:
        """Calculate Tier 2 - Confirmation Indicators (30 points)"""
        scores = {'trending': 0, 'ranging': 0}
        
        # 8. Multi-timeframe Alignment (10 points)
        mtf_score = self._analyze_mtf_alignment(df)
        scores[mtf_score['regime']] += mtf_score['points']

        # 9. RSI Behavior (6 points)
        rsi_score = self._analyze_rsi_behavior(df)
        scores[rsi_score['regime']] += rsi_score['points']

        # 10. EMA Separation (6 points)
        ema_score = self._analyze_ema_separation(df)
        scores[ema_score['regime']] += ema_score['points']
        
        # 11. Bollinger Band Behavior (4 points)
        bb_score = self._analyze_bollinger_behavior(df)
        if bb_score['regime'] not in ['trending', 'ranging']:
            self.logger.error(f"🚨 INVALID REGIME from Bollinger Bands: '{bb_score['regime']}'")
            bb_score['regime'] = 'ranging'
        scores[bb_score['regime']] += bb_score['points']

        # 12. MACD (4 points)
        macd_score = self._analyze_macd_behavior(df)
        if macd_score['regime'] not in ['trending', 'ranging']:
            self.logger.error(f"🚨 INVALID REGIME from MACD: '{macd_score['regime']}'")
            macd_score['regime'] = 'ranging'
        scores[macd_score['regime']] += macd_score['points']
        
        return scores
    
    def _calculate_tier3_scores(self, df: pd.DataFrame) -> Dict[str, int]:
        """Calculate Tier 3 - Context Filters (8 points + Economic Calendar Impact)"""
        scores = {'trending': 0, 'ranging': 0}

        # 13. Session Analysis (3 points)
        session_score = self._analyze_session_context(df)
        scores[session_score['regime']] += session_score['points']

        # 14. Time-based Filters (2 points)
        time_score = self._analyze_time_filters(df)
        scores[time_score['regime']] += time_score['points']

        # 15. Regime Persistence (3 points)
        persistence_score = self._analyze_regime_persistence()
        scores[persistence_score['regime']] += persistence_score['points']

        # 16. Economic Calendar Impact (can reduce scores before high-impact events)
        economic_impact = self._analyze_economic_calendar_impact()
        scores['trending'] += economic_impact['trending']
        scores['ranging'] += economic_impact['ranging']

        return scores

    def _analyze_economic_calendar_impact(self) -> Dict[str, int]:
        """Analyze economic calendar impact on regime confidence"""
        try:
            next_event = self.economic_calendar.get_next_event()

            if not next_event:
                return {'trending': 0, 'ranging': 0}

            # FIXED: Use UTC for consistency
            current_time = datetime.now(timezone.utc)
            event_time_utc = next_event['time'].astimezone(timezone.utc)
            time_until = event_time_utc - current_time
            minutes_until = time_until.total_seconds() / 60

            # High-impact events within 30 minutes: reduce confidence
            if next_event['impact'] == 'HIGH' and 0 <= minutes_until <= 30:
                # Reduce both trending and ranging scores to increase TRANSITIONAL likelihood
                penalty = -3  # Reduce confidence significantly
                return {'trending': penalty, 'ranging': penalty}

            # Medium-impact events within 15 minutes: slight reduction
            elif next_event['impact'] == 'MEDIUM' and 0 <= minutes_until <= 15:
                penalty = -1
                return {'trending': penalty, 'ranging': penalty}

            return {'trending': 0, 'ranging': 0}

        except Exception as e:
            self.logger.debug(f"Error analyzing economic calendar impact: {e}")
            return {'trending': 0, 'ranging': 0}

    # ============================================================================
    # TIER 1 ANALYSIS METHODS - PRICE ACTION INDICATORS (60 points)
    # ============================================================================

    def _analyze_swing_structure(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        1. Swing Structure Analysis (12 points) - MOST IMPORTANT
        Track swing highs/lows and identify trending vs ranging patterns
        """
        try:
            highs = df['high'].values
            lows = df['low'].values
            closes = df['close'].values

            # Use last 20 candles as specified
            lookback = min(20, len(highs))
            recent_highs = highs[-lookback:]
            recent_lows = lows[-lookback:]
            recent_closes = closes[-lookback:]

            # Primary approach: Check for clear directional bias using trend strength
            trend_strength = self._calculate_trend_strength(recent_highs, recent_lows, recent_closes)

            if trend_strength['direction'] == 'strong_up':
                return {'regime': 'trending', 'points': 12, 'pattern': 'Strong Uptrend', 'strength': trend_strength['strength']}
            elif trend_strength['direction'] == 'strong_down':
                return {'regime': 'trending', 'points': 12, 'pattern': 'Strong Downtrend', 'strength': trend_strength['strength']}
            elif trend_strength['direction'] == 'moderate_up':
                return {'regime': 'trending', 'points': 8, 'pattern': 'Moderate Uptrend', 'strength': trend_strength['strength']}
            elif trend_strength['direction'] == 'moderate_down':
                return {'regime': 'trending', 'points': 8, 'pattern': 'Moderate Downtrend', 'strength': trend_strength['strength']}

            # Secondary approach: Traditional swing analysis
            swing_highs = self._find_pivot_highs(recent_highs, 3)  # More sensitive with 3 candles
            swing_lows = self._find_pivot_lows(recent_lows, 3)

            # Check for traditional HH+HL or LH+LL patterns
            if self._is_higher_highs_higher_lows(swing_highs, swing_lows):
                return {'regime': 'trending', 'points': 12, 'pattern': 'HH+HL'}
            elif self._is_lower_highs_lower_lows(swing_highs, swing_lows):
                return {'regime': 'trending', 'points': 12, 'pattern': 'LH+LL'}

            # Check for ranging patterns
            if self._is_bouncing_range_improved(recent_highs, recent_lows, recent_closes):
                return {'regime': 'ranging', 'points': 12, 'pattern': 'Bouncing Range'}
            elif len(swing_highs) >= 2 and len(swing_lows) >= 2:
                return {'regime': 'ranging', 'points': 8, 'pattern': 'Multiple Swings'}

            return {'regime': 'ranging', 'points': 6, 'pattern': 'Unclear'}

        except Exception as e:
            self.logger.error(f"Error in swing structure analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'pattern': 'Error'}

    def _analyze_momentum_strength(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        2. Momentum Strength Analysis (10 points)
        Use consecutive candle direction instead of volume
        """
        try:
            closes = df['close'].values
            consecutive, direction = self._count_consecutive_direction(closes, 10)

            if consecutive >= 5:
                return {'regime': 'trending', 'points': 10, 'consecutive': consecutive}
            elif consecutive == 4:
                return {'regime': 'trending', 'points': 7, 'consecutive': consecutive}
            elif consecutive == 3:
                return {'regime': 'trending', 'points': 4, 'consecutive': consecutive}

            # Check for alternating pattern (ranging)
            if self._is_zigzag_pattern(closes, 10):
                return {'regime': 'ranging', 'points': 10, 'pattern': 'Zigzag'}
            elif consecutive <= 2:
                return {'regime': 'ranging', 'points': 7, 'consecutive': consecutive}

            return {'regime': 'ranging', 'points': 0, 'consecutive': consecutive}

        except Exception as e:
            self.logger.error(f"Error in momentum analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'consecutive': 0}

    def _analyze_candle_bodies(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        3. Candle Body Analysis (8 points)
        Analyze candle quality and body-to-range ratios
        """
        try:
            opens = df['open'].values[-10:]
            closes = df['close'].values[-10:]
            highs = df['high'].values[-10:]
            lows = df['low'].values[-10:]

            bodies = np.abs(closes - opens)
            ranges = highs - lows

            # Avoid division by zero
            ranges = np.where(ranges == 0, 0.0001, ranges)
            body_ratios = bodies / ranges
            avg_body_ratio = np.mean(body_ratios)

            # Calculate ATR for comparison
            atr_current = df['atr'].iloc[-1] if 'atr' in df.columns else np.mean(ranges)
            large_bodies = np.sum(bodies > (1.5 * atr_current))

            # Trending conditions
            if avg_body_ratio > 0.70:
                return {'regime': 'trending', 'points': 8, 'avg_ratio': avg_body_ratio}
            elif avg_body_ratio > 0.60 and large_bodies >= 3:
                return {'regime': 'trending', 'points': 6, 'avg_ratio': avg_body_ratio}
            elif large_bodies >= 5:
                return {'regime': 'trending', 'points': 5, 'avg_ratio': avg_body_ratio}

            # Ranging conditions
            if avg_body_ratio < 0.40:
                return {'regime': 'ranging', 'points': 8, 'avg_ratio': avg_body_ratio}
            elif self._count_doji_spinning_tops(body_ratios) > 5:
                return {'regime': 'ranging', 'points': 6, 'avg_ratio': avg_body_ratio}
            elif avg_body_ratio < 0.50:
                return {'regime': 'ranging', 'points': 5, 'avg_ratio': avg_body_ratio}

            return {'regime': 'ranging', 'points': 0, 'avg_ratio': avg_body_ratio}

        except Exception as e:
            self.logger.error(f"Error in candle body analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'avg_ratio': 0}

    def _analyze_breakouts(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        4. ENHANCED Breakout Detection (8 points)
        Hybrid breakout logic: Conservative + Aggressive + Retest
        """
        try:
            highs = df['high'].values
            lows = df['low'].values
            closes = df['close'].values
            opens = df['open'].values

            lookback = min(20, len(df) - 1)
            recent_high = np.max(highs[-lookback-1:-1])  # Exclude current candle
            recent_low = np.min(lows[-lookback-1:-1])

            current_high = highs[-1]
            current_low = lows[-1]
            current_close = closes[-1]
            current_open = opens[-1]
            prev_close = closes[-2] if len(closes) > 1 else current_close

            # Calculate candle strength for aggressive breakouts
            body_size = abs(current_close - current_open)
            total_range = current_high - current_low
            upper_wick = current_high - max(current_close, current_open)
            lower_wick = min(current_close, current_open) - current_low

            body_ratio = body_size / total_range if total_range > 0 else 0

            # === BULLISH BREAKOUT DETECTION ===

            # TYPE 1: Conservative (Close-based) - 8 points
            if current_close > recent_high and prev_close <= recent_high:
                return {'regime': 'trending', 'points': 8, 'type': 'Close Breakout Bull'}

            # TYPE 2: Aggressive (High-based with strong candle) - 6 points
            if (self.breakout_mode in ['AGGRESSIVE', 'HYBRID'] and
                current_high > recent_high and
                body_ratio > 0.60 and  # Strong body
                current_close > current_open and  # Bullish
                upper_wick < body_size * 0.3):  # Small upper wick

                return {'regime': 'trending', 'points': 6, 'type': 'High Breakout Bull'}

            # TYPE 3: Retest breakout - 7 points
            if (prev_close > recent_high and  # Already broke out
                current_low <= recent_high and    # Retested
                current_close > recent_high):      # Held above

                return {'regime': 'trending', 'points': 7, 'type': 'Retest Breakout Bull'}

            # === BEARISH BREAKOUT DETECTION ===

            # TYPE 1: Conservative (Close-based) - 8 points
            if current_close < recent_low and prev_close >= recent_low:
                return {'regime': 'trending', 'points': 8, 'type': 'Close Breakout Bear'}

            # TYPE 2: Aggressive (Low-based with strong candle) - 6 points
            if (self.breakout_mode in ['AGGRESSIVE', 'HYBRID'] and
                current_low < recent_low and
                body_ratio > 0.60 and
                current_close < current_open and  # Bearish
                lower_wick < body_size * 0.3):

                return {'regime': 'trending', 'points': 6, 'type': 'Low Breakout Bear'}

            # TYPE 3: Retest breakout - 7 points
            if (prev_close < recent_low and
                current_high >= recent_low and
                current_close < recent_low):

                return {'regime': 'trending', 'points': 7, 'type': 'Retest Breakout Bear'}

            # === RANGING SIGNALS ===

            # Rejection at resistance (wick > 50% of range)
            if (current_high > recent_high and
                current_close < recent_high and
                upper_wick > total_range * 0.5):

                return {'regime': 'ranging', 'points': 8, 'type': 'Rejection High'}

            # Rejection at support
            if (current_low < recent_low and
                current_close > recent_low and
                lower_wick > total_range * 0.5):

                return {'regime': 'ranging', 'points': 8, 'type': 'Rejection Low'}

            # False breakout (broke and immediately reversed)
            if (current_high > recent_high and
                current_close < recent_high and
                body_ratio > 0.5):

                return {'regime': 'ranging', 'points': 6, 'type': 'False Breakout High'}

            if (current_low < recent_low and
                current_close > recent_low and
                body_ratio > 0.5):

                return {'regime': 'ranging', 'points': 6, 'type': 'False Breakout Low'}

            # Legacy retest continuation check
            if self._is_retest_continuation(df, recent_high, recent_low):
                return {'regime': 'trending', 'points': 6, 'type': 'Retest Continuation'}

            # Rejection at levels
            current_high = highs[-1]
            if current_high > recent_high and current_close < recent_high:
                return {'regime': 'ranging', 'points': 8, 'type': 'Rejection at High'}

            current_low = lows[-1]
            if current_low < recent_low and current_close > recent_low:
                return {'regime': 'ranging', 'points': 8, 'type': 'Rejection at Low'}

            # False breakout
            if self._is_false_breakout(df, recent_high, recent_low):
                return {'regime': 'ranging', 'points': 6, 'type': 'False Breakout'}

            # Consolidation
            range_size = recent_high - recent_low
            if abs(current_close - (recent_high + recent_low) / 2) < range_size * 0.3:
                return {'regime': 'ranging', 'points': 5, 'type': 'Consolidation'}

            return {'regime': 'ranging', 'points': 0, 'type': 'No Clear Pattern'}

        except Exception as e:
            self.logger.error(f"Error in breakout analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'type': 'Error'}

    def _analyze_atr_expansion(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        5. ATR Expansion/Contraction Analysis (8 points)
        Leading volatility indicator
        """
        try:
            if 'atr' not in df.columns:
                return {'regime': 'ranging', 'points': 0, 'change': 0}

            atr_values = df['atr'].values
            if len(atr_values) < 20:
                return {'regime': 'ranging', 'points': 0, 'change': 0}

            atr_current = atr_values[-1]
            atr_10_ago = atr_values[-11] if len(atr_values) > 10 else atr_values[0]
            atr_20_high = np.max(atr_values[-20:])
            atr_20_low = np.min(atr_values[-20:])

            # Calculate percentage change
            change_pct = ((atr_current - atr_10_ago) / atr_10_ago * 100) if atr_10_ago > 0 else 0

            # Trending conditions (ATR expansion)
            if change_pct > 15:
                return {'regime': 'trending', 'points': 8, 'change': change_pct}
            elif atr_current >= atr_20_high * 0.95:  # Near 20-period high
                return {'regime': 'trending', 'points': 6, 'change': change_pct}
            elif change_pct > 5 and self._is_atr_accelerating(atr_values):
                return {'regime': 'trending', 'points': 5, 'change': change_pct}

            # Ranging conditions (ATR contraction)
            if change_pct < -15:
                return {'regime': 'ranging', 'points': 8, 'change': change_pct}
            elif atr_current <= atr_20_low * 1.05:  # Near 20-period low
                return {'regime': 'ranging', 'points': 6, 'change': change_pct}
            elif self._is_bollinger_squeeze(df):
                return {'regime': 'ranging', 'points': 8, 'change': change_pct}

            return {'regime': 'ranging', 'points': 0, 'change': change_pct}

        except Exception as e:
            self.logger.error(f"Error in ATR analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'change': 0}

    def _analyze_range_definition(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        6. Range Definition Analysis (6 points)
        Measure price behavior within defined range
        """
        try:
            highs = df['high'].values
            lows = df['low'].values
            closes = df['close'].values

            lookback = min(50, len(df))
            range_high = np.max(highs[-lookback:])
            range_low = np.min(lows[-lookback:])
            midpoint = (range_high + range_low) / 2
            range_size = range_high - range_low

            if range_size == 0:
                return {'regime': 'ranging', 'points': 0, 'touches': 0}

            # Count touches at support/resistance levels
            tolerance = range_size * 0.002  # 0.2% tolerance
            touches_high = self._count_touches(highs[-lookback:], range_high, tolerance)
            touches_low = self._count_touches(lows[-lookback:], range_low, tolerance)

            # Check if price stays within range
            current_close = closes[-1]
            atr_current = df['atr'].iloc[-1] if 'atr' in df.columns else range_size * 0.02
            distance_from_mid = abs(current_close - midpoint)

            # Ranging conditions
            if touches_high >= 2 and touches_low >= 2:
                return {'regime': 'ranging', 'points': 8, 'touches': touches_high + touches_low}
            elif distance_from_mid <= 2 * atr_current:  # Within 2 ATR of midpoint
                return {'regime': 'ranging', 'points': 6, 'touches': touches_high + touches_low}
            elif self._is_decreasing_range_size(df):
                return {'regime': 'ranging', 'points': 5, 'touches': touches_high + touches_low}

            # Trending conditions
            if current_close > range_high or current_close < range_low:
                return {'regime': 'trending', 'points': 6, 'touches': touches_high + touches_low}
            elif touches_high < 2 and touches_low < 2:
                return {'regime': 'trending', 'points': 4, 'touches': touches_high + touches_low}

            return {'regime': 'ranging', 'points': 0, 'touches': touches_high + touches_low}

        except Exception as e:
            self.logger.error(f"Error in range analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'touches': 0}

    def _analyze_price_oscillation(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        7. Price Oscillation Analysis (8 points)
        Measure back-and-forth movement
        """
        try:
            closes = df['close'].values
            if len(closes) < 21:
                return {'regime': 'ranging', 'points': 0, 'crosses': 0}

            # Calculate moving average
            ma = df['ema_21'].values if 'ema_21' in df.columns else pd.Series(closes).rolling(21).mean().values

            # Count MA crosses in last 20 candles
            lookback = min(20, len(closes) - 1)
            crosses = 0

            for i in range(lookback):
                idx1 = -(i+1)
                idx2 = -(i+2)
                if len(ma) > abs(idx2):
                    above1 = closes[idx1] > ma[idx1]
                    above2 = closes[idx2] > ma[idx2]
                    if above1 != above2:
                        crosses += 1

            # Calculate standard deviation of returns
            returns = np.diff(closes[-20:]) if len(closes) >= 20 else np.diff(closes)
            std_returns = np.std(returns) if len(returns) > 0 else 0

            # Ranging conditions (high oscillation)
            if crosses >= 5:
                return {'regime': 'ranging', 'points': 8, 'crosses': crosses}
            elif crosses >= 3 and std_returns > np.mean(np.abs(returns)) * 1.5:
                return {'regime': 'ranging', 'points': 6, 'crosses': crosses}
            elif crosses >= 3:
                return {'regime': 'ranging', 'points': 4, 'crosses': crosses}

            # Trending conditions (low oscillation)
            if crosses <= 2:
                return {'regime': 'trending', 'points': 8, 'crosses': crosses}
            elif crosses <= 3 and self._price_stays_one_side_ma(closes, ma, 15):
                return {'regime': 'trending', 'points': 6, 'crosses': crosses}

            return {'regime': 'ranging', 'points': 0, 'crosses': crosses}

        except Exception as e:
            self.logger.error(f"Error in oscillation analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'crosses': 0}

    # ============================================================================
    # TIER 2 ANALYSIS METHODS - CONFIRMATION INDICATORS (30 points)
    # ============================================================================

    def _analyze_mtf_alignment(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        8. ENHANCED Multi-timeframe Alignment Analysis (10 points)
        Real MTF analysis when enabled, fallback to simulation
        """
        try:
            # If MTF mode is enabled and we have cached results, use them
            if self.mtf_mode and self.mtf_cache:
                return self._analyze_mtf_from_cache()

            # Fallback to improved simulation (better than period multiplication)
            closes = df['close'].values
            if len(closes) < 50:
                return {'regime': 'ranging', 'points': 0, 'alignment': 'insufficient_data'}

            # Use proper trend analysis instead of simple direction
            trend_5m = self._get_enhanced_trend_direction(closes, 15)   # 5M trend
            trend_15m = self._get_enhanced_trend_direction(closes, 45)  # Simulated 15M
            trend_1h = self._get_enhanced_trend_direction(closes, 180)  # Simulated 1H

            # Enhanced alignment scoring
            alignment_score = 0
            alignment_type = 'no_alignment'

            # Perfect alignment (all same direction)
            if trend_5m == trend_15m == trend_1h and trend_5m != 'sideways':
                if trend_5m in ['strong_up', 'strong_down']:
                    alignment_score = 10
                    alignment_type = 'perfect_strong_alignment'
                elif trend_5m in ['up', 'down']:
                    alignment_score = 8
                    alignment_type = 'perfect_alignment'

            # Partial alignment (5M + 15M)
            elif trend_5m == trend_15m and trend_5m != 'sideways':
                if trend_5m in ['strong_up', 'strong_down']:
                    alignment_score = 6
                    alignment_type = 'partial_strong_alignment'
                elif trend_5m in ['up', 'down']:
                    alignment_score = 5
                    alignment_type = 'partial_alignment'

            # Higher timeframe dominance (1H overrides)
            elif trend_1h in ['strong_up', 'strong_down'] and trend_5m == trend_1h:
                alignment_score = 7
                alignment_type = 'higher_tf_alignment'

            # Ranging alignment
            elif trend_1h == 'sideways' and trend_5m == 'sideways':
                alignment_score = 5
                alignment_type = 'ranging_alignment'

            # Conflicting signals
            elif ((trend_5m in ['up', 'strong_up'] and trend_15m in ['down', 'strong_down']) or
                  (trend_5m in ['down', 'strong_down'] and trend_15m in ['up', 'strong_up'])):
                alignment_score = 0
                alignment_type = 'conflicting_signals'

            # Determine regime based on alignment
            if alignment_score >= 7:
                regime = 'trending'
            elif alignment_score >= 4:
                regime = 'ranging' if 'ranging' in alignment_type else 'trending'
            else:
                regime = 'ranging'

            return {
                'regime': regime,
                'points': alignment_score,
                'alignment': alignment_type,
                'trends': {
                    'M5': trend_5m,
                    'M15': trend_15m,
                    'H1': trend_1h
                }
            }

        except Exception as e:
            self.logger.error(f"Error in MTF analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'alignment': 'error'}

    def _analyze_mtf_from_cache(self) -> Dict[str, any]:
        """Analyze MTF alignment from cached results"""
        try:
            if not self.mtf_cache:
                return {'regime': 'ranging', 'points': 0, 'alignment': 'no_cache'}

            # Extract regime types from cache
            regime_5m = self.mtf_cache.get('M5', {}).get('regime', 'UNKNOWN')
            regime_15m = self.mtf_cache.get('M15', {}).get('regime', 'UNKNOWN')
            regime_1h = self.mtf_cache.get('H1', {}).get('regime', 'UNKNOWN')

            # Convert to trend directions
            trend_5m = self._regime_to_trend(regime_5m)
            trend_15m = self._regime_to_trend(regime_15m)
            trend_1h = self._regime_to_trend(regime_1h)

            # Calculate alignment score
            alignment_score = 0
            alignment_type = 'no_alignment'

            # Perfect alignment
            if trend_5m == trend_15m == trend_1h and trend_5m != 'sideways':
                alignment_score = 10
                alignment_type = 'perfect_mtf_alignment'

            # Partial alignment (5M + 15M)
            elif trend_5m == trend_15m and trend_5m != 'sideways':
                alignment_score = 6
                alignment_type = 'partial_mtf_alignment'

            # Higher timeframe dominance
            elif trend_1h != 'sideways' and trend_5m == trend_1h:
                alignment_score = 7
                alignment_type = 'higher_tf_mtf_alignment'

            # Ranging alignment
            elif trend_1h == 'sideways' and trend_5m == 'sideways':
                alignment_score = 5
                alignment_type = 'ranging_mtf_alignment'

            regime = 'trending' if alignment_score >= 6 else 'ranging'

            return {
                'regime': regime,
                'points': alignment_score,
                'alignment': alignment_type,
                'mtf_trends': {
                    'M5': trend_5m,
                    'M15': trend_15m,
                    'H1': trend_1h
                }
            }

        except Exception as e:
            self.logger.error(f"Error in MTF cache analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'alignment': 'cache_error'}

    def _regime_to_trend(self, regime: str) -> str:
        """Convert regime to trend direction"""
        if regime in ['TRENDING', 'STRONG_TRENDING']:
            return 'up'  # Simplified - would need price direction in real implementation
        elif regime in ['RANGING', 'STRONG_RANGING']:
            return 'sideways'
        else:
            return 'sideways'

    def _get_enhanced_trend_direction(self, closes: np.ndarray, period: int) -> str:
        """
        Enhanced trend direction analysis
        Returns: strong_up, up, sideways, down, strong_down
        """
        try:
            if len(closes) < period:
                return 'sideways'

            recent_closes = closes[-period:]

            # Linear regression for trend
            x = np.arange(len(recent_closes))
            slope, _ = np.polyfit(x, recent_closes, 1)

            # Normalize slope
            avg_price = np.mean(recent_closes)
            slope_pct = (slope / avg_price) * 100

            # Calculate trend strength
            price_change = (recent_closes[-1] - recent_closes[0]) / recent_closes[0] * 100

            # Determine trend direction and strength
            if slope_pct > 0.02:  # Strong uptrend
                if abs(price_change) > 1.0:
                    return 'strong_up'
                else:
                    return 'up'
            elif slope_pct < -0.02:  # Strong downtrend
                if abs(price_change) > 1.0:
                    return 'strong_down'
                else:
                    return 'down'
            else:
                return 'sideways'

        except Exception as e:
            self.logger.error(f"Error in enhanced trend direction: {e}")
            return 'sideways'

    def _analyze_rsi_behavior(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        9. RSI Behavior Analysis (6 points)
        Analyze RSI patterns for trending vs ranging markets
        """
        try:
            if 'rsi' not in df.columns:
                return {'regime': 'ranging', 'points': 0, 'rsi_value': 50}

            rsi_values = df['rsi'].values
            if len(rsi_values) < 10:
                return {'regime': 'ranging', 'points': 0, 'rsi_value': 50}

            current_rsi = rsi_values[-1]
            rsi_trend = self._get_rsi_trend(rsi_values[-10:])

            # Check for divergence
            has_divergence = self._check_rsi_divergence(df)

            # Trending conditions
            if (current_rsi > 70 or current_rsi < 30) and rsi_trend != 'sideways':
                return {'regime': 'trending', 'points': 6, 'rsi_value': current_rsi}
            elif abs(rsi_trend) > 0.5 and not has_divergence:  # Strong RSI trend
                return {'regime': 'trending', 'points': 4, 'rsi_value': current_rsi}
            elif not has_divergence and (current_rsi > 60 or current_rsi < 40):
                return {'regime': 'trending', 'points': 3, 'rsi_value': current_rsi}

            # Ranging conditions
            if 40 <= current_rsi <= 60:
                return {'regime': 'ranging', 'points': 6, 'rsi_value': current_rsi}
            elif has_divergence:
                return {'regime': 'ranging', 'points': 4, 'rsi_value': current_rsi}

            return {'regime': 'ranging', 'points': 0, 'rsi_value': current_rsi}

        except Exception as e:
            self.logger.error(f"Error in RSI analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'rsi_value': 50}

    def _analyze_ema_separation(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        10. EMA Separation Analysis (6 points)
        Use 8/21/50 EMA system
        """
        try:
            if not all(col in df.columns for col in ['ema_8', 'ema_21', 'ema_50']):
                return {'regime': 'ranging', 'points': 0, 'separation': 0}

            ema8 = df['ema_8'].iloc[-1]
            ema21 = df['ema_21'].iloc[-1]
            ema50 = df['ema_50'].iloc[-1]
            current_price = df['close'].iloc[-1]

            # Calculate separations as percentages
            sep_8_21 = abs(ema8 - ema21) / ema21 * 100
            sep_21_50 = abs(ema21 - ema50) / ema50 * 100
            avg_separation = (sep_8_21 + sep_21_50) / 2

            # Check alignment
            bullish_aligned = ema8 > ema21 > ema50
            bearish_aligned = ema8 < ema21 < ema50

            # Trending conditions
            if (bullish_aligned or bearish_aligned):
                if avg_separation > 0.08:  # Wide separation for XAUUSD
                    return {'regime': 'trending', 'points': 6, 'separation': avg_separation}
                elif avg_separation > 0.05:
                    return {'regime': 'trending', 'points': 4, 'separation': avg_separation}
                elif abs(current_price - ema8) / current_price > 0.003:  # Price far from EMAs
                    return {'regime': 'trending', 'points': 3, 'separation': avg_separation}

            # Ranging conditions
            if avg_separation < 0.03:  # EMAs compressed
                return {'regime': 'ranging', 'points': 6, 'separation': avg_separation}
            elif not (bullish_aligned or bearish_aligned):  # EMAs tangled
                return {'regime': 'ranging', 'points': 4, 'separation': avg_separation}
            elif self._is_price_whipsawing_emas(df):
                return {'regime': 'ranging', 'points': 5, 'separation': avg_separation}

            return {'regime': 'ranging', 'points': 0, 'separation': avg_separation}

        except Exception as e:
            self.logger.error(f"Error in EMA analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'separation': 0}

    def _analyze_bollinger_behavior(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        11. Bollinger Band Behavior Analysis (4 points)
        """
        try:
            if not all(col in df.columns for col in ['bb_upper', 'bb_lower', 'bb_percent', 'bb_width']):
                return {'regime': 'ranging', 'points': 0, 'bb_percent': 0.5}

            bb_percent = df['bb_percent'].iloc[-1]
            bb_width = df['bb_width'].iloc[-1]
            current_price = df['close'].iloc[-1]
            bb_upper = df['bb_upper'].iloc[-1]
            bb_lower = df['bb_lower'].iloc[-1]

            # Calculate average BB width for comparison
            avg_bb_width = df['bb_width'].rolling(20).mean().iloc[-1] if len(df) >= 20 else bb_width

            # Trending conditions
            if bb_percent > 0.8 or bb_percent < 0.2:  # Price at band extremes
                return {'regime': 'trending', 'points': 4, 'bb_percent': bb_percent}
            elif bb_width > avg_bb_width * 1.2:  # Bands expanding
                return {'regime': 'trending', 'points': 3, 'bb_percent': bb_percent}
            elif self._is_riding_bb_band(df):  # Price riding band
                return {'regime': 'trending', 'points': 4, 'bb_percent': bb_percent}

            # Ranging conditions
            if 0.3 <= bb_percent <= 0.7:  # Price in middle 40% of bands
                return {'regime': 'ranging', 'points': 4, 'bb_percent': bb_percent}
            elif bb_width < avg_bb_width * 0.5:  # Bollinger squeeze
                return {'regime': 'ranging', 'points': 4, 'bb_percent': bb_percent}

            return {'regime': 'ranging', 'points': 0, 'bb_percent': bb_percent}

        except Exception as e:
            self.logger.error(f"Error in Bollinger analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'bb_percent': 0.5}

    def _analyze_macd_behavior(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        12. MACD Analysis (4 points)
        """
        try:
            if not all(col in df.columns for col in ['macd', 'macd_signal', 'macd_histogram']):
                return {'regime': 'ranging', 'points': 0, 'macd_value': 0}

            macd = df['macd'].iloc[-1]
            macd_signal = df['macd_signal'].iloc[-1]
            histogram = df['macd_histogram'].iloc[-1]

            # Check for divergence
            has_divergence = self._check_macd_divergence(df)

            # Trending conditions
            if abs(macd) > abs(macd_signal) * 1.5:  # MACD strongly positive/negative
                return {'regime': 'trending', 'points': 4, 'macd_value': macd}
            elif abs(histogram) > abs(df['macd_histogram'].rolling(10).mean().iloc[-1]) * 1.2:  # Histogram expanding
                return {'regime': 'trending', 'points': 3, 'macd_value': macd}
            elif not has_divergence and abs(macd) > 0.1:
                return {'regime': 'trending', 'points': 2, 'macd_value': macd}

            # Ranging conditions
            if abs(macd) < 0.05:  # MACD near zero line
                return {'regime': 'ranging', 'points': 4, 'macd_value': macd}
            elif self._is_macd_oscillating(df):
                return {'regime': 'ranging', 'points': 3, 'macd_value': macd}
            elif has_divergence:
                return {'regime': 'ranging', 'points': 3, 'macd_value': macd}

            return {'regime': 'ranging', 'points': 0, 'macd_value': macd}

        except Exception as e:
            self.logger.error(f"Error in MACD analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'macd_value': 0}

    # ============================================================================
    # TIER 3 ANALYSIS METHODS - CONTEXT FILTERS (10 points)
    # ============================================================================

    def _analyze_session_context(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        13. Session Analysis (3 points)
        XAUUSD specific session behavior
        """
        try:
            current_time = datetime.now(timezone.utc)
            current_hour = current_time.hour

            # Determine current session
            if self.sessions['LONDON']['start'] <= current_hour <= self.sessions['LONDON']['end']:
                session = 'LONDON'
                # London session: trending bias
                return {'regime': 'trending', 'points': 3, 'session': session}
            elif self.sessions['NY']['start'] <= current_hour <= self.sessions['NY']['end']:
                session = 'NY'
                # NY session: trending bias (follow-through)
                return {'regime': 'trending', 'points': 2, 'session': session}
            elif (current_hour >= self.sessions['ASIAN']['start'] or
                  current_hour <= self.sessions['ASIAN']['end']):
                session = 'ASIAN'
                # Asian session: ranging bias (low volatility)
                return {'regime': 'ranging', 'points': 3, 'session': session}
            else:
                session = 'TRANSITION'
                return {'regime': 'ranging', 'points': 1, 'session': session}

        except Exception as e:
            self.logger.error(f"Error in session analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'session': 'unknown'}

    def _analyze_time_filters(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        14. Time-based Filters (2 points)
        Regime duration and stability checks
        """
        try:
            # Check regime duration
            if self.regime_duration < 10:  # New regime, wait for confirmation
                return {'regime': 'ranging', 'points': -2, 'duration': self.regime_duration}
            elif self.regime_duration >= 30:  # Established regime
                # Ensure regime_type is always 'trending' or 'ranging', never 'TRANSITIONAL'
                if self.previous_regime in ['TRENDING', 'STRONG_TRENDING']:
                    regime_type = 'trending'
                elif self.previous_regime in ['RANGING', 'STRONG_RANGING']:
                    regime_type = 'ranging'
                else:
                    regime_type = 'ranging'  # Default for TRANSITIONAL, ERROR, etc.
                return {'regime': regime_type, 'points': 2, 'duration': self.regime_duration}

            # Check for rapid flipping (transitional override)
            if self.regime_duration < 5 and self.previous_regime:
                return {'regime': 'ranging', 'points': 0, 'duration': self.regime_duration}

            return {'regime': 'ranging', 'points': 0, 'duration': self.regime_duration}

        except Exception as e:
            self.logger.error(f"Error in time filter analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'duration': 0}

    def _analyze_regime_persistence(self) -> Dict[str, any]:
        """
        15. Regime Persistence (3 points)
        Sticky filter to prevent whipsaws
        """
        try:
            if self.previous_regime is None:
                return {'regime': 'ranging', 'points': 0, 'persistence': 'no_history'}

            # Previous regime bonus - same regime gets bonus points
            # Convert regime to scoring format (only 'trending' or 'ranging' allowed)
            if self.previous_regime in ['STRONG_TRENDING', 'TRENDING']:
                return {'regime': 'trending', 'points': 3, 'persistence': 'same_as_previous'}
            elif self.previous_regime in ['STRONG_RANGING', 'RANGING']:
                return {'regime': 'ranging', 'points': 3, 'persistence': 'same_as_previous'}
            else:  # TRANSITIONAL
                return {'regime': 'ranging', 'points': 0, 'persistence': 'transitional_previous'}

        except Exception as e:
            self.logger.error(f"Error in persistence analysis: {e}")
            return {'regime': 'ranging', 'points': 0, 'persistence': 'error'}

    # ============================================================================
    # DECISION LOGIC AND HELPER METHODS
    # ============================================================================

    def _make_regime_decision(self, trending_score: int, ranging_score: int,
                            tier1_scores: Dict[str, int], tier2_scores: Dict[str, int],
                            tier3_scores: Dict[str, int], silent_mode: bool = False) -> Tuple[str, float]:
        """
        Final decision logic with confidence calculation
        Args:
            silent_mode: If True, suppress result logging (for MTF analysis)
        """
        try:
            max_score = max(trending_score, ranging_score)
            score_diff = abs(trending_score - ranging_score)

            # Log detailed breakdown (unless in silent mode)
            if not silent_mode:
                self._log_detailed_score_breakdown(trending_score, ranging_score, tier1_scores, tier2_scores, tier3_scores)

            # Check if swing structure agrees (most important)
            swing_structure_agrees = self._check_swing_structure_agreement(tier1_scores)

            # Decision thresholds
            STRONG_THRESHOLD = 50  # 51% of 98 points
            DIFF_THRESHOLD = 18    # 18% difference

            # Strong trending
            if (trending_score >= STRONG_THRESHOLD and
                score_diff >= DIFF_THRESHOLD and
                swing_structure_agrees['trending']):
                confidence = min((trending_score / 98) * 100, 100)

                # REMOVED: Verbose STRONG_TRENDING decision logging
                # Only log the final result (unless in silent mode)
                if not silent_mode:
                    self.logger.info(f"   Result: STRONG_TRENDING with {confidence:.1f}% confidence")

                return "STRONG_TRENDING", confidence

            # Strong ranging
            elif (ranging_score >= STRONG_THRESHOLD and
                  score_diff >= DIFF_THRESHOLD and
                  (swing_structure_agrees['ranging'] or swing_structure_agrees['oscillation'])):
                confidence = min((ranging_score / 98) * 100, 100)

                # REMOVED: Verbose STRONG_RANGING decision logging
                # Only log the final result (unless in silent mode)
                if not silent_mode:
                    self.logger.info(f"   Result: STRONG_RANGING with {confidence:.1f}% confidence")

                return "STRONG_RANGING", confidence

            # Transitional (conflicting signals or low scores)
            elif (score_diff < DIFF_THRESHOLD or
                  max_score < 45 or
                  not (swing_structure_agrees['trending'] or swing_structure_agrees['ranging'])):
                confidence = max_score / 98 * 100

                # ENHANCED: Log detailed TRANSITIONAL decision reasoning
                transitional_reasons = []
                if score_diff < DIFF_THRESHOLD:
                    transitional_reasons.append(f"Score difference too small: {score_diff:.1f} < {DIFF_THRESHOLD} (Trending:{trending_score:.1f} vs Ranging:{ranging_score:.1f})")
                if max_score < 45:
                    transitional_reasons.append(f"Maximum score too low: {max_score:.1f} < 45 (insufficient conviction)")
                if not (swing_structure_agrees['trending'] or swing_structure_agrees['ranging']):
                    swing_details = f"Trending:{swing_structure_agrees['trending']}, Ranging:{swing_structure_agrees['ranging']}, Oscillation:{swing_structure_agrees['oscillation']}"
                    transitional_reasons.append(f"Swing structure disagreement: {swing_details}")

                # REMOVED: Verbose transitional regime decision logging
                # Only log the final result (unless in silent mode)
                if not silent_mode:
                    self.logger.info(f"   Result: TRANSITIONAL with {confidence:.1f}% confidence")

                return "TRANSITIONAL", confidence

            # Default to stronger signal
            else:
                if trending_score > ranging_score:
                    confidence = (trending_score / 98) * 100

                    # REMOVED: Verbose TRENDING decision logging
                    # Only log the final result (unless in silent mode)
                    if not silent_mode:
                        self.logger.info(f"   Result: TRENDING with {confidence:.1f}% confidence")

                    return "TRENDING", confidence
                else:
                    confidence = (ranging_score / 98) * 100

                    # REMOVED: Verbose RANGING decision logging
                    # Only log the final result (unless in silent mode)
                    if not silent_mode:
                        self.logger.info(f"   Result: RANGING with {confidence:.1f}% confidence")

                    return "RANGING", confidence

        except Exception as e:
            self.logger.error(f"Error in regime decision: {e}")
            return "TRANSITIONAL", 0.0

    def _log_detailed_score_breakdown(self, trending_score: int, ranging_score: int,
                                    tier1_scores: Dict[str, int],
                                    tier2_scores: Dict[str, int],
                                    tier3_scores: Dict[str, int]):
        """Log detailed breakdown of all scoring factors"""
        try:
            # Economic calendar timing first
            self._log_economic_calendar_timing()

            # Detailed regime scoring breakdown
            self.logger.info("📊 REGIME SCORING BREAKDOWN:")
            self.logger.info(f"   Final Scores: Trending={trending_score}/98, Ranging={ranging_score}/98")

            # Tier 1 - Price Action (60 points)
            self.logger.info("   TIER 1 - Price Action (60pts):")
            self._log_tier1_breakdown(tier1_scores)

            # Tier 2 - Confirmation (30 points)
            self.logger.info("   TIER 2 - Confirmation (30pts):")
            self._log_tier2_breakdown(tier2_scores)

            # Tier 3 - Context (8 points + Economic Impact)
            self.logger.info("   TIER 3 - Context (8pts + Economic Impact):")
            self._log_tier3_breakdown(tier3_scores)

        except Exception as e:
            self.logger.error(f"Error logging score breakdown: {e}")

    def _log_tier1_breakdown(self, tier1_scores: Dict[str, int]):
        """Log Tier 1 - Price Action breakdown"""
        # Note: Individual factor scores are calculated within tier methods
        # We show the total contribution here
        self.logger.info(f"      Trending: {tier1_scores['trending']}/60, Ranging: {tier1_scores['ranging']}/60")
        self.logger.info("      • Swing Structure (12pts) - Most Important")
        self.logger.info("      • Momentum Strength (10pts)")
        self.logger.info("      • Candle Bodies (8pts)")
        self.logger.info("      • Breakouts (8pts)")
        self.logger.info("      • ATR Expansion (8pts)")
        self.logger.info("      • Range Definition (6pts)")
        self.logger.info("      • Price Oscillation (8pts)")

    def _log_tier2_breakdown(self, tier2_scores: Dict[str, int]):
        """Log Tier 2 - Confirmation breakdown"""
        self.logger.info(f"      Trending: {tier2_scores['trending']}/30, Ranging: {tier2_scores['ranging']}/30")
        self.logger.info("      • Multi-timeframe Alignment (10pts)")
        self.logger.info("      • Volume Analysis (8pts)")
        self.logger.info("      • MACD Confirmation (6pts)")
        self.logger.info("      • RSI Momentum (6pts)")

    def _log_tier3_breakdown(self, tier3_scores: Dict[str, int]):
        """Log Tier 3 - Context breakdown"""
        self.logger.info(f"      Trending: {tier3_scores['trending']}/8+, Ranging: {tier3_scores['ranging']}/8+")
        self.logger.info("      • Session Analysis (3pts)")
        self.logger.info("      • Time-based Filters (2pts)")
        self.logger.info("      • Regime Persistence (3pts)")
        self.logger.info("      • Economic Calendar Impact (variable penalty)")

    def _log_economic_calendar_timing(self):
        """Log next economic event timing from real calendar data"""
        try:
            # Get next event from real economic calendar
            next_event = self.economic_calendar.get_next_event()

            if next_event:
                # FIXED: Use UTC for consistency
                current_time = datetime.now(timezone.utc)
                event_time_utc = next_event['time'].astimezone(timezone.utc)
                time_until = event_time_utc - current_time

                # Handle negative time (past events)
                if time_until.total_seconds() < 0:
                    self.logger.info("   📅 ECONOMIC CALENDAR:")
                    self.logger.info("      Next Event: No upcoming events in calendar")
                    return

                hours = int(time_until.total_seconds() // 3600)
                minutes = int((time_until.total_seconds() % 3600) // 60)

                self.logger.info("   📅 ECONOMIC CALENDAR:")
                self.logger.info(f"      Next Event: {next_event['currency']} {next_event['event']} ({next_event['impact']} impact)")
                self.logger.info(f"      Time Until: {hours}h {minutes}m")
                self.logger.info(f"      Event Time: {next_event['time'].strftime('%Y-%m-%d %H:%M %Z')}")
            else:
                self.logger.info("   📅 ECONOMIC CALENDAR:")
                self.logger.info("      Next Event: No upcoming events found")

        except Exception as e:
            self.logger.error(f"Error logging economic calendar: {e}")
            # Fallback to indicate calendar issue
            self.logger.info("   📅 ECONOMIC CALENDAR:")
            self.logger.info("      Next Event: Calendar parsing error - check economic_calendar.txt")

    def _update_persistence(self, regime: str, confidence: float):
        """Update regime persistence tracking"""
        try:
            if self.previous_regime == regime:
                self.regime_duration += 1
            else:
                self.regime_duration = 1

            self.previous_regime = regime
            self.previous_confidence = confidence

        except Exception as e:
            self.logger.error(f"Error updating persistence: {e}")

    def _check_swing_structure_agreement(self, tier1_scores: Dict[str, int]) -> Dict[str, bool]:
        """Check if swing structure agrees with the decision"""
        # This is a simplified check - in full implementation,
        # we would track individual indicator results
        return {
            'trending': tier1_scores['trending'] >= 12,  # At least swing structure points
            'ranging': tier1_scores['ranging'] >= 12,
            'oscillation': tier1_scores['ranging'] >= 8
        }

    # ============================================================================
    # TECHNICAL INDICATOR CALCULATION METHODS
    # ============================================================================

    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Average True Range"""
        try:
            high = df['high']
            low = df['low']
            close = df['close']

            tr1 = high - low
            tr2 = abs(high - close.shift())
            tr3 = abs(low - close.shift())

            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = true_range.rolling(window=period).mean()

            return atr

        except Exception as e:
            self.logger.error(f"Error calculating ATR: {e}")
            return pd.Series([0] * len(df), index=df.index)

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Relative Strength Index"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))

            return rsi

        except Exception as e:
            self.logger.error(f"Error calculating RSI: {e}")
            return pd.Series([50] * len(prices), index=prices.index)

    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict:
        """Calculate MACD"""
        try:
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()

            macd = ema_fast - ema_slow
            macd_signal = macd.ewm(span=signal).mean()
            histogram = macd - macd_signal

            return {
                'macd': macd,
                'signal': macd_signal,
                'histogram': histogram
            }

        except Exception as e:
            self.logger.error(f"Error calculating MACD: {e}")
            return {
                'macd': pd.Series([0] * len(prices), index=prices.index),
                'signal': pd.Series([0] * len(prices), index=prices.index),
                'histogram': pd.Series([0] * len(prices), index=prices.index)
            }

    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: float = 2) -> Dict:
        """Calculate Bollinger Bands"""
        try:
            middle = prices.rolling(window=period).mean()
            std = prices.rolling(window=period).std()

            upper = middle + (std * std_dev)
            lower = middle - (std * std_dev)
            width = upper - lower
            percent_b = (prices - lower) / (upper - lower)

            return {
                'upper': upper,
                'lower': lower,
                'middle': middle,
                'width': width,
                'percent_b': percent_b
            }

        except Exception as e:
            self.logger.error(f"Error calculating Bollinger Bands: {e}")
            return {
                'upper': prices,
                'lower': prices,
                'middle': prices,
                'width': pd.Series([0] * len(prices), index=prices.index),
                'percent_b': pd.Series([0.5] * len(prices), index=prices.index)
            }

    def _calculate_trend_strength(self, highs: np.ndarray, lows: np.ndarray, closes: np.ndarray) -> Dict[str, any]:
        """Calculate trend strength using price progression"""
        try:
            if len(closes) < 10:
                return {'direction': 'unclear', 'strength': 0}

            # Calculate price change over period
            start_price = closes[0]
            end_price = closes[-1]
            price_change_pct = (end_price - start_price) / start_price * 100

            # Calculate consistency of direction
            up_moves = 0
            down_moves = 0
            for i in range(1, len(closes)):
                if closes[i] > closes[i-1]:
                    up_moves += 1
                elif closes[i] < closes[i-1]:
                    down_moves += 1

            total_moves = up_moves + down_moves
            if total_moves == 0:
                return {'direction': 'unclear', 'strength': 0}

            directional_consistency = max(up_moves, down_moves) / total_moves

            # Strong trend criteria: >2% price change AND >70% directional consistency
            if price_change_pct > 2 and directional_consistency > 0.7:
                return {'direction': 'strong_up', 'strength': price_change_pct}
            elif price_change_pct < -2 and directional_consistency > 0.7:
                return {'direction': 'strong_down', 'strength': abs(price_change_pct)}
            elif price_change_pct > 1 and directional_consistency > 0.6:
                return {'direction': 'moderate_up', 'strength': price_change_pct}
            elif price_change_pct < -1 and directional_consistency > 0.6:
                return {'direction': 'moderate_down', 'strength': abs(price_change_pct)}
            else:
                return {'direction': 'ranging', 'strength': directional_consistency}

        except Exception as e:
            self.logger.error(f"Error calculating trend strength: {e}")
            return {'direction': 'unclear', 'strength': 0}

    def _is_bouncing_range_improved(self, highs: np.ndarray, lows: np.ndarray, closes: np.ndarray) -> bool:
        """Improved bouncing range detection"""
        try:
            if len(highs) < 10:
                return False

            # Find the range bounds
            range_high = np.max(highs)
            range_low = np.min(lows)
            range_size = range_high - range_low

            if range_size == 0:
                return False

            # Count touches near the bounds (within 20% of range)
            tolerance = range_size * 0.2
            high_touches = np.sum(highs >= (range_high - tolerance))
            low_touches = np.sum(lows <= (range_low + tolerance))

            # Check if price stays within range most of the time
            midpoint = (range_high + range_low) / 2
            range_quarter = range_size * 0.25

            # Count candles in middle 50% of range
            middle_candles = np.sum((closes >= (midpoint - range_quarter)) &
                                  (closes <= (midpoint + range_quarter)))

            # Ranging criteria: multiple touches of bounds AND significant time in middle
            return (high_touches >= 2 and low_touches >= 2 and
                   middle_candles >= len(closes) * 0.3)

        except Exception as e:
            self.logger.error(f"Error in improved bouncing range detection: {e}")
            return False

    # ============================================================================
    # SWING STRUCTURE HELPER METHODS
    # ============================================================================

    def _find_pivot_highs(self, highs: np.ndarray, lookback: int = 5) -> List[Tuple[int, float]]:
        """Find pivot high points"""
        try:
            pivots = []
            for i in range(lookback, len(highs) - lookback):
                is_pivot = True
                current_high = highs[i]

                # Check left side
                for j in range(i - lookback, i):
                    if highs[j] >= current_high:
                        is_pivot = False
                        break

                # Check right side
                if is_pivot:
                    for j in range(i + 1, i + lookback + 1):
                        if highs[j] >= current_high:
                            is_pivot = False
                            break

                if is_pivot:
                    pivots.append((i, current_high))

            return pivots

        except Exception as e:
            self.logger.error(f"Error finding pivot highs: {e}")
            return []

    def _find_pivot_lows(self, lows: np.ndarray, lookback: int = 5) -> List[Tuple[int, float]]:
        """Find pivot low points"""
        try:
            pivots = []
            for i in range(lookback, len(lows) - lookback):
                is_pivot = True
                current_low = lows[i]

                # Check left side
                for j in range(i - lookback, i):
                    if lows[j] <= current_low:
                        is_pivot = False
                        break

                # Check right side
                if is_pivot:
                    for j in range(i + 1, i + lookback + 1):
                        if lows[j] <= current_low:
                            is_pivot = False
                            break

                if is_pivot:
                    pivots.append((i, current_low))

            return pivots

        except Exception as e:
            self.logger.error(f"Error finding pivot lows: {e}")
            return []

    def _is_higher_highs_higher_lows(self, swing_highs: List, swing_lows: List) -> bool:
        """Check for Higher Highs and Higher Lows pattern"""
        try:
            if len(swing_highs) < 3 or len(swing_lows) < 3:
                return False

            # Check last 3 swing highs for ascending pattern
            recent_highs = [h[1] for h in swing_highs[-3:]]
            hh_pattern = all(recent_highs[i] < recent_highs[i+1] for i in range(len(recent_highs)-1))

            # Check last 3 swing lows for ascending pattern
            recent_lows = [l[1] for l in swing_lows[-3:]]
            hl_pattern = all(recent_lows[i] < recent_lows[i+1] for i in range(len(recent_lows)-1))

            return hh_pattern and hl_pattern

        except Exception as e:
            self.logger.error(f"Error checking HH+HL pattern: {e}")
            return False

    def _is_lower_highs_lower_lows(self, swing_highs: List, swing_lows: List) -> bool:
        """Check for Lower Highs and Lower Lows pattern"""
        try:
            if len(swing_highs) < 3 or len(swing_lows) < 3:
                return False

            # Check last 3 swing highs for descending pattern
            recent_highs = [h[1] for h in swing_highs[-3:]]
            lh_pattern = all(recent_highs[i] > recent_highs[i+1] for i in range(len(recent_highs)-1))

            # Check last 3 swing lows for descending pattern
            recent_lows = [l[1] for l in swing_lows[-3:]]
            ll_pattern = all(recent_lows[i] > recent_lows[i+1] for i in range(len(recent_lows)-1))

            return lh_pattern and ll_pattern

        except Exception as e:
            self.logger.error(f"Error checking LH+LL pattern: {e}")
            return False

    def _has_partial_trend_structure(self, swing_highs: List, swing_lows: List) -> bool:
        """Check for partial trending structure (2 consecutive)"""
        try:
            if len(swing_highs) < 2 or len(swing_lows) < 2:
                return False

            # Check for 2 consecutive higher highs and higher lows
            if len(swing_highs) >= 2:
                recent_highs = [h[1] for h in swing_highs[-2:]]
                if recent_highs[0] < recent_highs[1]:
                    recent_lows = [l[1] for l in swing_lows[-2:]]
                    if len(recent_lows) >= 2 and recent_lows[0] < recent_lows[1]:
                        return True

            # Check for 2 consecutive lower highs and lower lows
            if len(swing_highs) >= 2:
                recent_highs = [h[1] for h in swing_highs[-2:]]
                if recent_highs[0] > recent_highs[1]:
                    recent_lows = [l[1] for l in swing_lows[-2:]]
                    if len(recent_lows) >= 2 and recent_lows[0] > recent_lows[1]:
                        return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking partial trend: {e}")
            return False

    def _is_bouncing_range(self, swing_highs: List, swing_lows: List) -> bool:
        """Check for bouncing range pattern"""
        try:
            if len(swing_highs) < 3 or len(swing_lows) < 3:
                return False

            # Get recent swing levels
            recent_highs = [h[1] for h in swing_highs[-3:]]
            recent_lows = [l[1] for l in swing_lows[-3:]]

            # Check if highs are similar (within 0.1% tolerance)
            high_tolerance = np.mean(recent_highs) * 0.001
            highs_similar = max(recent_highs) - min(recent_highs) <= high_tolerance

            # Check if lows are similar
            low_tolerance = np.mean(recent_lows) * 0.001
            lows_similar = max(recent_lows) - min(recent_lows) <= low_tolerance

            return highs_similar and lows_similar

        except Exception as e:
            self.logger.error(f"Error checking bouncing range: {e}")
            return False

    def _is_oscillating_pattern(self, df: pd.DataFrame) -> bool:
        """Check for oscillating pattern around moving average"""
        try:
            if len(df) < 21:
                return False

            closes = df['close'].values
            ma = df['ema_21'].values if 'ema_21' in df.columns else pd.Series(closes).rolling(21).mean().values

            # Count how many times price crosses the MA
            crosses = 0
            for i in range(1, min(20, len(closes))):
                if (closes[-i] > ma[-i]) != (closes[-(i+1)] > ma[-(i+1)]):
                    crosses += 1

            return crosses >= 3

        except Exception as e:
            self.logger.error(f"Error checking oscillating pattern: {e}")
            return False

    # ============================================================================
    # MOMENTUM AND PATTERN HELPER METHODS
    # ============================================================================

    def _count_consecutive_direction(self, closes: np.ndarray, lookback: int = 10) -> Tuple[int, bool]:
        """Count maximum consecutive candles in same direction within lookback period"""
        try:
            if len(closes) < 2:
                return 0, True

            # Look for the maximum consecutive sequence in the lookback period
            max_consecutive = 1
            max_direction = True

            # Check recent consecutive from last candle
            recent_consecutive = 1
            recent_direction = closes[-1] > closes[-2]

            for i in range(2, min(lookback + 1, len(closes))):
                current_dir = closes[-i] > closes[-(i+1)]
                if current_dir == recent_direction:
                    recent_consecutive += 1
                else:
                    break

            if recent_consecutive > max_consecutive:
                max_consecutive = recent_consecutive
                max_direction = recent_direction

            # Also scan through the entire lookback period for any consecutive sequences
            start_idx = max(0, len(closes) - lookback)
            for i in range(start_idx, len(closes) - 1):
                if i + 1 >= len(closes):
                    break

                current_consecutive = 1
                current_direction = closes[i+1] > closes[i]

                # Count consecutive from this point
                for j in range(i + 2, len(closes)):
                    next_dir = closes[j] > closes[j-1]
                    if next_dir == current_direction:
                        current_consecutive += 1
                    else:
                        break

                # Update maximum if this sequence is longer
                if current_consecutive > max_consecutive:
                    max_consecutive = current_consecutive
                    max_direction = current_direction

            return max_consecutive, max_direction

        except Exception as e:
            self.logger.error(f"Error counting consecutive direction: {e}")
            return 0, True

    def _is_zigzag_pattern(self, closes: np.ndarray, lookback: int = 10) -> bool:
        """Check for alternating up/down pattern"""
        try:
            if len(closes) < 4:
                return False

            directions = []
            for i in range(1, min(lookback, len(closes))):
                directions.append(closes[-i] > closes[-(i+1)])

            # Check for alternating pattern
            alternating = 0
            for i in range(1, len(directions)):
                if directions[i] != directions[i-1]:
                    alternating += 1

            return alternating >= len(directions) * 0.7  # 70% alternating

        except Exception as e:
            self.logger.error(f"Error checking zigzag pattern: {e}")
            return False

    def _count_doji_spinning_tops(self, body_ratios: np.ndarray) -> int:
        """Count doji and spinning top candles"""
        try:
            # Doji/spinning tops have body ratio < 0.3
            return np.sum(body_ratios < 0.3)
        except Exception as e:
            self.logger.error(f"Error counting doji: {e}")
            return 0

    def _is_retest_continuation(self, df: pd.DataFrame, recent_high: float, recent_low: float) -> bool:
        """Check for retest and continuation pattern"""
        try:
            closes = df['close'].values
            if len(closes) < 5:
                return False

            # Look for retest pattern in last 5 candles
            for i in range(1, 5):
                close_price = closes[-i]
                # Check if price retested the breakout level and continued
                if abs(close_price - recent_high) / recent_high < 0.002:  # Within 0.2%
                    return closes[-1] > recent_high  # Continued higher
                elif abs(close_price - recent_low) / recent_low < 0.002:
                    return closes[-1] < recent_low  # Continued lower

            return False
        except Exception as e:
            self.logger.error(f"Error checking retest: {e}")
            return False

    def _is_false_breakout(self, df: pd.DataFrame, recent_high: float, recent_low: float) -> bool:
        """Check for false breakout pattern"""
        try:
            highs = df['high'].values
            lows = df['low'].values
            closes = df['close'].values

            if len(closes) < 3:
                return False

            # Check last 3 candles for false breakout
            for i in range(1, 4):
                high_price = highs[-i]
                low_price = lows[-i]
                close_price = closes[-i]

                # False bullish breakout
                if high_price > recent_high and close_price < recent_high:
                    return True
                # False bearish breakout
                if low_price < recent_low and close_price > recent_low:
                    return True

            return False
        except Exception as e:
            self.logger.error(f"Error checking false breakout: {e}")
            return False

    def _is_atr_accelerating(self, atr_values: np.ndarray) -> bool:
        """Check if ATR is accelerating (slope increasing)"""
        try:
            if len(atr_values) < 5:
                return False

            recent_atr = atr_values[-5:]
            # Calculate simple slope
            x = np.arange(len(recent_atr))
            slope = np.polyfit(x, recent_atr, 1)[0]

            return slope > 0
        except Exception as e:
            self.logger.error(f"Error checking ATR acceleration: {e}")
            return False

    def _is_bollinger_squeeze(self, df: pd.DataFrame) -> bool:
        """Check for Bollinger Band squeeze"""
        try:
            if 'bb_width' not in df.columns:
                return False

            bb_width = df['bb_width'].values
            if len(bb_width) < 20:
                return False

            current_width = bb_width[-1]
            avg_width = np.mean(bb_width[-20:])

            return current_width < avg_width * 0.5
        except Exception as e:
            self.logger.error(f"Error checking BB squeeze: {e}")
            return False

    def _count_touches(self, prices: np.ndarray, level: float, tolerance: float) -> int:
        """Count how many times price touched a level"""
        try:
            touches = 0
            for price in prices:
                if abs(price - level) <= tolerance:
                    touches += 1
            return touches
        except Exception as e:
            self.logger.error(f"Error counting touches: {e}")
            return 0

    def _is_decreasing_range_size(self, df: pd.DataFrame) -> bool:
        """Check if range size is decreasing"""
        try:
            if len(df) < 40:
                return False

            # Compare recent 20 candles range vs previous 20
            recent_high = df['high'].iloc[-20:].max()
            recent_low = df['low'].iloc[-20:].min()
            recent_range = recent_high - recent_low

            prev_high = df['high'].iloc[-40:-20].max()
            prev_low = df['low'].iloc[-40:-20].min()
            prev_range = prev_high - prev_low

            return recent_range < prev_range * 0.8
        except Exception as e:
            self.logger.error(f"Error checking decreasing range: {e}")
            return False

    def _price_stays_one_side_ma(self, closes: np.ndarray, ma: np.ndarray, lookback: int) -> bool:
        """Check if price stays on one side of MA"""
        try:
            if len(closes) < lookback or len(ma) < lookback:
                return False

            above_count = 0
            below_count = 0

            for i in range(lookback):
                if closes[-(i+1)] > ma[-(i+1)]:
                    above_count += 1
                else:
                    below_count += 1

            # 80% on one side
            return above_count >= lookback * 0.8 or below_count >= lookback * 0.8
        except Exception as e:
            self.logger.error(f"Error checking price vs MA: {e}")
            return False

    def _get_trend_direction(self, closes: np.ndarray, period: int) -> str:
        """Get trend direction for given period"""
        try:
            if len(closes) < period:
                return 'sideways'

            start_price = closes[-period]
            end_price = closes[-1]
            change_pct = (end_price - start_price) / start_price * 100

            if change_pct > 1:
                return 'up'
            elif change_pct < -1:
                return 'down'
            else:
                return 'sideways'
        except Exception as e:
            self.logger.error(f"Error getting trend direction: {e}")
            return 'sideways'

    def _get_rsi_trend(self, rsi_values: np.ndarray) -> float:
        """Get RSI trend slope"""
        try:
            if len(rsi_values) < 3:
                return 0

            x = np.arange(len(rsi_values))
            slope = np.polyfit(x, rsi_values, 1)[0]
            return slope
        except Exception as e:
            self.logger.error(f"Error getting RSI trend: {e}")
            return 0

    def _check_rsi_divergence(self, df: pd.DataFrame) -> bool:
        """Check for RSI divergence"""
        try:
            # Simplified divergence check
            if len(df) < 20 or 'rsi' not in df.columns:
                return False

            prices = df['close'].values[-20:]
            rsi_values = df['rsi'].values[-20:]

            # Find recent highs/lows in both price and RSI
            price_trend = self._get_trend_direction(prices, len(prices))
            rsi_trend = 'up' if self._get_rsi_trend(rsi_values) > 0 else 'down'

            # Divergence if trends are opposite
            return (price_trend == 'up' and rsi_trend == 'down') or (price_trend == 'down' and rsi_trend == 'up')
        except Exception as e:
            self.logger.error(f"Error checking RSI divergence: {e}")
            return False

    def _is_price_whipsawing_emas(self, df: pd.DataFrame) -> bool:
        """Check if price is whipsawing through EMAs"""
        try:
            if not all(col in df.columns for col in ['close', 'ema_8', 'ema_21', 'ema_50']):
                return False

            closes = df['close'].values[-10:]
            ema8 = df['ema_8'].values[-10:]
            ema21 = df['ema_21'].values[-10:]
            ema50 = df['ema_50'].values[-10:]

            crosses = 0
            for i in range(1, len(closes)):
                # Count crosses through any EMA
                for ema in [ema8, ema21, ema50]:
                    if (closes[i] > ema[i]) != (closes[i-1] > ema[i-1]):
                        crosses += 1

            return crosses >= 3
        except Exception as e:
            self.logger.error(f"Error checking EMA whipsaw: {e}")
            return False

    def _is_riding_bb_band(self, df: pd.DataFrame) -> bool:
        """Check if price is riding Bollinger Band"""
        try:
            if not all(col in df.columns for col in ['close', 'bb_upper', 'bb_lower']):
                return False

            closes = df['close'].values[-5:]
            bb_upper = df['bb_upper'].values[-5:]
            bb_lower = df['bb_lower'].values[-5:]

            # Check if price stays near upper or lower band
            near_upper = np.sum(closes >= bb_upper * 0.995) >= 3
            near_lower = np.sum(closes <= bb_lower * 1.005) >= 3

            return near_upper or near_lower
        except Exception as e:
            self.logger.error(f"Error checking BB riding: {e}")
            return False

    def _check_macd_divergence(self, df: pd.DataFrame) -> bool:
        """Check for MACD divergence"""
        try:
            if len(df) < 20 or 'macd' not in df.columns:
                return False

            prices = df['close'].values[-20:]
            macd_values = df['macd'].values[-20:]

            price_trend = self._get_trend_direction(prices, len(prices))
            macd_trend = 'up' if np.polyfit(np.arange(len(macd_values)), macd_values, 1)[0] > 0 else 'down'

            return (price_trend == 'up' and macd_trend == 'down') or (price_trend == 'down' and macd_trend == 'up')
        except Exception as e:
            self.logger.error(f"Error checking MACD divergence: {e}")
            return False

    def _is_macd_oscillating(self, df: pd.DataFrame) -> bool:
        """Check if MACD is oscillating around zero"""
        try:
            if 'macd' not in df.columns:
                return False

            macd_values = df['macd'].values[-10:]

            # Count zero line crosses
            crosses = 0
            for i in range(1, len(macd_values)):
                if (macd_values[i] > 0) != (macd_values[i-1] > 0):
                    crosses += 1

            return crosses >= 2
        except Exception as e:
            self.logger.error(f"Error checking MACD oscillation: {e}")
            return False

# ============================================================================
# MAIN TESTING AND DEBUGGING FUNCTIONS
# ============================================================================

def test_enhanced_regime_detector():
    """Test the enhanced regime detector with sample data"""
    try:
        # Initialize detector
        detector = EnhancedRegimeDetector("XAUUSD!", "M5")

        # Create sample data for testing
        dates = pd.date_range(start='2024-01-01', periods=100, freq='5T')
        np.random.seed(42)

        # Generate sample OHLC data
        base_price = 2000
        prices = []
        for i in range(100):
            change = np.random.normal(0, 5)  # Random walk
            base_price += change
            prices.append(base_price)

        # Create OHLC from prices
        opens = prices[:-1] + [prices[-1]]
        closes = prices
        highs = [p + np.random.uniform(0, 3) for p in prices]
        lows = [p - np.random.uniform(0, 3) for p in prices]

        df = pd.DataFrame({
            'datetime': dates,
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': [1000] * 100
        })
        df.set_index('datetime', inplace=True)

        print("🚀 ENHANCED REGIME DETECTOR TEST")
        print("=" * 60)
        print(f"📊 Sample Data: {len(df)} candles")
        print(f"📈 Price Range: {df['low'].min():.2f} - {df['high'].max():.2f}")

        # Test regime detection
        regime, confidence, details = detector.detect_regime(df)

        print(f"\n🎯 REGIME DETECTION RESULTS:")
        print("-" * 40)
        print(f"Regime: {regime}")
        print(f"Confidence: {confidence:.2f}%")
        print(f"Trending Score: {details['trending_score']}/98")
        print(f"Ranging Score: {details['ranging_score']}/98")
        print(f"Score Percentage: {details['score_percentage']:.1f}%")

        print(f"\n📊 TIER BREAKDOWN:")
        print(f"Tier 1 (Price Action): T={details['tier1_scores']['trending']}, R={details['tier1_scores']['ranging']}")
        print(f"Tier 2 (Confirmation): T={details['tier2_scores']['trending']}, R={details['tier2_scores']['ranging']}")
        print(f"Tier 3 (Context): T={details['tier3_scores']['trending']}, R={details['tier3_scores']['ranging']}")

        print(f"\n✅ TEST COMPLETED SUCCESSFULLY!")
        return True

    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        return False

if __name__ == "__main__":
    test_enhanced_regime_detector()
