#!/usr/bin/env python3
"""
Test script to verify position sizing for all supported symbols
This will test XAUUSD!, EURUSD!, and BTCUSD without running the full trading system
"""

import sys
import os

# Add src to path
sys.path.append('src')

from fixed_live_trader import FixedLiveTrader

def test_position_sizing():
    """Test position sizing for all supported symbols"""
    print("🧪 TESTING MULTI-SYMBOL POSITION SIZING")
    print("=" * 60)
    
    # Test parameters
    test_balance = 10000.0  # $10,000 account
    test_atr = 100.0        # Generic ATR value
    
    symbols_to_test = ["XAUUSD!", "EURUSD!", "BTCUSD"]
    test_prices = {
        "XAUUSD!": 2000.0,   # $2000/oz
        "EURUSD!": 1.1000,   # 1.1000 EUR/USD
        "BTCUSD": 45000.0    # $45,000/BTC
    }
    
    print(f"📊 Test Parameters:")
    print(f"   Account Balance: ${test_balance:,.2f}")
    print(f"   Risk Percentage: 4%")
    print(f"   ATR Value: {test_atr}")
    print(f"   Stop Loss: 1.0 ATR")
    print("=" * 60)
    
    for symbol in symbols_to_test:
        print(f"\n🎯 Testing {symbol}:")
        try:
            # Create trader for this symbol
            trader = FixedLiveTrader(symbol=symbol)
            
            # Get symbol info
            contract_size = trader.SYMBOL_SPECS[symbol]["contract_size"]
            symbol_name = trader.SYMBOL_SPECS[symbol]["name"]
            test_price = test_prices[symbol]
            
            print(f"   Symbol Name: {symbol_name}")
            print(f"   Contract Size: {contract_size}")
            print(f"   Test Price: {test_price}")
            
            # Calculate position size
            lot_size = trader.calculate_position_size(test_balance, test_price, test_atr)
            
            # Manual calculation for verification
            risk_amount = test_balance * 0.04  # 4% risk
            stop_distance = test_atr * 1.0     # 1 ATR stop
            risk_per_lot = stop_distance * contract_size
            expected_lot_size = risk_amount / risk_per_lot
            expected_lot_size = round(expected_lot_size, 2)
            expected_lot_size = max(0.01, min(expected_lot_size, 10.0))
            
            print(f"   Risk Amount: ${risk_amount:.2f}")
            print(f"   Stop Distance: {stop_distance}")
            print(f"   Risk per Lot: ${risk_per_lot:.2f}")
            print(f"   Expected Lot Size: {expected_lot_size}")
            print(f"   Calculated Lot Size: {lot_size}")
            
            if abs(lot_size - expected_lot_size) < 0.01:
                print(f"   ✅ Position sizing CORRECT for {symbol}")
            else:
                print(f"   ❌ Position sizing INCORRECT for {symbol}")
                
        except Exception as e:
            print(f"   ❌ Error testing {symbol}: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY:")
    print("✅ All symbols should show correct position sizing")
    print("📊 Each symbol uses its specific contract size")
    print("💰 Risk management: 4% of account balance per trade")
    print("🛡️  Stop loss: 1.0 ATR from entry price")
    print("=" * 60)

def test_symbol_selection():
    """Test symbol selection and validation"""
    print("\n🧪 TESTING SYMBOL SELECTION")
    print("=" * 40)
    
    # Test valid symbols
    valid_symbols = ["XAUUSD!", "EURUSD!", "BTCUSD"]
    for symbol in valid_symbols:
        try:
            trader = FixedLiveTrader(symbol=symbol)
            print(f"✅ {symbol}: {trader.SYMBOL_SPECS[symbol]['name']} - Contract Size: {trader.SYMBOL_SPECS[symbol]['contract_size']}")
        except Exception as e:
            print(f"❌ {symbol}: Error - {e}")
    
    # Test unknown symbol
    try:
        trader = FixedLiveTrader(symbol="UNKNOWN!")
        print(f"⚠️  UNKNOWN!: Handled gracefully - Contract Size: {trader.SYMBOL_SPECS['UNKNOWN!']['contract_size']}")
    except Exception as e:
        print(f"❌ UNKNOWN!: Error - {e}")

if __name__ == "__main__":
    test_position_sizing()
    test_symbol_selection()
    
    print("\n🚀 To run the actual trading system:")
    print("   python fixed_live_trader.py BTCUSD")
    print("   python fixed_live_trader.py EURUSD")
    print("   python fixed_live_trader.py XAUUSD")
    print("\n🧪 To test BTCUSD specifically:")
    print("   python test_btcusd.py")
