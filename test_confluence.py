#!/usr/bin/env python3
"""
Test script to verify confluence calculation accuracy
"""

import pandas as pd
import numpy as np
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader

def create_test_data(price, ema_10, ema_20, reg_l, reg_u, reg_ls, reg_us, atr=None):
    """Create test dataframe with specific values - need at least 2 candles"""
    # Create multiple candles for ATR calculation
    if atr is None:
        atr = price * 0.002  # Default ATR

    # Create 20 candles for proper ATR calculation
    candle_count = 20
    data = {
        'close': [price] * candle_count,
        'high': [price + atr] * candle_count,  # Use ATR for range
        'low': [price - atr] * candle_count,
        'ema_10': [ema_10] * candle_count,
        'ema_20': [ema_20] * candle_count,
        'regression_lower': [reg_l] * candle_count,
        'regression_upper': [reg_u] * candle_count,
        'regression_lower_short': [reg_ls] * candle_count,
        'regression_upper_short': [reg_us] * candle_count,
        'accurate_trend_direction': ['UP'] * candle_count
    }
    return pd.DataFrame(data)

def test_confluence_calculation():
    """Test various confluence scenarios"""
    
    # Create a mock trader instance
    trader = FixedLiveTrader()
    
    print("🧪 TESTING CONFLUENCE CALCULATION ACCURACY")
    print("=" * 60)
    
    # Test Case 1: Your scenario - price at EMAs but far from regression channels
    print("\n📊 TEST CASE 1: Price at EMAs, far from regression channels")
    print("Expected: BuyConf=0.000, SellConf=0.000 (no meaningful levels nearby)")
    
    df = create_test_data(
        price=4329.31,
        ema_10=4329.31,   # At price
        ema_20=4329.31,   # At price  
        reg_l=4317.23,    # 12.08 points below
        reg_u=4333.87,    # 4.56 points above
        reg_ls=4315.17,   # 14.14 points below
        reg_us=4330.29    # 0.98 points above
    )
    
    confluence = trader._get_support_resistance_confluence(df, 4329.31, "UP")
    print(f"Result: BuyConf={confluence['buy_confluence']:.3f}, SellConf={confluence['sell_confluence']:.3f}")
    
    # Test Case 2: Price near regression lower channel
    print("\n📊 TEST CASE 2: Price near regression lower channel")
    print("Expected: BuyConf=0.8xx, SellConf=0.000 (near support)")
    
    df = create_test_data(
        price=4318.00,
        ema_10=4325.00,   # Above price
        ema_20=4325.00,   # Above price
        reg_l=4317.23,    # 0.77 points below (very close)
        reg_u=4333.87,    # Far above
        reg_ls=4315.17,   # Below price
        reg_us=4330.29    # Far above
    )
    
    confluence = trader._get_support_resistance_confluence(df, 4318.00, "UP")
    print(f"Result: BuyConf={confluence['buy_confluence']:.3f}, SellConf={confluence['sell_confluence']:.3f}")
    
    # Test Case 3: Price near regression upper channel
    print("\n📊 TEST CASE 3: Price near regression upper channel")
    print("Expected: BuyConf=0.000, SellConf=0.8xx (near resistance)")
    
    df = create_test_data(
        price=4333.00,
        ema_10=4325.00,   # Below price
        ema_20=4325.00,   # Below price
        reg_l=4317.23,    # Far below
        reg_u=4333.87,    # 0.87 points above (very close)
        reg_ls=4315.17,   # Far below
        reg_us=4330.29    # Below price
    )
    
    confluence = trader._get_support_resistance_confluence(df, 4333.00, "UP")
    print(f"Result: BuyConf={confluence['buy_confluence']:.3f}, SellConf={confluence['sell_confluence']:.3f}")
    
    # Test Case 4: Price exactly at regression lower channel
    print("\n📊 TEST CASE 4: Price exactly at regression lower channel")
    print("Expected: BuyConf=1.000, SellConf=0.000 (at support)")
    
    df = create_test_data(
        price=4317.23,
        ema_10=4325.00,   # Above price
        ema_20=4325.00,   # Above price
        reg_l=4317.23,    # Exactly at price
        reg_u=4333.87,    # Far above
        reg_ls=4315.17,   # Below price
        reg_us=4330.29    # Far above
    )
    
    confluence = trader._get_support_resistance_confluence(df, 4317.23, "UP")
    print(f"Result: BuyConf={confluence['buy_confluence']:.3f}, SellConf={confluence['sell_confluence']:.3f}")
    
    # Test Case 5: Price between all levels
    print("\n📊 TEST CASE 5: Price between all levels (no confluence)")
    print("Expected: BuyConf=0.000, SellConf=0.000 (no nearby levels)")
    
    df = create_test_data(
        price=4325.00,
        ema_10=4320.00,   # 5 points below
        ema_20=4320.00,   # 5 points below
        reg_l=4317.23,    # 7.77 points below
        reg_u=4333.87,    # 8.87 points above
        reg_ls=4315.17,   # 9.83 points below
        reg_us=4330.29    # 5.29 points above
    )
    
    confluence = trader._get_support_resistance_confluence(df, 4325.00, "UP")
    print(f"Result: BuyConf={confluence['buy_confluence']:.3f}, SellConf={confluence['sell_confluence']:.3f}")
    
    # Test Case 6: Current scenario - price very close to RegUS with exact ATR
    print("\n📊 TEST CASE 6: Price very close to RegUS (current scenario)")
    print("Expected: BuyConf=0.000, SellConf=0.8xx (near resistance)")
    print("ATR=9.295, Threshold=1.859 (0.2 ATR), RegUS distance=0.93")

    df = create_test_data(
        price=4368.76,
        ema_10=4368.76,   # At price
        ema_20=4368.76,   # At price
        reg_l=4331.90,    # Far below
        reg_u=4363.83,    # 4.93 points below
        reg_ls=4348.88,   # Far below
        reg_us=4369.69,   # 0.93 points above (very close!)
        atr=9.295         # Exact ATR from your log
    )

    confluence = trader._get_support_resistance_confluence(df, 4368.76, "UP")
    print(f"Result: BuyConf={confluence['buy_confluence']:.3f}, SellConf={confluence['sell_confluence']:.3f}")

    # Print debug info from confluence calculation
    debug_info = getattr(trader, '_last_confluence_debug', 'No debug info')
    print(f"Debug: {debug_info}")

    # Calculate expected confluence manually
    distance_to_regus = 4369.69 - 4368.76  # 0.93
    threshold = 9.295 * 0.2  # 1.859
    expected_confluence = 1.0 - (distance_to_regus / threshold)  # 1.0 - (0.93/1.859) = 0.5
    print(f"Expected SellConf calculation: 1.0 - ({distance_to_regus:.2f}/{threshold:.3f}) = {expected_confluence:.3f}")

    # Test Case 7: Candle wick touching support (your scenario)
    print("\n📊 TEST CASE 7: Candle wick touching support (your chart scenario)")
    print("Expected: BuyConf=1.000 (candle low touching/below support)")

    df = create_test_data(
        price=4354.17,    # Close price
        ema_10=4354.17,   # At close
        ema_20=4354.17,   # At close
        reg_l=4345.63,    # Far below
        reg_u=4379.77,    # Far above
        reg_ls=4350.14,   # Support level
        reg_us=4384.56,   # Far above
        atr=11.67         # From your log
    )

    # Create candle with low touching support
    current_candle = {
        'high': 4359.37,   # From your log
        'low': 4342.72,    # From your log - BELOW RegLS support!
        'close': 4354.17   # From your log
    }

    confluence = trader._get_support_resistance_confluence(df, 4354.17, "UP", current_candle)
    print(f"Result: BuyConf={confluence['buy_confluence']:.3f}, SellConf={confluence['sell_confluence']:.3f}")

    # Print debug info
    debug_info = getattr(trader, '_last_confluence_debug', 'No debug info')
    print(f"Debug: {debug_info}")

    # Manual calculation
    candle_low = 4342.72
    reg_ls = 4350.14
    distance = candle_low - reg_ls  # 4342.72 - 4350.14 = -7.42 (below support)
    print(f"Candle Low: {candle_low}, RegLS: {reg_ls}, Distance: {distance:.2f} (negative = below support)")
    print("Since candle low is BELOW support, BuyConf should be 1.000")

    # Test Case 8: EMA test-and-bounce (your EMA scenario)
    print("\n📊 TEST CASE 8: EMA test-and-bounce (your EMA scenario)")
    print("Expected: BuyConf=1.000 (candle tested below EMAs, closed back at EMAs)")

    df = create_test_data(
        price=4363.06,    # Close exactly at EMAs
        ema_10=4363.06,   # At close
        ema_20=4363.06,   # At close
        reg_l=4346.38,    # Far below
        reg_u=4374.72,    # Far above
        reg_ls=4359.58,   # Below close
        reg_us=4370.45,   # Above close
        atr=9.88          # From your log
    )

    # Create candle that tested below EMAs but closed back at EMAs
    current_candle = {
        'high': 4369.40,   # From your log
        'low': 4350.25,    # From your log - tested 12.81 points below EMAs!
        'close': 4363.06   # From your log - closed exactly at EMAs
    }

    confluence = trader._get_support_resistance_confluence(df, 4363.06, "UP", current_candle)
    print(f"Result: BuyConf={confluence['buy_confluence']:.3f}, SellConf={confluence['sell_confluence']:.3f}")

    # Print debug info
    debug_info = getattr(trader, '_last_confluence_debug', 'No debug info')
    print(f"Debug: {debug_info}")

    # Manual calculation
    candle_low = 4350.25
    ema_level = 4363.06
    close_price = 4363.06
    distance_low_to_ema = candle_low - ema_level  # 4350.25 - 4363.06 = -12.81 (tested below)
    distance_close_to_ema = abs(close_price - ema_level)  # 0.00 (closed exactly at EMA)
    print(f"Candle Low: {candle_low}, EMA: {ema_level}, Close: {close_price}")
    print(f"Low tested {distance_low_to_ema:.2f} points below EMA, but closed exactly at EMA")
    print("This is a classic test-and-bounce pattern - should generate BuyConf=1.000")

    # Test Case 9: EMA test-and-rejection (SELL scenario)
    print("\n📊 TEST CASE 9: EMA test-and-rejection (SELL scenario)")
    print("Expected: SellConf=1.000 (candle tested above EMAs, closed back at EMAs)")

    df = create_test_data(
        price=4350.00,    # Close exactly at EMAs
        ema_10=4350.00,   # At close
        ema_20=4350.00,   # At close
        reg_l=4340.00,    # Below
        reg_u=4370.00,    # Above
        reg_ls=4345.00,   # Below
        reg_us=4365.00,   # Above
        atr=10.0          # Test ATR
    )

    # Create candle that tested above EMAs but closed back at EMAs
    current_candle = {
        'high': 4365.00,   # Tested 15 points above EMAs
        'low': 4348.00,    # Stayed near EMAs
        'close': 4350.00   # Closed exactly at EMAs
    }

    confluence = trader._get_support_resistance_confluence(df, 4350.00, "DOWN", current_candle)
    print(f"Result: BuyConf={confluence['buy_confluence']:.3f}, SellConf={confluence['sell_confluence']:.3f}")

    # Print debug info
    debug_info = getattr(trader, '_last_confluence_debug', 'No debug info')
    print(f"Debug: {debug_info}")

    # Manual calculation
    candle_high = 4365.00
    ema_level = 4350.00
    close_price = 4350.00
    distance_high_to_ema = candle_high - ema_level  # 4365.00 - 4350.00 = 15.00 (tested above)
    distance_close_to_ema = abs(close_price - ema_level)  # 0.00 (closed exactly at EMA)
    print(f"Candle High: {candle_high}, EMA: {ema_level}, Close: {close_price}")
    print(f"High tested {distance_high_to_ema:.2f} points above EMA, but closed exactly at EMA")
    print("This is a classic test-and-rejection pattern - should generate SellConf=1.000")

    print("\n" + "=" * 60)
    print("✅ CONFLUENCE CALCULATION TEST COMPLETE")

if __name__ == "__main__":
    test_confluence_calculation()
