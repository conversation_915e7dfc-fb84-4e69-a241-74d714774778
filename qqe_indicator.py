"""
QQE (Quantitative Qualitative Estimation) Indicator Implementation
Based on the TradingView Pine Script version
Enhanced with Tick Volume Analysis for improved signal quality
"""

import numpy as np
import pandas as pd
from typing import Tuple, Dict, Any
import logging

class QQEIndicator:
    """QQE Indicator for signal generation"""
    
    def __init__(self,
                 rsi_period: int = 7,  # FIXED: Match user's TradingView setting (7)
                 rsi_smoothing: int = 5,
                 qqe_factor: float = 1.0,  # FIXED: Match user's TradingView setting (1.0)
                 threshold: int = 10,
                 volume_lookback: int = 10,
                 volume_divergence_lookback: int = 8,
                 divergence_strength_threshold: float = 0.02):
        """
        Initialize QQE Indicator with Volume Analysis

        Args:
            rsi_period: RSI calculation period (default: 7, matches user's TradingView)
            rsi_smoothing: RSI smoothing factor (default: 5)
            qqe_factor: QQE factor for band calculation (default: 1.0, matches user's TradingView)
            threshold: Threshold for signal filtering (default: 10)
            volume_lookback: Period for volume moving average (default: 10)
            volume_divergence_lookback: Period for divergence detection (default: 8)
            divergence_strength_threshold: Minimum strength to trigger filtering (default: 0.02)
        """
        self.rsi_period = rsi_period
        self.rsi_smoothing = rsi_smoothing
        self.qqe_factor = qqe_factor
        self.threshold = threshold
        self.wilders_period = rsi_period * 2 - 1

        # Volume analysis parameters
        self.volume_lookback = volume_lookback
        self.volume_divergence_lookback = volume_divergence_lookback
        self.divergence_strength_threshold = divergence_strength_threshold

        self.logger = logging.getLogger(__name__)
        
    def calculate_rsi(self, prices: pd.Series) -> pd.Series:
        """Calculate RSI exactly like Pine Script (uses RMA/EWM)"""
        delta = prices.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        # Pine Script uses RMA (Running Moving Average) which is EWM with alpha = 1/length
        alpha = 1.0 / self.rsi_period
        avg_gain = gain.ewm(alpha=alpha, adjust=False).mean()
        avg_loss = loss.ewm(alpha=alpha, adjust=False).mean()

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_ema(self, series: pd.Series, period: int) -> pd.Series:
        """Calculate Exponential Moving Average"""
        return series.ewm(span=period, adjust=False).mean()

    def calculate_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate volume-based indicators for QQE enhancement
        ONLY uses CLOSED candles (excludes current forming candle)

        Args:
            df: DataFrame with OHLCV data

        Returns:
            DataFrame with volume indicators added
        """
        try:
            # Ensure volume column exists
            if 'volume' not in df.columns:
                self.logger.warning("No volume data available, using default values")
                df['volume'] = 1.0  # Default volume

            # CRITICAL: Only use CLOSED candles for volume analysis (exclude current forming candle)
            # This ensures volume analysis matches QQE signal timing
            if len(df) < 2:
                # Not enough data for closed candle analysis
                df['volume_sma'] = df['volume']
                df['volume_ratio'] = 1.0
            else:
                # Use only closed candles for volume calculations
                closed_df = df[:-1].copy()  # Exclude current forming candle

                # Calculate volume indicators on closed candles only
                closed_df['volume_sma'] = closed_df['volume'].rolling(window=self.volume_lookback).mean()
                closed_df['volume_ratio'] = closed_df['volume'] / closed_df['volume_sma']

                # Extend the last closed candle values to current forming candle
                # This ensures we have values for all rows but based on closed data only
                df['volume_sma'] = closed_df['volume_sma'].reindex(df.index, method='ffill')
                df['volume_ratio'] = closed_df['volume_ratio'].reindex(df.index, method='ffill')

            # Volume momentum and trends - also based on closed candles only
            if len(df) < 2:
                df['volume_momentum'] = 0.0
                df['price_momentum'] = 0.0
                df['volume_weighted_momentum'] = 0.0
                df['volume_trend'] = 0
            else:
                # Calculate momentum indicators on closed candles
                closed_df['volume_momentum'] = closed_df['volume'].pct_change(periods=3)

                # Volume-weighted price momentum
                closed_df['price_momentum'] = closed_df['close'].pct_change(periods=3)
                closed_df['volume_weighted_momentum'] = closed_df['price_momentum'] * closed_df['volume_ratio']

                # Volume trend (increasing/decreasing volume) - CLEARER LOGIC
                # Compare recent 3-period average vs previous 3-period average
                recent_vol_avg = closed_df['volume'].rolling(3).mean()  # Periods N-2, N-1, N
                previous_vol_avg = recent_vol_avg.shift(3)              # Periods N-5, N-4, N-3

                closed_df['volume_trend'] = np.where(
                    recent_vol_avg > previous_vol_avg, 1, -1
                )

                # Alternative: Simple volume SMA trend (more intuitive)
                # closed_df['volume_trend'] = np.where(
                #     closed_df['volume_sma'] > closed_df['volume_sma'].shift(5), 1, -1
                # )

                # Extend to full dataframe
                df['volume_momentum'] = closed_df['volume_momentum'].reindex(df.index, method='ffill')
                df['price_momentum'] = closed_df['price_momentum'].reindex(df.index, method='ffill')
                df['volume_weighted_momentum'] = closed_df['volume_weighted_momentum'].reindex(df.index, method='ffill')
                df['volume_trend'] = closed_df['volume_trend'].reindex(df.index, method='ffill')

            # Volume strength classification
            df['volume_strength'] = np.where(
                df['volume_ratio'] >= 1.5, 'HIGH',
                np.where(df['volume_ratio'] >= 1.2, 'MODERATE',
                        np.where(df['volume_ratio'] >= 0.8, 'NORMAL', 'LOW'))
            )

            return df

        except Exception as e:
            self.logger.error(f"Error calculating volume indicators: {e}")
            return df

    def detect_volume_divergence(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Detect volume divergence patterns for trend-following system
        ONLY uses CLOSED candles (excludes current forming candle)

        Args:
            df: DataFrame with price and volume data

        Returns:
            DataFrame with divergence indicators added
        """
        try:
            # Calculate price and volume momentum over divergence lookback period
            lookback = self.volume_divergence_lookback

            # CRITICAL: Only use CLOSED candles for divergence detection
            if len(df) < 2:
                # Not enough data for closed candle analysis
                df['price_direction'] = 0
                df['volume_direction'] = 0
                df['volume_divergence'] = False
                df['divergence_strength'] = 0.0
                df['divergence_type'] = 'NONE'
                return df

            # Use only closed candles for divergence analysis
            closed_df = df[:-1].copy()  # Exclude current forming candle

            # Price direction over lookback period (closed candles only)
            closed_df['price_direction'] = np.where(
                closed_df['close'] > closed_df['close'].shift(lookback), 1, -1
            )

            # Volume direction over lookback period (closed candles only)
            closed_df['volume_direction'] = np.where(
                closed_df['volume_sma'] > closed_df['volume_sma'].shift(lookback), 1, -1
            )

            # Divergence detection (closed candles only)
            closed_df['volume_divergence'] = (closed_df['price_direction'] != closed_df['volume_direction'])

            # Divergence strength (how significant the divergence is)
            # Use manual calculation to avoid NaN issues
            price_change = abs((closed_df['close'] - closed_df['close'].shift(lookback)) / closed_df['close'].shift(lookback))
            volume_change = abs((closed_df['volume_sma'] - closed_df['volume_sma'].shift(lookback)) / closed_df['volume_sma'].shift(lookback))

            # Fill NaN values with 0
            price_change = price_change.fillna(0)
            volume_change = volume_change.fillna(0)

            closed_df['divergence_strength'] = np.where(
                closed_df['volume_divergence'],
                (price_change + volume_change) / 2,
                0
            )

            # Classify divergence type for trend-following
            closed_df['divergence_type'] = np.where(
                ~closed_df['volume_divergence'], 'NONE',
                np.where(
                    (closed_df['price_direction'] == 1) & (closed_df['volume_direction'] == -1),
                    'BEARISH_DIV',  # Price up, volume down - potential reversal
                    np.where(
                        (closed_df['price_direction'] == -1) & (closed_df['volume_direction'] == 1),
                        'BULLISH_DIV',  # Price down, volume up - potential reversal
                        'NONE'
                    )
                )
            )

            # Extend divergence analysis to full dataframe (including current forming candle)
            df['price_direction'] = closed_df['price_direction'].reindex(df.index, method='ffill')
            df['volume_direction'] = closed_df['volume_direction'].reindex(df.index, method='ffill')
            df['volume_divergence'] = closed_df['volume_divergence'].reindex(df.index, method='ffill')
            df['divergence_strength'] = closed_df['divergence_strength'].reindex(df.index, method='ffill')
            df['divergence_type'] = closed_df['divergence_type'].reindex(df.index, method='ffill')

            return df

        except Exception as e:
            self.logger.error(f"Error detecting volume divergence: {e}")
            return df
    
    def calculate_qqe_bands(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate QQE bands and signals - EXACT match to TradingView Pine Script
        Enhanced with Volume Analysis

        Args:
            df: DataFrame with OHLCV data

        Returns:
            DataFrame with QQE indicators and volume analysis added
        """
        try:
            # CRITICAL FIX: Volume analysis should only use CLOSED candles to match QQE behavior
            # Create a copy excluding the current forming candle for volume analysis
            if len(df) > 1:
                closed_candles_df = df[:-1].copy()  # Exclude current forming candle
                closed_candles_df = self.calculate_volume_indicators(closed_candles_df)
                closed_candles_df = self.detect_volume_divergence(closed_candles_df)

                # Copy volume analysis back to full dataframe (excluding last row)
                volume_columns = [
                    'volume_sma', 'volume_ratio', 'volume_momentum', 'volume_weighted_momentum',
                    'volume_trend', 'volume_strength', 'price_direction', 'volume_direction',
                    'volume_divergence', 'divergence_strength', 'divergence_type'
                ]

                for col in volume_columns:
                    if col in closed_candles_df.columns:
                        # Initialize column in full dataframe
                        if col not in df.columns:
                            df[col] = np.nan
                        # Copy closed candle values
                        df.loc[closed_candles_df.index, col] = closed_candles_df[col]
                        # Set current forming candle to last closed value
                        if len(closed_candles_df) > 0:
                            df.iloc[-1, df.columns.get_loc(col)] = closed_candles_df.iloc[-1][col]
            else:
                # Fallback for insufficient data
                df = self.calculate_volume_indicators(df)
                df = self.detect_volume_divergence(df)

            # Calculate RSI (exact match to Pine Script)
            df['rsi'] = self.calculate_rsi(df['close'])

            # Smooth RSI with EMA (RsiMa in Pine Script)
            df['rsi_ma'] = self.calculate_ema(df['rsi'], self.rsi_smoothing)

            # Calculate ATR of RSI (AtrRsi in Pine Script)
            df['atr_rsi'] = abs(df['rsi_ma'] - df['rsi_ma'].shift(1))
            df['ma_atr_rsi'] = self.calculate_ema(df['atr_rsi'], self.wilders_period)
            df['dar'] = self.calculate_ema(df['ma_atr_rsi'], self.wilders_period) * self.qqe_factor

            # Initialize bands and trend (exact match to Pine Script)
            df['longband'] = 0.0
            df['shortband'] = 0.0
            df['trend'] = 1  # Default to 1 like Pine Script
            df['fast_atr_rsi_tl'] = 0.0

            # Calculate QQE bands iteratively (exact Pine Script logic)
            # ONLY process CLOSED candles (exclude current forming candle)
            end_idx = len(df) - 1  # Exclude last candle (current forming)

            for i in range(1, end_idx):
                if pd.isna(df.loc[df.index[i], 'dar']) or pd.isna(df.loc[df.index[i], 'rsi_ma']):
                    df.loc[df.index[i], 'trend'] = df.loc[df.index[i-1], 'trend']
                    continue

                rsi_ma = df.loc[df.index[i], 'rsi_ma']  # RSIndex in Pine Script
                dar = df.loc[df.index[i], 'dar']  # DeltaFastAtrRsi in Pine Script
                rsi_ma_prev = df.loc[df.index[i-1], 'rsi_ma']

                # Calculate new bands (exact Pine Script logic)
                newlongband = rsi_ma - dar
                newshortband = rsi_ma + dar

                # Update longband (exact Pine Script logic)
                longband_prev = df.loc[df.index[i-1], 'longband']
                if rsi_ma_prev > longband_prev and rsi_ma > longband_prev:
                    df.loc[df.index[i], 'longband'] = max(longband_prev, newlongband)
                else:
                    df.loc[df.index[i], 'longband'] = newlongband

                # Update shortband (exact Pine Script logic)
                shortband_prev = df.loc[df.index[i-1], 'shortband']
                if rsi_ma_prev < shortband_prev and rsi_ma < shortband_prev:
                    df.loc[df.index[i], 'shortband'] = min(shortband_prev, newshortband)
                else:
                    df.loc[df.index[i], 'shortband'] = newshortband

                # Determine trend (exact Pine Script logic)
                longband_curr = df.loc[df.index[i], 'longband']
                shortband_curr = df.loc[df.index[i], 'shortband']
                trend_prev = df.loc[df.index[i-1], 'trend']

                # Pine Script exact logic:
                # cross_1 = cross(longband[1], RSIndex)
                # trend := cross(RSIndex, shortband[1]) ? 1 : cross_1 ? -1 : nz(trend[1], 1)

                # cross(RSIndex, shortband[1]) means RSIndex crosses ABOVE shortband[1]
                cross_rsi_above_shortband = (rsi_ma > shortband_prev and rsi_ma_prev <= shortband_prev)

                # cross(longband[1], RSIndex) means longband[1] crosses ABOVE RSIndex
                # This happens when RSIndex goes below longband[1]
                cross_longband_above_rsi = (longband_prev > rsi_ma and longband_prev <= rsi_ma_prev)

                if cross_rsi_above_shortband:
                    df.loc[df.index[i], 'trend'] = 1
                elif cross_longband_above_rsi:
                    df.loc[df.index[i], 'trend'] = -1
                else:
                    df.loc[df.index[i], 'trend'] = trend_prev if not pd.isna(trend_prev) else 1

                # Set FastAtrRsiTL (exact Pine Script logic)
                trend = df.loc[df.index[i], 'trend']
                if trend == 1:
                    df.loc[df.index[i], 'fast_atr_rsi_tl'] = longband_curr
                else:
                    df.loc[df.index[i], 'fast_atr_rsi_tl'] = shortband_curr

            # Generate QQE signals after calculating bands
            df = self.generate_qqe_signals(df)

            return df

        except Exception as e:
            self.logger.error(f"Error calculating QQE bands: {e}")
            return df
    
    def generate_qqe_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Generate QQE signals - EXACT match to TradingView Pine Script logic
        Enhanced with Volume Confirmation and Divergence Filtering

        Args:
            df: DataFrame with QQE bands calculated

        Returns:
            DataFrame with QQE signals and volume analysis added
        """
        try:
            # Initialize signal tracking (exact Pine Script logic)
            df['qqe_long_count'] = 0  # QQExlong in Pine Script
            df['qqe_short_count'] = 0  # QQExshort in Pine Script
            df['qqe_signal'] = 0
            df['qqe_signal_strength'] = 0.0
            df['qqe_trend_signal'] = 0  # Continuous trend signal
            df['qqe_long_signal'] = np.nan  # qqeLong in Pine Script
            df['qqe_short_signal'] = np.nan  # qqeShort in Pine Script

            # Volume-enhanced signal tracking
            df['qqe_volume_confirmation'] = 0.0  # Volume confirmation strength
            df['qqe_divergence_filter'] = False  # Divergence filter flag

            # Calculate QQE crosses (exact Pine Script logic)
            # ONLY process CLOSED candles (exclude current forming candle)
            end_idx = len(df) - 1  # Exclude last candle (current forming)

            for i in range(1, end_idx):
                if (pd.isna(df.loc[df.index[i], 'fast_atr_rsi_tl']) or
                    pd.isna(df.loc[df.index[i], 'rsi_ma'])):
                    df.loc[df.index[i], 'qqe_long_count'] = df.loc[df.index[i-1], 'qqe_long_count']
                    df.loc[df.index[i], 'qqe_short_count'] = df.loc[df.index[i-1], 'qqe_short_count']
                    continue

                fast_atr_rsi_tl = df.loc[df.index[i], 'fast_atr_rsi_tl']  # FastAtrRsiTL
                rsi_ma = df.loc[df.index[i], 'rsi_ma']  # RSIndex
                fast_atr_rsi_tl_prev = df.loc[df.index[i-1], 'fast_atr_rsi_tl']

                # Get previous counts
                qqe_long_prev = df.loc[df.index[i-1], 'qqe_long_count']
                qqe_short_prev = df.loc[df.index[i-1], 'qqe_short_count']

                # Pine Script logic: QQExlong := FastAtrRsiTL < RSIndex ? QQExlong + 1 : 0
                if fast_atr_rsi_tl < rsi_ma:
                    df.loc[df.index[i], 'qqe_long_count'] = qqe_long_prev + 1
                    df.loc[df.index[i], 'qqe_short_count'] = 0
                    df.loc[df.index[i], 'qqe_trend_signal'] = 1
                else:
                    df.loc[df.index[i], 'qqe_long_count'] = 0
                    df.loc[df.index[i], 'qqe_short_count'] = qqe_short_prev + 1
                    df.loc[df.index[i], 'qqe_trend_signal'] = -1

                # Get current counts
                qqe_long_count = df.loc[df.index[i], 'qqe_long_count']
                qqe_short_count = df.loc[df.index[i], 'qqe_short_count']

                # Pine Script signal generation logic (EXACT)
                # qqeLong = QQExlong == 1 ? FastAtrRsiTL[1] - 50 : na
                # qqeShort = QQExshort == 1 ? FastAtrRsiTL[1] - 50 : na

                # Get volume data for current candle
                volume_ratio = df.loc[df.index[i], 'volume_ratio'] if not pd.isna(df.loc[df.index[i], 'volume_ratio']) else 1.0
                volume_strength = df.loc[df.index[i], 'volume_strength']
                divergence_type = df.loc[df.index[i], 'divergence_type']

                # Calculate volume confirmation factor
                volume_confirmation = self._calculate_volume_confirmation(volume_ratio, volume_strength)
                df.loc[df.index[i], 'qqe_volume_confirmation'] = volume_confirmation

                # TEMPORARILY DISABLED: Smart divergence filtering with strength threshold
                # BEARISH_DIV: Block LONG trades, Allow SHORT trades
                # BULLISH_DIV: Block SHORT trades, Allow LONG trades
                current_qqe_signal = 0
                if qqe_long_count > 0:
                    current_qqe_signal = 1  # Long signal
                elif qqe_short_count > 0:
                    current_qqe_signal = -1  # Short signal

                # DIVERGENCE FILTER TEMPORARILY DISABLED - Always set to False
                divergence_filter = False
                divergence_strength = df.loc[df.index[i], 'divergence_strength']

                # COMMENTED OUT: Only apply filter if divergence is strong enough
                # if not pd.isna(divergence_strength) and divergence_strength >= self.divergence_strength_threshold:
                #     if divergence_type == 'BEARISH_DIV' and current_qqe_signal > 0:
                #         divergence_filter = True  # Block long trades during strong bearish divergence
                #     elif divergence_type == 'BULLISH_DIV' and current_qqe_signal < 0:
                #         divergence_filter = True  # Block short trades during strong bullish divergence

                df.loc[df.index[i], 'qqe_divergence_filter'] = divergence_filter

                if qqe_long_count == 1:
                    # Fresh QQE Long signal (crossover) - EXACT Pine Script with Volume Enhancement
                    df.loc[df.index[i], 'qqe_long_signal'] = fast_atr_rsi_tl_prev - 50

                    # Check divergence filter before setting signal
                    if not divergence_filter:
                        df.loc[df.index[i], 'qqe_signal'] = 1

                        # Base strength from Pine Script
                        base_strength = abs(fast_atr_rsi_tl_prev - 50) / 50

                        # Apply volume confirmation (enhance strength if volume confirms)
                        volume_enhanced_strength = base_strength * volume_confirmation
                        df.loc[df.index[i], 'qqe_signal_strength'] = min(volume_enhanced_strength, 1.0)
                    else:
                        # Signal blocked by divergence filter
                        df.loc[df.index[i], 'qqe_signal'] = 0
                        df.loc[df.index[i], 'qqe_signal_strength'] = 0.0

                elif qqe_short_count == 1:
                    # Fresh QQE Short signal (crossover) - EXACT Pine Script with Volume Enhancement
                    df.loc[df.index[i], 'qqe_short_signal'] = fast_atr_rsi_tl_prev - 50

                    # Check divergence filter before setting signal
                    if not divergence_filter:
                        df.loc[df.index[i], 'qqe_signal'] = -1

                        # Base strength from Pine Script
                        base_strength = abs(fast_atr_rsi_tl_prev - 50) / 50

                        # Apply volume confirmation (enhance strength if volume confirms)
                        volume_enhanced_strength = base_strength * volume_confirmation
                        df.loc[df.index[i], 'qqe_signal_strength'] = min(volume_enhanced_strength, 1.0)
                    else:
                        # Signal blocked by divergence filter
                        df.loc[df.index[i], 'qqe_signal'] = 0
                        df.loc[df.index[i], 'qqe_signal_strength'] = 0.0

                else:
                    # FIXED: EXACT TradingView logic - No signal when count != 1
                    # TradingView only generates signals on fresh crossovers (count == 1)
                    df.loc[df.index[i], 'qqe_signal'] = 0
                    df.loc[df.index[i], 'qqe_signal_strength'] = 0.0

            return df

        except Exception as e:
            self.logger.error(f"Error generating QQE signals: {e}")
            return df

    def _calculate_volume_confirmation(self, volume_ratio: float, volume_strength: str) -> float:
        """
        Calculate volume confirmation factor for QQE signals

        Args:
            volume_ratio: Current volume / average volume
            volume_strength: Volume strength classification

        Returns:
            Volume confirmation factor (0.5 to 1.5)
        """
        try:
            # Base confirmation factor
            base_factor = 1.0

            # Adjust based on volume ratio
            if volume_ratio >= 1.5:  # High volume
                base_factor = 1.3
            elif volume_ratio >= 1.2:  # Moderate high volume
                base_factor = 1.15
            elif volume_ratio >= 0.8:  # Normal volume
                base_factor = 1.0
            else:  # Low volume
                base_factor = 0.7

            # Fine-tune based on volume strength classification
            strength_multiplier = {
                'HIGH': 1.2,
                'MODERATE': 1.1,
                'NORMAL': 1.0,
                'LOW': 0.8
            }.get(volume_strength, 1.0)

            # Combine factors and ensure reasonable bounds
            confirmation_factor = base_factor * strength_multiplier
            return max(0.5, min(1.5, confirmation_factor))

        except Exception as e:
            self.logger.error(f"Error calculating volume confirmation: {e}")
            return 1.0  # Default to neutral if error
    
    def get_qqe_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Get current QQE analysis - ONLY uses CLOSED candles

        Args:
            df: DataFrame with QQE data

        Returns:
            Dictionary with QQE analysis
        """
        try:
            if len(df) < 2:
                return {}

            # Use LAST CLOSED candle (not current forming candle)
            # This matches Pine Script behavior which only uses closed candles
            latest = df.iloc[-2]  # -2 = last closed candle, -1 = current forming candle

            return {
                'rsi': latest.get('rsi', 0),
                'rsi_ma': latest.get('rsi_ma', 0),
                'fast_atr_rsi_tl': latest.get('fast_atr_rsi_tl', 0),
                'trend': latest.get('trend', 0),
                'qqe_signal': latest.get('qqe_signal', 0),
                'qqe_signal_strength': latest.get('qqe_signal_strength', 0),
                'qqe_trend_signal': latest.get('qqe_trend_signal', 0),
                'qqe_long_count': latest.get('qqe_long_count', 0),
                'qqe_short_count': latest.get('qqe_short_count', 0),
                'qqe_long_signal': latest.get('qqe_long_signal', np.nan),
                'qqe_short_signal': latest.get('qqe_short_signal', np.nan),
                'last_signal_type': 'LONG' if latest.get('qqe_long_count', 0) > 0 else 'SHORT' if latest.get('qqe_short_count', 0) > 0 else 'NONE',
                'pine_script_signal': 'LONG' if latest.get('qqe_long_count', 0) == 1 else 'SHORT' if latest.get('qqe_short_count', 0) == 1 else 'NONE',
                # Volume analysis additions
                'volume_ratio': latest.get('volume_ratio', 1.0),
                'volume_strength': latest.get('volume_strength', 'NORMAL'),
                'volume_confirmation': latest.get('qqe_volume_confirmation', 1.0),
                'divergence_type': latest.get('divergence_type', 'NONE'),
                'divergence_filter': latest.get('qqe_divergence_filter', False),
                'volume_trend': latest.get('volume_trend', 0),
                'volume_momentum': latest.get('volume_momentum', 0)
            }

        except Exception as e:
            self.logger.error(f"Error getting QQE analysis: {e}")
            return {}
