#!/usr/bin/env python3
"""
Test Channel State Variable Fix
Verify that channel_state variable is properly initialized in all code paths
"""

import sys
import pandas as pd
import numpy as np
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data():
    """Create test data for regime detection"""
    logger.info("📊 Creating test data...")
    
    # Create 120 periods of test data (need >100 for ATR lookback)
    periods = 120
    data = []
    
    for i in range(periods):
        # Create price data with trend
        base_price = 2000 + i * 0.1  # Slight upward trend
        
        # Add some volatility
        price = base_price + np.random.uniform(-0.5, 0.5)
        
        # Create OHLC
        high = price + 0.2
        low = price - 0.2
        open_price = price + np.random.uniform(-0.1, 0.1)
        close = price
        volume = 1000 + i * 10
        
        data.append({
            'datetime': pd.Timestamp('2024-01-01') + pd.Timedelta(minutes=5*i),
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('datetime', inplace=True)
    
    logger.info(f"✅ Created {len(df)} periods (need >100 for ATR lookback)")
    return df

def test_channel_state_variable():
    """Test that channel_state variable is properly initialized"""
    logger.info("\n🔧 TESTING CHANNEL STATE VARIABLE FIX...")
    
    try:
        # Import the fixed live trader
        from fixed_live_trader import FixedLiveTrader
        
        # Create trader instance
        trader = FixedLiveTrader()
        
        # Get test data
        df = create_test_data()
        
        # Add required technical indicators
        df_with_indicators = trader.feature_engineer.create_technical_indicators(df)
        df_with_indicators = trader.regime_detector.calculate_regime_indicators(df_with_indicators)
        df_with_indicators = trader.regime_detector.calculate_candle_position(df_with_indicators)
        
        # Test regime detection (this should not fail with channel_state error)
        logger.info("   📊 Testing regime detection...")

        regime_result = trader.regime_detector.detect_regime(df_with_indicators)

        # Unpack the tuple result
        regime, confidence, details, trend_direction, accurate_trend_direction = regime_result

        logger.info(f"   ✅ Regime detection successful!")
        logger.info(f"      Regime: {regime}")
        logger.info(f"      Confidence: {confidence:.2f}")
        logger.info(f"      Trend Direction: {trend_direction}")
        logger.info(f"      Channel State: {details.get('simple_channel_state', 'N/A')}")
        logger.info(f"      Trending Score: {details.get('trending_score', 'N/A')}")
        logger.info(f"      Ranging Score: {details.get('ranging_score', 'N/A')}")

        # Verify channel_state is properly set
        channel_state = details.get('simple_channel_state')
        if channel_state is not None:
            logger.info(f"   ✅ Channel state properly initialized: {channel_state}")
        else:
            logger.error(f"   ❌ Channel state is None (should be initialized)")
            return False
        
        logger.info("\n✅ Channel State Variable Test: PASSED")
        return True
        
    except Exception as e:
        if "cannot access local variable 'channel_state'" in str(e):
            logger.error(f"❌ Channel State Variable Test: FAILED - Variable scope issue still exists")
            logger.error(f"   Error: {e}")
            return False
        else:
            logger.error(f"❌ Channel State Variable Test: FAILED - Different error: {e}")
            import traceback
            logger.error(f"   Traceback: {traceback.format_exc()}")
            return False

def test_both_code_paths():
    """Test both short-term and long-term regime detection paths"""
    logger.info("\n🔧 TESTING BOTH CODE PATHS...")
    
    try:
        from fixed_live_trader import FixedLiveTrader
        
        # Create trader instance
        trader = FixedLiveTrader()
        
        # Test 1: Create data that should use short-term regression
        logger.info("   📊 Test 1: Short-term regression path...")
        df1 = create_test_data()
        
        # Modify data to create conflicting trends (should trigger short-term usage)
        # Make recent prices go down while overall trend is up
        for i in range(-5, 0):  # Last 5 candles
            df1.iloc[i, df1.columns.get_loc('close')] = df1.iloc[i]['close'] - (abs(i) * 0.3)
            df1.iloc[i, df1.columns.get_loc('high')] = df1.iloc[i]['close'] + 0.1
            df1.iloc[i, df1.columns.get_loc('low')] = df1.iloc[i]['close'] - 0.1
        
        df1_with_indicators = trader.feature_engineer.create_technical_indicators(df1)
        df1_with_indicators = trader.regime_detector.calculate_regime_indicators(df1_with_indicators)
        df1_with_indicators = trader.regime_detector.calculate_candle_position(df1_with_indicators)
        regime1_result = trader.regime_detector.detect_regime(df1_with_indicators)

        # Unpack tuple
        regime1, conf1, details1, trend1, acc_trend1 = regime1_result

        logger.info(f"      Result: {regime1}, Channel State: {details1.get('simple_channel_state', 'N/A')}")

        # Test 2: Create data that should use long-term regression
        logger.info("   📊 Test 2: Long-term regression path...")
        df2 = create_test_data()

        # Keep consistent trend (should use long-term)
        df2_with_indicators = trader.feature_engineer.create_technical_indicators(df2)
        df2_with_indicators = trader.regime_detector.calculate_regime_indicators(df2_with_indicators)
        df2_with_indicators = trader.regime_detector.calculate_candle_position(df2_with_indicators)
        regime2_result = trader.regime_detector.detect_regime(df2_with_indicators)

        # Unpack tuple
        regime2, conf2, details2, trend2, acc_trend2 = regime2_result

        logger.info(f"      Result: {regime2}, Channel State: {details2.get('simple_channel_state', 'N/A')}")

        # Verify both tests have channel_state defined
        if details1.get('simple_channel_state') is not None and details2.get('simple_channel_state') is not None:
            logger.info("   ✅ Both code paths properly initialize channel_state")
        else:
            logger.error("   ❌ One or both code paths failed to initialize channel_state")
            return False
        
        logger.info("\n✅ Both Code Paths Test: PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Both Code Paths Test: FAILED - {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run channel state fix tests"""
    logger.info("🧪 TESTING CHANNEL STATE VARIABLE FIX")
    logger.info("=" * 70)
    
    tests = [
        ("Channel State Variable", test_channel_state_variable),
        ("Both Code Paths", test_both_code_paths)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            logger.error(f"❌ {test_name}: FAILED with exception - {e}")
    
    logger.info("\n" + "=" * 70)
    logger.info("🏁 CHANNEL STATE FIX TEST SUMMARY")
    logger.info(f"   ✅ Passed: {passed}")
    logger.info(f"   ❌ Failed: {failed}")
    logger.info(f"   📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        logger.info("🎉 ALL CHANNEL STATE TESTS PASSED!")
        logger.info("   The 'cannot access local variable channel_state' error is fixed")
        return True
    else:
        logger.error(f"⚠️ {failed} tests failed. Channel state issue may remain.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
