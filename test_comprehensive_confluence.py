#!/usr/bin/env python3
"""
Comprehensive Confluence System Test

Tests the complete buy/sell confluence calculation including:
1. Regression channels (10, 20, 100-period) with wick detection
2. EMA levels with wick detection  
3. Swing highs/lows integration
4. Proper column names and data structure
"""

import pandas as pd
import numpy as np
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader

def create_comprehensive_test_data():
    """Create test data with all required columns and realistic scenarios"""
    
    # Create 30 candles for swing detection
    dates = pd.date_range('2024-01-01', periods=30, freq='5min')
    
    # Base price around 4300
    base_prices = np.linspace(4295, 4305, 30)
    
    # Create realistic OHLC data with some volatility
    np.random.seed(42)  # For reproducible results
    
    opens = base_prices + np.random.normal(0, 1, 30)
    highs = opens + np.abs(np.random.normal(2, 1, 30))
    lows = opens - np.abs(np.random.normal(2, 1, 30))
    closes = opens + np.random.normal(0, 1.5, 30)
    
    # Ensure OHLC relationships are valid
    for i in range(30):
        highs[i] = max(opens[i], highs[i], closes[i])
        lows[i] = min(opens[i], lows[i], closes[i])
    
    df = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': [500] * 30,
        
        # EMA levels (CORRECT column names)
        'ema_10': [4302.0] * 30,
        'ema_20': [4300.0] * 30,
        
        # Regression channels (CORRECT column names as expected by code)
        'regression_upper': [4310.0] * 30,        # 20-period upper
        'regression_lower': [4290.0] * 30,        # 20-period lower
        'regression_upper_short': [4308.0] * 30,  # 10-period upper
        'regression_lower_short': [4292.0] * 30,  # 10-period lower
        'regression_upper_regime': [4320.0] * 30, # 100-period upper
        'regression_lower_regime': [4280.0] * 30, # 100-period lower
    }, index=dates)
    
    return df

def test_scenario(trader, df, scenario_name, current_price, current_candle, expected_buy_min=0.0, expected_sell_min=0.0):
    """Test a specific confluence scenario"""
    
    print(f"\n📊 {scenario_name}")
    print("-" * 50)
    print(f"Current Price: {current_price}")
    print(f"Candle HIGH: {current_candle['high']}")
    print(f"Candle LOW: {current_candle['low']}")
    print(f"Candle CLOSE: {current_candle['close']}")
    
    # Calculate confluence
    confluence = trader._get_support_resistance_confluence(
        df, current_price, "UP", current_candle
    )
    
    buy_conf = confluence['buy_confluence']
    sell_conf = confluence['sell_confluence']
    
    print(f"📈 BUY Confluence: {buy_conf:.3f} (expected ≥ {expected_buy_min:.3f})")
    print(f"📉 SELL Confluence: {sell_conf:.3f} (expected ≥ {expected_sell_min:.3f})")
    
    # Check results
    buy_passed = buy_conf >= expected_buy_min
    sell_passed = sell_conf >= expected_sell_min
    
    print(f"✅ BUY Test: {'PASSED' if buy_passed else 'FAILED'}")
    print(f"✅ SELL Test: {'PASSED' if sell_passed else 'FAILED'}")
    
    return buy_passed, sell_passed

def test_comprehensive_confluence():
    """Test the complete confluence system"""
    
    print("🧪 Comprehensive Confluence System Test")
    print("=" * 60)
    
    # Create test trader
    trader = FixedLiveTrader("XAUUSD!")
    
    # Create test data
    df = create_comprehensive_test_data()
    
    print("📋 Test Data Setup:")
    print("  - EMA10: 4302.0, EMA20: 4300.0")
    print("  - 20-period regression: 4290.0 - 4310.0")
    print("  - 10-period regression: 4292.0 - 4308.0") 
    print("  - 100-period regression: 4280.0 - 4320.0")
    print("  - 30 candles with swing highs/lows")
    
    test_results = []
    
    # Test 1: BUY signal - LOW wick tests 20-period regression support
    test_results.append(test_scenario(
        trader, df, "Test 1: BUY - LOW Wick Tests 20-Period Regression Support",
        current_price=4295.0,
        current_candle={'high': 4298.0, 'low': 4289.5, 'close': 4295.0},  # LOW tests 4290 support
        expected_buy_min=0.5,  # Should get good confluence
        expected_sell_min=0.0
    ))
    
    # Test 2: SELL signal - HIGH wick tests 10-period regression resistance  
    test_results.append(test_scenario(
        trader, df, "Test 2: SELL - HIGH Wick Tests 10-Period Regression Resistance",
        current_price=4305.0,
        current_candle={'high': 4308.5, 'low': 4302.0, 'close': 4305.0},  # HIGH tests 4308 resistance
        expected_buy_min=0.0,
        expected_sell_min=0.5  # Should get good confluence
    ))
    
    # Test 3: BUY signal - LOW wick tests EMA20 support
    test_results.append(test_scenario(
        trader, df, "Test 3: BUY - LOW Wick Tests EMA20 Support",
        current_price=4300.1,
        current_candle={'high': 4302.0, 'low': 4299.8, 'close': 4300.1},  # LOW tests EMA20 at 4300
        expected_buy_min=0.8,  # EMA test should give high confluence
        expected_sell_min=0.0
    ))
    
    # Test 4: SELL signal - HIGH wick tests EMA10 resistance
    test_results.append(test_scenario(
        trader, df, "Test 4: SELL - HIGH Wick Tests EMA10 Resistance", 
        current_price=4301.9,
        current_candle={'high': 4302.2, 'low': 4300.0, 'close': 4301.9},  # HIGH tests EMA10 at 4302
        expected_buy_min=0.0,
        expected_sell_min=0.8  # EMA test should give high confluence
    ))
    
    # Test 5: BUY signal - LOW wick tests 100-period regression support
    test_results.append(test_scenario(
        trader, df, "Test 5: BUY - LOW Wick Tests 100-Period Regression Support",
        current_price=4285.0,
        current_candle={'high': 4288.0, 'low': 4279.5, 'close': 4285.0},  # LOW tests 4280 support
        expected_buy_min=0.5,  # Should get good confluence
        expected_sell_min=0.0
    ))
    
    # Test 6: No significant wick tests
    test_results.append(test_scenario(
        trader, df, "Test 6: No Significant Wick Tests",
        current_price=4300.0,
        current_candle={'high': 4301.0, 'low': 4299.0, 'close': 4300.0},  # No significant tests
        expected_buy_min=0.0,
        expected_sell_min=0.0  # Should get low confluence
    ))
    
    # Summary
    print("\n🎯 Test Summary")
    print("=" * 30)
    
    total_tests = len(test_results) * 2  # Each scenario tests both BUY and SELL
    passed_tests = sum(buy + sell for buy, sell in test_results)
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed Tests: {passed_tests}")
    print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
    
    # Individual results
    for i, (buy_passed, sell_passed) in enumerate(test_results, 1):
        print(f"  Test {i}: BUY {'✅' if buy_passed else '❌'}, SELL {'✅' if sell_passed else '❌'}")
    
    overall_success = passed_tests >= (total_tests * 0.8)  # 80% pass rate
    
    if overall_success:
        print(f"\n🎉 SUCCESS: Confluence system working correctly!")
        print("✅ Wick-based detection implemented for all levels")
        print("✅ Regression channels properly detected")
        print("✅ EMA test-and-bounce working")
        print("✅ Swing highs/lows integrated")
    else:
        print(f"\n❌ ISSUES DETECTED: Some confluence calculations failed")
        print("Need to investigate specific failures")
    
    return overall_success

if __name__ == "__main__":
    try:
        success = test_comprehensive_confluence()
        exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
