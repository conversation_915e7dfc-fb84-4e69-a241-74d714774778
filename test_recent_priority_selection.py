#!/usr/bin/env python3
"""
Test the improved swing detection that prioritizes recent swing points when they're close enough
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_recent_priority_selection():
    """Test that the algorithm prioritizes recent swing points when they're close to the extreme"""
    print("🧪 TESTING RECENT PRIORITY SWING SELECTION")
    print("=" * 60)
    
    # Create trader instance
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test Scenario: Recent swing low (4208.74) vs older extreme low (4194.55)
    print("\n📊 SCENARIO: Recent vs Older Swing Points")
    dates = pd.date_range(start='2024-01-01', periods=20, freq='5min')
    
    # Create data that matches your XAUUSD situation:
    # - Very old extreme low at 4194.55 (17 candles ago)
    # - Recent relevant low at 4208.74 (2-3 candles ago)
    
    highs = [
        4200.0, 4198.0, 4196.55, 4198.0, 4200.0,  # Old extreme low at index 2 (4194.55)
        4202.0, 4205.0, 4208.0, 4210.0, 4212.0,   # Recovery
        4215.0, 4218.0, 4220.0, 4218.0, 4216.0,   # Peak and decline
        4214.0, 4212.0, 4210.74, 4212.0, 4214.0   # Recent low at index 17 (4208.74)
    ]
    
    lows = [
        4197.0, 4195.0, 4194.55, 4195.0, 4197.0,  # OLD EXTREME LOW at index 2
        4199.0, 4202.0, 4205.0, 4207.0, 4209.0,   # Recovery
        4212.0, 4215.0, 4217.0, 4215.0, 4213.0,   # Peak and decline
        4211.0, 4209.0, 4208.74, 4209.0, 4211.0   # RECENT LOW at index 17
    ]
    
    closes = [(h + l) / 2 for h, l in zip(highs, lows)]
    opens = closes.copy()
    
    # Add ATR column for threshold calculation
    atr_values = [2.5] * 20  # Typical XAUUSD ATR
    
    df = pd.DataFrame({
        'time': dates,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'tick_volume': [100] * 20,
        'atr': atr_values
    }).set_index('time')
    
    print("📈 DATA ANALYSIS:")
    print("Swing Low Candidates:")
    print(f"  📊 Old Extreme Low: 4194.55 (index 2, ~17 candles ago)")
    print(f"  📊 Recent Low: 4208.74 (index 17, ~2 candles ago)")
    print(f"  📊 Price Difference: {4208.74 - 4194.55:.2f} points")
    print(f"  📊 ATR Threshold (0.5 ATR): {0.5 * 2.5:.2f} points")
    print()
    
    print("Expected Logic:")
    print(f"  🎯 Difference ({4208.74 - 4194.55:.2f}) > Threshold ({0.5 * 2.5:.2f})")
    print(f"  🎯 Should select: 4208.74 (recent and relevant)")
    print(f"  🎯 Should NOT select: 4194.55 (too old and extreme)")
    print()
    
    print("Last 5 candles:")
    for i in range(5):
        idx = -(5-i)
        candle = df.iloc[idx]
        marker = ""
        if len(df) + idx == 17:
            marker = " ← Expected Recent Low (4208.74)"
        elif len(df) + idx == 2:
            marker = " ← Old Extreme Low (4194.55)"
        print(f"  {len(df) + idx:2d}: H={candle['high']:8.2f} L={candle['low']:8.2f}{marker}")
    
    # Run swing detection
    print(f"\n🔍 RUNNING IMPROVED SWING DETECTION:")
    swing_points = trader.find_recent_swing_points(df)
    
    print(f"\n📋 RESULTS:")
    success = True
    
    if swing_points['recent_low']:
        detected_low = swing_points['recent_low']
        expected_low = 4208.74  # Recent, relevant low
        old_extreme_low = 4194.55  # Old, extreme low
        candles_ago = swing_points['recent_low_candles_ago']
        
        print(f"✅ Detected Swing Low: {detected_low:.2f}")
        print(f"   📅 Candles ago: {candles_ago}")
        
        if abs(detected_low - expected_low) < 0.01:
            print(f"   🎉 CORRECT: Selected recent relevant low (4208.74)")
            print(f"   ✅ Avoided old extreme low (4194.55)")
            success = True
        elif abs(detected_low - old_extreme_low) < 0.01:
            print(f"   ❌ INCORRECT: Selected old extreme low (4194.55)")
            print(f"   ⚠️  Should have selected recent relevant low (4208.74)")
            success = False
        else:
            print(f"   ❓ UNEXPECTED: Selected {detected_low:.2f}")
            success = False
    else:
        print("❌ No swing low detected")
        success = False
    
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}: Recent priority selection test")
    print("=" * 60)
    
    if success:
        print("🎉 The algorithm correctly prioritizes recent relevant swing points!")
        print("📊 It avoids selecting old extreme points that are no longer relevant")
        print("🔍 This should fix your XAUUSD issue with 4194.55 vs 4208.74")
    else:
        print("⚠️  The algorithm still needs adjustment")
        print("🔧 May need to adjust the ATR threshold or recent candle definition")
    
    return success

if __name__ == "__main__":
    test_recent_priority_selection()
