"""
Complete System Validation Script
Validates the entire XAUUSD LSTM Trading System
"""
import sys
import os
from pathlib import Path
import logging
import pandas as pd
import numpy as np
from datetime import datetime

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from config.config import *

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def validate_project_structure():
    """Validate project structure and files"""
    logger.info("🔍 Validating project structure...")
    
    required_dirs = [
        'src', 'config', 'data', 'models', 'reports', 'logs', 'tests'
    ]
    
    required_files = [
        'main.py',
        'test_system.py',
        'requirements.txt',
        'README.md',
        'config/config.py',
        'src/data_manager.py',
        'src/feature_engineering.py',
        'src/data_preprocessing.py',
        'src/lstm_model.py',
        'src/training_pipeline.py',
        'src/model_evaluation.py',
        'src/live_data_processor.py',
        'src/trading_logic.py',
        'src/risk_management.py',
        'src/mt5_integration.py',
        'src/live_trading_system.py',
        'src/performance_dashboard.py'
    ]
    
    # Check directories
    missing_dirs = []
    for dir_name in required_dirs:
        if not Path(dir_name).exists():
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        logger.error(f"❌ Missing directories: {missing_dirs}")
        return False
    
    # Check files
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"❌ Missing files: {missing_files}")
        return False
    
    logger.info("✅ Project structure validation passed")
    return True

def validate_data_integrity():
    """Validate data file integrity"""
    logger.info("🔍 Validating data integrity...")
    
    data_file = Path('data/XAU_5m_data.csv')
    
    if not data_file.exists():
        logger.error("❌ Data file not found: data/XAU_5m_data.csv")
        return False
    
    try:
        # Load and validate data
        data = pd.read_csv(data_file, index_col=0, parse_dates=True)
        
        # Check required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            logger.error(f"❌ Missing columns in data: {missing_columns}")
            return False
        
        # Check data size
        if len(data) < 10000:
            logger.warning(f"⚠️ Data size is small: {len(data)} records")
        
        # Check for NaN values
        nan_count = data.isnull().sum().sum()
        if nan_count > 0:
            logger.warning(f"⚠️ Found {nan_count} NaN values in data")
        
        # Check OHLC relationships
        invalid_ohlc = (
            (data['high'] < data['low']) |
            (data['high'] < data['open']) |
            (data['high'] < data['close']) |
            (data['low'] > data['open']) |
            (data['low'] > data['close'])
        ).sum()
        
        if invalid_ohlc > 0:
            logger.warning(f"⚠️ Found {invalid_ohlc} invalid OHLC relationships")
        
        logger.info(f"✅ Data validation passed. Records: {len(data)}, Period: {data.index[0]} to {data.index[-1]}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Data validation failed: {e}")
        return False

def validate_configuration():
    """Validate configuration settings"""
    logger.info("🔍 Validating configuration...")

    try:
        # Import and check config
        import config.config as cfg

        # Check critical parameters
        critical_params = {
            'RISK_PERCENT': cfg.RISK_PERCENT,
            'ATR_MULTIPLIER': cfg.ATR_MULTIPLIER,
            'SIGNAL_THRESHOLD': cfg.SIGNAL_THRESHOLD,
            'SEQUENCE_LENGTH': cfg.SEQUENCE_LENGTH,
            'MIN_ACCOUNT_BALANCE': cfg.MIN_ACCOUNT_BALANCE
        }
        
        for param, value in critical_params.items():
            if value is None:
                logger.error(f"❌ Critical parameter {param} is None")
                return False
            
            if isinstance(value, (int, float)) and value <= 0:
                logger.error(f"❌ Critical parameter {param} has invalid value: {value}")
                return False
        
        # Validate risk parameters
        if cfg.RISK_PERCENT > 10:
            logger.warning(f"⚠️ High risk percentage: {cfg.RISK_PERCENT}%")

        if cfg.ATR_MULTIPLIER < 1 or cfg.ATR_MULTIPLIER > 3:
            logger.warning(f"⚠️ ATR multiplier outside normal range: {cfg.ATR_MULTIPLIER}")
        
        logger.info("✅ Configuration validation passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration validation failed: {e}")
        return False

def validate_dependencies():
    """Validate Python dependencies"""
    logger.info("🔍 Validating dependencies...")
    
    required_packages = [
        'pandas', 'numpy', 'sklearn', 'tensorflow',
        'matplotlib', 'seaborn', 'plotly', 'joblib',
        'MetaTrader5', 'talib'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"❌ Missing packages: {missing_packages}")
        logger.info("Install missing packages with: pip install -r requirements.txt")
        return False
    
    logger.info("✅ Dependencies validation passed")
    return True

def validate_model_architecture():
    """Validate LSTM model architecture"""
    logger.info("🔍 Validating model architecture...")
    
    try:
        from src.lstm_model import LSTMTradingModel
        
        # Test model creation
        input_shape = (60, 50)  # sequence_length, num_features
        num_classes = 3
        
        model = LSTMTradingModel(input_shape, num_classes)
        keras_model = model.build_model()
        
        if keras_model is None:
            logger.error("❌ Failed to build model")
            return False
        
        # Check model structure
        if len(keras_model.layers) < 5:
            logger.error("❌ Model architecture too simple")
            return False
        
        # Check input/output shapes
        if keras_model.input_shape[1:] != input_shape:
            logger.error(f"❌ Input shape mismatch: expected {input_shape}, got {keras_model.input_shape[1:]}")
            return False
        
        if keras_model.output_shape[1] != num_classes:
            logger.error(f"❌ Output shape mismatch: expected {num_classes}, got {keras_model.output_shape[1]}")
            return False
        
        logger.info("✅ Model architecture validation passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Model architecture validation failed: {e}")
        return False

def validate_feature_engineering():
    """Validate feature engineering pipeline"""
    logger.info("🔍 Validating feature engineering...")
    
    try:
        from src.feature_engineering import FeatureEngineer
        
        # Create sample data
        dates = pd.date_range('2023-01-01', periods=1000, freq='5T')
        np.random.seed(42)
        
        close_prices = 2000 + np.cumsum(np.random.randn(1000) * 0.1)
        
        sample_data = pd.DataFrame({
            'open': close_prices + np.random.randn(1000) * 0.05,
            'high': close_prices + np.abs(np.random.randn(1000) * 0.1),
            'low': close_prices - np.abs(np.random.randn(1000) * 0.1),
            'close': close_prices,
            'volume': np.random.randint(100, 1000, 1000)
        }, index=dates)
        
        # Ensure OHLC relationships
        sample_data['high'] = np.maximum.reduce([
            sample_data['open'], sample_data['high'], 
            sample_data['low'], sample_data['close']
        ])
        sample_data['low'] = np.minimum.reduce([
            sample_data['open'], sample_data['high'], 
            sample_data['low'], sample_data['close']
        ])
        
        # Test feature engineering
        feature_engineer = FeatureEngineer()
        features_data = feature_engineer.create_technical_indicators(sample_data)
        
        if features_data is None:
            logger.error("❌ Feature engineering failed")
            return False
        
        if len(features_data.columns) <= len(sample_data.columns):
            logger.error("❌ No features were created")
            return False
        
        # Test target creation
        target_data = feature_engineer.create_target_variable(features_data)
        
        if 'target' not in target_data.columns:
            logger.error("❌ Target variable not created")
            return False
        
        logger.info(f"✅ Feature engineering validation passed. Created {len(features_data.columns) - len(sample_data.columns)} features")
        return True
        
    except Exception as e:
        logger.error(f"❌ Feature engineering validation failed: {e}")
        return False

def validate_risk_management():
    """Validate risk management system"""
    logger.info("🔍 Validating risk management...")
    
    try:
        from src.risk_management import RiskManager
        
        risk_manager = RiskManager()
        
        # Test position size calculation
        position_size = risk_manager.calculate_optimal_position_size(
            account_balance=10000,
            entry_price=2000.0,
            stop_loss=1990.0,
            confidence=0.8
        )
        
        if position_size <= 0:
            logger.error("❌ Invalid position size calculation")
            return False
        
        if position_size > 1.0:  # Unreasonably large position
            logger.error(f"❌ Position size too large: {position_size}")
            return False
        
        # Test risk validation
        account_info = {
            'balance': 10000,
            'equity': 10000,
            'free_margin': 5000,
            'margin_level': 1000
        }
        
        is_valid, reason = risk_manager.validate_trade_risk(
            account_info=account_info,
            position_size=0.1,
            entry_price=2000.0,
            stop_loss=1990.0
        )
        
        if not isinstance(is_valid, bool):
            logger.error("❌ Risk validation should return boolean")
            return False
        
        logger.info("✅ Risk management validation passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Risk management validation failed: {e}")
        return False

def run_comprehensive_validation():
    """Run comprehensive system validation"""
    logger.info("🚀 Starting comprehensive system validation...")
    
    validation_tests = [
        ("Project Structure", validate_project_structure),
        ("Data Integrity", validate_data_integrity),
        ("Configuration", validate_configuration),
        ("Dependencies", validate_dependencies),
        ("Model Architecture", validate_model_architecture),
        ("Feature Engineering", validate_feature_engineering),
        ("Risk Management", validate_risk_management)
    ]
    
    passed_tests = 0
    total_tests = len(validation_tests)
    
    for test_name, test_func in validation_tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running {test_name} validation...")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                passed_tests += 1
                logger.info(f"✅ {test_name} validation PASSED")
            else:
                logger.error(f"❌ {test_name} validation FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} validation ERROR: {e}")
    
    # Final summary
    logger.info(f"\n{'='*60}")
    logger.info(f"VALIDATION SUMMARY")
    logger.info(f"{'='*60}")
    logger.info(f"Tests passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL VALIDATIONS PASSED! System is ready for use.")
        logger.info("\nNext steps:")
        logger.info("1. Train the model: python main.py train")
        logger.info("2. Test the system: python main.py test")
        logger.info("3. Run live trading: python main.py trade --mt5-login YOUR_LOGIN --mt5-password YOUR_PASSWORD --mt5-server YOUR_SERVER")
        return True
    else:
        logger.error(f"❌ {total_tests - passed_tests} validation(s) failed. Please fix the issues before proceeding.")
        return False

def main():
    """Main function"""
    try:
        success = run_comprehensive_validation()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Validation interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error during validation: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
