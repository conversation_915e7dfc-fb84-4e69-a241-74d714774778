#!/usr/bin/env python3
"""
Simulated Volume Integration Test
Tests the volume integration without requiring MT5 connection
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')

# Import the enhanced components
from qqe_indicator import QQEIndicator
from fixed_live_trader import FixedFeatureEngineer, RegimeDetector

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_realistic_test_data():
    """Create realistic OHLCV data that simulates different volume scenarios"""
    logger.info("📊 Creating realistic test data with volume scenarios...")
    
    np.random.seed(42)
    periods = 200
    base_price = 2000.0
    
    data = []
    current_price = base_price
    
    for i in range(periods):
        # Create different scenarios
        if i < 50:
            # Normal correlation scenario
            price_change = np.random.normal(0, 0.3)
            volume_multiplier = 1 + abs(price_change) * 2  # Volume correlates with price movement
        elif i < 100:
            # High volume trending scenario
            price_change = np.random.normal(0.2, 0.2)  # Slight upward bias
            volume_multiplier = np.random.uniform(1.5, 2.5)  # High volume
        elif i < 150:
            # Volume divergence scenario (bearish)
            price_change = np.random.normal(0.1, 0.15)  # Price still going up
            volume_multiplier = np.random.uniform(0.3, 0.7)  # But volume decreasing
        else:
            # Low volume ranging scenario
            price_change = np.random.normal(0, 0.1)  # Small movements
            volume_multiplier = np.random.uniform(0.5, 0.9)  # Low volume
        
        current_price += price_change
        
        # Create OHLC
        high = current_price + abs(np.random.normal(0, 0.15))
        low = current_price - abs(np.random.normal(0, 0.15))
        open_price = current_price + np.random.normal(0, 0.05)
        close = current_price
        
        # Create volume
        base_volume = 1000
        volume = base_volume * volume_multiplier * np.random.uniform(0.8, 1.2)
        
        data.append({
            'datetime': pd.Timestamp('2024-01-01') + pd.Timedelta(minutes=5*i),
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('datetime', inplace=True)
    
    logger.info(f"✅ Created {len(df)} periods with different volume scenarios")
    logger.info(f"   Periods 0-49: Normal correlation")
    logger.info(f"   Periods 50-99: High volume trending")
    logger.info(f"   Periods 100-149: Volume divergence (bearish)")
    logger.info(f"   Periods 150-199: Low volume ranging")
    
    return df

def test_complete_signal_generation():
    """Test complete signal generation with volume integration"""
    logger.info("\n🎯 TESTING COMPLETE SIGNAL GENERATION WITH VOLUME...")
    
    try:
        # Create components
        feature_engineer = FixedFeatureEngineer()
        regime_detector = RegimeDetector()
        qqe_indicator = QQEIndicator(volume_lookback=20, volume_divergence_lookback=10)
        
        # Get test data
        df = create_realistic_test_data()
        
        # Process through the complete pipeline
        logger.info("   🔄 Step 1: Creating technical indicators...")
        df_features = feature_engineer.create_technical_indicators(df)
        
        logger.info("   🔄 Step 2: Calculating regime indicators...")
        df_regime = regime_detector.calculate_regime_indicators(df_features)
        
        logger.info("   🔄 Step 3: Calculating QQE with volume...")
        df_qqe = qqe_indicator.calculate_qqe_bands(df_regime)
        
        logger.info("   🔄 Step 4: Getting QQE analysis...")
        qqe_analysis = qqe_indicator.get_qqe_analysis(df_qqe)
        
        logger.info("   🔄 Step 5: Detecting regime...")
        regime, confidence, details, trend_dir, accurate_trend = regime_detector.detect_regime(df_qqe)
        
        # Test different periods to see volume effects
        test_periods = [49, 99, 149, 199]  # End of each scenario
        
        for period in test_periods:
            if period < len(df_qqe):
                logger.info(f"\n   📊 ANALYSIS FOR PERIOD {period}:")
                
                # Get data for this period
                period_data = df_qqe.iloc[period]
                
                # Volume analysis
                volume_ratio = period_data.get('volume_ratio', 1.0)
                volume_strength = period_data.get('volume_strength', 'NORMAL')
                divergence_type = period_data.get('divergence_type', 'NONE')
                divergence_filter = period_data.get('qqe_divergence_filter', False)
                
                # QQE analysis
                qqe_signal = period_data.get('qqe_signal', 0)
                qqe_strength = period_data.get('qqe_signal_strength', 0)
                volume_confirmation = period_data.get('qqe_volume_confirmation', 1.0)
                
                logger.info(f"      Volume Ratio: {volume_ratio:.2f}x")
                logger.info(f"      Volume Strength: {volume_strength}")
                logger.info(f"      Volume Confirmation: {volume_confirmation:.2f}x")
                logger.info(f"      Divergence Type: {divergence_type}")
                logger.info(f"      Divergence Filter: {divergence_filter}")
                logger.info(f"      QQE Signal: {qqe_signal} (strength: {qqe_strength:.3f})")
                
                # Simulate signal generation logic
                if qqe_signal != 0 and not divergence_filter:
                    signal_type = "BUY" if qqe_signal > 0 else "SELL"
                    enhanced_confidence = qqe_strength * volume_confirmation
                    logger.info(f"      🎯 SIGNAL: {signal_type} (enhanced confidence: {enhanced_confidence:.3f})")
                elif divergence_filter:
                    logger.info(f"      🚫 SIGNAL BLOCKED: {divergence_type} divergence detected")
                else:
                    logger.info(f"      ⚪ NO SIGNAL: QQE neutral")
        
        # Test regime detection with volume
        logger.info(f"\n   🏛️ FINAL REGIME ANALYSIS:")
        logger.info(f"      Regime: {regime} (confidence: {confidence:.3f})")
        logger.info(f"      Trend Direction: {accurate_trend}")
        
        # Check volume reasoning in regime detection
        reasoning = details.get('reasoning', [])
        volume_reasoning = [r for r in reasoning if 'Volume' in r]
        
        logger.info(f"      Volume Reasoning:")
        for reason in volume_reasoning:
            logger.info(f"        - {reason}")
        
        logger.info("✅ Complete Signal Generation Test: PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Complete Signal Generation Test: FAILED - {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

def test_volume_scenarios():
    """Test specific volume scenarios"""
    logger.info("\n📈 TESTING SPECIFIC VOLUME SCENARIOS...")
    
    try:
        qqe = QQEIndicator()
        
        # Test high volume scenario
        high_vol_confirmation = qqe._calculate_volume_confirmation(2.0, 'HIGH')
        logger.info(f"   ✅ High Volume (2.0x, HIGH): Confirmation = {high_vol_confirmation:.2f}x")
        
        # Test low volume scenario
        low_vol_confirmation = qqe._calculate_volume_confirmation(0.5, 'LOW')
        logger.info(f"   ✅ Low Volume (0.5x, LOW): Confirmation = {low_vol_confirmation:.2f}x")
        
        # Test normal volume scenario
        normal_vol_confirmation = qqe._calculate_volume_confirmation(1.0, 'NORMAL')
        logger.info(f"   ✅ Normal Volume (1.0x, NORMAL): Confirmation = {normal_vol_confirmation:.2f}x")
        
        # Verify confirmation factors are within expected ranges
        assert 1.2 <= high_vol_confirmation <= 1.5, f"High volume confirmation out of range: {high_vol_confirmation}"
        assert 0.5 <= low_vol_confirmation <= 0.8, f"Low volume confirmation out of range: {low_vol_confirmation}"
        assert 0.9 <= normal_vol_confirmation <= 1.1, f"Normal volume confirmation out of range: {normal_vol_confirmation}"
        
        logger.info("✅ Volume Scenarios Test: PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Volume Scenarios Test: FAILED - {e}")
        return False

def main():
    """Run simulated volume integration tests"""
    logger.info("🧪 STARTING SIMULATED VOLUME INTEGRATION TESTS")
    logger.info("=" * 80)
    
    tests = [
        ("Complete Signal Generation", test_complete_signal_generation),
        ("Volume Scenarios", test_volume_scenarios)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            logger.error(f"❌ {test_name}: FAILED with exception - {e}")
    
    logger.info("\n" + "=" * 80)
    logger.info("🏁 SIMULATED TEST SUMMARY")
    logger.info(f"   ✅ Passed: {passed}")
    logger.info(f"   ❌ Failed: {failed}")
    logger.info(f"   📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        logger.info("🎉 ALL SIMULATED TESTS PASSED! Volume integration is fully functional.")
        return True
    else:
        logger.error(f"⚠️ {failed} tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
