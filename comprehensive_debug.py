#!/usr/bin/env python3
"""
Comprehensive Debug and Verification System
Deep dive into every component to verify correctness
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import time
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from data_manager import DataManager
from feature_engineering import FeatureEngineer
from mt5_integration import MT5Manager

# Import ML libraries
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_selection import mutual_info_classif
import xgboost as xgb

class ComprehensiveDebugger:
    def __init__(self):
        self.data_manager = DataManager()
        self.feature_engineer = FeatureEngineer()
        self.mt5_manager = MT5Manager()
        self.model = None
        self.top_features = None
        
    def debug_step_1_data_loading(self):
        """Debug Step 1: Historical Data Loading"""
        print("🔍 STEP 1: Historical Data Loading")
        print("=" * 60)
        
        try:
            df = self.data_manager.load_historical_data()
            recent_data = df.tail(30000).copy()
            
            print(f"✅ Total historical records: {len(df):,}")
            print(f"✅ Recent data used: {len(recent_data):,}")
            print(f"✅ Date range: {df.index[0]} to {df.index[-1]}")
            print(f"✅ Columns: {list(df.columns)}")
            
            # Check for data quality issues
            print(f"\n📊 Data Quality Check:")
            for col in ['open', 'high', 'low', 'close']:
                null_count = recent_data[col].isnull().sum()
                zero_count = (recent_data[col] == 0).sum()
                print(f"   {col}: {null_count} nulls, {zero_count} zeros")
            
            # Show sample data
            print(f"\n📈 Sample Recent Data (last 5 bars):")
            print(recent_data.tail(5)[['open', 'high', 'low', 'close']])
            
            return recent_data
            
        except Exception as e:
            print(f"❌ Error in data loading: {e}")
            return None
    
    def debug_step_2_feature_engineering(self, df):
        """Debug Step 2: Feature Engineering"""
        print(f"\n🔍 STEP 2: Feature Engineering")
        print("=" * 60)
        
        try:
            features_df = self.feature_engineer.create_technical_indicators(df)
            
            print(f"✅ Features created: {features_df.shape}")
            print(f"✅ Feature columns: {len(features_df.columns)}")
            
            # Check for NaN issues
            nan_summary = features_df.isnull().sum()
            high_nan_features = nan_summary[nan_summary > len(features_df) * 0.1]
            
            print(f"\n📊 NaN Analysis:")
            print(f"   Total features: {len(features_df.columns)}")
            print(f"   Features with >10% NaN: {len(high_nan_features)}")
            
            if len(high_nan_features) > 0:
                print(f"   High NaN features: {list(high_nan_features.index[:5])}")
            
            # Show sample features
            key_features = ['vwap', 'rsi', 'macd', 'bb_upper', 'bb_lower']
            available_features = [f for f in key_features if f in features_df.columns]
            
            print(f"\n📈 Sample Features (last 3 bars):")
            if available_features:
                print(features_df[available_features].tail(3))
            
            return features_df
            
        except Exception as e:
            print(f"❌ Error in feature engineering: {e}")
            return None
    
    def debug_step_3_target_creation(self, features_df):
        """Debug Step 3: Target Creation"""
        print(f"\n🔍 STEP 3: Target Creation")
        print("=" * 60)
        
        try:
            # Create targets (same logic as live system)
            features_df['future_return'] = (features_df['close'].shift(-3) - features_df['close']) / features_df['close']
            
            print(f"✅ Future returns calculated")
            print(f"   Min return: {features_df['future_return'].min():.6f}")
            print(f"   Max return: {features_df['future_return'].max():.6f}")
            print(f"   Mean return: {features_df['future_return'].mean():.6f}")
            print(f"   Std return: {features_df['future_return'].std():.6f}")
            
            # Calculate thresholds
            up_threshold = features_df['future_return'].quantile(0.65)
            down_threshold = features_df['future_return'].quantile(0.35)
            
            print(f"\n📊 Threshold Analysis:")
            print(f"   Up threshold (65th percentile): {up_threshold:.6f}")
            print(f"   Down threshold (35th percentile): {down_threshold:.6f}")
            
            # Create targets
            features_df['target'] = np.where(features_df['future_return'] > up_threshold, 1,
                                           np.where(features_df['future_return'] < down_threshold, 0, np.nan))
            
            clear_df = features_df.dropna(subset=['target']).copy()
            clear_df['target'] = clear_df['target'].astype(int)
            
            print(f"\n🎯 Target Distribution:")
            print(f"   Total clear signals: {len(clear_df):,}")
            print(f"   Up signals: {sum(clear_df['target'] == 1):,} ({sum(clear_df['target'] == 1)/len(clear_df)*100:.1f}%)")
            print(f"   Down signals: {sum(clear_df['target'] == 0):,} ({sum(clear_df['target'] == 0)/len(clear_df)*100:.1f}%)")
            
            return clear_df
            
        except Exception as e:
            print(f"❌ Error in target creation: {e}")
            return None
    
    def debug_step_4_feature_selection(self, clear_df):
        """Debug Step 4: Feature Selection"""
        print(f"\n🔍 STEP 4: Feature Selection")
        print("=" * 60)
        
        try:
            # Prepare features
            feature_columns = [col for col in clear_df.columns 
                              if col not in ['target', 'future_return', 'close', 'open', 'high', 'low', 'volume']]
            X = clear_df[feature_columns]
            y = clear_df['target']
            
            print(f"✅ Initial features: {len(feature_columns)}")
            
            # Remove high-NaN features
            nan_ratios = X.isnull().sum() / len(X)
            valid_features = nan_ratios[nan_ratios < 0.1].index.tolist()
            X_clean = X[valid_features].fillna(X[valid_features].median())
            
            print(f"✅ After NaN filtering: {len(valid_features)} features")
            
            # Feature selection
            mi_scores = mutual_info_classif(X_clean, y, random_state=42)
            feature_scores = pd.Series(mi_scores, index=X_clean.columns).sort_values(ascending=False)
            self.top_features = feature_scores.head(12).index.tolist()
            
            print(f"\n🎯 Top 12 Selected Features:")
            for i, (feature, score) in enumerate(feature_scores.head(12).items()):
                print(f"   {i+1:2d}. {feature:20s}: {score:.6f}")
            
            X_selected = X_clean[self.top_features]
            
            print(f"\n📊 Selected Features Statistics:")
            print(f"   Shape: {X_selected.shape}")
            print(f"   NaN count: {X_selected.isnull().sum().sum()}")
            
            return X_selected, y
            
        except Exception as e:
            print(f"❌ Error in feature selection: {e}")
            return None, None
    
    def debug_step_5_model_training(self, X, y):
        """Debug Step 5: Model Training"""
        print(f"\n🔍 STEP 5: Model Training")
        print("=" * 60)
        
        try:
            # Train XGBoost model (same as live system)
            scale_pos_weight = sum(y == 0) / sum(y == 1)
            
            print(f"✅ Class balance:")
            print(f"   Class 0 (Down): {sum(y == 0):,}")
            print(f"   Class 1 (Up): {sum(y == 1):,}")
            print(f"   Scale pos weight: {scale_pos_weight:.4f}")
            
            self.model = xgb.XGBClassifier(
                n_estimators=150,
                max_depth=5,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                scale_pos_weight=scale_pos_weight,
                random_state=42,
                n_jobs=-1,
                eval_metric='logloss'
            )
            
            self.model.fit(X, y)
            
            print(f"✅ Model trained successfully")
            
            # Test predictions on training data
            train_proba = self.model.predict_proba(X)[:, 1]
            
            print(f"\n📊 Training Predictions Analysis:")
            print(f"   Min probability: {train_proba.min():.6f}")
            print(f"   Max probability: {train_proba.max():.6f}")
            print(f"   Mean probability: {train_proba.mean():.6f}")
            print(f"   Std probability: {train_proba.std():.6f}")
            
            # Check for identical predictions
            unique_probs = len(np.unique(train_proba))
            print(f"   Unique predictions: {unique_probs:,} out of {len(train_proba):,}")
            
            if unique_probs < 100:
                print(f"   ⚠️  WARNING: Very few unique predictions!")
                print(f"   Most common predictions:")
                prob_counts = pd.Series(train_proba).value_counts().head(5)
                for prob, count in prob_counts.items():
                    print(f"      {prob:.6f}: {count:,} times")
            
            return True
            
        except Exception as e:
            print(f"❌ Error in model training: {e}")
            return False
    
    def debug_step_6_live_data_connection(self):
        """Debug Step 6: Live Data Connection"""
        print(f"\n🔍 STEP 6: Live Data Connection")
        print("=" * 60)
        
        try:
            # Connect to MT5
            if not self.mt5_manager.connect():
                print("❌ Failed to connect to MT5")
                return False
            
            print("✅ MT5 connected successfully")
            
            # Get account info
            account_info = self.mt5_manager.get_account_info()
            if account_info:
                print(f"✅ Account: {account_info['login']}, Balance: ${account_info['balance']:.2f}")
            
            # Test symbol
            symbol = "XAUUSD!"
            tick = self.mt5_manager.get_symbol_info_tick(symbol)
            if tick:
                print(f"✅ Current {symbol}: Bid={tick['bid']:.5f}, Ask={tick['ask']:.5f}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error in live data connection: {e}")
            return False
    
    def debug_step_7_live_prediction_deep_dive(self):
        """Debug Step 7: Deep Dive into Live Predictions"""
        print(f"\n🔍 STEP 7: Live Prediction Deep Dive")
        print("=" * 60)
        
        if self.model is None or self.top_features is None:
            print("❌ Model or features not available")
            return False
        
        try:
            symbol = "XAUUSD!"
            timeframe = "M5"
            
            # Get multiple live predictions to check for variation
            predictions = []
            feature_sets = []
            
            for i in range(3):
                print(f"\n🔄 Live Prediction Test {i+1}/3:")
                
                # Get live data
                df = self.mt5_manager.get_latest_data(symbol, timeframe, 200)
                if df is None or len(df) < 100:
                    print("   ❌ Failed to get live data")
                    continue
                
                print(f"   ✅ Retrieved {len(df)} bars")
                print(f"   📊 Latest bar: O={df.iloc[-1]['open']:.2f}, H={df.iloc[-1]['high']:.2f}, L={df.iloc[-1]['low']:.2f}, C={df.iloc[-1]['close']:.2f}")
                
                # Calculate features
                features_df = self.feature_engineer.create_technical_indicators(df)
                if len(features_df) == 0:
                    print("   ❌ Failed to calculate features")
                    continue
                
                # Get latest features
                latest_features = features_df[self.top_features].iloc[-1]
                latest_features = latest_features.fillna(latest_features.median())
                
                print(f"   🎯 Key Features:")
                for j, (feature, value) in enumerate(latest_features.items()):
                    if j < 5:  # Show first 5 features
                        print(f"      {feature}: {value:.6f}")
                
                # Make prediction
                features_array = latest_features.values.reshape(1, -1)
                pred_proba = self.model.predict_proba(features_array)[0, 1]
                confidence = abs(pred_proba - 0.5) * 2
                
                print(f"   📈 Prediction: {pred_proba:.6f} (Confidence: {confidence:.6f})")
                
                predictions.append(pred_proba)
                feature_sets.append(latest_features.copy())
                
                if i < 2:  # Don't wait after last iteration
                    print("   ⏳ Waiting 30 seconds...")
                    time.sleep(30)
            
            # Analyze predictions
            print(f"\n📊 Prediction Analysis:")
            if len(predictions) > 0:
                print(f"   Predictions: {[f'{p:.6f}' for p in predictions]}")
                print(f"   Unique predictions: {len(set(predictions))}")
                
                if len(set(predictions)) == 1:
                    print(f"   ⚠️  WARNING: All predictions identical!")
                    
                    # Compare feature sets
                    if len(feature_sets) > 1:
                        print(f"\n🔍 Feature Comparison:")
                        for feature in self.top_features[:5]:
                            values = [fs[feature] for fs in feature_sets]
                            print(f"   {feature}: {[f'{v:.6f}' for v in values]}")
                            if len(set(values)) == 1:
                                print(f"      ⚠️  {feature} values identical!")
                else:
                    print(f"   ✅ Predictions vary appropriately")
            
            return True
            
        except Exception as e:
            print(f"❌ Error in live prediction analysis: {e}")
            return False
    
    def debug_step_8_model_validation(self):
        """Debug Step 8: Model Validation"""
        print(f"\n🔍 STEP 8: Model Validation")
        print("=" * 60)
        
        if self.model is None:
            print("❌ Model not available")
            return False
        
        try:
            # Create test data with known patterns
            print("🧪 Testing model with synthetic data:")
            
            # Test 1: All zeros
            test_zeros = np.zeros((1, len(self.top_features)))
            pred_zeros = self.model.predict_proba(test_zeros)[0, 1]
            print(f"   All zeros: {pred_zeros:.6f}")
            
            # Test 2: All ones
            test_ones = np.ones((1, len(self.top_features)))
            pred_ones = self.model.predict_proba(test_ones)[0, 1]
            print(f"   All ones: {pred_ones:.6f}")
            
            # Test 3: Random values
            np.random.seed(42)
            test_random = np.random.randn(1, len(self.top_features))
            pred_random = self.model.predict_proba(test_random)[0, 1]
            print(f"   Random values: {pred_random:.6f}")
            
            # Test 4: Check if model is stuck
            test_variations = []
            for i in range(5):
                test_var = np.random.randn(1, len(self.top_features)) * 0.1
                pred_var = self.model.predict_proba(test_var)[0, 1]
                test_variations.append(pred_var)
            
            print(f"   Variations: {[f'{p:.6f}' for p in test_variations]}")
            
            if len(set(test_variations)) == 1:
                print(f"   ❌ MODEL APPEARS STUCK - All predictions identical!")
            else:
                print(f"   ✅ Model responds to input changes")
            
            return True
            
        except Exception as e:
            print(f"❌ Error in model validation: {e}")
            return False
    
    def run_comprehensive_debug(self):
        """Run complete diagnostic"""
        print("🚀 COMPREHENSIVE DEBUG AND VERIFICATION")
        print("=" * 80)
        print("This will thoroughly test every component of the trading system")
        print("=" * 80)
        
        # Step 1: Data Loading
        recent_data = self.debug_step_1_data_loading()
        if recent_data is None:
            return False
        
        # Step 2: Feature Engineering
        features_df = self.debug_step_2_feature_engineering(recent_data)
        if features_df is None:
            return False
        
        # Step 3: Target Creation
        clear_df = self.debug_step_3_target_creation(features_df)
        if clear_df is None:
            return False
        
        # Step 4: Feature Selection
        X_selected, y = self.debug_step_4_feature_selection(clear_df)
        if X_selected is None:
            return False
        
        # Step 5: Model Training
        if not self.debug_step_5_model_training(X_selected, y):
            return False
        
        # Step 6: Live Data Connection
        if not self.debug_step_6_live_data_connection():
            return False
        
        # Step 7: Live Prediction Analysis
        if not self.debug_step_7_live_prediction_deep_dive():
            return False
        
        # Step 8: Model Validation
        if not self.debug_step_8_model_validation():
            return False
        
        print(f"\n🎯 COMPREHENSIVE DEBUG COMPLETED")
        print("=" * 80)
        
        # Disconnect
        self.mt5_manager.disconnect()
        
        return True

def main():
    """Main function"""
    print("🔍 COMPREHENSIVE DEBUG AND VERIFICATION SYSTEM")
    print("This will perform deep analysis of every system component")
    print()
    
    debugger = ComprehensiveDebugger()
    success = debugger.run_comprehensive_debug()
    
    if success:
        print("✅ Comprehensive debug completed successfully!")
    else:
        print("❌ Issues found during debug - see details above")

if __name__ == "__main__":
    main()
