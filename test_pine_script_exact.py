#!/usr/bin/env python3
"""
Test QQE with EXACT Pine Script Logic
"""

import sys
import os
sys.path.append('src')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Import MT5 and QQE
from mt5_integration import <PERSON>T<PERSON><PERSON>ana<PERSON>

def calculate_rsi(prices, period=14):
    """Calculate RSI exactly like Pine Script"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_ema(series, period):
    """Calculate EMA exactly like Pine Script"""
    return series.ewm(span=period, adjust=False).mean()

def test_pine_script_exact():
    """Test QQE with exact Pine Script logic"""
    print("🧪 Testing QQE with EXACT Pine Script Logic")
    print("=" * 60)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Connect to MT5
    mt5_manager = MT5Manager()
    if not mt5_manager.connect():
        print("❌ Failed to connect to MT5")
        return
    
    # Get recent data
    df = mt5_manager.get_latest_data("XAUUSD!", "M5", 100)
    if df is None or len(df) < 50:
        print("❌ Failed to get market data")
        return
    
    print(f"📊 Got {len(df)} periods of XAUUSD M5 data")
    print(f"   Latest close: {df['close'].iloc[-1]:.2f}")
    
    # Pine Script exact parameters
    RSI_Period = 14
    SF = 5  # RSI Smoothing
    QQE = 4.238  # Fast QQE Factor
    ThreshHold = 10
    
    Wilders_Period = RSI_Period * 2 - 1  # 27
    
    print(f"\n🔧 Pine Script Parameters:")
    print(f"   RSI_Period: {RSI_Period}")
    print(f"   SF (RSI Smoothing): {SF}")
    print(f"   QQE (Fast QQE Factor): {QQE}")
    print(f"   Wilders_Period: {Wilders_Period}")
    
    # Step 1: Calculate RSI
    df['Rsi'] = calculate_rsi(df['close'], RSI_Period)
    
    # Step 2: Calculate RsiMa (smoothed RSI)
    df['RsiMa'] = calculate_ema(df['Rsi'], SF)
    
    # Step 3: Calculate AtrRsi
    df['AtrRsi'] = abs(df['RsiMa'] - df['RsiMa'].shift(1))
    
    # Step 4: Calculate MaAtrRsi
    df['MaAtrRsi'] = calculate_ema(df['AtrRsi'], Wilders_Period)
    
    # Step 5: Calculate dar
    df['dar'] = calculate_ema(df['MaAtrRsi'], Wilders_Period) * QQE
    
    # Initialize bands and trend
    df['longband'] = 0.0
    df['shortband'] = 0.0
    df['trend'] = 1  # Default to 1 like Pine Script
    df['FastAtrRsiTL'] = 0.0
    
    # Pine Script exact band calculation
    for i in range(1, len(df)):
        if pd.isna(df.iloc[i]['dar']) or pd.isna(df.iloc[i]['RsiMa']):
            df.iloc[i, df.columns.get_loc('trend')] = df.iloc[i-1]['trend']
            continue
            
        RSIndex = df.iloc[i]['RsiMa']
        DeltaFastAtrRsi = df.iloc[i]['dar']
        RSIndex_prev = df.iloc[i-1]['RsiMa']
        
        # Calculate new bands
        newlongband = RSIndex - DeltaFastAtrRsi
        newshortband = RSIndex + DeltaFastAtrRsi
        
        # Update longband (exact Pine Script logic)
        longband_prev = df.iloc[i-1]['longband']
        if RSIndex_prev > longband_prev and RSIndex > longband_prev:
            df.iloc[i, df.columns.get_loc('longband')] = max(longband_prev, newlongband)
        else:
            df.iloc[i, df.columns.get_loc('longband')] = newlongband
        
        # Update shortband (exact Pine Script logic)
        shortband_prev = df.iloc[i-1]['shortband']
        if RSIndex_prev < shortband_prev and RSIndex < shortband_prev:
            df.iloc[i, df.columns.get_loc('shortband')] = min(shortband_prev, newshortband)
        else:
            df.iloc[i, df.columns.get_loc('shortband')] = newshortband
        
        # Calculate trend (exact Pine Script logic)
        longband_curr = df.iloc[i]['longband']
        shortband_curr = df.iloc[i]['shortband']
        trend_prev = df.iloc[i-1]['trend']
        
        # cross(RSIndex, shortband[1]) ? 1 : cross_1 ? -1 : nz(trend[1], 1)
        cross_rsi_above_shortband = (RSIndex > shortband_prev and RSIndex_prev <= shortband_prev)
        cross_longband_above_rsi = (longband_prev > RSIndex_prev and longband_curr <= RSIndex)
        
        if cross_rsi_above_shortband:
            df.iloc[i, df.columns.get_loc('trend')] = 1
        elif cross_longband_above_rsi:
            df.iloc[i, df.columns.get_loc('trend')] = -1
        else:
            df.iloc[i, df.columns.get_loc('trend')] = trend_prev if not pd.isna(trend_prev) else 1
        
        # Set FastAtrRsiTL
        trend = df.iloc[i]['trend']
        if trend == 1:
            df.iloc[i, df.columns.get_loc('FastAtrRsiTL')] = longband_curr
        else:
            df.iloc[i, df.columns.get_loc('FastAtrRsiTL')] = shortband_curr
    
    # Calculate QQE signals (exact Pine Script logic)
    df['QQExlong'] = 0
    df['QQExshort'] = 0
    df['qqeLong'] = np.nan
    df['qqeShort'] = np.nan
    
    for i in range(1, len(df)):
        if pd.isna(df.iloc[i]['FastAtrRsiTL']) or pd.isna(df.iloc[i]['RsiMa']):
            df.iloc[i, df.columns.get_loc('QQExlong')] = df.iloc[i-1]['QQExlong']
            df.iloc[i, df.columns.get_loc('QQExshort')] = df.iloc[i-1]['QQExshort']
            continue
            
        FastAtrRsiTL = df.iloc[i]['FastAtrRsiTL']
        RSIndex = df.iloc[i]['RsiMa']
        
        # Pine Script exact logic
        QQExlong_prev = df.iloc[i-1]['QQExlong']
        QQExshort_prev = df.iloc[i-1]['QQExshort']
        
        # QQExlong := FastAtrRsiTL < RSIndex ? QQExlong + 1 : 0
        if FastAtrRsiTL < RSIndex:
            df.iloc[i, df.columns.get_loc('QQExlong')] = QQExlong_prev + 1
            df.iloc[i, df.columns.get_loc('QQExshort')] = 0
        else:
            df.iloc[i, df.columns.get_loc('QQExlong')] = 0
            df.iloc[i, df.columns.get_loc('QQExshort')] = QQExshort_prev + 1
        
        # Generate signals
        QQExlong = df.iloc[i]['QQExlong']
        QQExshort = df.iloc[i]['QQExshort']
        
        # qqeLong = QQExlong == 1 ? FastAtrRsiTL[1] - 50 : na
        if QQExlong == 1:
            FastAtrRsiTL_prev = df.iloc[i-1]['FastAtrRsiTL'] if i > 0 else FastAtrRsiTL
            df.iloc[i, df.columns.get_loc('qqeLong')] = FastAtrRsiTL_prev - 50
        
        # qqeShort = QQExshort == 1 ? FastAtrRsiTL[1] - 50 : na
        if QQExshort == 1:
            FastAtrRsiTL_prev = df.iloc[i-1]['FastAtrRsiTL'] if i > 0 else FastAtrRsiTL
            df.iloc[i, df.columns.get_loc('qqeShort')] = FastAtrRsiTL_prev - 50
    
    # Show results
    print(f"\n📈 Last 10 Periods - Pine Script Exact:")
    print("Time                 | RSI   | RsiMa | Long_B | Short_B | TL    | Trend | QQELong | QQEShort | Signal")
    print("-" * 120)
    
    recent = df.tail(10)
    for idx, row in recent.iterrows():
        time_str = idx.strftime('%m-%d %H:%M')
        rsi = row['Rsi']
        rsi_ma = row['RsiMa']
        longband = row['longband']
        shortband = row['shortband']
        tl = row['FastAtrRsiTL']
        trend = int(row['trend'])
        qqe_long = int(row['QQExlong'])
        qqe_short = int(row['QQExshort'])
        
        signal = ""
        if not pd.isna(row['qqeLong']):
            signal = f"LONG({row['qqeLong']:.1f})"
        elif not pd.isna(row['qqeShort']):
            signal = f"SHORT({row['qqeShort']:.1f})"
        else:
            signal = "----"
        
        print(f"{time_str} | {rsi:5.1f} | {rsi_ma:5.1f} | {longband:6.1f} | {shortband:7.1f} | {tl:5.1f} | {trend:5d} | {qqe_long:7d} | {qqe_short:8d} | {signal}")
    
    # Check last candle
    last_row = df.iloc[-1]
    print(f"\n🎯 Last Candle Analysis:")
    print(f"   RSI MA: {last_row['RsiMa']:.1f}")
    print(f"   Fast ATR RSI TL: {last_row['FastAtrRsiTL']:.1f}")
    print(f"   Condition: TL < RSI_MA = {'TRUE' if last_row['FastAtrRsiTL'] < last_row['RsiMa'] else 'FALSE'}")
    print(f"   QQExlong: {int(last_row['QQExlong'])}")
    print(f"   QQExshort: {int(last_row['QQExshort'])}")
    
    if not pd.isna(last_row['qqeLong']):
        print(f"   ✅ PINE SCRIPT LONG SIGNAL: {last_row['qqeLong']:.1f}")
    elif not pd.isna(last_row['qqeShort']):
        print(f"   ✅ PINE SCRIPT SHORT SIGNAL: {last_row['qqeShort']:.1f}")
    else:
        print(f"   ❌ NO PINE SCRIPT SIGNAL")
    
    mt5_manager.disconnect()
    print(f"\n✅ Pine Script Exact Test Complete!")

if __name__ == "__main__":
    try:
        test_pine_script_exact()
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
