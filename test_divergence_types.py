#!/usr/bin/env python3
"""
Test Divergence Types and Filtering
Check if BULLISH_DIV is being detected and analyze filtering strictness
"""

import sys
import pandas as pd
import numpy as np
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_bullish_div_scenario():
    """Create scenario that should produce BULLISH_DIV"""
    logger.info("📊 Creating BULLISH_DIV scenario...")
    
    # Create 15 periods with BULLISH divergence pattern
    data = []
    
    for i in range(15):
        if i < 10:
            # First 10 periods: Higher prices, Lower volume
            price = 2010 - i * 0.05  # Prices declining
            volume = 1000 + i * 20   # Volume low initially
        else:
            # Last 5 periods: Lower prices, Higher volume (BULLISH DIVERGENCE)
            price = 2009.5 - (i-10) * 0.1  # Prices continue down
            volume = 1200 + (i-10) * 200   # Volume increasing significantly
        
        data.append({
            'datetime': pd.Timestamp('2024-01-01') + pd.Timedelta(minutes=5*i),
            'open': price + 0.01,
            'high': price + 0.05,
            'low': price - 0.05,
            'close': price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('datetime', inplace=True)
    
    logger.info(f"✅ Created {len(df)} periods")
    logger.info(f"   Price: {df.iloc[0]['close']:.2f} → {df.iloc[-1]['close']:.2f} (DOWN)")
    logger.info(f"   Volume: {df.iloc[0]['volume']} → {df.iloc[-1]['volume']} (UP)")
    logger.info(f"   Expected: BULLISH_DIV (price down, volume up)")
    
    return df

def create_no_divergence_scenario():
    """Create scenario with no divergence"""
    logger.info("📊 Creating NO DIVERGENCE scenario...")
    
    data = []
    
    for i in range(15):
        # Both price and volume trending up together
        price = 2000 + i * 0.05  # Prices increasing
        volume = 1000 + i * 50   # Volume increasing
        
        data.append({
            'datetime': pd.Timestamp('2024-01-01') + pd.Timedelta(minutes=5*i),
            'open': price,
            'high': price + 0.03,
            'low': price - 0.03,
            'close': price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('datetime', inplace=True)
    
    logger.info(f"✅ Created {len(df)} periods")
    logger.info(f"   Price: {df.iloc[0]['close']:.2f} → {df.iloc[-1]['close']:.2f} (UP)")
    logger.info(f"   Volume: {df.iloc[0]['volume']} → {df.iloc[-1]['volume']} (UP)")
    logger.info(f"   Expected: NONE (both up)")
    
    return df

def test_divergence_detection():
    """Test all divergence types"""
    logger.info("\n🔧 TESTING DIVERGENCE DETECTION...")
    
    try:
        from qqe_indicator import QQEIndicator
        
        # Test scenarios
        scenarios = [
            ("BULLISH_DIV", create_bullish_div_scenario),
            ("NO_DIVERGENCE", create_no_divergence_scenario)
        ]
        
        results = {}
        
        for scenario_name, create_func in scenarios:
            logger.info(f"\n   📊 Testing {scenario_name}:")
            
            # Create QQE indicator
            qqe = QQEIndicator(volume_divergence_lookback=10)
            
            # Get test data
            df = create_func()
            
            # Calculate divergence
            df_with_qqe = qqe.calculate_qqe_bands(df)
            
            # Check result
            last_row = df_with_qqe.iloc[-1]
            divergence_type = last_row.get('divergence_type', 'NONE')
            price_direction = last_row.get('price_direction', 0)
            volume_direction = last_row.get('volume_direction', 0)
            
            logger.info(f"      Result: {divergence_type}")
            logger.info(f"      Price Direction: {int(price_direction):+d}")
            logger.info(f"      Volume Direction: {int(volume_direction):+d}")
            
            results[scenario_name] = {
                'divergence_type': divergence_type,
                'price_direction': int(price_direction),
                'volume_direction': int(volume_direction)
            }
        
        # Verify results
        logger.info(f"\n   📊 VERIFICATION:")
        
        bullish_result = results.get('BULLISH_DIV', {})
        if bullish_result.get('divergence_type') == 'BULLISH_DIV':
            logger.info(f"      ✅ BULLISH_DIV: Correctly detected")
        else:
            logger.error(f"      ❌ BULLISH_DIV: Expected BULLISH_DIV, got {bullish_result.get('divergence_type')}")
        
        no_div_result = results.get('NO_DIVERGENCE', {})
        if no_div_result.get('divergence_type') == 'NONE':
            logger.info(f"      ✅ NO_DIVERGENCE: Correctly detected")
        else:
            logger.error(f"      ❌ NO_DIVERGENCE: Expected NONE, got {no_div_result.get('divergence_type')}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Divergence Detection Test: FAILED - {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return {}

def analyze_filtering_strictness():
    """Analyze how strict the divergence filtering is"""
    logger.info("\n🔧 ANALYZING FILTERING STRICTNESS...")
    
    try:
        from qqe_indicator import QQEIndicator
        
        # Create scenario with weak divergence
        logger.info("   📊 Creating WEAK divergence scenario...")
        
        data = []
        for i in range(15):
            if i < 10:
                # Slight price increase, slight volume decrease
                price = 2000 + i * 0.01  # Very small price increase
                volume = 1100 - i * 5    # Very small volume decrease
            else:
                # Continue the pattern
                price = 2000.1 + (i-10) * 0.005  # Tiny price increase
                volume = 1050 - (i-10) * 2       # Tiny volume decrease
        
            data.append({
                'datetime': pd.Timestamp('2024-01-01') + pd.Timedelta(minutes=5*i),
                'open': price,
                'high': price + 0.02,
                'low': price - 0.02,
                'close': price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('datetime', inplace=True)
        
        logger.info(f"      Price change: {df.iloc[0]['close']:.3f} → {df.iloc[-1]['close']:.3f} ({((df.iloc[-1]['close']/df.iloc[0]['close']-1)*100):+.2f}%)")
        logger.info(f"      Volume change: {df.iloc[0]['volume']:.0f} → {df.iloc[-1]['volume']:.0f} ({((df.iloc[-1]['volume']/df.iloc[0]['volume']-1)*100):+.2f}%)")
        
        # Test with QQE
        qqe = QQEIndicator(volume_divergence_lookback=10)
        df_with_qqe = qqe.calculate_qqe_bands(df)
        
        last_row = df_with_qqe.iloc[-1]
        divergence_type = last_row.get('divergence_type', 'NONE')
        divergence_strength = last_row.get('divergence_strength', 0)
        
        logger.info(f"      Detected: {divergence_type}")
        logger.info(f"      Strength: {divergence_strength:.4f}")
        
        # Analyze sensitivity
        logger.info(f"\n   📊 FILTERING SENSITIVITY ANALYSIS:")
        logger.info(f"      Current system detects: {divergence_type}")
        
        if divergence_type == 'NONE':
            logger.info(f"      ✅ System is conservative - only detects strong divergences")
            logger.info(f"      💡 This prevents false signals but may miss subtle patterns")
        else:
            logger.info(f"      ⚠️ System detected weak divergence - may be sensitive")
        
        return divergence_type, divergence_strength
        
    except Exception as e:
        logger.error(f"❌ Filtering Analysis: FAILED - {e}")
        return None, 0

def suggest_filtering_adjustments():
    """Suggest ways to make filtering less strict"""
    logger.info("\n🔧 SUGGESTIONS TO REDUCE FILTERING STRICTNESS:")
    logger.info("=" * 60)
    
    logger.info("📊 CURRENT FILTERING LOGIC:")
    logger.info("   - BEARISH_DIV: Blocks LONG trades only")
    logger.info("   - BULLISH_DIV: Blocks SHORT trades only")
    logger.info("   - Uses 10-period lookback for divergence detection")
    
    logger.info("\n🎯 OPTIONS TO MAKE LESS STRICT:")
    
    logger.info("\n   1. REDUCE LOOKBACK PERIOD:")
    logger.info("      Current: 10 periods")
    logger.info("      Suggestion: 7-8 periods (more responsive to recent changes)")
    logger.info("      Effect: Detects shorter-term divergences")
    
    logger.info("\n   2. ADD DIVERGENCE STRENGTH THRESHOLD:")
    logger.info("      Current: Any divergence triggers filter")
    logger.info("      Suggestion: Only filter if strength > 0.02")
    logger.info("      Effect: Ignores weak/noise divergences")
    
    logger.info("\n   3. PARTIAL FILTERING:")
    logger.info("      Current: Complete block of conflicting trades")
    logger.info("      Suggestion: Reduce position size instead of blocking")
    logger.info("      Effect: Still trade but with reduced risk")
    
    logger.info("\n   4. TIME-BASED FILTERING:")
    logger.info("      Current: Filter remains until divergence clears")
    logger.info("      Suggestion: Filter only for 2-3 candles")
    logger.info("      Effect: Temporary caution instead of permanent block")
    
    logger.info("\n   5. COMBINED CONFIRMATION:")
    logger.info("      Current: Volume divergence alone triggers filter")
    logger.info("      Suggestion: Require price momentum confirmation")
    logger.info("      Effect: More robust filtering with fewer false positives")

def main():
    """Run divergence analysis"""
    logger.info("🧪 TESTING DIVERGENCE TYPES AND FILTERING")
    logger.info("=" * 70)
    
    # Test divergence detection
    results = test_divergence_detection()
    
    # Analyze filtering strictness
    div_type, div_strength = analyze_filtering_strictness()
    
    # Provide suggestions
    suggest_filtering_adjustments()
    
    logger.info("\n" + "=" * 70)
    logger.info("🏁 ANALYSIS SUMMARY")
    
    if results:
        bullish_detected = results.get('BULLISH_DIV', {}).get('divergence_type') == 'BULLISH_DIV'
        logger.info(f"   ✅ BULLISH_DIV Detection: {'Working' if bullish_detected else 'Issue found'}")
        logger.info(f"   📊 Filtering Strictness: {'Conservative' if div_type == 'NONE' else 'Sensitive'}")
        
        if not bullish_detected:
            logger.info("   💡 BULLISH_DIV may not be occurring in your market conditions")
            logger.info("   🎯 Gold tends to show more BEARISH_DIV during uptrends")
        
        logger.info("\n   🔧 RECOMMENDATIONS:")
        logger.info("   1. Consider reducing lookback period to 7-8 periods")
        logger.info("   2. Add divergence strength threshold (>0.02)")
        logger.info("   3. Use partial position sizing instead of complete blocking")
        
        return True
    else:
        logger.error("   ❌ Analysis failed - check implementation")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
