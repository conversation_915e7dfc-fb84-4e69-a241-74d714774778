"""
Feature Engineering Module
Creates technical indicators and features for LSTM model training
"""
import pandas as pd
import numpy as np
import talib
from typing import Dict, List, Optional, Tuple
import logging
from sklearn.preprocessing import StandardScaler, RobustScaler
import warnings
warnings.filterwarnings('ignore')

from config.config import *

# Set up logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

class FeatureEngineer:
    """
    Creates technical indicators and features for machine learning
    """
    
    def __init__(self):
        self.scaler = None
        self.feature_columns = []
        
    def create_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create comprehensive technical indicators
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with technical indicators added
        """
        try:
            data = df.copy()
            
            # Price-based indicators
            data = self._add_price_indicators(data)
            
            # Momentum indicators
            data = self._add_momentum_indicators(data)
            
            # Volatility indicators
            data = self._add_volatility_indicators(data)
            
            # Volume indicators
            data = self._add_volume_indicators(data)
            
            # Market structure indicators
            data = self._add_market_structure_indicators(data)
            
            # Time-based features
            data = self._add_time_features(data)
            
            logger.info(f"Created {len(data.columns) - len(df.columns)} technical indicators")
            
            return data
            
        except Exception as e:
            logger.error(f"Error creating technical indicators: {e}")
            return df
    
    def _add_price_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add price-based technical indicators"""
        try:
            # Moving averages
            df['ema_fast'] = talib.EMA(df['close'], timeperiod=TECHNICAL_INDICATORS['EMA_FAST'])
            df['ema_slow'] = talib.EMA(df['close'], timeperiod=TECHNICAL_INDICATORS['EMA_SLOW'])
            df['sma_20'] = talib.SMA(df['close'], timeperiod=20)
            df['sma_50'] = talib.SMA(df['close'], timeperiod=50)
            
            # Price position relative to moving averages
            df['price_vs_ema_fast'] = (df['close'] - df['ema_fast']) / df['ema_fast']
            df['price_vs_ema_slow'] = (df['close'] - df['ema_slow']) / df['ema_slow']
            df['ema_fast_vs_slow'] = (df['ema_fast'] - df['ema_slow']) / df['ema_slow']
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = talib.BBANDS(
                df['close'], 
                timeperiod=TECHNICAL_INDICATORS['BB_PERIOD'],
                nbdevup=TECHNICAL_INDICATORS['BB_STD'],
                nbdevdn=TECHNICAL_INDICATORS['BB_STD']
            )
            df['bb_upper'] = bb_upper
            df['bb_middle'] = bb_middle
            df['bb_lower'] = bb_lower
            df['bb_width'] = (bb_upper - bb_lower) / bb_middle
            df['bb_position'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)
            
            # Price channels
            df['highest_high_20'] = df['high'].rolling(window=20).max()
            df['lowest_low_20'] = df['low'].rolling(window=20).min()
            df['channel_position'] = (df['close'] - df['lowest_low_20']) / (df['highest_high_20'] - df['lowest_low_20'])
            
            return df
            
        except Exception as e:
            logger.error(f"Error adding price indicators: {e}")
            return df
    
    def _add_momentum_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add momentum-based indicators"""
        try:
            # RSI
            df['rsi'] = talib.RSI(df['close'], timeperiod=TECHNICAL_INDICATORS['RSI_PERIOD'])
            df['rsi_overbought'] = (df['rsi'] > 70).astype(int)
            df['rsi_oversold'] = (df['rsi'] < 30).astype(int)
            
            # MACD
            macd, macd_signal, macd_hist = talib.MACD(
                df['close'],
                fastperiod=TECHNICAL_INDICATORS['MACD_FAST'],
                slowperiod=TECHNICAL_INDICATORS['MACD_SLOW'],
                signalperiod=TECHNICAL_INDICATORS['MACD_SIGNAL']
            )
            df['macd'] = macd
            df['macd_signal'] = macd_signal
            df['macd_histogram'] = macd_hist
            df['macd_bullish'] = (macd > macd_signal).astype(int)
            
            # Stochastic
            stoch_k, stoch_d = talib.STOCH(
                df['high'], df['low'], df['close'],
                fastk_period=TECHNICAL_INDICATORS['STOCH_K'],
                slowk_period=TECHNICAL_INDICATORS['STOCH_D'],
                slowd_period=TECHNICAL_INDICATORS['STOCH_D']
            )
            df['stoch_k'] = stoch_k
            df['stoch_d'] = stoch_d
            df['stoch_overbought'] = (stoch_k > 80).astype(int)
            df['stoch_oversold'] = (stoch_k < 20).astype(int)
            
            # Williams %R
            df['williams_r'] = talib.WILLR(
                df['high'], df['low'], df['close'],
                timeperiod=TECHNICAL_INDICATORS['WILLIAMS_R_PERIOD']
            )
            
            # CCI
            df['cci'] = talib.CCI(
                df['high'], df['low'], df['close'],
                timeperiod=TECHNICAL_INDICATORS['CCI_PERIOD']
            )
            
            # Rate of Change
            df['roc_5'] = talib.ROC(df['close'], timeperiod=5)
            df['roc_10'] = talib.ROC(df['close'], timeperiod=10)
            
            return df
            
        except Exception as e:
            logger.error(f"Error adding momentum indicators: {e}")
            return df
    
    def _add_volatility_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volatility-based indicators"""
        try:
            # ATR (Average True Range)
            df['atr'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=TECHNICAL_INDICATORS['ATR_PERIOD'])
            df['atr_normalized'] = df['atr'] / df['close']
            
            # True Range
            df['true_range'] = talib.TRANGE(df['high'], df['low'], df['close'])
            
            # Volatility measures
            df['price_range'] = (df['high'] - df['low']) / df['close']
            df['body_size'] = abs(df['close'] - df['open']) / df['close']
            df['upper_shadow'] = (df['high'] - np.maximum(df['open'], df['close'])) / df['close']
            df['lower_shadow'] = (np.minimum(df['open'], df['close']) - df['low']) / df['close']
            
            # Rolling volatility
            df['volatility_5'] = df['close'].pct_change().rolling(window=5).std()
            df['volatility_20'] = df['close'].pct_change().rolling(window=20).std()
            
            # Volatility regime
            df['high_volatility'] = (df['atr_normalized'] > df['atr_normalized'].rolling(window=50).quantile(0.8)).astype(int)
            df['low_volatility'] = (df['atr_normalized'] < df['atr_normalized'].rolling(window=50).quantile(0.2)).astype(int)
            
            return df
            
        except Exception as e:
            logger.error(f"Error adding volatility indicators: {e}")
            return df
    
    def _add_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volume-based indicators"""
        try:
            # Volume moving averages
            df['volume_sma_20'] = df['volume'].rolling(window=20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma_20']
            
            # Volume-price indicators
            df['vwap'] = (df['volume'] * (df['high'] + df['low'] + df['close']) / 3).cumsum() / df['volume'].cumsum()
            df['price_vs_vwap'] = (df['close'] - df['vwap']) / df['vwap']
            
            # On-Balance Volume
            df['obv'] = talib.OBV(df['close'], df['volume'])
            df['obv_sma'] = df['obv'].rolling(window=20).mean()
            
            # Volume oscillator
            df['volume_oscillator'] = (df['volume'].rolling(window=5).mean() - df['volume'].rolling(window=20).mean()) / df['volume'].rolling(window=20).mean()
            
            return df
            
        except Exception as e:
            logger.error(f"Error adding volume indicators: {e}")
            return df
    
    def _add_market_structure_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add market structure and regime indicators"""
        try:
            # ADX (Average Directional Index) for trend strength
            df['adx'] = talib.ADX(df['high'], df['low'], df['close'], timeperiod=TECHNICAL_INDICATORS['ADX_PERIOD'])
            df['di_plus'] = talib.PLUS_DI(df['high'], df['low'], df['close'], timeperiod=TECHNICAL_INDICATORS['ADX_PERIOD'])
            df['di_minus'] = talib.MINUS_DI(df['high'], df['low'], df['close'], timeperiod=TECHNICAL_INDICATORS['ADX_PERIOD'])
            
            # Trend classification
            df['strong_trend'] = (df['adx'] > 25).astype(int)
            df['weak_trend'] = (df['adx'] < 20).astype(int)
            df['bullish_trend'] = ((df['di_plus'] > df['di_minus']) & (df['adx'] > 20)).astype(int)
            df['bearish_trend'] = ((df['di_minus'] > df['di_plus']) & (df['adx'] > 20)).astype(int)
            
            # Support and resistance levels
            df['pivot_high'] = self._identify_pivot_highs(df['high'])
            df['pivot_low'] = self._identify_pivot_lows(df['low'])
            
            # Price momentum (create before market regime classification)
            df['momentum_5'] = df['close'] / df['close'].shift(5) - 1
            df['momentum_20'] = df['close'] / df['close'].shift(20) - 1

            # Market regime (trending vs ranging)
            df['market_regime'] = self._classify_market_regime(df)
            
            # Fractal patterns
            df['fractal_up'] = self._identify_fractal_up(df)
            df['fractal_down'] = self._identify_fractal_down(df)
            
            return df
            
        except Exception as e:
            logger.error(f"Error adding market structure indicators: {e}")
            return df
    
    def _add_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add time-based features"""
        try:
            # Extract time components
            df['hour'] = df.index.hour
            df['day_of_week'] = df.index.dayofweek
            df['day_of_month'] = df.index.day
            df['month'] = df.index.month
            
            # Trading session indicators
            df['london_session'] = ((df['hour'] >= 8) & (df['hour'] < 16)).astype(int)
            df['new_york_session'] = ((df['hour'] >= 13) & (df['hour'] < 21)).astype(int)
            df['asian_session'] = ((df['hour'] >= 0) & (df['hour'] < 8)).astype(int)
            df['overlap_session'] = ((df['hour'] >= 13) & (df['hour'] < 16)).astype(int)
            
            # Cyclical encoding for time features
            df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
            df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
            df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
            df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
            df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
            df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
            
            return df
            
        except Exception as e:
            logger.error(f"Error adding time features: {e}")
            return df
    
    def _identify_pivot_highs(self, high_series: pd.Series, window: int = 5) -> pd.Series:
        """Identify pivot high points"""
        try:
            pivot_highs = pd.Series(0, index=high_series.index)
            
            for i in range(window, len(high_series) - window):
                if high_series.iloc[i] == high_series.iloc[i-window:i+window+1].max():
                    pivot_highs.iloc[i] = 1
            
            return pivot_highs
            
        except Exception as e:
            logger.error(f"Error identifying pivot highs: {e}")
            return pd.Series(0, index=high_series.index)
    
    def _identify_pivot_lows(self, low_series: pd.Series, window: int = 5) -> pd.Series:
        """Identify pivot low points"""
        try:
            pivot_lows = pd.Series(0, index=low_series.index)
            
            for i in range(window, len(low_series) - window):
                if low_series.iloc[i] == low_series.iloc[i-window:i+window+1].min():
                    pivot_lows.iloc[i] = 1
            
            return pivot_lows
            
        except Exception as e:
            logger.error(f"Error identifying pivot lows: {e}")
            return pd.Series(0, index=low_series.index)
    
    def _classify_market_regime(self, df: pd.DataFrame) -> pd.Series:
        """
        Classify market regime as trending (1) or ranging (0)
        """
        try:
            # Use ADX and price movement to classify regime
            regime = pd.Series(0, index=df.index)
            
            # Trending market conditions
            trending_conditions = (
                (df['adx'] > 25) &  # Strong trend
                (abs(df['momentum_20']) > 0.02)  # Significant price movement
            )
            
            regime[trending_conditions] = 1
            
            return regime
            
        except Exception as e:
            logger.error(f"Error classifying market regime: {e}")
            return pd.Series(0, index=df.index)
    
    def _identify_fractal_up(self, df: pd.DataFrame, window: int = 2) -> pd.Series:
        """Identify upward fractal patterns"""
        try:
            fractal_up = pd.Series(0, index=df.index)
            
            for i in range(window, len(df) - window):
                if (df['high'].iloc[i] > df['high'].iloc[i-window:i].max() and
                    df['high'].iloc[i] > df['high'].iloc[i+1:i+window+1].max()):
                    fractal_up.iloc[i] = 1
            
            return fractal_up
            
        except Exception as e:
            logger.error(f"Error identifying fractal up: {e}")
            return pd.Series(0, index=df.index)
    
    def _identify_fractal_down(self, df: pd.DataFrame, window: int = 2) -> pd.Series:
        """Identify downward fractal patterns"""
        try:
            fractal_down = pd.Series(0, index=df.index)
            
            for i in range(window, len(df) - window):
                if (df['low'].iloc[i] < df['low'].iloc[i-window:i].min() and
                    df['low'].iloc[i] < df['low'].iloc[i+1:i+window+1].min()):
                    fractal_down.iloc[i] = 1
            
            return fractal_down
            
        except Exception as e:
            logger.error(f"Error identifying fractal down: {e}")
            return pd.Series(0, index=df.index)
    
    def create_target_variable(self, df: pd.DataFrame, horizon: int = PREDICTION_HORIZON) -> pd.DataFrame:
        """
        Create target variable for LSTM training
        
        Args:
            df: DataFrame with features
            horizon: Prediction horizon in periods
            
        Returns:
            DataFrame with target variable added
        """
        try:
            data = df.copy()
            
            # Calculate future returns
            data['future_return'] = data['close'].shift(-horizon) / data['close'] - 1
            
            # Create classification targets based on return thresholds
            # Use ATR to determine meaningful price movements
            atr_threshold = data['atr'] / data['close'] * 0.5  # Half ATR as threshold
            
            # Multi-class target: 0=Hold, 1=Buy, 2=Sell
            data['target'] = 0  # Default to hold
            
            # Buy signal: positive return above threshold
            buy_condition = data['future_return'] > atr_threshold
            data.loc[buy_condition, 'target'] = 1
            
            # Sell signal: negative return below threshold
            sell_condition = data['future_return'] < -atr_threshold
            data.loc[sell_condition, 'target'] = 2
            
            # Binary target for trend direction
            data['trend_target'] = (data['future_return'] > 0).astype(int)
            
            # Remove rows with NaN targets (last 'horizon' rows)
            data = data.dropna(subset=['future_return', 'target'])
            
            logger.info(f"Created target variables. Distribution:")
            logger.info(f"Hold: {(data['target'] == 0).sum()}")
            logger.info(f"Buy: {(data['target'] == 1).sum()}")
            logger.info(f"Sell: {(data['target'] == 2).sum()}")
            
            return data

        except Exception as e:
            logger.error(f"Error creating target variable: {e}")
            return df

    def prepare_features_for_training(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """
        Prepare features for LSTM training by selecting and scaling

        Args:
            df: DataFrame with all features

        Returns:
            Tuple of (processed DataFrame, list of feature columns)
        """
        try:
            data = df.copy()

            # Remove non-feature columns
            exclude_columns = [
                'open', 'high', 'low', 'close', 'volume',  # Raw OHLCV
                'future_return',  # Target-related
                'datetime'  # Time column if present
            ]

            # Select feature columns
            feature_columns = [col for col in data.columns
                             if col not in exclude_columns and
                             not col.startswith('target')]

            # Remove columns with too many NaN values
            nan_threshold = 0.1  # Remove columns with >10% NaN
            for col in feature_columns.copy():
                if data[col].isnull().sum() / len(data) > nan_threshold:
                    feature_columns.remove(col)
                    logger.warning(f"Removed feature {col} due to too many NaN values")

            # Forward fill remaining NaN values
            data[feature_columns] = data[feature_columns].fillna(method='ffill')

            # Remove any remaining NaN rows
            initial_len = len(data)
            data = data.dropna(subset=feature_columns)
            if len(data) < initial_len:
                logger.info(f"Removed {initial_len - len(data)} rows with NaN values")

            # Store feature columns for later use
            self.feature_columns = feature_columns

            logger.info(f"Selected {len(feature_columns)} features for training")

            return data, feature_columns

        except Exception as e:
            logger.error(f"Error preparing features for training: {e}")
            return df, []

    def scale_features(self, df: pd.DataFrame, feature_columns: List[str],
                      fit_scaler: bool = True) -> pd.DataFrame:
        """
        Scale features using RobustScaler

        Args:
            df: DataFrame with features
            feature_columns: List of columns to scale
            fit_scaler: Whether to fit the scaler (True for training, False for prediction)

        Returns:
            DataFrame with scaled features
        """
        try:
            data = df.copy()

            if fit_scaler:
                # Fit scaler on training data
                self.scaler = RobustScaler()
                scaled_features = self.scaler.fit_transform(data[feature_columns])
                logger.info("Fitted scaler on training data")
            else:
                # Use existing scaler for prediction data
                if self.scaler is None:
                    logger.error("Scaler not fitted. Cannot scale features.")
                    return data
                scaled_features = self.scaler.transform(data[feature_columns])

            # Replace original features with scaled versions
            data[feature_columns] = scaled_features

            return data

        except Exception as e:
            logger.error(f"Error scaling features: {e}")
            return df

    def create_sequences(self, df: pd.DataFrame, feature_columns: List[str],
                        target_column: str = 'target',
                        sequence_length: int = SEQUENCE_LENGTH) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create sequences for LSTM training

        Args:
            df: DataFrame with features and targets
            feature_columns: List of feature columns
            target_column: Name of target column
            sequence_length: Length of input sequences

        Returns:
            Tuple of (X sequences, y targets)
        """
        try:
            # Prepare data
            features = df[feature_columns].values
            targets = df[target_column].values

            X, y = [], []

            # Create sequences
            for i in range(sequence_length, len(features)):
                X.append(features[i-sequence_length:i])
                y.append(targets[i])

            X = np.array(X)
            y = np.array(y)

            logger.info(f"Created {len(X)} sequences of length {sequence_length}")
            logger.info(f"Feature shape: {X.shape}, Target shape: {y.shape}")

            return X, y

        except Exception as e:
            logger.error(f"Error creating sequences: {e}")
            return np.array([]), np.array([])

    def balance_dataset(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Balance dataset to prevent class imbalance

        Args:
            X: Feature sequences
            y: Target labels

        Returns:
            Tuple of balanced (X, y)
        """
        try:
            from sklearn.utils import resample

            # Get unique classes and their counts
            unique_classes, counts = np.unique(y, return_counts=True)
            logger.info(f"Original class distribution: {dict(zip(unique_classes, counts))}")

            # Find minimum class size
            min_class_size = min(counts)

            # Downsample majority classes
            X_balanced = []
            y_balanced = []

            for class_label in unique_classes:
                class_indices = np.where(y == class_label)[0]

                if len(class_indices) > min_class_size:
                    # Downsample
                    sampled_indices = resample(class_indices,
                                             n_samples=min_class_size,
                                             random_state=42)
                else:
                    sampled_indices = class_indices

                X_balanced.append(X[sampled_indices])
                y_balanced.append(y[sampled_indices])

            X_balanced = np.vstack(X_balanced)
            y_balanced = np.hstack(y_balanced)

            # Shuffle the balanced dataset
            shuffle_indices = np.random.permutation(len(X_balanced))
            X_balanced = X_balanced[shuffle_indices]
            y_balanced = y_balanced[shuffle_indices]

            unique_classes_balanced, counts_balanced = np.unique(y_balanced, return_counts=True)
            logger.info(f"Balanced class distribution: {dict(zip(unique_classes_balanced, counts_balanced))}")

            return X_balanced, y_balanced

        except Exception as e:
            logger.error(f"Error balancing dataset: {e}")
            return X, y

    def get_feature_importance_names(self) -> List[str]:
        """Get list of feature names for importance analysis"""
        return self.feature_columns if self.feature_columns else []
