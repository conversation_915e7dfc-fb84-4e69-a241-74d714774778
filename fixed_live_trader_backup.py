#!/usr/bin/env python3
"""
Fixed Live Trading System
Uses the properly rebuilt model with all issues resolved
"""

import os
import sys
import pandas as pd
import numpy as np
import time
import threading
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Tuple
import logging
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager
from trade_logger import TradeLogger

# Import ML libraries
import joblib

# Import QQE Indicator
from qqe_indicator import QQEIndicator

# Import Simple Regression Channel
from simple_regression_channel import SimpleRegressionChannel

class FixedFeatureEngineer:
    """Fixed feature engineering - same as rebuild system"""
    
    def create_technical_indicators(self, df):
        """Create technical indicators with proper calculations"""
        df = df.copy()
        
        # Basic price features
        df['return_1'] = df['close'].pct_change(1)
        df['return_3'] = df['close'].pct_change(3)
        df['return_5'] = df['close'].pct_change(5)
        
        # Volatility
        df['high_low_pct'] = (df['high'] - df['low']) / df['close']
        df['close_open_pct'] = (df['close'] - df['open']) / df['open']
        
        # Moving averages (MIDTERM: Longer periods)
        df['sma_5'] = df['close'].rolling(8).mean()   # Slightly longer
        df['sma_10'] = df['close'].rolling(13).mean() # Slightly longer
        df['sma_20'] = df['close'].rolling(30).mean() # Longer for midterm
        df['sma_50'] = df['close'].rolling(60).mean() # Longer for midterm
        
        # Price relative to moving averages
        df['price_sma5_ratio'] = df['close'] / df['sma_5']
        df['price_sma20_ratio'] = df['close'] / df['sma_20']
        df['price_sma50_ratio'] = df['close'] / df['sma_50']
        
        # Bollinger Bands (MIDTERM: Increased period for more stable bands)
        bb_period = 30
        bb_std = 2
        df['bb_middle'] = df['close'].rolling(bb_period).mean()
        bb_rolling_std = df['close'].rolling(bb_period).std()
        df['bb_upper'] = df['bb_middle'] + (bb_rolling_std * bb_std)
        df['bb_lower'] = df['bb_middle'] - (bb_rolling_std * bb_std)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # RSI
        def calculate_rsi(prices, period=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        df['rsi'] = calculate_rsi(df['close'])
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Rolling min/max (FIXED)
        df['highest_high_5'] = df['high'].rolling(5).max()
        df['lowest_low_5'] = df['low'].rolling(5).min()
        df['highest_high_10'] = df['high'].rolling(15).max()  # MIDTERM: Longer periods
        df['lowest_low_10'] = df['low'].rolling(15).min()
        df['highest_high_20'] = df['high'].rolling(30).max()
        df['lowest_low_20'] = df['low'].rolling(30).min()
        
        # Price position in recent range
        df['price_position_5'] = (df['close'] - df['lowest_low_5']) / (df['highest_high_5'] - df['lowest_low_5'])
        df['price_position_20'] = (df['close'] - df['lowest_low_20']) / (df['highest_high_20'] - df['lowest_low_20'])
        
        # Volume features (MIDTERM: Longer period)
        if 'volume' in df.columns:
            df['volume_sma'] = df['volume'].rolling(30).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # Momentum indicators
        df['momentum_3'] = df['close'] / df['close'].shift(3)
        df['momentum_5'] = df['close'] / df['close'].shift(5)
        df['momentum_10'] = df['close'] / df['close'].shift(10)
        
        # Stochastic %K
        def calculate_stochastic(high, low, close, k_period=14):
            lowest_low = low.rolling(k_period).min()
            highest_high = high.rolling(k_period).max()
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            return k_percent
        
        df['stoch_k'] = calculate_stochastic(df['high'], df['low'], df['close'])
        df['stoch_d'] = df['stoch_k'].rolling(3).mean()
        
        # Williams %R
        df['williams_r'] = -100 * ((df['highest_high_20'] - df['close']) / (df['highest_high_20'] - df['lowest_low_20']))
        
        # Average True Range (ATR)
        df['tr1'] = df['high'] - df['low']
        df['tr2'] = abs(df['high'] - df['close'].shift(1))
        df['tr3'] = abs(df['low'] - df['close'].shift(1))
        df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
        df['atr'] = df['true_range'].rolling(21).mean()  # MIDTERM: Longer ATR period
        df['atr_ratio'] = df['atr'] / df['close']
        
        # Clean up
        df = df.drop(['tr1', 'tr2', 'tr3'], axis=1, errors='ignore')
        
        return df

class RegimeDetector:
    """Enhanced Market Regime Detector - Optimized for 5M Short-term Trading"""

    def __init__(self):
        # Setup logging
        self.logger = logging.getLogger(__name__)

        # MIDTERM M5 parameters (optimized for midterm trend detection)
        self.atr_lookback = 100      # 100 periods = ~8.3 hours (more stable)
        self.ema_fast_periods = 21   # 21 periods = ~1.75 hours (slower, more stable)
        self.ema_slow_periods = 34   # 34 periods = ~2.8 hours (stronger confirmation)
        self.slope_lookback = 8      # 8 periods = 40 minutes (more stable slope)
        self.bb_periods = 30         # Bollinger Bands period (more stable bands)
        self.rsi_periods = 21        # RSI for momentum detection (less sensitive)

        # ENHANCED Thresholds (XAUUSD 5m optimized - more sensitive to detect actual trends)
        # RATIONALE: Previous thresholds were too high for XAUUSD 5-minute data, causing trending markets
        # to be classified as ranging. New thresholds are calibrated for XAUUSD price movements.
        self.trending_atr_threshold = 0.55    # ATR above 55th percentile (unchanged)
        self.ranging_atr_threshold = 0.40     # ATR below 40th percentile (unchanged)
        self.trending_slope_threshold = 0.0006  # EMA slope threshold (0.06% - was 0.12%, XAUUSD optimized)
        self.ranging_slope_threshold = 0.0002   # EMA slope threshold (0.02% - was 0.04%, XAUUSD optimized)
        self.bb_width_threshold = 0.40        # BB width percentile (unchanged)
        self.bb_squeeze_threshold = 0.25      # BB squeeze detection (unchanged)

        # ENHANCED: Trend detection thresholds (XAUUSD 5m optimized)
        self.fast_slope_threshold = 0.0004    # Fast EMA slope (0.04% - was 0.08%, XAUUSD optimized)
        self.momentum_threshold = 0.60        # RSI momentum threshold (unchanged)

        # NEW: Candlestick approval thresholds
        self.long_candle_threshold = 0.60     # Close above 60% of candle range for longs
        self.short_candle_threshold = 0.40    # Close below 40% of candle range for shorts

        # NEW: Bull/Bear acceleration tracking (Phase 1: Monitoring)
        self.bull_strength_history = []  # Track last 3 bull strength values
        self.bear_strength_history = []  # Track last 3 bear strength values
        self.bull_velocity_history = []  # Track last 2 velocity values
        self.bear_velocity_history = []  # Track last 2 velocity values

        # NEW: Swing point distance filtering
        self.swing_distance_atr_threshold = 1.5  # Maximum distance from recent swing point (in ATR)

    def calculate_regime_indicators(self, df):
        """ENHANCED: Calculate all indicators needed for regime detection with faster response"""
        df = df.copy()

        # ENHANCED: ATR Percentile with faster lookback
        df['atr_percentile'] = df['atr'].rolling(self.atr_lookback).rank(pct=True)

        # ENHANCED: Dual EMA system for faster trend detection
        df['ema_fast'] = df['close'].ewm(span=self.ema_fast_periods).mean()
        df['ema_slow'] = df['close'].ewm(span=self.ema_slow_periods).mean()

        # Fast EMA slope for early trend detection
        df['ema_fast_slope'] = (df['ema_fast'] - df['ema_fast'].shift(self.slope_lookback)) / df['ema_fast'].shift(self.slope_lookback)
        df['ema_fast_slope_abs'] = abs(df['ema_fast_slope'])

        # Slow EMA slope for confirmation
        df['ema_slow_slope'] = (df['ema_slow'] - df['ema_slow'].shift(self.slope_lookback)) / df['ema_slow'].shift(self.slope_lookback)
        df['ema_slow_slope_abs'] = abs(df['ema_slow_slope'])

        # Legacy compatibility
        df['ema_21'] = df['ema_slow']
        df['ema_slope'] = df['ema_slow_slope']
        df['ema_slope_abs'] = df['ema_slow_slope_abs']

        # NEW: RSI for momentum detection
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_periods).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_periods).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi_momentum'] = np.where(df['rsi'] > 50, 'UP', 'DOWN')

        # NEW: Price momentum over multiple timeframes
        df['momentum_3'] = (df['close'] - df['close'].shift(3)) / df['close'].shift(3)
        df['momentum_5'] = (df['close'] - df['close'].shift(5)) / df['close'].shift(5)
        df['momentum_8'] = (df['close'] - df['close'].shift(8)) / df['close'].shift(8)

        # Combined momentum score
        df['momentum_score'] = (
            np.where(df['momentum_3'] > 0, 1, -1) +
            np.where(df['momentum_5'] > 0, 1, -1) +
            np.where(df['momentum_8'] > 0, 1, -1)
        )

        # Bollinger Bands Width (ranging indicator)
        if 'bb_middle' not in df.columns:
            df['bb_middle'] = df['close'].rolling(self.bb_periods).mean()
            bb_std = df['close'].rolling(self.bb_periods).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
            df['bb_lower'] = df['bb_middle'] - (bb_std * 2)

        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_width_percentile'] = df['bb_width'].rolling(self.atr_lookback).rank(pct=True)

        # Price volatility (additional confirmation) - MIDTERM: Longer period
        df['price_volatility'] = df['close'].rolling(30).std() / df['close']
        df['vol_percentile'] = df['price_volatility'].rolling(self.atr_lookback).rank(pct=True)

        # NEW: Regression Channel Slope Analysis (20-period lookback for XAUUSD 5m)
        def calculate_regression_slope(prices, periods=20):
            """Calculate linear regression slope over specified periods"""
            slopes = []
            for i in range(len(prices)):
                if i < periods - 1:
                    slopes.append(np.nan)
                else:
                    # Get the last 'periods' prices
                    y_values = prices.iloc[i-periods+1:i+1].values
                    x_values = np.arange(periods)

                    # Calculate linear regression slope
                    if len(y_values) == periods and not np.any(np.isnan(y_values)):
                        # Using numpy polyfit for linear regression (degree 1)
                        slope, _ = np.polyfit(x_values, y_values, 1)
                        # Normalize slope as percentage change per period
                        normalized_slope = (slope / y_values[-1]) * 100 if y_values[-1] != 0 else 0
                        slopes.append(normalized_slope)
                    else:
                        slopes.append(np.nan)
            return pd.Series(slopes, index=prices.index)

        # Calculate regression slope
        df['regression_slope'] = calculate_regression_slope(df['close'], periods=20)
        df['regression_slope_abs'] = abs(df['regression_slope'])

        # Regression slope strength classification for XAUUSD 5m (FURTHER OPTIMIZED)
        # Based on actual XAUUSD 5m data analysis: 0.005% = weak, 0.015% = moderate, 0.03%+ = strong
        df['regression_strength'] = np.where(
            df['regression_slope_abs'] >= 0.03, 'STRONG',     # Was 0.06% - now 0.03% (XAUUSD optimized)
            np.where(df['regression_slope_abs'] >= 0.015, 'MODERATE',  # Was 0.035% - now 0.015% (XAUUSD optimized)
                    np.where(df['regression_slope_abs'] >= 0.005, 'WEAK', 'FLAT'))  # Was 0.015% - now 0.005% (XAUUSD optimized)
        )

        # NEW: Regression Channel Bands for ranging market filtering
        def calculate_regression_channel(prices, periods=20):
            """Calculate regression channel with upper and lower bounds"""
            regression_lines = []
            upper_bounds = []
            lower_bounds = []
            positions = []

            for i in range(len(prices)):
                if i < periods - 1:
                    regression_lines.append(np.nan)
                    upper_bounds.append(np.nan)
                    lower_bounds.append(np.nan)
                    positions.append(np.nan)
                else:
                    # Get the last 'periods' prices
                    y_values = prices.iloc[i-periods+1:i+1].values
                    x_values = np.arange(periods)

                    if len(y_values) == periods and not np.any(np.isnan(y_values)):
                        # Calculate linear regression
                        slope, intercept = np.polyfit(x_values, y_values, 1)

                        # Calculate regression line value at current point
                        regression_value = slope * (periods - 1) + intercept
                        regression_lines.append(regression_value)

                        # Calculate residuals (distance from regression line)
                        regression_line_values = slope * x_values + intercept
                        residuals = y_values - regression_line_values
                        std_residual = np.std(residuals)

                        # Create channel bounds (±2 standard deviations)
                        upper_bound = regression_value + (2 * std_residual)
                        lower_bound = regression_value - (2 * std_residual)

                        upper_bounds.append(upper_bound)
                        lower_bounds.append(lower_bound)

                        # Calculate position within channel (0 = lower bound, 1 = upper bound)
                        current_price = y_values[-1]
                        if upper_bound != lower_bound:
                            position = (current_price - lower_bound) / (upper_bound - lower_bound)
                        else:
                            position = 0.5  # Middle if no range
                        positions.append(position)
                    else:
                        regression_lines.append(np.nan)
                        upper_bounds.append(np.nan)
                        lower_bounds.append(np.nan)
                        positions.append(np.nan)

            return (pd.Series(regression_lines, index=prices.index),
                    pd.Series(upper_bounds, index=prices.index),
                    pd.Series(lower_bounds, index=prices.index),
                    pd.Series(positions, index=prices.index))

        # Calculate regression channel
        df['regression_line'], df['regression_upper'], df['regression_lower'], df['regression_position'] = calculate_regression_channel(df['close'], periods=20)

        # Bollinger Band Squeeze Detection
        df['bb_squeeze'] = df['bb_width_percentile'] < self.bb_squeeze_threshold

        # ENHANCED: Multi-factor trend direction
        df['ema_fast_trend'] = np.where(df['ema_fast_slope'] > 0, 'UP', 'DOWN')
        df['ema_slow_trend'] = np.where(df['ema_slow_slope'] > 0, 'UP', 'DOWN')
        df['ema_cross_trend'] = np.where(df['ema_fast'] > df['ema_slow'], 'UP', 'DOWN')

        # Price trend (higher highs/lower lows)
        df['price_trend_3'] = np.where(df['close'] > df['close'].shift(3), 'UP', 'DOWN')
        df['price_trend_5'] = np.where(df['close'] > df['close'].shift(5), 'UP', 'DOWN')
        df['price_trend_8'] = np.where(df['close'] > df['close'].shift(8), 'UP', 'DOWN')

        # Moving average trend
        df['ma_trend'] = np.where(df['sma_20'] > df['sma_20'].shift(3), 'UP', 'DOWN')

        # ENHANCED: Accurate trend direction with more factors (8 indicators - added regression slope)
        # NOTE: Regression slope only votes when NOT flat - flat slopes indicate ranging (no directional bias)
        regression_is_flat = (df['regression_strength'] == 'FLAT')
        regression_up_votes = np.where(
            regression_is_flat, 0,  # No vote when flat (ranging condition)
            (df['regression_slope'] > 0).astype(int) * 2  # 2 votes when trending
        )

        trend_up_votes = (
            (df['ema_fast_trend'] == 'UP').astype(int) +
            (df['ema_slow_trend'] == 'UP').astype(int) +
            (df['ema_cross_trend'] == 'UP').astype(int) +
            (df['price_trend_3'] == 'UP').astype(int) +
            (df['price_trend_5'] == 'UP').astype(int) +
            (df['rsi_momentum'] == 'UP').astype(int) +
            (df['momentum_score'] > 0).astype(int) +
            # NEW: Regression slope vote (weighted as 2 votes, but only when not flat)
            regression_up_votes
        )

        # Dynamic consensus based on whether regression slope is participating
        # When regression is flat: 4 out of 7 votes needed
        # When regression is trending: 5 out of 9 votes needed
        consensus_threshold = np.where(regression_is_flat, 4, 5)
        df['accurate_trend_direction'] = np.where(trend_up_votes >= consensus_threshold, 'UP', 'DOWN')

        # Legacy trend direction for backward compatibility
        df['trend_direction'] = np.where(df['ema_slope'] > 0, 'BUY', 'SELL')

        # ENHANCED: Multi-factor trend strength
        df['trend_strength'] = (
            df['ema_fast_slope_abs'] * 500 +  # Fast EMA contribution
            df['ema_slow_slope_abs'] * 500 +  # Slow EMA contribution
            abs(df['momentum_score']) * 100   # Momentum contribution
        )

        return df

    def calculate_candle_position(self, df):
        """FIXED: Calculate close position within full candle range (high to low)"""
        df = df.copy()

        # Calculate full candle range (high to low)
        df['candle_range'] = df['high'] - df['low']

        # Calculate close position within full candle range (0 = low, 1 = high)
        df['close_range_position'] = np.where(
            df['candle_range'] > 0,
            (df['close'] - df['low']) / df['candle_range'],
            0.5  # Default to middle if no range
        )

        # Also keep body analysis for additional info
        df['candle_body_high'] = np.maximum(df['open'], df['close'])
        df['candle_body_low'] = np.minimum(df['open'], df['close'])
        df['candle_body_height'] = df['candle_body_high'] - df['candle_body_low']
        df['close_body_position'] = np.where(
            df['candle_body_height'] > 0,
            (df['close'] - df['candle_body_low']) / df['candle_body_height'],
            0.5
        )

        return df

    def check_candle_approval(self, df, trade_direction):
        """FIXED: Check if LAST CLOSED candle approves trade based on close position in full candle range"""
        if len(df) < 2:
            return False, "Need at least 2 candles"

        # Use the LAST CLOSED candle (index -2), not current open candle (index -1)
        last_closed_candle = df.iloc[-2]
        close_position = last_closed_candle['close_range_position']  # Use full range position

        # Get candle details for logging
        candle_high = last_closed_candle['high']
        candle_low = last_closed_candle['low']
        candle_close = last_closed_candle['close']
        candle_range = candle_high - candle_low

        if trade_direction.upper() in ['BUY', 'LONG']:
            # For longs: close should be in top third (≥66.7% of range)
            approved = close_position >= self.long_candle_threshold
            reason = f"Last closed candle: Close {candle_close:.5f} at {close_position:.1%} of range [{candle_low:.5f}-{candle_high:.5f}] (need ≥{self.long_candle_threshold:.0%} for longs)"
        elif trade_direction.upper() in ['SELL', 'SHORT']:
            # For shorts: close should be in bottom third (≤33.3% of range)
            approved = close_position <= self.short_candle_threshold
            reason = f"Last closed candle: Close {candle_close:.5f} at {close_position:.1%} of range [{candle_low:.5f}-{candle_high:.5f}] (need ≤{self.short_candle_threshold:.0%} for shorts)"
        else:
            approved = False
            reason = f"Unknown trade direction: {trade_direction}"

        return approved, reason



    def check_candle_exit_confirmation(self, df, position_type):
        """Check if LAST CLOSED candle confirms exit for given position type"""
        if len(df) < 2:  # Need at least 2 candles to get the closed candle
            return False, "Need at least 2 candles", 0.0

        try:
            # Use LAST CLOSED candle (index -2), not current forming candle (index -1)
            closed_candle = df.iloc[-2]
            candle_high = closed_candle['high']
            candle_low = closed_candle['low']
            candle_close = closed_candle['close']
            candle_range = candle_high - candle_low

            if candle_range <= 0:
                return False, "No candle range", 0.5

            close_position = (candle_close - candle_low) / candle_range

            if position_type.upper() == 'BUY':
                # For BUY exits: close should be below 40% of range (bearish confirmation)
                confirmed = close_position <= 0.40
                reason = f"Current candle: Close {candle_close:.5f} at {close_position:.1%} of range [{candle_low:.5f}-{candle_high:.5f}] (need ≤40% to exit BUY)"
            elif position_type.upper() == 'SELL':
                # For SELL exits: close should be above 60% of range (bullish confirmation)
                confirmed = close_position >= 0.60
                reason = f"Current candle: Close {candle_close:.5f} at {close_position:.1%} of range [{candle_low:.5f}-{candle_high:.5f}] (need ≥60% to exit SELL)"
            else:
                return False, f"Unknown position type: {position_type}", 0.0

            return confirmed, reason, close_position

        except Exception as e:
            return False, f"Error checking candle confirmation: {e}", 0.0

    def calculate_recent_candle_strength(self, df, lookback=8):
        """MIDTERM: Calculate recent candle strength for logging (longer lookback)"""
        # CRITICAL FIX: Only use CLOSED candles, exclude current forming candle
        if len(df) < lookback + 1:  # Need +1 because we exclude the last (forming) candle
            return {
                'bullish_strength': 0.0,
                'bearish_strength': 0.0,
                'net_strength': 0.0,
                'dominant_bias': 'NEUTRAL',
                'candles_analyzed': len(df) - 1 if len(df) > 0 else 0
            }

        # Use df[:-1] to exclude the current forming candle, then take last 'lookback' closed candles
        closed_candles_df = df[:-1]  # Exclude current forming candle
        recent_candles = closed_candles_df.tail(lookback).copy()

        # Calculate individual candle strengths
        recent_candles['candle_range'] = recent_candles['high'] - recent_candles['low']
        recent_candles['candle_body'] = abs(recent_candles['close'] - recent_candles['open'])
        recent_candles['body_to_range_ratio'] = recent_candles['candle_body'] / recent_candles['candle_range']

        # Bullish/Bearish classification
        recent_candles['is_bullish'] = recent_candles['close'] > recent_candles['open']
        recent_candles['is_bearish'] = recent_candles['close'] < recent_candles['open']

        # Weight by body strength and ATR
        atr_recent = recent_candles['atr'].iloc[-1] if 'atr' in recent_candles.columns else recent_candles['candle_range'].mean()
        recent_candles['strength_weight'] = (recent_candles['candle_body'] / atr_recent) * recent_candles['body_to_range_ratio']

        # Calculate weighted strengths
        bullish_strength = (recent_candles['is_bullish'] * recent_candles['strength_weight']).sum()
        bearish_strength = (recent_candles['is_bearish'] * recent_candles['strength_weight']).sum()

        # Normalize to 0-1 scale
        total_strength = bullish_strength + bearish_strength
        if total_strength > 0:
            bullish_strength_norm = bullish_strength / total_strength
            bearish_strength_norm = bearish_strength / total_strength
        else:
            bullish_strength_norm = 0.5
            bearish_strength_norm = 0.5

        net_strength = bullish_strength_norm - bearish_strength_norm

        # Determine dominant bias
        if net_strength > 0.15:
            dominant_bias = 'BULLISH'
        elif net_strength < -0.15:
            dominant_bias = 'BEARISH'
        else:
            dominant_bias = 'NEUTRAL'

        # NEW: Calculate acceleration (Phase 1: Monitoring)
        acceleration_data = self.calculate_acceleration(bullish_strength_norm, bearish_strength_norm)

        return {
            'bullish_strength': bullish_strength_norm,
            'bearish_strength': bearish_strength_norm,
            'net_strength': net_strength,
            'dominant_bias': dominant_bias,
            'candles_analyzed': lookback,
            # NEW: Add acceleration data
            'bull_acceleration': acceleration_data['bull_acceleration'],
            'bear_acceleration': acceleration_data['bear_acceleration'],
            'bull_velocity': acceleration_data['bull_velocity'],
            'bear_velocity': acceleration_data['bear_velocity'],
            'acceleration_available': acceleration_data['acceleration_available']
        }

    def calculate_acceleration(self, current_bull_strength, current_bear_strength):
        """NEW: Calculate bull/bear acceleration (Phase 1: Monitoring)"""
        # NOTE: This method now receives strength values from CLOSED candles only
        # since calculate_recent_candle_strength was fixed to exclude forming candle

        # Update strength history (keep last 3 values)
        self.bull_strength_history.append(current_bull_strength)
        self.bear_strength_history.append(current_bear_strength)

        if len(self.bull_strength_history) > 3:
            self.bull_strength_history.pop(0)
        if len(self.bear_strength_history) > 3:
            self.bear_strength_history.pop(0)

        # Need at least 2 data points for velocity, 3 for acceleration
        if len(self.bull_strength_history) < 2:
            return {
                'bull_acceleration': 0.0,
                'bear_acceleration': 0.0,
                'bull_velocity': 0.0,
                'bear_velocity': 0.0,
                'acceleration_available': False
            }

        # Calculate current velocities (1st derivative)
        bull_velocity = (self.bull_strength_history[-1] - self.bull_strength_history[-2]) * 100  # Convert to percentage
        bear_velocity = (self.bear_strength_history[-1] - self.bear_strength_history[-2]) * 100

        # Update velocity history (keep last 2 values)
        self.bull_velocity_history.append(bull_velocity)
        self.bear_velocity_history.append(bear_velocity)

        if len(self.bull_velocity_history) > 2:
            self.bull_velocity_history.pop(0)
        if len(self.bear_velocity_history) > 2:
            self.bear_velocity_history.pop(0)

        # Calculate acceleration (2nd derivative) if we have enough data
        if len(self.bull_velocity_history) >= 2:
            bull_acceleration = self.bull_velocity_history[-1] - self.bull_velocity_history[-2]
            bear_acceleration = self.bear_velocity_history[-1] - self.bear_velocity_history[-2]
            acceleration_available = True
        else:
            bull_acceleration = 0.0
            bear_acceleration = 0.0
            acceleration_available = False

        return {
            'bull_acceleration': bull_acceleration,
            'bear_acceleration': bear_acceleration,
            'bull_velocity': bull_velocity,
            'bear_velocity': bear_velocity,
            'acceleration_available': acceleration_available
        }


    def detect_regime(self, df):
        """ENHANCED: Detect market regime with improved sensitivity for short-term trends"""
        if len(df) < self.atr_lookback:
            return "INSUFFICIENT_DATA", 0.0, {}, None, None

        # Get latest values
        latest = df.iloc[-1]

        atr_pct = latest['atr_percentile']
        fast_slope_abs = latest['ema_fast_slope_abs']
        slow_slope_abs = latest['ema_slow_slope_abs']
        bb_width_pct = latest['bb_width_percentile']
        vol_pct = latest['vol_percentile']
        trend_direction = latest['trend_direction']
        bb_squeeze = latest['bb_squeeze']
        rsi = latest['rsi']
        momentum_score = latest['momentum_score']

        # NEW: Regression channel slope values
        regression_slope = latest['regression_slope']
        regression_slope_abs = latest['regression_slope_abs']
        regression_strength = latest['regression_strength']

        # ENHANCED: Multi-factor regime scoring system with detailed reasoning
        trending_score = 0
        ranging_score = 0
        reasoning = []  # Track reasoning for each factor

        # ATR Analysis (Weight: 3 points) - More sensitive thresholds
        if atr_pct > self.trending_atr_threshold:
            trending_score += 3
            reasoning.append(f"ATR: HIGH volatility ({atr_pct:.1%} > {self.trending_atr_threshold:.1%}) → +3 TRENDING")
        elif atr_pct < self.ranging_atr_threshold:
            ranging_score += 3
            reasoning.append(f"ATR: LOW volatility ({atr_pct:.1%} < {self.ranging_atr_threshold:.1%}) → +3 RANGING")
        else:  # Middle zone - award to closer threshold
            if atr_pct > 0.5:
                trending_score += 1.5
                reasoning.append(f"ATR: MEDIUM-HIGH volatility ({atr_pct:.1%}) → +1.5 TRENDING")
            else:
                ranging_score += 1.5
                reasoning.append(f"ATR: MEDIUM-LOW volatility ({atr_pct:.1%}) → +1.5 RANGING")

        # ENHANCED: Fast EMA Slope Analysis (Weight: 2.5 points) - Early trend detection
        if fast_slope_abs > self.fast_slope_threshold:
            trending_score += 2.5
            reasoning.append(f"Fast EMA: STRONG slope ({fast_slope_abs:.4f} > {self.fast_slope_threshold:.4f}) → +2.5 TRENDING")
        elif fast_slope_abs < (self.fast_slope_threshold * 0.5):
            ranging_score += 2.5
            reasoning.append(f"Fast EMA: FLAT slope ({fast_slope_abs:.4f} < {self.fast_slope_threshold * 0.5:.4f}) → +2.5 RANGING")
        else:
            mid_threshold = self.fast_slope_threshold * 0.75
            if fast_slope_abs > mid_threshold:
                trending_score += 1
                reasoning.append(f"Fast EMA: MODERATE slope ({fast_slope_abs:.4f} > {mid_threshold:.4f}) → +1 TRENDING")
            else:
                ranging_score += 1
                reasoning.append(f"Fast EMA: WEAK slope ({fast_slope_abs:.4f} ≤ {mid_threshold:.4f}) → +1 RANGING")

        # Slow EMA Slope Analysis (Weight: 2 points) - Confirmation
        if slow_slope_abs > self.trending_slope_threshold:
            trending_score += 2
            reasoning.append(f"Slow EMA: STRONG slope ({slow_slope_abs:.4f} > {self.trending_slope_threshold:.4f}) → +2 TRENDING")
        elif slow_slope_abs < self.ranging_slope_threshold:
            ranging_score += 2
            reasoning.append(f"Slow EMA: FLAT slope ({slow_slope_abs:.4f} < {self.ranging_slope_threshold:.4f}) → +2 RANGING")
        else:
            mid_threshold = (self.trending_slope_threshold + self.ranging_slope_threshold) / 2
            if slow_slope_abs > mid_threshold:
                trending_score += 0.5
                reasoning.append(f"Slow EMA: MODERATE slope ({slow_slope_abs:.4f} > {mid_threshold:.4f}) → +0.5 TRENDING")
            else:
                ranging_score += 0.5
                reasoning.append(f"Slow EMA: WEAK slope ({slow_slope_abs:.4f} ≤ {mid_threshold:.4f}) → +0.5 RANGING")

        # NEW: RSI Momentum Analysis (Weight: 1.5 points)
        rsi_upper = 50 + self.momentum_threshold * 50
        rsi_lower = 50 - self.momentum_threshold * 50
        if rsi > rsi_upper:  # Strong momentum up
            trending_score += 1.5
            reasoning.append(f"RSI: STRONG bullish momentum ({rsi:.1f} > {rsi_upper:.1f}) → +1.5 TRENDING")
        elif rsi < rsi_lower:  # Strong momentum down
            trending_score += 1.5
            reasoning.append(f"RSI: STRONG bearish momentum ({rsi:.1f} < {rsi_lower:.1f}) → +1.5 TRENDING")
        elif 45 < rsi < 55:  # Neutral momentum = ranging
            ranging_score += 1.5
            reasoning.append(f"RSI: NEUTRAL momentum ({rsi:.1f} in 45-55 range) → +1.5 RANGING")
        else:  # Mild momentum
            trending_score += 0.5
            reasoning.append(f"RSI: MILD momentum ({rsi:.1f}) → +0.5 TRENDING")

        # NEW: Multi-timeframe Momentum Analysis (Weight: 1.5 points)
        if abs(momentum_score) >= 2:  # Strong directional momentum
            trending_score += 1.5
            reasoning.append(f"Momentum: STRONG directional ({momentum_score:+d}/3) → +1.5 TRENDING")
        elif momentum_score == 0:  # No clear momentum
            ranging_score += 1.5
            reasoning.append(f"Momentum: NO clear direction ({momentum_score}/3) → +1.5 RANGING")
        else:  # Weak momentum
            trending_score += 0.5
            reasoning.append(f"Momentum: WEAK directional ({momentum_score:+d}/3) → +0.5 TRENDING")

        # Bollinger Band Analysis (Weight: 2 points)
        if bb_squeeze:
            ranging_score += 2
            reasoning.append(f"BB: SQUEEZE detected (width {bb_width_pct:.1%} < {self.bb_squeeze_threshold:.1%}) → +2 RANGING")
        elif bb_width_pct > 0.65:  # More sensitive threshold
            trending_score += 2
            reasoning.append(f"BB: WIDE bands ({bb_width_pct:.1%} > 65%) → +2 TRENDING")
        elif bb_width_pct > 0.40:
            trending_score += 1
            reasoning.append(f"BB: MODERATE width ({bb_width_pct:.1%} > 40%) → +1 TRENDING")
        elif bb_width_pct < self.bb_width_threshold:
            ranging_score += 1
            reasoning.append(f"BB: NARROW bands ({bb_width_pct:.1%} < {self.bb_width_threshold:.1%}) → +1 RANGING")
        else:
            reasoning.append(f"BB: NEUTRAL width ({bb_width_pct:.1%}) → No points")

        # Volatility confirmation (Weight: 1 point)
        if vol_pct > 0.55:  # More sensitive threshold
            trending_score += 1
            reasoning.append(f"Volatility: HIGH ({vol_pct:.1%} > 55%) → +1 TRENDING")
        else:
            ranging_score += 1
            reasoning.append(f"Volatility: LOW ({vol_pct:.1%} ≤ 55%) → +1 RANGING")

        # Simple Regression Channel Analysis (Weight: 4 points)
        # Note: This will be called from FixedLiveTrader with simple_regression passed as parameter
        simple_channel_info = None
        channel_state = None
        regime_contribution = 0

        # Check if simple_regression is available (passed from FixedLiveTrader)
        if hasattr(self, '_simple_regression') and self._simple_regression is not None:
            simple_channel_info = self._simple_regression.update_channel_state(df)
            channel_state = simple_channel_info['state']
            regime_contribution = simple_channel_info['regime_contribution']

        if simple_channel_info and channel_state == 'ACTIVE_CHANNEL':
            # Use new regime contribution system based on channel slope
            channel_data = simple_channel_info['channel_data']
            if channel_data:
                simple_slope = channel_data['slope']
                simple_slope_abs = abs(simple_slope)
                r_squared = channel_data['r_squared']
                candle_count = channel_data['candle_count']
                # Determine channel type based on slope
                if simple_slope_abs <= 0.05:
                    channel_type = "FLAT_RANGING"
                else:
                    channel_type = "TRENDING"

                # Apply regime contribution (negative for RANGING, zero for TRENDING)
                if regime_contribution < 0:
                    # Flat channel - contributes to RANGING
                    ranging_score += abs(regime_contribution)  # Convert negative to positive for ranging_score
                    reasoning.append(f"Simple Channel: FLAT {channel_type} slope ({simple_slope:+.3f}, R²={r_squared:.2f}, {candle_count} candles) → +{abs(regime_contribution)} RANGING")
                else:
                    # Trending channel - no contribution, let other indicators decide
                    direction = "UP" if simple_slope > 0 else "DOWN"
                    reasoning.append(f"Simple Channel: {channel_type} {direction} slope ({simple_slope:+.3f}, R²={r_squared:.2f}, {candle_count} candles) → No regime bias")
            else:
                reasoning.append(f"Simple Channel: ACTIVE but no data → No points")

        elif channel_state == 'BUILDING':
            # Building state (no channel yet) - use traditional regression
            reasoning.append(f"Simple Channel: BUILDING new channel → Use traditional regression")

        # Fallback to traditional regression if simple channel fails
        if channel_state != 'ACTIVE_CHANNEL' and not pd.isna(regression_slope_abs):
            if regression_strength == 'STRONG':  # >= 0.10% slope
                trending_score += 1.5  # Reduced weight as fallback
                direction = "UP" if regression_slope > 0 else "DOWN"
                reasoning.append(f"Fallback Regression: STRONG {direction} slope ({regression_slope:+.3f}% per period) → +1.5 TRENDING")
            elif regression_strength == 'MODERATE':  # >= 0.05% slope
                trending_score += 1
                direction = "UP" if regression_slope > 0 else "DOWN"
                reasoning.append(f"Fallback Regression: MODERATE {direction} slope ({regression_slope:+.3f}% per period) → +1 TRENDING")
            elif regression_strength == 'WEAK':  # >= 0.02% slope
                trending_score += 0.5
                direction = "UP" if regression_slope > 0 else "DOWN"
                reasoning.append(f"Fallback Regression: WEAK {direction} slope ({regression_slope:+.3f}% per period) → +0.5 TRENDING")
            else:  # FLAT < 0.02%
                ranging_score += 1.5
                reasoning.append(f"Fallback Regression: FLAT slope ({regression_slope:+.3f}% per period) → +1.5 RANGING")

        # ENHANCED: Decision logic with more sensitive thresholds
        score_diff = abs(trending_score - ranging_score)
        max_possible_score = 19.0  # Updated max score (13.5 + 4.5 for adaptive regression + 1.0 quality bonus)
        confidence = max(trending_score, ranging_score) / max_possible_score

        # More sensitive regime classification
        min_score = 3.0  # Even less conservative
        min_score_diff = 1.0  # More sensitive to differences

        # Determine regime with detailed reasoning
        if trending_score >= min_score and score_diff >= min_score_diff and trending_score > ranging_score:
            regime = "TRENDING"
            regime_reason = f"TRENDING: Score {trending_score:.1f} > {ranging_score:.1f} (diff {score_diff:.1f} ≥ {min_score_diff})"
        elif ranging_score >= min_score and score_diff >= min_score_diff and ranging_score > trending_score:
            regime = "RANGING"
            regime_reason = f"RANGING: Score {ranging_score:.1f} > {trending_score:.1f} (diff {score_diff:.1f} ≥ {min_score_diff})"
        else:
            regime = "TRANSITIONAL"
            if score_diff < min_score_diff:
                regime_reason = f"TRANSITIONAL: Scores too close ({trending_score:.1f} vs {ranging_score:.1f}, diff {score_diff:.1f} < {min_score_diff})"
            else:
                regime_reason = f"TRANSITIONAL: Insufficient evidence (max score {max(trending_score, ranging_score):.1f} < {min_score})"

        # Enhanced regime details for logging with reasoning
        details = {
            'atr_percentile': atr_pct,
            'fast_ema_slope': latest['ema_fast_slope'],
            'fast_ema_slope_abs': fast_slope_abs,
            'slow_ema_slope': latest['ema_slow_slope'],
            'slow_ema_slope_abs': slow_slope_abs,
            'bb_width_percentile': bb_width_pct,
            'bb_squeeze': bb_squeeze,
            'rsi': rsi,
            'momentum_score': momentum_score,
            'trending_score': trending_score,
            'ranging_score': ranging_score,
            'score_diff': score_diff,
            'confidence': confidence,
            'trend_strength': latest['trend_strength'],
            'reasoning': reasoning,
            'regime_reason': regime_reason,
            'simple_channel_state': channel_state,
            'simple_channel_info': simple_channel_info
        }

        # Get accurate trend direction
        accurate_trend_direction = latest['accurate_trend_direction']

        # DEBUG: Log individual trend voting components
        self.logger.info(f"🔍 TREND DIRECTION VOTING DEBUG:")
        self.logger.info(f"   EMA Fast Trend: {latest.get('ema_fast_trend', 'N/A')} (slope: {latest.get('ema_fast_slope', 'N/A'):+.6f})")
        self.logger.info(f"   EMA Slow Trend: {latest.get('ema_slow_trend', 'N/A')} (slope: {latest.get('ema_slow_slope', 'N/A'):+.6f})")
        self.logger.info(f"   EMA Cross Trend: {latest.get('ema_cross_trend', 'N/A')} (fast: {latest.get('ema_fast', 'N/A'):.2f}, slow: {latest.get('ema_slow', 'N/A'):.2f})")
        self.logger.info(f"   Price Trend 3: {latest.get('price_trend_3', 'N/A')} (current: {latest.get('close', 'N/A'):.2f}, 3-ago: {latest.get('close', 'N/A'):.2f})")
        self.logger.info(f"   Price Trend 5: {latest.get('price_trend_5', 'N/A')}")
        self.logger.info(f"   RSI Momentum: {latest.get('rsi_momentum', 'N/A')} (RSI: {latest.get('rsi', 'N/A'):.1f})")
        self.logger.info(f"   Momentum Score: {latest.get('momentum_score', 'N/A')}/3 (3p: {latest.get('momentum_3', 'N/A'):+.4f}, 5p: {latest.get('momentum_5', 'N/A'):+.4f}, 8p: {latest.get('momentum_8', 'N/A'):+.4f})")
        self.logger.info(f"   Regression Slope: {latest.get('regression_slope', 'N/A'):+.4f}% ({latest.get('regression_strength', 'N/A')})")

        # DEBUG: Show regression slope calculation details
        if len(df) >= 20:
            last_20_prices = df['close'].tail(20).values
            first_price = last_20_prices[0]
            last_price = last_20_prices[-1]
            simple_slope_pct = ((last_price - first_price) / first_price) * 100
            self.logger.info(f"🔍 REGRESSION SLOPE DEBUG:")
            self.logger.info(f"   Last 20 candles: {first_price:.2f} → {last_price:.2f}")
            self.logger.info(f"   Simple slope: {simple_slope_pct:+.4f}% over 20 periods")
            self.logger.info(f"   Regression slope: {latest.get('regression_slope', 'N/A'):+.4f}% per period")
            self.logger.info(f"   Expected total change: {latest.get('regression_slope', 0) * 20:+.4f}% over 20 periods")

        self.logger.info(f"   Final Decision: {accurate_trend_direction}")

        return regime, confidence, details, trend_direction, accurate_trend_direction

    def set_simple_regression(self, simple_regression):
        """Set the simple regression channel instance for regime detection"""
        self._simple_regression = simple_regression

class FixedLiveTrader:
    def __init__(self, symbol="XAUUSD!"):
        # Trading parameters
        self.symbol = symbol

        # Initialize components with symbol
        self.mt5_manager = MT5Manager(symbol=symbol)
        self.feature_engineer = FixedFeatureEngineer()
        self.regime_detector = RegimeDetector()
        self.trade_logger = TradeLogger("trade_log.csv")

        # Setup logging
        self.logger = logging.getLogger(__name__)

        # Load trained model
        self.model = None
        self.scaler = None
        self.selected_features = None

        # Symbol specifications for position sizing
        self.SYMBOL_SPECS = {
            "XAUUSD!": {"contract_size": 100, "name": "Gold"},      # 1 lot = 100 ounces
            "EURUSD!": {"contract_size": 100000, "name": "Euro"},   # 1 lot = 100,000 units
            "BTCUSD": {"contract_size": 1, "name": "Bitcoin"}       # 1 lot = 1 BTC
        }

        # Other trading parameters
        self.timeframe = "M5"
        self.risk_percent = 4.0
        self.min_confidence = 0.05  # Lowered to match ±5% candle strength thresholds

        # Validate symbol
        if self.symbol not in self.SYMBOL_SPECS:
            self.logger.warning(f"⚠️ Unknown symbol {self.symbol}. Using default contract size of 100.")
            self.SYMBOL_SPECS[self.symbol] = {"contract_size": 100, "name": "Unknown"}

        self.logger.info(f"🎯 Trading System initialized for {self.SYMBOL_SPECS[self.symbol]['name']} ({self.symbol})")
        self.logger.info(f"📊 Contract size: {self.SYMBOL_SPECS[self.symbol]['contract_size']}")

        # Position tracking for single concurrent trade
        self.current_position = None  # {'type': 'BUY'/'SELL', 'ticket': ticket_id, 'time': datetime, 'volume': float, 'remaining_volume': float}
        self.last_signal = None  # Track last signal for opposite signal detection
        self.last_trade_time = None  # Track last trade time to prevent overtrading

        # Trailing stop tracking
        self.trailing_stop_data = None  # {'initial_sl': price, 'current_sl': price, 'atr_value': value, 'profit_atr_count': 0}

        # Regime tracking
        self.current_regime = None
        self.regime_confidence = 0.0
        self.last_regime_change = None

        # NEW: Pending regime change exit tracking
        self.pending_regime_exit = None  # {'reason': str, 'unfavorable_regime': str, 'position_type': str}

        # NEW: Iteration tracking for enhanced logging
        self.iteration_count = 0

        # NEW: Previous candle strength tracking for relative change signals
        self.previous_candle_strength = None

        # NEW: Store latest features_df for exit confirmations
        self.latest_features_df = None

        # NEW: QQE Indicator for primary signal generation - FIXED: Match user's TradingView settings
        self.qqe_indicator = QQEIndicator(
            rsi_period=7,  # USER'S TRADINGVIEW SETTING: 7
            rsi_smoothing=5,
            qqe_factor=1.0,  # USER'S TRADINGVIEW SETTING: 1
            threshold=10
        )

        # NEW: Swing point distance filtering
        self.swing_distance_atr_threshold = 1.5  # Maximum distance from recent swing point (in ATR)

        # Simple Regression Channel System
        self.simple_regression = SimpleRegressionChannel(
            periods=20,                       # Fixed 20-period lookback
            std_multiplier=2.0               # 2 standard deviations for channel bounds
        )

        # Pass simple regression to regime detector
        self.regime_detector.set_simple_regression(self.simple_regression)

    def get_latest_data_safe(self):
        """Safely get latest features data for exit confirmations"""
        if hasattr(self, 'latest_features_df') and self.latest_features_df is not None and len(self.latest_features_df) > 0:
            return self.latest_features_df
        else:
            # Fallback: get fresh data if stored data not available
            try:
                df = self.mt5_manager.get_latest_data(self.symbol, self.timeframe, 50)
                if df is not None and len(df) > 0:
                    features_df = self.feature_engineer.create_technical_indicators(df)
                    features_df = self.regime_detector.calculate_regime_indicators(features_df)
                    features_df = self.regime_detector.calculate_candle_position(features_df)
                    return features_df
            except Exception as e:
                self.logger.error(f"❌ Error getting fallback data: {e}")
            return None

    def find_recent_swing_points(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Find recent swing high and low points by searching back through historical data

        Logic:
        - Recent High: L2 > L3 and L1 < L2, then H2 is recent high
        - Recent Low: H2 > H3 and H1 < H2, then L2 is recent low
        - Search back up to 50 candles to find the most recent swing points

        Returns dict with recent swing points and distances
        """
        try:
            if len(df) < 5:  # Need at least 5 candles for 3-candle pattern search
                return {
                    'recent_high': None,
                    'recent_low': None,
                    'recent_high_distance': None,
                    'recent_low_distance': None,
                    'swing_points_available': False
                }

            current_price = df.iloc[-1]['close']  # Current price for distance calculation
            recent_high = None
            recent_low = None
            recent_high_index = None
            recent_low_index = None

            # Search back through candles to find swing points
            # Start from 3 candles back (to have a 3-candle pattern) and go back up to 50 candles
            max_lookback = min(50, len(df) - 2)  # Don't go beyond available data

            self.logger.info(f"🔍 SWING POINT SEARCH: Looking back {max_lookback} candles")

            # Search for swing points independently - find most recent high and most recent low separately

            # Search for most recent swing high
            for i in range(3, max_lookback):
                if recent_high is not None:
                    break

                # Get 3-candle pattern: [i+1, i, i-1] where i is the middle candle
                if i + 1 >= len(df):
                    continue

                c1_idx = -(i-1)  # Most recent of the 3
                c2_idx = -i      # Middle candle
                c3_idx = -(i+1)  # Oldest of the 3

                c1 = df.iloc[c1_idx]
                c2 = df.iloc[c2_idx]
                c3 = df.iloc[c3_idx]

                l1, l2, l3 = c1['low'], c2['low'], c3['low']
                h1, h2, h3 = c1['high'], c2['high'], c3['high']

                # Check for recent high: L2 > L3 and L1 < L2, then H2 is recent high
                if l2 > l3 and l1 < l2:
                    recent_high = h2
                    recent_high_index = c2_idx
                    self.logger.info(f"✅ RECENT HIGH FOUND: H2={h2:.5f} at index {c2_idx} ({i} candles back)")
                    break

            # Search for most recent swing low (independent search)
            for i in range(3, max_lookback):
                if recent_low is not None:
                    break

                # Get 3-candle pattern: [i+1, i, i-1] where i is the middle candle
                if i + 1 >= len(df):
                    continue

                c1_idx = -(i-1)  # Most recent of the 3
                c2_idx = -i      # Middle candle
                c3_idx = -(i+1)  # Oldest of the 3

                c1 = df.iloc[c1_idx]
                c2 = df.iloc[c2_idx]
                c3 = df.iloc[c3_idx]

                l1, l2, l3 = c1['low'], c2['low'], c3['low']
                h1, h2, h3 = c1['high'], c2['high'], c3['high']

                # Check for recent low: H2 > H3 and H1 < H2, then L2 is recent low
                if h2 > h3 and h1 < h2:
                    recent_low = l2
                    recent_low_index = c2_idx
                    self.logger.info(f"✅ RECENT LOW FOUND: L2={l2:.5f} at index {c2_idx} ({i} candles back)")
                    break

            # Log results
            if recent_high is None and recent_low is None:
                self.logger.info(f"❌ No swing points found in {max_lookback} candle lookback")
            else:
                if recent_high:
                    self.logger.info(f"📍 Recent High: {recent_high:.5f} ({abs(recent_high_index)} candles ago)")
                if recent_low:
                    self.logger.info(f"📍 Recent Low: {recent_low:.5f} ({abs(recent_low_index)} candles ago)")

            # Calculate distances from current price
            recent_high_distance = abs(current_price - recent_high) if recent_high else None
            recent_low_distance = abs(current_price - recent_low) if recent_low else None

            return {
                'recent_high': recent_high,
                'recent_low': recent_low,
                'recent_high_distance': recent_high_distance,
                'recent_low_distance': recent_low_distance,
                'swing_points_available': recent_high is not None or recent_low is not None,
                'recent_high_candles_ago': abs(recent_high_index) if recent_high_index else None,
                'recent_low_candles_ago': abs(recent_low_index) if recent_low_index else None,
                'search_depth': max_lookback
            }

        except Exception as e:
            self.logger.error(f"❌ Error in swing point detection: {e}")
            return {
                'recent_high': None,
                'recent_low': None,
                'recent_high_distance': None,
                'recent_low_distance': None,
                'swing_points_available': False
            }

    def check_swing_distance_filter(self, signal: str, swing_points: Dict[str, Any], atr_value: float) -> Tuple[bool, str]:
        """
        Check if trade entry is within acceptable distance from recent swing points

        Args:
            signal: "BUY" or "SELL"
            swing_points: Dict from find_recent_swing_points()
            atr_value: Current ATR value

        Returns:
            Tuple of (approved: bool, reason: str)
        """
        try:
            if not swing_points.get('swing_points_available', False):
                # No swing points found - allow trade
                return True, "No recent swing points found"

            max_distance_points = atr_value * self.swing_distance_atr_threshold

            if signal == "BUY":
                # For LONG trades, check distance from recent low
                recent_low = swing_points.get('recent_low')
                if recent_low is not None:
                    distance = swing_points.get('recent_low_distance')
                    if distance is not None:
                        if distance <= max_distance_points:
                            return True, f"BUY approved: {distance:.5f} points from recent low {recent_low:.5f} (≤{max_distance_points:.5f} = {self.swing_distance_atr_threshold}ATR)"
                        else:
                            return False, f"BUY rejected: {distance:.5f} points from recent low {recent_low:.5f} (>{max_distance_points:.5f} = {self.swing_distance_atr_threshold}ATR)"
                # No recent low found - allow trade
                return True, "BUY approved: No recent low found"

            elif signal == "SELL":
                # For SHORT trades, check distance from recent high
                recent_high = swing_points.get('recent_high')
                if recent_high is not None:
                    distance = swing_points.get('recent_high_distance')
                    if distance is not None:
                        if distance <= max_distance_points:
                            return True, f"SELL approved: {distance:.5f} points from recent high {recent_high:.5f} (≤{max_distance_points:.5f} = {self.swing_distance_atr_threshold}ATR)"
                        else:
                            return False, f"SELL rejected: {distance:.5f} points from recent high {recent_high:.5f} (>{max_distance_points:.5f} = {self.swing_distance_atr_threshold}ATR)"
                # No recent high found - allow trade
                return True, "SELL approved: No recent high found"

            # Unknown signal type - reject
            return False, f"Unknown signal type: {signal}"

        except Exception as e:
            self.logger.error(f"❌ Error in swing distance filter: {e}")
            # On error, allow trade to avoid blocking system
            return True, f"Error in swing filter - allowing trade: {e}"

    def start_trading(self):
        self.is_running = False
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/fixed_live_trading.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def is_position_profitable(self):
        """Check if current position is in profit"""
        try:
            if not self.current_position:
                return False, 0, "No position"

            # Get current market price
            tick_info = self.mt5_manager.get_symbol_info_tick(self.symbol)
            if not tick_info:
                return False, 0, "No tick data"

            entry_price = self.current_position['price']
            position_type = self.current_position['type']

            # Use appropriate price for position type
            current_price = tick_info['ask'] if position_type == 'BUY' else tick_info['bid']

            # Calculate profit
            if position_type == 'BUY':
                profit_points = current_price - entry_price
                is_profitable = current_price > entry_price
            else:  # SELL
                profit_points = entry_price - current_price
                is_profitable = current_price < entry_price

            return is_profitable, profit_points, f"{position_type}: {entry_price:.5f} → {current_price:.5f}"

        except Exception as e:
            self.logger.error(f"❌ Error checking position profit: {e}")
            return False, 0, f"Error: {e}"

    def load_model(self):
        """DISABLED: Model loading (using candle strength instead)"""
        try:
            # Model loading disabled - using candle strength decision instead
            self.model = None
            self.scaler = None
            self.selected_features = None

            self.logger.info("✅ Model loading DISABLED - Using QQE + Candle Strength voting system")
            self.logger.info("📊 PRIMARY Signal: QQE crossover with candle strength voting")
            self.logger.info("📊 SECONDARY Signal: Strong candle strength ±50% when QQE neutral")
            return True

        except Exception as e:
            self.logger.error(f"❌ Error in model setup: {e}")
            return False
    
    def get_live_prediction(self):
        """Get live prediction using fixed model with regime detection"""
        try:
            # Get live data
            df = self.mt5_manager.get_latest_data(self.symbol, self.timeframe, 200)
            if df is None or len(df) < 100:
                return None, 0, "No data", None, None, None, {}, {}

            # Calculate features
            features_df = self.feature_engineer.create_technical_indicators(df)

            # Add regime indicators
            features_df = self.regime_detector.calculate_regime_indicators(features_df)

            # NEW: Add candlestick position analysis
            features_df = self.regime_detector.calculate_candle_position(features_df)

            # Store latest features_df for exit confirmations
            self.latest_features_df = features_df

            # Detect current regime
            regime, regime_conf, regime_details, trend_direction, accurate_trend_direction = self.regime_detector.detect_regime(features_df)

            # NEW: Calculate recent candle strength for logging
            candle_strength = self.regime_detector.calculate_recent_candle_strength(features_df)

            # DEBUG: Show candle strength calculation details
            self.logger.info(f"🕯️ CANDLE STRENGTH DEBUG (CLOSED CANDLES ONLY):")
            self.logger.info(f"   Bullish Strength: {candle_strength['bullish_strength']:.4f}")
            self.logger.info(f"   Bearish Strength: {candle_strength['bearish_strength']:.4f}")
            self.logger.info(f"   Net Strength: {candle_strength['net_strength']:.4f} ({candle_strength['net_strength']*100:+.1f}%)")
            self.logger.info(f"   Lookback: {candle_strength.get('candles_analyzed', 'N/A')} CLOSED candles (excluding current forming)")

            # Show recent CLOSED candles for verification (excluding current forming candle)
            if len(features_df) >= 4:  # Need at least 4 to show 3 closed candles
                closed_candles = features_df[:-1]  # Exclude current forming candle
                recent_closed = closed_candles[['open', 'high', 'low', 'close']].tail(3)
                current_forming = features_df.iloc[-1]  # Current forming candle

                self.logger.info(f"   Last 3 CLOSED candles (used in calculation):")
                for i, (idx, candle) in enumerate(recent_closed.iterrows()):
                    candle_type = "🟢 BULL" if candle['close'] > candle['open'] else "🔴 BEAR" if candle['close'] < candle['open'] else "⚪ DOJI"
                    body_size = abs(candle['close'] - candle['open'])
                    full_range = candle['high'] - candle['low']
                    body_pct = (body_size / full_range * 100) if full_range > 0 else 0
                    self.logger.info(f"     {i+1}: {candle_type} | Body: {body_size:.2f} ({body_pct:.1f}% of range) | O:{candle['open']:.2f} H:{candle['high']:.2f} L:{candle['low']:.2f} C:{candle['close']:.2f}")

                # Show current forming candle for comparison (NOT used in calculation)
                forming_type = "🟢 BULL" if current_forming['close'] > current_forming['open'] else "🔴 BEAR" if current_forming['close'] < current_forming['open'] else "⚪ DOJI"
                forming_body = abs(current_forming['close'] - current_forming['open'])
                forming_range = current_forming['high'] - current_forming['low']
                forming_pct = (forming_body / forming_range * 100) if forming_range > 0 else 0
                self.logger.info(f"   Current FORMING candle (NOT used in calculation):")
                self.logger.info(f"     ⏳ {forming_type} | Body: {forming_body:.2f} ({forming_pct:.1f}% of range) | O:{current_forming['open']:.2f} H:{current_forming['high']:.2f} L:{current_forming['low']:.2f} C:{current_forming['close']:.2f}")

            # Get ATR for stop loss calculation (no longer need ML features)
            latest_atr = features_df['atr'].iloc[-1]
            if pd.isna(latest_atr):
                latest_atr = features_df['atr'].dropna().iloc[-1] if len(features_df['atr'].dropna()) > 0 else 0.01

            # NEW: QQE-based primary signal generation with candle strength voting
            candle_net_strength = candle_strength['net_strength'] * 100  # Convert to percentage

            # Calculate QQE indicators
            features_df = self.qqe_indicator.calculate_qqe_bands(features_df)
            features_df = self.qqe_indicator.generate_qqe_signals(features_df)
            qqe_analysis = self.qqe_indicator.get_qqe_analysis(features_df)

            # PRIMARY: QQE Signal Generation
            qqe_signal = qqe_analysis.get('qqe_signal', 0)
            qqe_strength = qqe_analysis.get('qqe_signal_strength', 0)

            # VOTING SYSTEM: QQE + Candle Strength
            raw_signal = None
            base_confidence = 0.0
            signal_reason = ""

            if qqe_signal != 0:
                # QQE generated a signal - now check candle strength vote
                if qqe_signal == 1:  # QQE Long
                    if candle_net_strength > 0:  # Candle strength agrees
                        raw_signal = "BUY"
                        # Combined confidence: QQE strength + candle strength agreement
                        base_confidence = min((qqe_strength * 0.7) + (abs(candle_net_strength) / 100 * 0.3), 1.0)
                        signal_reason = f"QQE_LONG + CANDLE_AGREE: QQE strength {qqe_strength:.3f}, Candle {candle_net_strength:+.1f}%"
                    elif abs(candle_net_strength) < 20:  # Neutral candle strength - QQE decides
                        raw_signal = "BUY"
                        base_confidence = qqe_strength * 0.8  # Reduced confidence without candle agreement
                        signal_reason = f"QQE_LONG + CANDLE_NEUTRAL: QQE strength {qqe_strength:.3f}, Candle {candle_net_strength:+.1f}%"
                    else:  # Candle strength disagrees - no signal
                        raw_signal = None
                        base_confidence = 0.0
                        signal_reason = f"QQE_LONG + CANDLE_DISAGREE: QQE strength {qqe_strength:.3f}, Candle {candle_net_strength:+.1f}%"

                elif qqe_signal == -1:  # QQE Short
                    if candle_net_strength < 0:  # Candle strength agrees
                        raw_signal = "SELL"
                        # Combined confidence: QQE strength + candle strength agreement
                        base_confidence = min((qqe_strength * 0.7) + (abs(candle_net_strength) / 100 * 0.3), 1.0)
                        signal_reason = f"QQE_SHORT + CANDLE_AGREE: QQE strength {qqe_strength:.3f}, Candle {candle_net_strength:+.1f}%"
                    elif abs(candle_net_strength) < 20:  # Neutral candle strength - QQE decides
                        raw_signal = "SELL"
                        base_confidence = qqe_strength * 0.8  # Reduced confidence without candle agreement
                        signal_reason = f"QQE_SHORT + CANDLE_NEUTRAL: QQE strength {qqe_strength:.3f}, Candle {candle_net_strength:+.1f}%"
                    else:  # Candle strength disagrees - no signal
                        raw_signal = None
                        base_confidence = 0.0
                        signal_reason = f"QQE_SHORT + CANDLE_DISAGREE: QQE strength {qqe_strength:.3f}, Candle {candle_net_strength:+.1f}%"
            else:
                # No QQE signal - check if strong candle strength can generate signal
                if abs(candle_net_strength) >= 50:  # Very strong candle strength
                    if candle_net_strength > 0:
                        raw_signal = "BUY"
                        signal_reason = f"CANDLE_ONLY_STRONG: {candle_net_strength:+.1f}% (≥50%)"
                    else:
                        raw_signal = "SELL"
                        signal_reason = f"CANDLE_ONLY_STRONG: {candle_net_strength:+.1f}% (≤-50%)"
                    base_confidence = min(abs(candle_net_strength) / 100 * 0.6, 1.0)  # Lower confidence without QQE
                else:
                    raw_signal = None
                    base_confidence = 0.0
                    signal_reason = f"NO_SIGNAL: QQE neutral, Candle {candle_net_strength:+.1f}% (need ±50%)"

            # Apply acceleration-based confidence weighting if available
            ml_confidence = base_confidence
            if raw_signal and candle_strength.get('acceleration_available', False):
                bull_accel = candle_strength.get('bull_acceleration', 0)
                bear_accel = candle_strength.get('bear_acceleration', 0)

                # Calculate acceleration factor
                if raw_signal == "BUY":
                    accel_factor = max(0.5, min(1.5, 1.0 + (bull_accel / 100)))
                    signal_reason += f" | Bull Accel: {bull_accel:+.1f}% (factor: {accel_factor:.2f})"
                else:  # SELL
                    accel_factor = max(0.5, min(1.5, 1.0 + (bear_accel / 100)))
                    signal_reason += f" | Bear Accel: {bear_accel:+.1f}% (factor: {accel_factor:.2f})"

                # Apply acceleration factor to confidence
                ml_confidence = min(base_confidence * accel_factor, 1.0)

            # Update previous strength for next iteration (keep for logging)
            self.previous_candle_strength = candle_net_strength

            # NEW: Apply swing point distance filtering
            if raw_signal is not None:
                swing_points = self.find_recent_swing_points(features_df)
                swing_approved, swing_reason = self.check_swing_distance_filter(raw_signal, swing_points, latest_atr)
                if not swing_approved:
                    raw_signal = None
                    signal_reason = f"SWING_DISTANCE_VETO: {swing_reason}"

            # Store for logging
            self.current_ml_signal = raw_signal
            self.current_trend_direction = accurate_trend_direction
            self.regime_confidence = regime_conf

            # Apply regime-based logic with trend direction filtering and BB position
            final_signal, logic = self.apply_regime_logic(raw_signal, regime, trend_direction, accurate_trend_direction, features_df)

            # RE-ENABLED: Apply candlestick approval filter
            if final_signal is not None:
                candle_approved, candle_reason = self.regime_detector.check_candle_approval(features_df, final_signal)
                if not candle_approved:
                    final_signal = None
                    logic = f"CANDLE_VETO({candle_reason})"

            # Enhanced detailed info with new features (using relative change signals)
            candle_info = f"Candle:{candle_strength['dominant_bias']}({candle_strength['net_strength']:+.2f})"
            candle_strength_pct = candle_net_strength
            regime_reason_brief = regime_details.get('regime_reason', '').split(':')[0] if regime_details.get('regime_reason') else ''

            # Add signal reason to details
            if 'signal_reason' in locals():
                signal_info = f"Signal:{signal_reason}"
            else:
                signal_info = f"Current:{candle_strength_pct:+.1f}%"

            details = f"CandleStrength:{raw_signal if raw_signal else 'NONE'} | {signal_info} | Regime:{regime}({regime_conf:.2f}) | Trend:{accurate_trend_direction} | {candle_info} | Logic:{logic} | Reason:{regime_reason_brief}"

            return final_signal, ml_confidence, details, latest_atr, regime, logic, regime_details, candle_strength, qqe_analysis, features_df

        except Exception as e:
            self.logger.error(f"❌ Error getting prediction: {e}")
            return None, 0, str(e), None, None, None, {}, {}, {}, None

    def apply_regime_logic(self, raw_signal, regime, trend_direction, accurate_trend_direction, features_df=None):
        """TRENDING ONLY: Only trade in trending markets, block all ranging/transitional signals"""
        if raw_signal is None:
            return None, "NO_SIGNAL_FROM_CANDLE_STRENGTH"

        if regime == "TRENDING":
            # TRENDING: Allow signals aligned with trend direction
            signal_aligns_with_trend = (
                (accurate_trend_direction == "UP" and raw_signal == "BUY") or
                (accurate_trend_direction == "DOWN" and raw_signal == "SELL")
            )

            if signal_aligns_with_trend:
                return raw_signal, f"TREND_ALIGNED({accurate_trend_direction})"
            else:
                # VETO signals that go against the trend in TRENDING markets
                return None, f"TREND_VETO({accurate_trend_direction}_blocks_{raw_signal})"

        elif regime == "RANGING":
            # RANGING: Block all signals - no trading in ranging markets
            return None, f"RANGING_BLOCKED_TRENDING_ONLY"

        else:  # TRANSITIONAL
            # TRANSITIONAL: Block all signals - no trading in transitional markets
            return None, f"TRANSITIONAL_BLOCKED_TRENDING_ONLY"

    def check_regime_change(self, new_regime):
        """Check if regime has changed and handle position closure (smart logic)"""
        if self.current_regime is None:
            self.current_regime = new_regime
            return False

        if new_regime != self.current_regime:
            self.logger.info(f"🔄 REGIME CHANGE: {self.current_regime} → {new_regime}")

            # Smart regime change logic
            if self.current_position:
                # Check if position is profitable
                current_positions = self.mt5_manager.get_positions()
                is_profitable = False
                profit = 0

                if current_positions:
                    for pos in current_positions:
                        if pos['ticket'] == self.current_position['ticket']:
                            profit = pos.get('profit', 0)
                            is_profitable = profit > 0
                            break

                # SMART REGIME CHANGE LOGIC: Preserve profitable trades in key transitions
                should_close = False
                should_remove_tp = False

                if self.current_regime == "TRANSITIONAL" and new_regime == "TRENDING":
                    # TRANSITIONAL → TRENDING: Only close if NOT profitable
                    if not is_profitable:
                        should_close = True
                        reason = "TRANSITIONAL→TRENDING (Not Profitable)"
                    else:
                        self.logger.info(f"💰 TRANSITIONAL→TRENDING but position is PROFITABLE (${profit:.2f}) - Keeping position open")
                        should_remove_tp = True  # Remove TP to use trailing stops

                elif self.current_regime == "RANGING" and new_regime == "TRANSITIONAL":
                    # RANGING → TRANSITIONAL: Only close if NOT profitable (sign of going to trend)
                    if not is_profitable:
                        should_close = True
                        reason = "RANGING→TRANSITIONAL (Not Profitable)"
                    else:
                        self.logger.info(f"💰 RANGING→TRANSITIONAL but position is PROFITABLE (${profit:.2f}) - Keeping position open (trend forming)")
                        should_remove_tp = True  # Remove TP to prepare for trending

                elif self.current_regime == "RANGING" and new_regime == "TRENDING":
                    # RANGING → TRENDING: Close with candle confirmation
                    should_close = True
                    reason = "RANGING→TRENDING (Direct)"

                elif self.current_regime == "TRENDING" and new_regime in ["RANGING", "TRANSITIONAL"]:
                    # TRENDING → other: Close with candle confirmation (trend ending)
                    should_close = True
                    reason = f"TRENDING→{new_regime} (Trend Ending)"

                # NEW: Apply candle confirmation to regime change exits
                if should_close:
                    # Get latest data for candle confirmation
                    try:
                        latest_data = self.get_latest_data_safe()
                        if latest_data is not None and len(latest_data) > 0:
                            candle_confirmed, candle_reason, close_position = self.regime_detector.check_candle_exit_confirmation(
                                latest_data, self.current_position['type']
                            )

                            if candle_confirmed:
                                self.logger.info(f"🔄 REGIME CHANGE EXIT CONFIRMED: {reason}")
                                self.logger.info(f"   Candle confirmation: {candle_reason}")
                                self.close_current_position(f"Regime Change - {reason}")
                                self.pending_regime_exit = None  # Clear any pending exit
                            else:
                                # Regime change would close position but candle doesn't confirm - track it
                                self.pending_regime_exit = {
                                    'reason': reason,
                                    'unfavorable_regime': new_regime,
                                    'position_type': self.current_position['type']
                                }
                                self.logger.info(f"⚠️ REGIME CHANGE EXIT BLOCKED: {reason}")
                                self.logger.info(f"   Candle confirmation: {candle_reason}")
                                self.logger.info(f"   Will monitor for candle confirmation in subsequent loops")
                        else:
                            # Fallback to immediate close if candle data unavailable
                            self.logger.info(f"🔄 REGIME CHANGE EXIT (FALLBACK): {reason} [No candle data]")
                            self.close_current_position(f"Regime Change - {reason}")
                    except Exception as e:
                        self.logger.error(f"❌ Error in regime change candle confirmation: {e}")
                        # Fallback to immediate close
                        self.logger.info(f"🔄 REGIME CHANGE EXIT (FALLBACK): {reason}")
                        self.close_current_position(f"Regime Change - {reason}")
                elif should_remove_tp:
                    # Remove take profit to use trailing stops in trending environment
                    self.remove_take_profit(f"Regime Change - {self.current_regime}→{new_regime}")

            self.current_regime = new_regime
            self.last_regime_change = datetime.now()
            return True

        return False

    def log_enhanced_regime_analysis(self, regime_details, candle_strength, qqe_analysis=None, features_df=None, atr_value=None):
        """ENHANCED: Log detailed regime analysis with specific reasoning and QQE data"""
        self.logger.info("📊 DETAILED REGIME ANALYSIS:")
        self.logger.info(f"   ATR Percentile: {regime_details.get('atr_percentile', 0):.1%}")
        self.logger.info(f"   Fast EMA Slope: {regime_details.get('fast_ema_slope_abs', 0)*1000:.2f} (abs)")
        self.logger.info(f"   Slow EMA Slope: {regime_details.get('slow_ema_slope_abs', 0)*1000:.2f} (abs)")
        self.logger.info(f"   RSI: {regime_details.get('rsi', 50):.1f}")
        self.logger.info(f"   Momentum Score: {regime_details.get('momentum_score', 0):+d}/3")
        self.logger.info(f"   BB Width Percentile: {regime_details.get('bb_width_percentile', 0):.1%}")
        self.logger.info(f"   Trending Score: {regime_details.get('trending_score', 0):.1f}")
        self.logger.info(f"   Ranging Score: {regime_details.get('ranging_score', 0):.1f}")
        self.logger.info(f"   Score Difference: {regime_details.get('score_diff', 0):.1f}")

        # NEW: Log detailed reasoning for regime classification
        regime_reason = regime_details.get('regime_reason', 'No reason available')
        self.logger.info(f"🎯 REGIME DECISION: {regime_reason}")

        reasoning = regime_details.get('reasoning', [])
        if reasoning:
            self.logger.info("🔍 DETAILED SCORING BREAKDOWN:")
            for reason in reasoning:
                self.logger.info(f"   • {reason}")

        self.logger.info("🕯️ CANDLE STRENGTH ANALYSIS:")
        self.logger.info(f"   Dominant Bias: {candle_strength['dominant_bias']}")
        self.logger.info(f"   Net Strength: {candle_strength['net_strength']:+.2f}")
        self.logger.info(f"   Bullish Strength: {candle_strength['bullish_strength']:.1%}")
        self.logger.info(f"   Bearish Strength: {candle_strength['bearish_strength']:.1%}")
        self.logger.info(f"   Candles Analyzed: {candle_strength['candles_analyzed']}")

        # NEW: Display acceleration data (Phase 1: Monitoring)
        if candle_strength.get('acceleration_available', False):
            self.logger.info("⚡ ACCELERATION ANALYSIS (BASED ON CLOSED CANDLES ONLY):")
            self.logger.info(f"   Bull Velocity: {candle_strength['bull_velocity']:+.2f}%")
            self.logger.info(f"   Bear Velocity: {candle_strength['bear_velocity']:+.2f}%")
            self.logger.info(f"   Bull Acceleration: {candle_strength['bull_acceleration']:+.2f}%")
            self.logger.info(f"   Bear Acceleration: {candle_strength['bear_acceleration']:+.2f}%")

            # FIXED: Interpret acceleration based on velocity (momentum direction) first
            bull_accel = candle_strength['bull_acceleration']
            bear_accel = candle_strength['bear_acceleration']
            bull_velocity = candle_strength['bull_velocity']
            bear_velocity = candle_strength['bear_velocity']

            # Determine dominant momentum direction from velocity SIGN (not magnitude)
            if bull_velocity > 0:
                # Bullish momentum (positive bull velocity)
                if bull_accel > 5:
                    accel_interpretation = "🚀 BULLISH ACCELERATION"
                elif bull_accel < -5:
                    accel_interpretation = "🛑 BULLISH DECELERATION"
                else:
                    accel_interpretation = "➡️ BULLISH MOMENTUM STABLE"
            else:
                # Bearish momentum (negative bull velocity = positive bear velocity)
                if bear_accel > 5:
                    accel_interpretation = "🚀 BEARISH ACCELERATION"
                elif bear_accel < -5:
                    accel_interpretation = "🛑 BEARISH DECELERATION"
                else:
                    accel_interpretation = "➡️ BEARISH MOMENTUM STABLE"

            self.logger.info(f"   Interpretation: {accel_interpretation}")
        else:
            self.logger.info("⚡ ACCELERATION ANALYSIS: Insufficient data (need 3+ periods)")

        # NEW: QQE Analysis Logging
        if qqe_analysis:
            self.logger.info("📈 QQE INDICATOR ANALYSIS:")
            self.logger.info(f"   RSI: {qqe_analysis.get('rsi', 0):.1f}")
            self.logger.info(f"   RSI MA: {qqe_analysis.get('rsi_ma', 0):.1f}")
            self.logger.info(f"   Fast ATR RSI TL: {qqe_analysis.get('fast_atr_rsi_tl', 0):.1f}")
            self.logger.info(f"   QQE Trend: {int(qqe_analysis.get('trend', 0)):+d}")
            self.logger.info(f"   QQE Signal: {int(qqe_analysis.get('qqe_signal', 0)):+d}")
            self.logger.info(f"   QQE Trend Signal: {int(qqe_analysis.get('qqe_trend_signal', 0)):+d}")
            self.logger.info(f"   QQE Signal Strength: {qqe_analysis.get('qqe_signal_strength', 0):.3f}")
            self.logger.info(f"   QQE Long Count: {qqe_analysis.get('qqe_long_count', 0)}")
            self.logger.info(f"   QQE Short Count: {qqe_analysis.get('qqe_short_count', 0)}")
            self.logger.info(f"   Last QQE Signal Type: {qqe_analysis.get('last_signal_type', 'NONE')}")
        else:
            self.logger.info("📈 QQE INDICATOR ANALYSIS: Not available")

        # Simple Regression Channel Analysis Logging
        if hasattr(self, 'simple_regression'):
            try:
                channel_status = self.simple_regression.get_status_summary()
                self.logger.info("📊 SIMPLE REGRESSION CHANNEL:")
                self.logger.info(f"   State: {channel_status['state']}")
                self.logger.info(f"   Regime Contribution: {channel_status['regime_contribution']:+.1f}")

                if channel_status['has_active_channel']:
                    self.logger.info(f"   Channel Quality: R² = {channel_status.get('channel_r_squared', 0):.3f}")
                    self.logger.info(f"   Channel Candles: {channel_status.get('channel_candles', 0)}")
                    self.logger.info(f"   Channel Width: {channel_status.get('channel_width', 0):.5f}")
                    self.logger.info(f"   Channel Slope: {channel_status.get('channel_slope', 0):+.6f}")

                    if features_df is not None and len(features_df) > 0:
                        current_price = features_df.iloc[-1]['close']
                        position = self.simple_regression.get_channel_position(current_price)
                        if position is not None:
                            self.logger.info(f"   Current Position: {position:.3f} (0=bottom, 1=top)")
                else:
                    self.logger.info("   Status: Building new channel")

            except Exception as e:
                self.logger.error(f"❌ Error in simple channel logging: {e}")
        else:
            self.logger.info("📊 SIMPLE REGRESSION CHANNEL: Not available")

        # NEW: Swing Point Analysis Logging
        if features_df is not None and atr_value is not None:
            try:
                swing_points = self.find_recent_swing_points(features_df)
                if swing_points.get('swing_points_available', False):
                    self.logger.info("🎯 SWING POINT ANALYSIS:")

                    recent_high = swing_points.get('recent_high')
                    recent_low = swing_points.get('recent_low')
                    recent_high_distance = swing_points.get('recent_high_distance')
                    recent_low_distance = swing_points.get('recent_low_distance')
                    high_candles_ago = swing_points.get('recent_high_candles_ago')
                    low_candles_ago = swing_points.get('recent_low_candles_ago')
                    search_depth = swing_points.get('search_depth', 0)

                    if recent_high is not None:
                        distance_atr = recent_high_distance / atr_value if atr_value > 0 else 0
                        self.logger.info(f"   Recent High: {recent_high:.5f} ({high_candles_ago} candles ago)")
                        self.logger.info(f"   High Distance: {recent_high_distance:.5f} ({distance_atr:.2f} ATR)")

                    if recent_low is not None:
                        distance_atr = recent_low_distance / atr_value if atr_value > 0 else 0
                        self.logger.info(f"   Recent Low: {recent_low:.5f} ({low_candles_ago} candles ago)")
                        self.logger.info(f"   Low Distance: {recent_low_distance:.5f} ({distance_atr:.2f} ATR)")

                    max_distance = atr_value * self.swing_distance_atr_threshold
                    self.logger.info(f"   Max Distance Allowed: {max_distance:.5f} ({self.swing_distance_atr_threshold} ATR)")
                    self.logger.info(f"   Search Depth: {search_depth} candles")
                else:
                    search_depth = swing_points.get('search_depth', 0)
                    self.logger.info(f"🎯 SWING POINT ANALYSIS: No swing points found (searched {search_depth} candles)")
            except Exception as e:
                self.logger.error(f"❌ Error in swing point logging: {e}")
        else:
            self.logger.info("🎯 SWING POINT ANALYSIS: Data not available")

    def calculate_position_size(self, balance, current_price, atr_value):
        """Calculate position size for 4% risk with 1 ATR stop loss - Multi-symbol support"""
        try:
            # 4% of account balance at risk
            risk_amount = balance * (self.risk_percent / 100)

            # Stop loss distance in price points
            stop_loss_distance = atr_value * 1.0

            # Get symbol-specific contract size
            contract_size = self.SYMBOL_SPECS[self.symbol]["contract_size"]
            symbol_name = self.SYMBOL_SPECS[self.symbol]["name"]

            # Risk per lot = stop_loss_distance * contract_size
            risk_per_lot = stop_loss_distance * contract_size

            # Calculate lot size: Risk Amount / Risk per Lot
            lot_size = risk_amount / risk_per_lot

            # Round to 2 decimal places and apply limits
            lot_size = round(lot_size, 2)
            lot_size = max(0.01, min(lot_size, 10.0))

            self.logger.info(f"💰 Position Sizing ({symbol_name} - {self.symbol}):")
            self.logger.info(f"   Balance: ${balance:.2f}")
            self.logger.info(f"   Risk Amount (4%): ${risk_amount:.2f}")
            self.logger.info(f"   Stop Distance: {stop_loss_distance:.5f}")
            self.logger.info(f"   Contract Size: {contract_size}")
            self.logger.info(f"   Risk per Lot: ${risk_per_lot:.2f}")
            self.logger.info(f"   Calculated Lot Size: {lot_size}")

            return lot_size

        except Exception as e:
            self.logger.error(f"❌ Error calculating position size: {e}")
            return 0.01

    def update_trailing_stop(self, current_price, atr_value):
        """Update trailing stop loss every 1 ATR in profit"""
        try:
            if not self.current_position or not self.trailing_stop_data:
                return False

            position_type = self.current_position['type']
            entry_price = self.current_position['price']
            current_sl = self.trailing_stop_data['current_sl']

            # Calculate profit in ATR units
            if position_type == 'BUY':
                profit_points = current_price - entry_price
                profit_atr = profit_points / atr_value

                # Check if we've gained another ATR in profit
                if profit_atr >= (self.trailing_stop_data['profit_atr_count'] + 1):
                    # Move stop loss up by 1 ATR
                    new_sl = current_sl + atr_value
                    self.trailing_stop_data['profit_atr_count'] += 1
                    self.trailing_stop_data['current_sl'] = new_sl

                    self.logger.info(f"🔄 TRAILING STOP UPDATE (BUY):")
                    self.logger.info(f"   Profit: {profit_atr:.2f} ATR")
                    self.logger.info(f"   Old SL: {current_sl:.5f}")
                    self.logger.info(f"   New SL: {new_sl:.5f} (+1 ATR)")

                    # Update stop loss in MT5
                    success = self.mt5_manager.modify_position(
                        ticket=self.current_position['ticket'],
                        stop_loss=new_sl
                    )

                    if success:
                        # NEW: Close 1/3 of remaining position when SL gets trailed
                        partial_success, partial_msg = self.close_partial_position(close_fraction=1/3)
                        if partial_success:
                            self.logger.info(f"💰 PROFIT TAKING: {partial_msg}")
                        else:
                            self.logger.warning(f"⚠️ Partial close failed: {partial_msg}")

                        # Log trailing stop update
                        if 'trade_id' in self.current_position:
                            self.trade_logger.update_trailing_stop(self.current_position['trade_id'])

                    return success

            else:  # SELL position
                profit_points = entry_price - current_price
                profit_atr = profit_points / atr_value

                # Check if we've gained another ATR in profit
                if profit_atr >= (self.trailing_stop_data['profit_atr_count'] + 1):
                    # Move stop loss down by 1 ATR
                    new_sl = current_sl - atr_value
                    self.trailing_stop_data['profit_atr_count'] += 1
                    self.trailing_stop_data['current_sl'] = new_sl

                    self.logger.info(f"🔄 TRAILING STOP UPDATE (SELL):")
                    self.logger.info(f"   Profit: {profit_atr:.2f} ATR")
                    self.logger.info(f"   Old SL: {current_sl:.5f}")
                    self.logger.info(f"   New SL: {new_sl:.5f} (-1 ATR)")

                    # Update stop loss in MT5
                    success = self.mt5_manager.modify_position(
                        ticket=self.current_position['ticket'],
                        stop_loss=new_sl
                    )

                    if success:
                        # NEW: Close 1/3 of remaining position when SL gets trailed
                        partial_success, partial_msg = self.close_partial_position(close_fraction=1/3)
                        if partial_success:
                            self.logger.info(f"💰 PROFIT TAKING: {partial_msg}")
                        else:
                            self.logger.warning(f"⚠️ Partial close failed: {partial_msg}")

                        # Log trailing stop update
                        if 'trade_id' in self.current_position:
                            self.trade_logger.update_trailing_stop(self.current_position['trade_id'])

                    return success

            return False

        except Exception as e:
            self.logger.error(f"❌ Error updating trailing stop: {e}")
            return False

    def close_partial_position(self, close_fraction=1/3):
        """Close a fraction of the current position with MT5-valid volume"""
        try:
            if not self.current_position:
                return False, "No position to close"

            # Get current position details from MT5
            positions = self.mt5_manager.get_positions(self.symbol)
            if not positions:
                return False, "Position not found in MT5"

            current_pos = positions[0]
            current_volume = current_pos['volume']

            # Calculate volume to close (1/3 of remaining)
            volume_to_close = current_volume * close_fraction

            # Get symbol info for volume step and minimum volume
            symbol_info = self.mt5_manager.symbol_info
            if not symbol_info:
                return False, "Cannot get symbol info"

            volume_step = symbol_info.volume_step
            volume_min = symbol_info.volume_min

            # Round to valid MT5 volume (must be multiple of volume_step)
            volume_to_close = round(volume_to_close / volume_step) * volume_step

            # Ensure minimum volume requirements
            if volume_to_close < volume_min:
                return False, f"Volume too small: {volume_to_close} < {volume_min}"

            # Ensure we don't close more than available
            if volume_to_close >= current_volume:
                volume_to_close = current_volume  # Close entire position

            self.logger.info(f"💰 PARTIAL CLOSE: Closing {volume_to_close:.2f} of {current_volume:.2f} lots ({close_fraction:.1%})")

            # Close partial position
            success = self.mt5_manager.close_position(
                ticket=self.current_position['ticket'],
                symbol=self.symbol,
                volume=volume_to_close
            )

            if success:
                # Update remaining volume tracking
                remaining_volume = current_volume - volume_to_close
                self.current_position['remaining_volume'] = remaining_volume

                self.logger.info(f"✅ Partial close successful. Remaining: {remaining_volume:.2f} lots")

                # If position is fully closed, clear tracking
                if remaining_volume <= volume_min:
                    self.logger.info("🔚 Position fully closed")
                    self.current_position = None
                    self.trailing_stop_data = None

                return True, f"Closed {volume_to_close:.2f} lots"
            else:
                return False, "MT5 close failed"

        except Exception as e:
            self.logger.error(f"❌ Error closing partial position: {e}")
            return False, str(e)

    def check_current_positions(self):
        """Check if we have any open positions"""
        try:
            positions = self.mt5_manager.get_positions(self.symbol)
            if positions and len(positions) > 0:
                # We have an open position
                pos = positions[0]  # Get first position
                self.current_position = {
                    'type': 'BUY' if pos['type'] == 0 else 'SELL',
                    'ticket': pos['ticket'],
                    'time': datetime.fromtimestamp(pos['time']),
                    'volume': pos['volume'],
                    'remaining_volume': pos['volume'],  # Track remaining volume for partial closes
                    'price': pos['price_open']
                }
                return True
            else:
                self.current_position = None
                return False
        except Exception as e:
            self.logger.error(f"❌ Error checking positions: {e}")
            return False

    def close_current_position(self, reason="Opposite Signal"):
        """Close the current position"""
        try:
            if not self.current_position:
                return True

            self.logger.info(f"🔄 Closing current {self.current_position['type']} position - {reason}")

            # Close position
            result = self.mt5_manager.close_position(
                ticket=self.current_position['ticket'],
                symbol=self.symbol,
                volume=self.current_position['volume']
            )

            if result:
                self.logger.info("✅ Position closed successfully!")

                # LOG TRADE RESULT
                if self.current_position and 'trade_id' in self.current_position:
                    # Get current price for exit price
                    tick_info = self.mt5_manager.get_symbol_info_tick(self.symbol)
                    if tick_info:
                        exit_price = tick_info['bid'] if self.current_position['type'] == 'SELL' else tick_info['ask']

                        # Calculate max excursion
                        max_excursion = self.trade_logger.calculate_max_excursion(
                            ticket=self.current_position['ticket'],
                            entry_price=self.current_position['price'],
                            direction=self.current_position['type'],
                            entry_time=self.current_position['time'],
                            exit_time=datetime.now()
                        )

                        result_data = {
                            'exit_price': exit_price,
                            'exit_reason': reason,
                            'max_favorable_pips': max_excursion['max_favorable_pips'],
                            'max_adverse_pips': max_excursion['max_adverse_pips']
                        }

                        self.trade_logger.log_trade_result(self.current_position['trade_id'], result_data)

                self.current_position = None
                self.trailing_stop_data = None  # Clear trailing stop data
                return True
            else:
                self.logger.error("❌ Failed to close position")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error closing position: {e}")
            return False

    def remove_take_profit(self, reason="Regime Change"):
        """Remove take profit from current position to use trailing stops"""
        try:
            if not self.current_position:
                return True

            self.logger.info(f"🎯 Removing Take Profit from {self.current_position['type']} position - {reason}")

            # Modify position to remove take profit (set to 0)
            success = self.mt5_manager.modify_position(
                ticket=self.current_position['ticket'],
                take_profit=0.0  # Remove take profit
            )

            if success:
                self.logger.info(f"✅ Take Profit removed - Now using trailing stops only")
                return True
            else:
                self.logger.error(f"❌ Failed to remove Take Profit")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error removing take profit: {e}")
            return False

    def execute_trade(self, signal, confidence, balance, atr_value, regime, logic):
        """Execute trade with regime-based single position management"""
        try:
            if signal is None:
                self.logger.info(f"⚠️ No signal - {logic}")
                return False

            if confidence < self.min_confidence:
                self.logger.info(f"⚠️ Low confidence ({confidence:.3f}) - Skipping trade")
                return False

            # OVERTRADING PROTECTION: Minimum 30 seconds between trades
            current_time = datetime.now()
            if self.last_trade_time and (current_time - self.last_trade_time).seconds < 30:
                self.logger.info(f"⚠️ Trade too soon - Last trade {(current_time - self.last_trade_time).seconds}s ago (min 30s)")
                return False

            if atr_value is None or atr_value <= 0:
                self.logger.error(f"❌ Invalid ATR value: {atr_value}")
                return False

            # Check for current positions
            has_position = self.check_current_positions()

            # PRIORITY: Check for sensitive closing conditions FIRST (regardless of new signal)
            if has_position and self.current_position:
                current_type = self.current_position['type']

                # Use the candle strength from the main prediction (avoid recalculating)
                try:
                    # Get the candle strength from the main analysis
                    result = self.get_live_prediction()
                    if result and len(result) >= 10:
                        _, _, _, _, _, _, _, candle_strength_data, _, _ = result
                        candle_net_strength = candle_strength_data['net_strength'] * 100

                        # SENSITIVE CLOSING LOGIC (PRIORITY - works regardless of signal)
                        should_close = False
                        close_reason = ""

                        # DISABLED: Sensitive closing logic (candle strength sign change exits)
                        # This logic would close SELL when strength > 0 and BUY when strength < 0
                        # Currently disabled per user request

                        # Placeholder for future sensitive exit logic
                        should_close = False
                        close_reason = ""

                        # NEW: Phase 2 - Acceleration-based early exits
                        if not should_close and candle_strength_data.get('acceleration_available', False):
                            bull_accel = candle_strength_data.get('bull_acceleration', 0)
                            bear_accel = candle_strength_data.get('bear_acceleration', 0)
                            bull_velocity = candle_strength_data.get('bull_velocity', 0)
                            bear_velocity = candle_strength_data.get('bear_velocity', 0)

                            self.logger.info(f"🔍 ACCELERATION EXIT CHECK: {current_type} position")
                            self.logger.info(f"   Bull Velocity: {bull_velocity:+.1f}% | Bear Velocity: {bear_velocity:+.1f}%")
                            self.logger.info(f"   Bull Accel: {bull_accel:+.1f}% | Bear Accel: {bear_accel:+.1f}%")

                            # Check if position is profitable before allowing velocity/acceleration exits
                            is_profitable, profit_points, profit_info = self.is_position_profitable()
                            self.logger.info(f"💰 PROFIT CHECK: {profit_info} | Profitable: {is_profitable} | Points: {profit_points:+.5f}")

                            if not is_profitable:
                                self.logger.info(f"⚠️ VELOCITY/ACCELERATION EXITS BLOCKED: Position not in profit")
                                self.logger.info(f"   Bull Velocity: {bull_velocity:+.1f}% | Bear Velocity: {bear_velocity:+.1f}%")
                                self.logger.info(f"   Bull Accel: {bull_accel:+.1f}% | Bear Accel: {bear_accel:+.1f}%")
                                self.logger.info(f"   Will only exit profitable positions to protect profits")
                            else:
                                # ENHANCED: Phase 2.5 - Momentum direction change exits (ONLY IN PROFIT)
                                if current_type == 'BUY' and bull_velocity < 0:
                                    # Check if new signal aligns with current position before closing
                                    signal_aligns = signal and signal == 'BUY'
                                    if signal_aligns:
                                        self.logger.info(f"⚠️ VELOCITY EXIT BLOCKED: New {signal} signal aligns with current {current_type} position")
                                        self.logger.info(f"   Bull velocity: {bull_velocity:+.1f}% < 0% (would trigger exit but signal reinforces position)")
                                    else:
                                        should_close = True
                                        close_reason = f"BUY Close: Momentum turned bearish (bull velocity {bull_velocity:+.1f}% < 0%) [IN PROFIT: {profit_points:+.5f}]"
                                        self.logger.info(f"🚨 MOMENTUM DIRECTION EXIT: {close_reason}")
                                elif current_type == 'SELL' and bear_velocity < 0:
                                    # Check if new signal aligns with current position before closing
                                    signal_aligns = signal and signal == 'SELL'
                                    if signal_aligns:
                                        self.logger.info(f"⚠️ VELOCITY EXIT BLOCKED: New {signal} signal aligns with current {current_type} position")
                                        self.logger.info(f"   Bear velocity: {bear_velocity:+.1f}% < 0% (would trigger exit but signal reinforces position)")
                                    else:
                                        should_close = True
                                        close_reason = f"SELL Close: Momentum turned bullish (bear velocity {bear_velocity:+.1f}% < 0%) [IN PROFIT: {profit_points:+.5f}]"
                                        self.logger.info(f"🚨 MOMENTUM DIRECTION EXIT: {close_reason}")
                                # Original Phase 2 - Acceleration threshold exits (ONLY IN PROFIT)
                                elif current_type == 'BUY' and bull_accel < -10:
                                    # Check if new signal aligns with current position before closing
                                    signal_aligns = signal and signal == 'BUY'
                                    if signal_aligns:
                                        self.logger.info(f"⚠️ ACCELERATION EXIT BLOCKED: New {signal} signal aligns with current {current_type} position")
                                        self.logger.info(f"   Bull acceleration: {bull_accel:+.1f}% < -10% (would trigger exit but signal reinforces position)")
                                    else:
                                        should_close = True
                                        close_reason = f"BUY Close: Bull acceleration deceleration ({bull_accel:+.1f}% < -10%) [IN PROFIT: {profit_points:+.5f}]"
                                        self.logger.info(f"🚨 ACCELERATION THRESHOLD EXIT: {close_reason}")
                                elif current_type == 'SELL' and bear_accel < -10:
                                    # Check if new signal aligns with current position before closing
                                    signal_aligns = signal and signal == 'SELL'
                                    if signal_aligns:
                                        self.logger.info(f"⚠️ ACCELERATION EXIT BLOCKED: New {signal} signal aligns with current {current_type} position")
                                        self.logger.info(f"   Bear acceleration: {bear_accel:+.1f}% < -10% (would trigger exit but signal reinforces position)")
                                    else:
                                        should_close = True
                                        close_reason = f"SELL Close: Bear acceleration deceleration ({bear_accel:+.1f}% < -10%) [IN PROFIT: {profit_points:+.5f}]"
                                        self.logger.info(f"🚨 ACCELERATION THRESHOLD EXIT: {close_reason}")
                                else:
                                    self.logger.info(f"   No acceleration exit: All thresholds safe (position in profit)")
                        elif not should_close:
                            accel_available = candle_strength_data.get('acceleration_available', False)
                            self.logger.info(f"🔍 ACCELERATION EXIT SKIPPED: acceleration_available={accel_available}")

                        if should_close:
                            self.logger.info(f"🔄 SENSITIVE CLOSING: {close_reason}")
                            if not self.close_current_position("Sensitive Candle Strength"):
                                return False
                            # Position closed - can now potentially open new position
                            has_position = False  # Update status
                        else:
                            # No sensitive closing needed - check for same/opposite signals
                            if signal and current_type == signal:
                                self.logger.info(f"⚠️ Same signal ({signal}) - Already have {current_type} position")
                                return False
                            elif signal and ((current_type == 'BUY' and signal == 'SELL') or (current_type == 'SELL' and signal == 'BUY')):
                                # Traditional opposite signal (strong signal >30% or <-30%)
                                self.logger.info(f"🔄 Strong opposite signal detected: Current={current_type}, New={signal}")
                                if not self.close_current_position("Strong Opposite Signal"):
                                    return False
                                # Position closed - can now open new position
                                has_position = False  # Update status
                            elif signal is None:
                                # No new signal and no sensitive closing - keep position
                                return False
                    else:
                        # Fallback to traditional logic if can't get candle strength
                        if signal and ((current_type == 'BUY' and signal == 'SELL') or (current_type == 'SELL' and signal == 'BUY')):
                            self.logger.info(f"🔄 Opposite signal detected: Current={current_type}, New={signal}")
                            if not self.close_current_position("Opposite Signal"):
                                return False
                            has_position = False  # Update status
                        elif signal and current_type == signal:
                            self.logger.info(f"⚠️ Same signal ({signal}) - Already have {current_type} position")
                            return False
                        elif signal is None:
                            return False

                except Exception as e:
                    self.logger.error(f"❌ Error in sensitive closing logic: {e}")
                    # Fallback to traditional logic
                    if signal and ((current_type == 'BUY' and signal == 'SELL') or (current_type == 'SELL' and signal == 'BUY')):
                        self.logger.info(f"🔄 Opposite signal detected: Current={current_type}, New={signal}")
                        if not self.close_current_position("Opposite Signal"):
                            return False
                        has_position = False  # Update status
                    elif signal and current_type == signal:
                        self.logger.info(f"⚠️ Same signal ({signal}) - Already have {current_type} position")
                        return False
                    elif signal is None:
                        return False
            elif has_position:
                self.logger.info(f"⚠️ Already have open position - Only 1 concurrent trade allowed")
                return False

            # Get current price
            tick = self.mt5_manager.get_symbol_info_tick(self.symbol)
            if not tick:
                self.logger.error("❌ Cannot get current price")
                return False

            # Calculate position size using ATR
            current_price = tick['ask'] if signal == "BUY" else tick['bid']
            lot_size = self.calculate_position_size(balance, current_price, atr_value)

            # Set stop loss and take profit based on regime
            if signal == "BUY":
                price = tick['ask']
                stop_loss = price - (atr_value * 1.5)  # 1.5 ATR stop loss (more conservative)
                # NEW: Set 1:0.5 take profit for RANGING markets
                if regime == "RANGING":
                    take_profit = price + (atr_value * 0.75)  # 1:0.5 risk-reward (0.75 ATR TP)
                else:
                    take_profit = None  # No take profit for TRENDING/TRANSITIONAL
            else:
                price = tick['bid']
                stop_loss = price + (atr_value * 1.5)  # 1.5 ATR stop loss (more conservative)
                # NEW: Set 1:0.5 take profit for RANGING markets
                if regime == "RANGING":
                    take_profit = price - (atr_value * 0.75)  # 1:0.5 risk-reward (0.75 ATR TP)
                else:
                    take_profit = None  # No take profit for TRENDING/TRANSITIONAL

            # Execute order
            self.logger.info(f"📈 REGIME-BASED TRADE:")
            self.logger.info(f"   Signal: {signal} ({logic})")
            self.logger.info(f"   Regime: {regime}")
            self.logger.info(f"   Confidence: {confidence:.3f}")
            self.logger.info(f"   Price: {price:.5f}")
            self.logger.info(f"   Lot Size: {lot_size}")
            self.logger.info(f"   Stop Loss: {stop_loss:.5f} (1.5 ATR)")
            self.logger.info(f"   ATR: {atr_value:.5f}")
            if take_profit:
                self.logger.info(f"   Take Profit: {take_profit:.5f} (1:0.5 R:R - RANGING)")
            else:
                self.logger.info(f"   Take Profit: None (TRENDING/TRANSITIONAL)")

            # Place order with regime-based take profit
            result = self.mt5_manager.place_order(
                symbol=self.symbol,
                order_type=signal,
                lot_size=lot_size,
                price=price,
                stop_loss=stop_loss,
                take_profit=take_profit,  # Set TP for RANGING, None for others
                comment=f"Fixed_Model_Conf_{confidence:.3f}"
            )

            if result:
                self.logger.info("✅ Order placed successfully!")
                # Update current position tracking
                self.current_position = {
                    'type': signal,
                    'ticket': result,  # Assuming place_order returns ticket
                    'time': datetime.now(),
                    'volume': lot_size,
                    'remaining_volume': lot_size,  # Track remaining volume for partial closes
                    'price': price
                }

                # Initialize trailing stop data
                self.trailing_stop_data = {
                    'initial_sl': stop_loss,
                    'current_sl': stop_loss,
                    'atr_value': atr_value,
                    'profit_atr_count': 0
                }

                self.last_signal = signal
                self.last_trade_time = datetime.now()  # Update for overtrading protection

                # LOG TRADE EXECUTION
                trade_data = {
                    'ticket': result,
                    'symbol': self.symbol,
                    'direction': signal,
                    'volume': lot_size,
                    'entry_price': price,
                    'stop_loss': stop_loss,
                    'regime': regime,
                    'regime_confidence': self.regime_confidence,
                    'trend_direction': getattr(self, 'current_trend_direction', 'UNKNOWN'),
                    'ml_signal': getattr(self, 'current_ml_signal', signal),
                    'ml_confidence': confidence,
                    'final_decision': signal,
                    'logic': logic,
                    'atr_value': atr_value
                }

                trade_id = self.trade_logger.log_trade_execution(trade_data)
                if trade_id:
                    self.current_position['trade_id'] = trade_id

                self.logger.info(f"🎯 TRAILING STOP INITIALIZED:")
                self.logger.info(f"   Initial SL: {stop_loss:.5f}")
                self.logger.info(f"   ATR Value: {atr_value:.5f}")
                self.logger.info(f"   Will trail every 1 ATR in profit")
                return True
            else:
                self.logger.error("❌ Failed to place order")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error executing trade: {e}")
            return False
    
    def trading_loop(self):
        """Main trading loop with fixed model"""
        self.logger.info("🚀 Starting fixed live trading loop (300-second intervals)...")

        last_prediction_time = None

        while self.is_running:
            try:
                current_time = datetime.now()

                # Make predictions every 300 seconds (5 minutes) for midterm approach
                if last_prediction_time is None or (current_time - last_prediction_time).seconds >= 300:

                    # Increment iteration counter
                    self.iteration_count += 1

                    # Get account info
                    account_info = self.mt5_manager.get_account_info()
                    if not account_info:
                        self.logger.error("❌ Cannot get account info")
                        time.sleep(60)
                        continue

                    balance = account_info['balance']

                    # Check current position status
                    has_position = self.check_current_positions()
                    position_info = ""
                    if has_position and self.current_position:
                        position_info = f" | Current: {self.current_position['type']} @ {self.current_position['price']:.2f}"

                    # Get prediction with regime detection
                    signal, confidence, details, atr_value, regime, logic, regime_details, candle_strength, qqe_analysis, features_df = self.get_live_prediction()

                    # PRIORITY: Check for position closing BEFORE processing new signals
                    if has_position and self.current_position and candle_strength:
                        current_type = self.current_position['type']
                        candle_net_strength = candle_strength['net_strength'] * 100

                        # Check for sensitive closing and acceleration exits
                        should_close = False
                        close_reason = ""

                        # DISABLED: Sensitive closing logic (candle strength sign change exits)
                        # This logic would close SELL when strength > 0 and BUY when strength < 0
                        # Currently disabled per user request

                        # Skip sensitive closing - go directly to other exit checks
                        should_close = False
                        close_reason = ""

                        # Acceleration-based exits
                        if not should_close and candle_strength.get('acceleration_available', False):
                            bull_velocity = candle_strength.get('bull_velocity', 0)
                            bear_velocity = candle_strength.get('bear_velocity', 0)
                            bull_accel = candle_strength.get('bull_acceleration', 0)
                            bear_accel = candle_strength.get('bear_acceleration', 0)

                            self.logger.info(f"🔍 ACCELERATION EXIT CHECK: {current_type} position")
                            self.logger.info(f"   Bull Velocity: {bull_velocity:+.1f}% | Bear Velocity: {bear_velocity:+.1f}%")
                            self.logger.info(f"   Bull Accel: {bull_accel:+.1f}% | Bear Accel: {bear_accel:+.1f}%")

                            # Check if position is profitable before allowing velocity/acceleration exits
                            is_profitable, profit_points, profit_info = self.is_position_profitable()
                            self.logger.info(f"💰 PROFIT CHECK: {profit_info} | Profitable: {is_profitable} | Points: {profit_points:+.5f}")

                            if not is_profitable:
                                self.logger.info(f"⚠️ VELOCITY/ACCELERATION EXITS BLOCKED: Position not in profit")
                                self.logger.info(f"   Will only exit profitable positions to protect profits")
                                # Skip all velocity/acceleration exit logic
                                velocity_exit_triggered = False
                            else:
                                # ENHANCED Phase 2.5 - Momentum direction change exits with candle confirmation (ONLY IN PROFIT)
                                velocity_exit_triggered = False
                            candle_confirmation = False

                            # Get current candle data for confirmation
                            try:
                                # Get latest data for candle confirmation
                                latest_data = self.get_latest_data_safe()
                                if latest_data is not None and len(latest_data) >= 2:  # Need at least 2 candles to get closed candle
                                    # Use LAST CLOSED candle (index -2), not current forming candle (index -1)
                                    closed_candle = latest_data.iloc[-2]
                                    candle_high = closed_candle['high']
                                    candle_low = closed_candle['low']
                                    candle_close = closed_candle['close']
                                    candle_range = candle_high - candle_low

                                    if candle_range > 0:
                                        close_position = (candle_close - candle_low) / candle_range

                                        # Check velocity exit conditions with candle confirmation (ONLY IN PROFIT)
                                        if current_type == 'BUY' and bull_velocity < 0:
                                            velocity_exit_triggered = True
                                            candle_confirmation = close_position <= 0.40  # Below 40% for BUY exits
                                            self.logger.info(f"🔍 BUY VELOCITY EXIT: bull_velocity {bull_velocity:+.1f}% < 0 [IN PROFIT: {profit_points:+.5f}]")
                                            self.logger.info(f"   Candle confirmation (LAST CLOSED): Close at {close_position:.1%} of range (need ≤40% to exit)")

                                        elif current_type == 'SELL' and bear_velocity < 0:
                                            velocity_exit_triggered = True
                                            candle_confirmation = close_position >= 0.60  # Above 60% for SELL exits
                                            self.logger.info(f"🔍 SELL VELOCITY EXIT: bear_velocity {bear_velocity:+.1f}% < 0 [IN PROFIT: {profit_points:+.5f}]")
                                            self.logger.info(f"   Candle confirmation (LAST CLOSED): Close at {close_position:.1%} of range (need ≥60% to exit)")

                                        # Only exit if both velocity changed AND candle confirms
                                        if velocity_exit_triggered and candle_confirmation:
                                            should_close = True
                                            if current_type == 'BUY':
                                                close_reason = f"BUY Close: Momentum turned bearish (bull velocity {bull_velocity:+.1f}% < 0%) + Candle confirmation (last closed at {close_position:.1%} ≤ 40%) [PROFIT: {profit_points:+.5f}]"
                                            else:
                                                close_reason = f"SELL Close: Momentum turned bullish (bear velocity {bear_velocity:+.1f}% < 0%) + Candle confirmation (last closed at {close_position:.1%} ≥ 60%) [PROFIT: {profit_points:+.5f}]"
                                            self.logger.info(f"🚨 CONFIRMED MOMENTUM DIRECTION EXIT: {close_reason}")
                                        elif velocity_exit_triggered and not candle_confirmation:
                                            self.logger.info(f"⚠️ VELOCITY EXIT BLOCKED: Momentum changed but last closed candle doesn't confirm (close at {close_position:.1%})")

                            except Exception as e:
                                self.logger.error(f"❌ Error in candle confirmation for velocity exit: {e}")
                                # Fallback to original logic if candle data unavailable
                                if current_type == 'BUY' and bull_velocity < 0:
                                    should_close = True
                                    close_reason = f"BUY Close: Momentum turned bearish (bull velocity {bull_velocity:+.1f}% < 0%) [No candle confirmation]"
                                    self.logger.info(f"🚨 MOMENTUM DIRECTION EXIT (FALLBACK): {close_reason}")
                                elif current_type == 'SELL' and bear_velocity < 0:
                                    should_close = True
                                    close_reason = f"SELL Close: Momentum turned bullish (bear velocity {bear_velocity:+.1f}% < 0%) [No candle confirmation]"
                                    self.logger.info(f"🚨 MOMENTUM DIRECTION EXIT (FALLBACK): {close_reason}")
                            # Phase 2 - Acceleration threshold exits WITH candle confirmation (consistent logic) - ONLY IN PROFIT
                            if not should_close and is_profitable:  # Only check if velocity exit didn't trigger AND position is profitable
                                acceleration_exit_triggered = False
                                accel_candle_confirmation = False

                                # Check acceleration exit conditions (ONLY IN PROFIT)
                                if current_type == 'BUY' and bull_accel < -10:
                                    # Check if new signal aligns with current position before triggering exit
                                    signal_aligns = signal and signal == 'BUY'
                                    if signal_aligns:
                                        self.logger.info(f"⚠️ ACCELERATION EXIT BLOCKED: New {signal} signal aligns with current {current_type} position")
                                        self.logger.info(f"   Bull acceleration: {bull_accel:+.1f}% < -10% (would trigger exit but signal reinforces position)")
                                        acceleration_exit_triggered = False
                                    else:
                                        acceleration_exit_triggered = True
                                        accel_exit_reason = f"BUY Close: Bull acceleration deceleration ({bull_accel:+.1f}% < -10%) [IN PROFIT: {profit_points:+.5f}]"
                                elif current_type == 'SELL' and bear_accel < -10:
                                    # Check if new signal aligns with current position before triggering exit
                                    signal_aligns = signal and signal == 'SELL'
                                    if signal_aligns:
                                        self.logger.info(f"⚠️ ACCELERATION EXIT BLOCKED: New {signal} signal aligns with current {current_type} position")
                                        self.logger.info(f"   Bear acceleration: {bear_accel:+.1f}% < -10% (would trigger exit but signal reinforces position)")
                                        acceleration_exit_triggered = False
                                    else:
                                        acceleration_exit_triggered = True
                                        accel_exit_reason = f"SELL Close: Bear acceleration deceleration ({bear_accel:+.1f}% < -10%) [IN PROFIT: {profit_points:+.5f}]"

                                # Apply candle confirmation to acceleration exits
                                if acceleration_exit_triggered:
                                    try:
                                        latest_data = self.get_latest_data_safe()
                                        if latest_data is not None and len(latest_data) >= 2:  # Need at least 2 candles to get closed candle
                                            # Use LAST CLOSED candle (index -2), not current forming candle (index -1)
                                            closed_candle = latest_data.iloc[-2]
                                            candle_high = closed_candle['high']
                                            candle_low = closed_candle['low']
                                            candle_close = closed_candle['close']
                                            candle_range = candle_high - candle_low

                                            if candle_range > 0:
                                                close_position = (candle_close - candle_low) / candle_range

                                                # Same candle confirmation logic as velocity exits
                                                if current_type == 'BUY' and close_position <= 0.40:
                                                    accel_candle_confirmation = True
                                                elif current_type == 'SELL' and close_position >= 0.60:
                                                    accel_candle_confirmation = True

                                                if accel_candle_confirmation:
                                                    should_close = True
                                                    close_reason = f"{accel_exit_reason} + Candle confirmation (close at {close_position:.1%})"
                                                    self.logger.info(f"🚨 ACCELERATION EXIT CONFIRMED: {close_reason}")
                                                else:
                                                    self.logger.info(f"⚠️ ACCELERATION EXIT BLOCKED: {accel_exit_reason}")
                                                    self.logger.info(f"   Candle confirmation: Close at {close_position:.1%} (BUY needs ≤40%, SELL needs ≥60%)")
                                            else:
                                                # No candle range - fallback to original logic
                                                should_close = True
                                                close_reason = f"{accel_exit_reason} [No candle range - fallback]"
                                                self.logger.info(f"🚨 ACCELERATION EXIT (FALLBACK): {close_reason}")
                                        else:
                                            # No candle data - fallback to original logic
                                            should_close = True
                                            close_reason = f"{accel_exit_reason} [No candle data - fallback]"
                                            self.logger.info(f"🚨 ACCELERATION EXIT (FALLBACK): {close_reason}")
                                    except Exception as e:
                                        self.logger.error(f"❌ Error in acceleration exit candle confirmation: {e}")
                                        # Fallback to original logic
                                        should_close = True
                                        close_reason = f"{accel_exit_reason} [Error - fallback]"
                                        self.logger.info(f"🚨 ACCELERATION EXIT (FALLBACK): {close_reason}")
                                else:
                                    self.logger.info(f"   No acceleration exit: All thresholds safe (position in profit)")
                            elif not should_close and not is_profitable:
                                # Log that acceleration exits are also blocked for unprofitable positions
                                if (current_type == 'BUY' and bull_accel < -10) or (current_type == 'SELL' and bear_accel < -10):
                                    self.logger.info(f"⚠️ ACCELERATION EXIT BLOCKED: Position not in profit")
                                    if current_type == 'BUY':
                                        self.logger.info(f"   Bull acceleration: {bull_accel:+.1f}% < -10% (would trigger exit if profitable)")
                                    else:
                                        self.logger.info(f"   Bear acceleration: {bear_accel:+.1f}% < -10% (would trigger exit if profitable)")
                                else:
                                    self.logger.info(f"   No acceleration exit: All thresholds safe (position not profitable)")

                        # Check for opposite signals
                        if not should_close and signal and ((current_type == 'BUY' and signal == 'SELL') or (current_type == 'SELL' and signal == 'BUY')):
                            should_close = True
                            close_reason = f"Opposite signal: Current={current_type}, New={signal}"
                            self.logger.info(f"🔄 OPPOSITE SIGNAL DETECTED: {close_reason}")

                        # NEW: Check for pending regime change exits
                        if not should_close and self.pending_regime_exit and self.current_position:
                            pending_exit = self.pending_regime_exit

                            # Check if unfavorable regime persists
                            if regime == pending_exit['unfavorable_regime']:
                                # Regime still unfavorable, check candle confirmation
                                try:
                                    latest_data = self.get_latest_data_safe()
                                    if latest_data is not None and len(latest_data) > 0:
                                        candle_confirmed, candle_reason, close_position = self.regime_detector.check_candle_exit_confirmation(
                                            latest_data, pending_exit['position_type']
                                        )

                                        if candle_confirmed:
                                            should_close = True
                                            close_reason = f"PENDING REGIME EXIT CONFIRMED: {pending_exit['reason']}"
                                            self.logger.info(f"🚨 PENDING REGIME EXIT NOW CONFIRMED: {pending_exit['reason']}")
                                            self.logger.info(f"   Candle confirmation: {candle_reason}")
                                            self.pending_regime_exit = None  # Clear pending exit
                                        else:
                                            self.logger.info(f"🔍 PENDING REGIME EXIT: Still waiting for candle confirmation")
                                            self.logger.info(f"   Unfavorable regime persists: {regime}")
                                            self.logger.info(f"   Candle status: {candle_reason}")
                                except Exception as e:
                                    self.logger.error(f"❌ Error checking pending regime exit: {e}")
                            else:
                                # Regime changed back to favorable, clear pending exit
                                self.logger.info(f"✅ PENDING REGIME EXIT CLEARED: Regime changed back to {regime} (favorable)")
                                self.pending_regime_exit = None

                        # NEW: Check if new signal aligns with current position - if so, don't close
                        if should_close and signal and "Opposite signal" not in close_reason:
                            # Check if new signal aligns with current position
                            signal_aligns = (current_type == 'BUY' and signal == 'BUY') or (current_type == 'SELL' and signal == 'SELL')
                            if signal_aligns:
                                should_close = False
                                self.logger.info(f"🔄 KEEPING POSITION: New {signal} signal aligns with current {current_type} position")
                                self.logger.info(f"   Original close reason: {close_reason}")
                                self.logger.info(f"   Decision: Keep position open since new signal confirms direction")

                        # Close position if any condition is met
                        if should_close:
                            self.logger.info(f"🔄 CLOSING POSITION: {close_reason}")
                            if self.close_current_position(close_reason):
                                has_position = False
                                self.current_position = None
                                self.trailing_stop_data = None

                    # Update trailing stop if we still have a position
                    if has_position and self.current_position and atr_value:
                        tick_info = self.mt5_manager.get_symbol_info_tick(self.symbol)
                        if tick_info:
                            current_price = tick_info['bid'] if self.current_position['type'] == 'SELL' else tick_info['ask']
                            self.update_trailing_stop(current_price, atr_value)

                    if signal is not None or regime:  # Show info even if no signal
                        # Check for regime change
                        if regime:
                            regime_changed = self.check_regime_change(regime)

                        self.logger.info(f"🎯 REGIME ANALYSIS: {details}{position_info}")

                        # NEW: Log enhanced analysis every iteration for detailed monitoring
                        if regime_details and candle_strength:
                            self.log_enhanced_regime_analysis(regime_details, candle_strength, qqe_analysis, features_df, atr_value)

                        # Execute trade if signal is valid and confidence is high enough
                        if signal and confidence >= self.min_confidence:
                            self.execute_trade(signal, confidence, balance, atr_value, regime, logic)
                        elif signal is None:
                            self.logger.info(f"⚠️ No trade - {logic}")
                        else:
                            self.logger.info(f"⚠️ Confidence too low ({confidence:.3f} < {self.min_confidence}) - No trade")
                    else:
                        self.logger.warning(f"⚠️ Failed to get prediction: {details}{position_info}")
                    
                    last_prediction_time = current_time
                
                # Sleep for 10 seconds (faster loop for better responsiveness)
                time.sleep(10)
                
            except KeyboardInterrupt:
                self.logger.info("🛑 Trading stopped by user")
                break
            except Exception as e:
                self.logger.error(f"❌ Error in trading loop: {e}")
                time.sleep(60)
    
    def start_trading(self):
        """Start the fixed live trading system"""
        symbol_name = self.SYMBOL_SPECS[self.symbol]["name"]
        self.logger.info(f"🚀 Starting Fixed {symbol_name} ({self.symbol}) Live Trading System...")

        # Load model
        if not self.load_model():
            self.logger.error("❌ Failed to load model")
            return False

        # Connect to MT5
        if not self.mt5_manager.connect():
            self.logger.error("❌ Failed to connect to MT5")
            return False

        account_info = self.mt5_manager.get_account_info()
        if account_info:
            self.logger.info(f"✅ Connected - Account: {account_info['login']}, Balance: ${account_info['balance']:.2f}")

        # Start trading
        self.is_running = True

        try:
            self.trading_loop()
        except KeyboardInterrupt:
            self.logger.info("🛑 Trading stopped by user")
        finally:
            self.is_running = False
            self.mt5_manager.disconnect()
            self.logger.info("✅ Trading system shutdown complete")

        return True
    
    def stop_trading(self):
        """Stop the trading system"""
        self.is_running = False

def main():
    """Main function with multi-symbol support"""
    import sys

    # Default symbol
    symbol = "XAUUSD!"

    # Check for command line argument
    if len(sys.argv) > 1:
        symbol = sys.argv[1].upper()
        # Ensure proper format
        if symbol == "BTCUSD":
            symbol = "BTCUSD"
        elif symbol == "EURUSD":
            symbol = "EURUSD!"
        elif symbol == "XAUUSD":
            symbol = "XAUUSD!"

    # Create trader with specified symbol
    trader = FixedLiveTrader(symbol=symbol)
    symbol_name = trader.SYMBOL_SPECS[symbol]["name"]

    print(f"🚀 TRENDING-ONLY {symbol_name} ({symbol}) Live Trading System - QQE + CANDLE STRENGTH")
    print("=" * 70)
    print("📊 Decision: QQE Indicator with Candle Strength Voting (ML Model DISABLED)")
    print("🎯 PRIMARY: QQE crossover signals with candle strength confirmation")
    print("🎯 SECONDARY: Strong candle strength ±50% when QQE neutral")
    print("🗳️  VOTING: QQE (70%) + Candle Strength (30%) for signal confidence")
    print("💰 Risk per Trade: 4% | Stop Loss: 1.5 ATR | TP: 0.75 ATR (RANGING)")
    print("🔧 Single Concurrent Trade Only")
    print("📈 TRENDING ONLY: Blocks all signals in RANGING/TRANSITIONAL markets")
    print("⚠️  BALANCED: Generates both BUY and SELL signals (in trending markets only)")
    print(f"📈 Trading Symbol: {symbol_name} ({symbol})")
    print(f"📊 Contract Size: {trader.SYMBOL_SPECS[symbol]['contract_size']}")
    print("=" * 70)
    print("💡 Usage: python fixed_live_trader.py [SYMBOL]")
    print("   Examples: python fixed_live_trader.py BTCUSD")
    print("            python fixed_live_trader.py EURUSD")
    print("            python fixed_live_trader.py XAUUSD")
    print("=" * 70)

    # Start trading
    try:
        trader.start_trading()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down trading system...")
        trader.stop_trading()

if __name__ == "__main__":
    main()
