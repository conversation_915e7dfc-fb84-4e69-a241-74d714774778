#!/usr/bin/env python3
"""
Quick training script with simplified approach
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score
from sklearn.preprocessing import StandardScaler
import sys
import os

# Add src to path
sys.path.append('src')

from data_manager import DataManager
from feature_engineering import FeatureEngineer

def quick_train():
    """Quick training with simplified approach"""
    
    print("🚀 Quick Training with Simplified Approach...")
    
    # Load data
    data_manager = DataManager()
    df = data_manager.load_historical_data()
    
    # Use recent data only (last 2 years for faster processing)
    df_recent = df.tail(100000)  # Last 100k records
    print(f"📊 Using recent data: {df_recent.shape}")
    
    # Create features
    feature_engineer = FeatureEngineer()
    df_features = feature_engineer.create_technical_indicators(df_recent)
    
    # Create targets with simpler approach
    df_features['future_return'] = df_features['close'].shift(-1) / df_features['close'] - 1
    
    # Simple binary classification: up (1) or down (0)
    df_features['target'] = (df_features['future_return'] > 0).astype(int)
    
    # Remove NaN rows
    df_clean = df_features.dropna()
    print(f"📊 Clean data shape: {df_clean.shape}")
    
    # Select features (avoid using future information)
    feature_cols = [col for col in df_clean.columns if col not in [
        'open', 'high', 'low', 'close', 'volume', 'target', 'future_return', 
        'trend_target', 'market_regime'
    ]]
    
    print(f"📈 Using {len(feature_cols)} features")
    
    # Prepare data
    X = df_clean[feature_cols].values
    y = df_clean['target'].values
    
    # Split data temporally
    split_idx = int(len(X) * 0.8)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    
    print(f"📊 Train: {X_train.shape}, Test: {X_test.shape}")
    print(f"🎯 Train target distribution: {np.bincount(y_train)}")
    print(f"🎯 Test target distribution: {np.bincount(y_test)}")
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Train Random Forest (faster than LSTM for testing)
    print("🌲 Training Random Forest...")
    rf = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        random_state=42,
        n_jobs=-1
    )
    
    rf.fit(X_train_scaled, y_train)
    
    # Evaluate
    train_pred = rf.predict(X_train_scaled)
    test_pred = rf.predict(X_test_scaled)
    
    train_acc = accuracy_score(y_train, train_pred)
    test_acc = accuracy_score(y_test, test_pred)
    
    print(f"📊 Train Accuracy: {train_acc:.4f}")
    print(f"📊 Test Accuracy: {test_acc:.4f}")
    
    print("\n📋 Test Classification Report:")
    print(classification_report(y_test, test_pred))
    
    # Feature importance
    feature_importance = pd.DataFrame({
        'feature': feature_cols,
        'importance': rf.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print("\n🔝 Top 10 Most Important Features:")
    print(feature_importance.head(10))
    
    # Check if model is learning
    if test_acc > 0.52:  # Better than random for binary classification
        print("✅ Model is learning! Accuracy > 52%")
        return True
    else:
        print("❌ Model is not learning well. Accuracy <= 52%")
        return False

if __name__ == "__main__":
    try:
        success = quick_train()
        if success:
            print("\n🎉 Quick training successful! The data pipeline works.")
            print("💡 Now we can proceed with LSTM training with confidence.")
        else:
            print("\n⚠️ Need to investigate data quality issues further.")
    except Exception as e:
        print(f"❌ Error during quick training: {e}")
        import traceback
        traceback.print_exc()
