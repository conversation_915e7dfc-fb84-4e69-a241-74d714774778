#!/usr/bin/env python3
"""
DEBUG PROFIT_SL_COUNT ISSUE
Test to reproduce the issue where initial trailing needs 2 units instead of 1
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_profit_sl_count_logic():
    """Test the profit_sl_count logic to see if there's an issue"""
    print("🔍 DEBUGGING PROFIT_SL_COUNT ISSUE")
    print("=" * 50)
    
    # Simulate the trailing stop data initialization
    print("\n📊 SIMULATING TRAILING STOP DATA INITIALIZATION:")
    
    trailing_stop_data = {
        'initial_sl': 3935.09,
        'current_sl': 3935.09,
        'original_sl_distance': 890.5,
        'raw_sl_distance': 19.40,
        'profit_sl_count': 0  # Should start at 0
    }
    
    print(f"✅ INITIALIZED: profit_sl_count = {trailing_stop_data['profit_sl_count']}")
    
    # Test the logic for determining how many units are needed
    print(f"\n🧮 TESTING UNITS NEEDED CALCULATION:")
    
    units_needed = trailing_stop_data['profit_sl_count'] + 1
    print(f"Units needed: {trailing_stop_data['profit_sl_count']} + 1 = {units_needed}")
    
    if units_needed == 1:
        print("✅ CORRECT: First trailing should need 1 unit")
    else:
        print(f"❌ WRONG: First trailing needs {units_needed} units instead of 1")
    
    # Test different profit scenarios
    print(f"\n📈 TESTING DIFFERENT PROFIT SCENARIOS:")
    
    test_profits = [445.25, 890.5, 1335.75, 1781.0]  # 0.5, 1.0, 1.5, 2.0 SL units
    
    for profit in test_profits:
        profit_sl_units = profit / trailing_stop_data['original_sl_distance']
        should_trigger = profit_sl_units >= (trailing_stop_data['profit_sl_count'] + 1)
        
        print(f"\nProfit: {profit:.1f} points")
        print(f"SL Units: {profit_sl_units:.4f}")
        print(f"Need: {trailing_stop_data['profit_sl_count'] + 1} units")
        print(f"Should trigger: {'✅ YES' if should_trigger else '❌ NO'}")
        
        if should_trigger and trailing_stop_data['profit_sl_count'] == 0:
            print("🎯 FIRST TRAILING WOULD TRIGGER")
            # Simulate the increment
            trailing_stop_data['profit_sl_count'] += 1
            print(f"🔧 profit_sl_count incremented to {trailing_stop_data['profit_sl_count']}")
            
            # Now test what happens next
            next_units_needed = trailing_stop_data['profit_sl_count'] + 1
            print(f"📊 NEXT TRAILING: Need {next_units_needed} units")
            
            if next_units_needed == 2:
                print("✅ CORRECT: Second trailing should need 2 units")
            else:
                print(f"❌ WRONG: Second trailing needs {next_units_needed} units")
            break

def test_user_scenario():
    """Test the specific scenario from user's logs"""
    print(f"\n" + "=" * 50)
    print("🎯 TESTING USER'S SPECIFIC SCENARIO")
    print("=" * 50)
    
    # User's data from logs
    entry_price = 4003.42
    current_price = 4010.36
    sl_distance = 574.2  # points
    profit_points = 645.4  # points
    
    print(f"📊 USER'S DATA:")
    print(f"Entry: {entry_price}")
    print(f"Current: {current_price}")
    print(f"SL Distance: {sl_distance} points")
    print(f"Profit: {profit_points} points")
    
    # Calculate SL units
    profit_sl_units = profit_points / sl_distance
    print(f"SL Units: {profit_sl_units:.4f}")
    
    # Test with different profit_sl_count values
    print(f"\n🧪 TESTING DIFFERENT profit_sl_count VALUES:")
    
    for count in [0, 1, 2]:
        units_needed = count + 1
        should_trigger = profit_sl_units >= units_needed
        
        print(f"\nIf profit_sl_count = {count}:")
        print(f"  Need: {units_needed} units")
        print(f"  Have: {profit_sl_units:.4f} units")
        print(f"  Should trigger: {'✅ YES' if should_trigger else '❌ NO'}")
        
        if count == 0 and units_needed == 1:
            print("  📝 This should be the INITIAL state")
        elif count == 1 and units_needed == 2:
            print("  📝 This should be AFTER first trailing")
            if not should_trigger:
                print("  ⚠️  This matches user's logs - needs 2 units but only has 1.12")

def test_potential_bug_scenarios():
    """Test potential scenarios that could cause the bug"""
    print(f"\n" + "=" * 50)
    print("🐛 TESTING POTENTIAL BUG SCENARIOS")
    print("=" * 50)
    
    print("\n🔍 SCENARIO 1: profit_sl_count gets incremented without actual trailing")
    
    trailing_data = {'profit_sl_count': 0}
    print(f"Initial: profit_sl_count = {trailing_data['profit_sl_count']}")
    
    # Simulate some condition that might incorrectly increment the count
    print("❌ BUG: Something increments count without trailing...")
    trailing_data['profit_sl_count'] += 1
    print(f"After bug: profit_sl_count = {trailing_data['profit_sl_count']}")
    
    units_needed = trailing_data['profit_sl_count'] + 1
    print(f"Now needs: {units_needed} units (should be 1, but is 2)")
    
    print("\n🔍 SCENARIO 2: Trailing data gets reinitialized after increment")
    
    trailing_data = {'profit_sl_count': 1}  # After successful trailing
    print(f"After trailing: profit_sl_count = {trailing_data['profit_sl_count']}")
    
    # Simulate reinitialization
    print("❌ BUG: Trailing data gets reinitialized...")
    trailing_data = {'profit_sl_count': 0}
    print(f"After reinit: profit_sl_count = {trailing_data['profit_sl_count']}")
    
    # But then something sets it back to 1
    print("❌ BUG: But something sets it back to 1...")
    trailing_data['profit_sl_count'] = 1
    print(f"Final state: profit_sl_count = {trailing_data['profit_sl_count']}")
    
    units_needed = trailing_data['profit_sl_count'] + 1
    print(f"Now needs: {units_needed} units (wrong for initial state)")

if __name__ == "__main__":
    print("🚀 STARTING PROFIT_SL_COUNT DEBUG TESTS")
    
    # Test 1: Basic logic
    test_profit_sl_count_logic()
    
    # Test 2: User's specific scenario
    test_user_scenario()
    
    # Test 3: Potential bug scenarios
    test_potential_bug_scenarios()
    
    print(f"\n" + "=" * 50)
    print("📋 SUMMARY")
    print("=" * 50)
    print("✅ Added debug logging to track profit_sl_count changes")
    print("✅ The logic itself appears correct")
    print("⚠️  Issue likely caused by:")
    print("   1. profit_sl_count getting incremented without actual trailing")
    print("   2. Trailing data being reinitialized incorrectly")
    print("   3. Race condition between threads")
    print("\n🔍 Check the logs for:")
    print("   - 'profit_sl_count INITIALIZED to X' messages")
    print("   - 'profit_sl_count INCREMENTED from X to Y' messages")
    print("   - Any discrepancies in the count values")
