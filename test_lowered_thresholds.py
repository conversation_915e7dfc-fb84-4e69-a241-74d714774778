#!/usr/bin/env python3
"""
Test the lowered candle strength thresholds (±15% instead of ±30%)
"""

import sys
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_lowered_thresholds():
    """Test that lowered thresholds generate more signals"""
    print("🧪 TESTING LOWERED CANDLE STRENGTH THRESHOLDS")
    print("=" * 55)
    
    try:
        # Initialize trader
        trader = FixedLiveTrader()
        
        # Connect to MT5
        if not trader.mt5_manager.connect():
            print("❌ Cannot connect to MT5")
            return
        
        print("✅ Connected to MT5")
        
        # Test 1: Verify new thresholds
        print("\n1️⃣ NEW THRESHOLD VERIFICATION")
        print("-" * 35)
        
        print("OLD THRESHOLDS:")
        print("• BUY: Candle Strength > +30%")
        print("• SELL: Candle Strength < -30%")
        print("• NO SIGNAL: Between -30% and +30%")
        print("")
        print("NEW THRESHOLDS:")
        print("• BUY: Candle Strength > +15%")
        print("• SELL: Candle Strength < -15%")
        print("• NO SIGNAL: Between -15% and +15%")
        print("")
        print("EXPECTED RESULT: More trading signals generated")
        
        # Test 2: Current system status
        print(f"\n2️⃣ CURRENT SYSTEM STATUS")
        print("-" * 35)
        
        result = trader.get_live_prediction()
        if result:
            signal, confidence, details, atr_value, regime, logic, regime_details, candle_strength = result
            candle_net_strength = candle_strength['net_strength'] * 100
            
            print(f"Current Candle Strength: {candle_net_strength:+.1f}%")
            print(f"Signal Generated: {signal}")
            print(f"Logic: {logic}")
            print(f"Regime: {regime}")
            
            # Test the new logic
            print(f"\n3️⃣ NEW THRESHOLD LOGIC TEST")
            print("-" * 35)
            
            if candle_net_strength > 15:
                expected_signal = "BUY"
                reason = f"Strength {candle_net_strength:+.1f}% > +15%"
            elif candle_net_strength < -15:
                expected_signal = "SELL"
                reason = f"Strength {candle_net_strength:+.1f}% < -15%"
            else:
                expected_signal = None
                reason = f"Strength {candle_net_strength:+.1f}% between -15% and +15%"
            
            print(f"Expected Signal: {expected_signal}")
            print(f"Actual Signal: {signal}")
            print(f"Reason: {reason}")
            
            if expected_signal == signal:
                print("✅ CORRECT: New thresholds working as expected")
            else:
                print("❌ MISMATCH: Check implementation")
        
        # Test 4: Signal range comparison
        print(f"\n4️⃣ SIGNAL RANGE COMPARISON")
        print("-" * 35)
        
        test_strengths = [-50, -30, -25, -20, -15, -10, -5, 0, 5, 10, 15, 20, 25, 30, 50]
        
        print("Strength | OLD (±30%) | NEW (±15%) | More Signals?")
        print("-" * 50)
        
        more_signals_count = 0
        for strength in test_strengths:
            # Old logic
            if strength > 30:
                old_signal = "BUY"
            elif strength < -30:
                old_signal = "SELL"
            else:
                old_signal = "NONE"
            
            # New logic
            if strength > 15:
                new_signal = "BUY"
            elif strength < -15:
                new_signal = "SELL"
            else:
                new_signal = "NONE"
            
            more_signals = "YES" if (old_signal == "NONE" and new_signal != "NONE") else "NO"
            if more_signals == "YES":
                more_signals_count += 1
            
            print(f"{strength:+4.0f}%    | {old_signal:^10s} | {new_signal:^10s} | {more_signals:^13s}")
        
        print(f"\n📊 ANALYSIS RESULTS:")
        print(f"• Additional signals generated: {more_signals_count} out of {len(test_strengths)} test cases")
        print(f"• Signal range expanded from ±30% to ±15%")
        print(f"• More sensitive to market movements")
        
        # Test 5: Expected benefits and risks
        print(f"\n5️⃣ EXPECTED BENEFITS & RISKS")
        print("-" * 35)
        
        print("✅ BENEFITS:")
        print("• More trading opportunities")
        print("• Earlier entry into trends")
        print("• Better capture of medium-strength signals")
        print("• Increased trading frequency")
        print("")
        print("⚠️  RISKS:")
        print("• More false signals possible")
        print("• Increased trading costs (spreads)")
        print("• Need for better risk management")
        print("• More frequent position changes")
        
        # Test 6: Specific scenarios that will now generate signals
        print(f"\n6️⃣ NEW SIGNAL SCENARIOS")
        print("-" * 35)
        
        new_scenarios = [
            ("+25%", "BUY", "Medium bullish strength (was no signal)"),
            ("+20%", "BUY", "Moderate bullish strength (was no signal)"),
            ("+16%", "BUY", "Weak bullish strength (was no signal)"),
            ("-16%", "SELL", "Weak bearish strength (was no signal)"),
            ("-20%", "SELL", "Moderate bearish strength (was no signal)"),
            ("-25%", "SELL", "Medium bearish strength (was no signal)")
        ]
        
        print("These scenarios will now generate signals:")
        for strength, signal, description in new_scenarios:
            print(f"• {strength:>5s} → {signal:4s} | {description}")
        
        print(f"\n✅ LOWERED THRESHOLDS TEST COMPLETE")
        print("=" * 55)
        print("🎯 Thresholds lowered from ±30% to ±15%")
        print("📈 More trading signals will be generated")
        print("⚡ System more sensitive to market movements")
        print("🚀 Ready for increased trading activity")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            trader.mt5_manager.disconnect()
        except:
            pass

if __name__ == "__main__":
    test_lowered_thresholds()
