#!/usr/bin/env python3
"""
Test the strict swing detection that requires immediate neighbor confirmation
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_strict_swing_detection():
    """Test that the algorithm only detects valid swing points"""
    print("🧪 TESTING STRICT SWING POINT DETECTION")
    print("=" * 60)
    
    # Create trader instance
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test Scenario 1: Valid swing high (higher than immediate neighbors)
    print("\n📊 TEST 1: Valid Swing High")
    dates1 = pd.date_range(start='2024-01-01', periods=7, freq='5min')
    
    # Create clear swing high pattern: low -> higher -> HIGHEST -> lower -> low
    highs1 = [4200.0, 4205.0, 4215.0, 4210.0, 4205.0, 4200.0, 4195.0]  # Index 2 is swing high
    lows1 = [4195.0, 4200.0, 4210.0, 4205.0, 4200.0, 4195.0, 4190.0]
    closes1 = [(h + l) / 2 for h, l in zip(highs1, lows1)]
    
    df1 = pd.DataFrame({
        'time': dates1,
        'open': closes1,
        'high': highs1,
        'low': lows1,
        'close': closes1,
        'tick_volume': [100] * 7,
        'atr': [2.5] * 7
    }).set_index('time')
    
    print("Valid Swing High Pattern:")
    for i, (h, l) in enumerate(zip(highs1, lows1)):
        marker = " ← SWING HIGH" if i == 2 else ""
        print(f"  {i}: H={h:7.1f} L={l:7.1f}{marker}")
    
    swing_points1 = trader.find_recent_swing_points(df1)
    if swing_points1['recent_high']:
        detected_high = swing_points1['recent_high']
        expected_high = 4215.0
        print(f"✅ Detected High: {detected_high:.1f} (Expected: {expected_high:.1f})")
        if abs(detected_high - expected_high) < 0.1:
            print("   🎉 CORRECT: Valid swing high detected")
        else:
            print("   ❌ INCORRECT: Wrong swing high detected")
    else:
        print("❌ No swing high detected (should have found 4215.0)")
    
    # Test Scenario 2: Invalid swing high (not higher than immediate neighbor)
    print("\n📊 TEST 2: Invalid Swing High (Not Higher Than Neighbor)")
    dates2 = pd.date_range(start='2024-01-02', periods=7, freq='5min')
    
    # Create invalid pattern: 4200 -> 4210 -> 4205 -> 4215 (4205 is NOT higher than 4210)
    highs2 = [4200.0, 4210.0, 4205.0, 4215.0, 4210.0, 4205.0, 4200.0]  # Index 2 should NOT be swing high
    lows2 = [4195.0, 4205.0, 4200.0, 4210.0, 4205.0, 4200.0, 4195.0]
    closes2 = [(h + l) / 2 for h, l in zip(highs2, lows2)]
    
    df2 = pd.DataFrame({
        'time': dates2,
        'open': closes2,
        'high': highs2,
        'low': lows2,
        'close': closes2,
        'tick_volume': [100] * 7,
        'atr': [2.5] * 7
    }).set_index('time')
    
    print("Invalid Swing High Pattern:")
    for i, (h, l) in enumerate(zip(highs2, lows2)):
        marker = " ← NOT swing high (4205 < 4210)" if i == 2 else ""
        marker += " ← REAL swing high" if i == 3 else ""
        print(f"  {i}: H={h:7.1f} L={l:7.1f}{marker}")
    
    swing_points2 = trader.find_recent_swing_points(df2)
    if swing_points2['recent_high']:
        detected_high = swing_points2['recent_high']
        print(f"✅ Detected High: {detected_high:.1f}")
        if abs(detected_high - 4215.0) < 0.1:
            print("   🎉 CORRECT: Correctly ignored invalid swing and found real one (4215.0)")
        elif abs(detected_high - 4205.0) < 0.1:
            print("   ❌ INCORRECT: Detected invalid swing high (4205.0)")
        else:
            print(f"   ❓ UNEXPECTED: Detected {detected_high:.1f}")
    else:
        print("❌ No swing high detected")
    
    # Test Scenario 3: Valid swing low (lower than immediate neighbors)
    print("\n📊 TEST 3: Valid Swing Low")
    dates3 = pd.date_range(start='2024-01-03', periods=7, freq='5min')
    
    # Create clear swing low pattern: high -> lower -> LOWEST -> higher -> high
    highs3 = [4220.0, 4215.0, 4205.0, 4210.0, 4215.0, 4220.0, 4225.0]
    lows3 = [4215.0, 4210.0, 4200.0, 4205.0, 4210.0, 4215.0, 4220.0]  # Index 2 is swing low
    closes3 = [(h + l) / 2 for h, l in zip(highs3, lows3)]
    
    df3 = pd.DataFrame({
        'time': dates3,
        'open': closes3,
        'high': highs3,
        'low': lows3,
        'close': closes3,
        'tick_volume': [100] * 7,
        'atr': [2.5] * 7
    }).set_index('time')
    
    print("Valid Swing Low Pattern:")
    for i, (h, l) in enumerate(zip(highs3, lows3)):
        marker = " ← SWING LOW" if i == 2 else ""
        print(f"  {i}: H={h:7.1f} L={l:7.1f}{marker}")
    
    swing_points3 = trader.find_recent_swing_points(df3)
    if swing_points3['recent_low']:
        detected_low = swing_points3['recent_low']
        expected_low = 4200.0
        print(f"✅ Detected Low: {detected_low:.1f} (Expected: {expected_low:.1f})")
        if abs(detected_low - expected_low) < 0.1:
            print("   🎉 CORRECT: Valid swing low detected")
        else:
            print("   ❌ INCORRECT: Wrong swing low detected")
    else:
        print("❌ No swing low detected (should have found 4200.0)")
    
    print(f"\n✅ STRICT SWING DETECTION TESTS COMPLETE")
    print("=" * 60)
    print("🎯 The algorithm now requires:")
    print("   • Swing HIGH: Must be higher than IMMEDIATE neighbors")
    print("   • Swing LOW: Must be lower than IMMEDIATE neighbors")
    print("   • Additional confirmation from nearby candles")
    print("🔍 This should eliminate false swing points in your live trading!")

if __name__ == "__main__":
    test_strict_swing_detection()
