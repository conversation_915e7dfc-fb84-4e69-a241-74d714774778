#!/usr/bin/env python3
"""
UNIT CONVERSION DEBUGGING SCRIPT
Debug and fix the SL distance vs profit calculation inconsistency
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader
import pandas as pd
import numpy as np

def debug_unit_conversions():
    """Debug unit conversion issues in SL distance vs profit calculations"""
    print("🔍 DEBUGGING UNIT CONVERSION ISSUES")
    print("=" * 60)
    
    # Create trader instance
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test scenario based on user's logs
    print("\n📊 USER'S SCENARIO:")
    entry_price = 3954.49
    current_price = 3964.17
    actual_sl_price = 3935.09  # Estimated from user's 890 points comment
    
    print(f"Entry Price: {entry_price}")
    print(f"Current Price: {current_price}")
    print(f"Estimated SL Price: {actual_sl_price}")
    
    # Calculate raw differences
    raw_sl_distance = abs(actual_sl_price - entry_price)
    raw_profit = current_price - entry_price
    
    print(f"\n🧮 RAW CALCULATIONS:")
    print(f"Raw SL Distance: {raw_sl_distance:.2f} (price difference)")
    print(f"Raw Profit: {raw_profit:.2f} (price difference)")
    print(f"Raw Profit in SL Units: {raw_profit / raw_sl_distance:.4f}")
    
    # Test pip size calculation
    print(f"\n📏 PIP SIZE TESTING:")
    try:
        pip_size = trader.mt5_manager.get_pip_size("XAUUSD!")
        print(f"XAUUSD Pip Size: {pip_size}")
        
        # Convert to points (assuming 1 point = 0.01 for XAUUSD)
        sl_distance_points = raw_sl_distance / 0.01
        profit_points = raw_profit / 0.01
        
        print(f"SL Distance in Points: {sl_distance_points:.1f}")
        print(f"Profit in Points: {profit_points:.1f}")
        print(f"Profit in SL Units (points): {profit_points / sl_distance_points:.4f}")
        
    except Exception as e:
        print(f"❌ Error getting pip size: {e}")
        # Use fallback values
        pip_size = 0.01
        sl_distance_points = raw_sl_distance / pip_size
        profit_points = raw_profit / pip_size
        
        print(f"Using fallback pip size: {pip_size}")
        print(f"SL Distance in Points: {sl_distance_points:.1f}")
        print(f"Profit in Points: {profit_points:.1f}")
    
    # Test the actual trailing_stop_data calculation
    print(f"\n🔧 TRAILING STOP DATA SIMULATION:")
    
    # Simulate how trailing_stop_data is initialized
    initial_sl = actual_sl_price
    entry_price_sim = entry_price
    
    # This is the actual calculation from the code
    original_sl_distance_calc = abs(initial_sl - entry_price_sim) if initial_sl and entry_price_sim else 0.001
    
    print(f"Simulated trailing_stop_data calculation:")
    print(f"  initial_sl: {initial_sl}")
    print(f"  entry_price: {entry_price_sim}")
    print(f"  original_sl_distance: {original_sl_distance_calc:.5f}")
    
    # Check if this matches the user's reported 19.4
    print(f"\n🎯 COMPARISON WITH USER'S LOGS:")
    print(f"User reported SL distance: 19.40")
    print(f"Our calculation: {original_sl_distance_calc:.2f}")
    print(f"Ratio: {original_sl_distance_calc / 19.40:.4f}" if original_sl_distance_calc != 19.40 else "✅ MATCH!")
    
    # Test profit calculation
    print(f"\n💰 PROFIT CALCULATION SIMULATION:")
    current_price_sim = current_price
    profit_points_calc = current_price_sim - entry_price_sim
    profit_sl_units_calc = profit_points_calc / original_sl_distance_calc
    
    print(f"Simulated profit calculation:")
    print(f"  current_price: {current_price_sim}")
    print(f"  entry_price: {entry_price_sim}")
    print(f"  profit_points: {profit_points_calc:.2f}")
    print(f"  profit_sl_units: {profit_sl_units_calc:.4f}")
    
    print(f"\nUser reported profit: 9.68pts")
    print(f"Our calculation: {profit_points_calc:.2f}")
    print(f"Ratio: {profit_points_calc / 9.68:.4f}" if profit_points_calc != 9.68 else "✅ MATCH!")
    
    # Identify the conversion issue
    print(f"\n🚨 CONVERSION ISSUE ANALYSIS:")
    
    # If user says 890 points but system shows 19.4
    if abs(original_sl_distance_calc - 19.40) < 0.1:
        conversion_factor = 890 / 19.40
        print(f"❌ FOUND ISSUE: SL distance conversion factor ≈ {conversion_factor:.1f}")
        print(f"   Real SL distance: 890 points")
        print(f"   System shows: 19.4")
        print(f"   Missing multiplication by: {conversion_factor:.1f}")
    
    # If user says 900 points but system shows 9.68
    if abs(profit_points_calc - 9.68) < 0.1:
        profit_conversion_factor = 900 / 9.68
        print(f"❌ FOUND ISSUE: Profit conversion factor ≈ {profit_conversion_factor:.1f}")
        print(f"   Real profit: 900 points")
        print(f"   System shows: 9.68")
        print(f"   Missing multiplication by: {profit_conversion_factor:.1f}")
    
    return {
        'raw_sl_distance': raw_sl_distance,
        'raw_profit': raw_profit,
        'calculated_sl_distance': original_sl_distance_calc,
        'calculated_profit': profit_points_calc,
        'pip_size': pip_size
    }

def test_unit_consistency_fix():
    """Test the proposed unit consistency fix"""
    print("\n" + "=" * 60)
    print("🔧 TESTING UNIT CONSISTENCY FIX")
    print("=" * 60)
    
    # Test values from user's scenario
    entry_price = 3954.49
    current_price = 3964.17
    actual_sl_price = 3935.09
    
    # PROPOSED FIX: Always work in points (multiply by 100 for XAUUSD)
    XAUUSD_POINT_MULTIPLIER = 100  # 1 price unit = 100 points for XAUUSD
    
    print(f"📊 PROPOSED FIX - CONSISTENT POINT CALCULATION:")
    
    # Calculate everything in points
    sl_distance_points = abs(actual_sl_price - entry_price) * XAUUSD_POINT_MULTIPLIER
    profit_points = (current_price - entry_price) * XAUUSD_POINT_MULTIPLIER
    profit_sl_units = profit_points / sl_distance_points
    
    print(f"SL Distance: {sl_distance_points:.1f} points")
    print(f"Profit: {profit_points:.1f} points")
    print(f"Profit in SL Units: {profit_sl_units:.4f}")
    
    print(f"\n✅ EXPECTED RESULTS:")
    print(f"SL Distance should be: ~890 points")
    print(f"Profit should be: ~900 points")
    print(f"SL Units should be: ~1.01 (triggering trailing)")
    
    print(f"\n🎯 FIX VALIDATION:")
    print(f"SL Distance match: {'✅' if abs(sl_distance_points - 890) < 50 else '❌'}")
    print(f"Profit match: {'✅' if abs(profit_points - 900) < 50 else '❌'}")
    print(f"Should trigger trailing: {'✅' if profit_sl_units >= 1.0 else '❌'}")
    
    return {
        'fixed_sl_distance': sl_distance_points,
        'fixed_profit': profit_points,
        'fixed_sl_units': profit_sl_units
    }

if __name__ == "__main__":
    print("🚀 STARTING UNIT CONVERSION DEBUG")
    
    # Debug current issues
    debug_results = debug_unit_conversions()
    
    # Test proposed fix
    fix_results = test_unit_consistency_fix()
    
    print(f"\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    
    print(f"Current SL Distance: {debug_results['calculated_sl_distance']:.2f}")
    print(f"Fixed SL Distance: {fix_results['fixed_sl_distance']:.1f} points")
    
    print(f"Current Profit: {debug_results['calculated_profit']:.2f}")
    print(f"Fixed Profit: {fix_results['fixed_profit']:.1f} points")
    
    print(f"Fixed SL Units: {fix_results['fixed_sl_units']:.4f}")
    print(f"Trailing should trigger: {'✅ YES' if fix_results['fixed_sl_units'] >= 1.0 else '❌ NO'}")
