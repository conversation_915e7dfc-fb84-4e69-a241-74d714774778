#!/usr/bin/env python3
"""
Test script to verify enhanced candle confirmation revert logic
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_enhanced_revert_logic():
    """Test the enhanced revert logic with ATR profit checking"""
    print("🔄 Testing Enhanced Candle Confirmation Revert Logic...")
    
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test the revert method exists and is callable
    print("📊 Testing revert method:")
    try:
        result = trader.check_candle_confirmation_stop_revert()
        print("   ✅ Revert method executed successfully")
        print(f"   📊 Result: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test ATR calculation method
    print("\n📏 Testing ATR calculation:")
    try:
        # Create sample data for ATR calculation
        sample_data = pd.DataFrame({
            'high': [4200 + i for i in range(20)],
            'low': [4190 + i for i in range(20)],
            'close': [4195 + i for i in range(20)]
        })
        
        atr_value = trader.calculate_atr(sample_data)
        print(f"   ✅ ATR calculation works: {atr_value:.5f}")
    except Exception as e:
        print(f"   ❌ ATR calculation error: {e}")
    
    # Test stop loss calculation method
    print("\n🎯 Testing stop loss calculation:")
    try:
        current_price = 4200.0
        atr_value = 7.0
        
        buy_sl = trader.calculate_stop_loss_price(current_price, atr_value, 'BUY')
        sell_sl = trader.calculate_stop_loss_price(current_price, atr_value, 'SELL')
        
        print(f"   ✅ BUY SL calculation: {buy_sl:.5f} (should be ~{current_price - 1.5*atr_value:.5f})")
        print(f"   ✅ SELL SL calculation: {sell_sl:.5f} (should be ~{current_price + 1.5*atr_value:.5f})")
    except Exception as e:
        print(f"   ❌ Stop loss calculation error: {e}")

def simulate_revert_scenarios():
    """Simulate different revert scenarios"""
    print("\n🎬 Simulating Revert Scenarios...")
    
    scenarios = [
        {
            'name': 'Low Profit (0.5 ATR)',
            'entry_price': 4200.0,
            'current_price': 4203.5,  # 0.5 ATR profit (assuming 7.0 ATR)
            'atr_value': 7.0,
            'position_type': 'BUY',
            'expected_action': 'REVERT to original SL'
        },
        {
            'name': 'High Profit (1.5 ATR)',
            'entry_price': 4200.0,
            'current_price': 4210.5,  # 1.5 ATR profit
            'atr_value': 7.0,
            'position_type': 'BUY',
            'expected_action': 'SET ATR trailing stop'
        },
        {
            'name': 'SELL Low Profit (0.8 ATR)',
            'entry_price': 4200.0,
            'current_price': 4194.4,  # 0.8 ATR profit for SELL
            'atr_value': 7.0,
            'position_type': 'SELL',
            'expected_action': 'REVERT to original SL'
        },
        {
            'name': 'SELL High Profit (2.0 ATR)',
            'entry_price': 4200.0,
            'current_price': 4186.0,  # 2.0 ATR profit for SELL
            'atr_value': 7.0,
            'position_type': 'SELL',
            'expected_action': 'SET ATR trailing stop'
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📊 Scenario: {scenario['name']}")
        print(f"   Entry: {scenario['entry_price']}")
        print(f"   Current: {scenario['current_price']}")
        print(f"   Position: {scenario['position_type']}")
        
        # Calculate profit
        if scenario['position_type'] == 'BUY':
            profit_points = scenario['current_price'] - scenario['entry_price']
        else:
            profit_points = scenario['entry_price'] - scenario['current_price']
        
        profit_atr = profit_points / scenario['atr_value']
        
        print(f"   Profit: {profit_points:.1f} points ({profit_atr:.2f} ATR)")
        print(f"   Expected: {scenario['expected_action']}")
        
        if profit_atr >= 1.0:
            print(f"   ✅ Would trigger ATR trailing stop")
        else:
            print(f"   ✅ Would revert to original stop loss")

def main():
    """Run all enhanced revert tests"""
    print("🧪 ENHANCED CANDLE CONFIRMATION REVERT TEST")
    print("=" * 60)
    
    # Test 1: Enhanced revert logic
    test_enhanced_revert_logic()
    
    # Test 2: Simulate scenarios
    simulate_revert_scenarios()
    
    print("\n📊 SUMMARY")
    print("=" * 30)
    print("✅ Enhanced revert logic implemented!")
    print("🎯 Key features:")
    print("   • Checks if position is 1+ ATR in profit before reverting")
    print("   • If 1+ ATR profit: Sets ATR-based trailing stop (1.5 ATR from current price)")
    print("   • If <1 ATR profit: Reverts to original stop loss")
    print("   • Initializes trailing_stop_data for continued ATR trailing")
    print("   • Maintains partial exit system (1/3 closure on ATR trails)")
    
    print("\n📝 Expected behavior:")
    print("   🔄 Low profit: 'CANDLE CONFIRMATION STOP REVERTED: Back to original SL'")
    print("   🎯 High profit: 'ATR TRAILING STOP SET: [price] (1.5 ATR from current price)'")

if __name__ == "__main__":
    main()
