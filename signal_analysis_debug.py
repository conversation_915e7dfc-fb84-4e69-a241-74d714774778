#!/usr/bin/env python3
"""
Signal Analysis Debug Tool
Analyzes specific bars to understand why signals didn't appear
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import all trading system components
from mt5_integration import MT5Manager
from fixed_live_trader import FixedFeatureEngineer, RegimeDetector
from simple_regression_channel import SimpleRegressionChannel, ChannelState
from qqe_indicator import QQEIndicator

class SignalAnalysisDebug:
    def __init__(self):
        self.mt5_manager = MT5Manager()
        self.feature_engineer = FixedFeatureEngineer()
        self.regime_detector = RegimeDetector()
        self.qqe_indicator = QQEIndicator()
        
        # Initialize simple regression channel
        self.simple_regression = SimpleRegressionChannel(
            periods=20,                       # Fixed 20-period lookback
            std_multiplier=2.0               # 2 standard deviations for channel bounds
        )

        # Pass simple regression to regime detector
        self.regime_detector.set_simple_regression(self.simple_regression)
        
        self.symbol = "XAUUSD!"
        self.timeframe = "M5"
        
    def get_data_and_indicators(self):
        """Get data and calculate all indicators"""
        print("🔍 Getting data and calculating indicators...")
        
        if not self.mt5_manager.connect():
            raise Exception("Failed to connect to MT5")
        
        # Get 500 candles to ensure indicators have enough historical data
        df = self.mt5_manager.get_latest_data(self.symbol, self.timeframe, 500)
        if df is None or len(df) < 400:
            raise Exception("Failed to get sufficient data")

        print(f"📊 Got {len(df)} candles, calculating indicators with full history...")

        # Calculate all technical indicators using EXACT same settings with full history
        df = self.feature_engineer.create_technical_indicators(df)
        df = self.qqe_indicator.calculate_qqe_bands(df)
        df = self.regime_detector.calculate_regime_indicators(df)

        print(f"📊 Using last 200 candles for analysis (indicators now have {len(df)-200} candles of lookback)")

        # Use last 200 candles for analysis (but keep full history for indicator calculations)
        df = df.iloc[-200:].copy()
        df.reset_index(drop=True, inplace=True)

        return df
    
    def analyze_specific_bar(self, df, bar_index, expected_signal):
        """Analyze conditions at a specific bar"""
        print(f"\n{'='*80}")
        print(f"📊 ANALYZING BAR #{bar_index} (Expected: {expected_signal})")
        print(f"{'='*80}")
        
        if bar_index >= len(df):
            print(f"❌ Bar {bar_index} is out of range (max: {len(df)-1})")
            return
        
        # Get data up to current bar
        current_df = df.iloc[:bar_index+1].copy()
        
        if len(current_df) < 50:
            print(f"❌ Insufficient data at bar {bar_index} (need 50+, have {len(current_df)})")
            return
        
        current_candle = current_df.iloc[-1]
        
        print(f"📅 Bar {bar_index}: {current_candle.name} | Price: {current_candle['close']:.2f}")
        print(f"🕯️ OHLC: O:{current_candle['open']:.2f} H:{current_candle['high']:.2f} L:{current_candle['low']:.2f} C:{current_candle['close']:.2f}")
        
        # 1. QQE Analysis
        print(f"\n🔍 QQE ANALYSIS:")

        # Debug: Print all QQE-related columns to see what's available
        qqe_columns = [col for col in current_candle.index if 'qqe' in col.lower() or col in ['trend', 'rsi', 'rsi_ma', 'fast_atr_rsi_tl']]
        print(f"   Available QQE columns: {qqe_columns}")
        for col in qqe_columns:
            print(f"   {col}: {current_candle.get(col, 'N/A')}")

        # Use correct column names
        qqe_trend = current_candle.get('qqe_trend_signal', 0)  # This is the continuous trend signal
        qqe_signal = current_candle.get('qqe_signal', 0)
        qqe_strength = abs(current_candle.get('qqe_signal_strength', 0))
        rsi = current_candle.get('rsi', 50)
        rsi_ma = current_candle.get('rsi_ma', 50)
        fast_atr_rsi_tl = current_candle.get('fast_atr_rsi_tl', 50)

        print(f"\n   RSI: {rsi:.1f}")
        print(f"   RSI MA: {rsi_ma:.1f}")
        print(f"   Fast ATR RSI TL: {fast_atr_rsi_tl:.1f}")
        print(f"   QQE Trend: {qqe_trend:+.0f} ({'BULLISH' if qqe_trend > 0 else 'BEARISH' if qqe_trend < 0 else 'NEUTRAL'})")
        print(f"   QQE Signal: {qqe_signal:+.0f} ({'BUY' if qqe_signal > 0 else 'SELL' if qqe_signal < 0 else 'NONE'})")
        print(f"   QQE Strength: {qqe_strength:.3f}")
        
        # 2. Candle Strength Analysis
        print(f"\n🔍 CANDLE STRENGTH ANALYSIS:")
        if len(current_df) >= 9:
            lookback_candles = current_df.iloc[-9:-1]  # Exclude current candle
            
            bullish_strength = 0
            bearish_strength = 0
            
            for _, candle in lookback_candles.iterrows():
                candle_range = candle['high'] - candle['low']
                if candle_range > 0:
                    close_position = (candle['close'] - candle['low']) / candle_range
                    
                    if candle['close'] > candle['open']:  # Bullish candle
                        body_strength = (candle['close'] - candle['open']) / candle_range
                        bullish_strength += body_strength * close_position
                    else:  # Bearish candle
                        body_strength = (candle['open'] - candle['close']) / candle_range
                        bearish_strength += body_strength * (1 - close_position)
            
            total_strength = bullish_strength + bearish_strength
            if total_strength > 0:
                net_strength = (bullish_strength - bearish_strength) / total_strength
            else:
                net_strength = 0
            
            print(f"   Bullish Strength: {bullish_strength:.3f}")
            print(f"   Bearish Strength: {bearish_strength:.3f}")
            print(f"   Net Strength: {net_strength:+.3f} ({'BULLISH' if net_strength > 0.2 else 'BEARISH' if net_strength < -0.2 else 'NEUTRAL'})")
        else:
            net_strength = 0
            print(f"   ❌ Insufficient data for candle strength")
        
        # 3. Regime Analysis
        print(f"\n🔍 REGIME ANALYSIS:")
        regime, confidence, details, trend_dir, accurate_trend = self.regime_detector.detect_regime(current_df)
        print(f"   Market Regime: {regime} (Confidence: {confidence:.1%})")
        print(f"   Trend Direction: {trend_dir}")
        
        # 4. Signal Generation Logic
        print(f"\n🔍 SIGNAL GENERATION LOGIC:")
        signal = None
        reasons = []
        
        # QQE + Candle Strength Logic (OPTIMIZED THRESHOLDS)
        if qqe_trend > 0 and qqe_signal > 0 and net_strength > 0.15:  # Lowered from 0.2 to 0.15
            signal = 'BUY'
            reasons.append(f"QQE BUY (trend:{qqe_trend:+.0f}, signal:{qqe_signal:+.0f}) + Bullish Candles ({net_strength:+.3f})")
        elif qqe_trend < 0 and qqe_signal < 0 and net_strength < -0.15:  # Lowered from -0.2 to -0.15
            signal = 'SELL'
            reasons.append(f"QQE SELL (trend:{qqe_trend:+.0f}, signal:{qqe_signal:+.0f}) + Bearish Candles ({net_strength:+.3f})")
        elif qqe_strength > 0.5 and qqe_trend > 0 and qqe_signal > 0:  # High QQE strength override
            signal = 'BUY'
            reasons.append(f"Strong QQE BUY Signal (strength:{qqe_strength:.3f}, trend:{qqe_trend:+.0f})")
        elif qqe_strength > 0.5 and qqe_trend < 0 and qqe_signal < 0:  # High QQE strength override
            signal = 'SELL'
            reasons.append(f"Strong QQE SELL Signal (strength:{qqe_strength:.3f}, trend:{qqe_trend:+.0f})")
        elif abs(net_strength) > 0.5:  # Strong candle strength override
            if net_strength > 0.5:
                signal = 'BUY'
                reasons.append(f"Strong Bullish Candles Override ({net_strength:+.3f})")
            elif net_strength < -0.5:
                signal = 'SELL'
                reasons.append(f"Strong Bearish Candles Override ({net_strength:+.3f})")
        
        # Check why signal might be blocked
        blocking_reasons = []
        
        if qqe_trend > 0 and qqe_signal <= 0:
            blocking_reasons.append(f"QQE trend bullish ({qqe_trend:+.0f}) but no signal ({qqe_signal:+.0f})")
        elif qqe_trend < 0 and qqe_signal >= 0:
            blocking_reasons.append(f"QQE trend bearish ({qqe_trend:+.0f}) but no signal ({qqe_signal:+.0f})")
        elif qqe_trend == 0:
            blocking_reasons.append(f"QQE trend neutral ({qqe_trend:+.0f})")
        
        if abs(net_strength) <= 0.15:
            blocking_reasons.append(f"Weak candle strength ({net_strength:+.3f}, need >0.15 or <-0.15)")
        
        print(f"   Generated Signal: {signal if signal else 'NONE'}")
        print(f"   Expected Signal: {expected_signal}")
        
        if reasons:
            print(f"   ✅ Signal Reasons:")
            for reason in reasons:
                print(f"      • {reason}")
        
        if blocking_reasons:
            print(f"   ❌ Blocking Reasons:")
            for reason in blocking_reasons:
                print(f"      • {reason}")
        
        # 5. Match Analysis
        print(f"\n🎯 MATCH ANALYSIS:")
        if signal == expected_signal:
            print(f"   ✅ MATCH: Generated '{signal}' matches expected '{expected_signal}'")
        elif signal is None:
            print(f"   ❌ MISS: No signal generated, expected '{expected_signal}'")
        else:
            print(f"   ❌ WRONG: Generated '{signal}', expected '{expected_signal}'")
        
        return {
            'bar': bar_index,
            'expected': expected_signal,
            'generated': signal,
            'qqe_trend': qqe_trend,
            'qqe_signal': qqe_signal,
            'net_strength': net_strength,
            'regime': regime,
            'reasons': reasons,
            'blocking_reasons': blocking_reasons
        }
    
    def run_analysis(self):
        """Run analysis on specific bars"""
        try:
            print("🚀 SIGNAL ANALYSIS DEBUG TOOL")
            print("=" * 60)
            
            # Get data
            df = self.get_data_and_indicators()
            
            # Bars to analyze (user specified)
            analysis_bars = [
                (11, 'BUY'),   # Bar 11 - expected BUY
                (22, 'BUY'),   # Bar 22 - expected BUY  
                (31, 'BUY'),   # Bar 31 - expected BUY
                (36, 'BUY'),   # Bar 36 - expected BUY
                (58, 'SELL'),  # Bar 58 - expected SELL
                (67, 'BUY'),   # Bar 67 - expected BUY
                (68, 'BUY'),   # Bar 68 - expected BUY
                (98, 'BUY'),   # Bar 98 - expected BUY
                (117, 'SELL'), # Bar 117 - expected SELL
                (130, 'BUY'),  # Bar 130 - expected BUY
            ]
            
            results = []
            
            for bar_index, expected_signal in analysis_bars:
                result = self.analyze_specific_bar(df, bar_index, expected_signal)
                if result:
                    results.append(result)
            
            # Summary
            print(f"\n{'='*80}")
            print(f"📊 ANALYSIS SUMMARY")
            print(f"{'='*80}")
            
            matches = sum(1 for r in results if r['generated'] == r['expected'])
            misses = sum(1 for r in results if r['generated'] is None)
            wrongs = sum(1 for r in results if r['generated'] and r['generated'] != r['expected'])
            
            print(f"Total Analyzed: {len(results)}")
            print(f"✅ Matches: {matches}")
            print(f"❌ Misses: {misses}")
            print(f"❌ Wrong Signals: {wrongs}")
            print(f"Success Rate: {matches/len(results)*100:.1f}%")
            
            return results
            
        except Exception as e:
            print(f"❌ Error in analysis: {e}")
            return None
        finally:
            if self.mt5_manager.connected:
                self.mt5_manager.disconnect()

def main():
    """Main function"""
    analyzer = SignalAnalysisDebug()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
