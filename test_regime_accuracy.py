#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced Regime Detector
Tests every component against exact user specifications
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# Add src to path
sys.path.append('src')
from enhanced_regime_detector import EnhancedRegimeDetector

class RegimeDetectorTester:
    """Comprehensive tester for regime detector accuracy"""
    
    def __init__(self):
        self.detector = EnhancedRegimeDetector("XAUUSD!", "M5")
        self.test_results = {}
        self.failed_tests = []
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def create_test_data(self, pattern_type: str, length: int = 100) -> pd.DataFrame:
        """Create specific test data patterns"""
        dates = pd.date_range(start='2024-01-01', periods=length, freq='5T')
        
        if pattern_type == "strong_uptrend":
            # Create clear HH+HL pattern
            base_price = 2000
            prices = []
            for i in range(length):
                # Add upward bias with some noise
                trend = i * 0.5  # Strong upward trend
                noise = np.random.normal(0, 1)
                prices.append(base_price + trend + noise)
                
        elif pattern_type == "strong_downtrend":
            # Create clear LH+LL pattern
            base_price = 2100
            prices = []
            for i in range(length):
                trend = -i * 0.5  # Strong downward trend
                noise = np.random.normal(0, 1)
                prices.append(base_price + trend + noise)
                
        elif pattern_type == "ranging":
            # Create bouncing range pattern
            base_price = 2000
            range_size = 20
            prices = []
            for i in range(length):
                # Oscillate between range bounds
                cycle_pos = (i % 20) / 20  # 0 to 1
                range_price = base_price + (np.sin(cycle_pos * 2 * np.pi) * range_size/2)
                noise = np.random.normal(0, 0.5)
                prices.append(range_price + noise)
                
        elif pattern_type == "consecutive_bullish":
            # Create 6 consecutive bullish candles
            base_price = 2000
            prices = [base_price]
            for i in range(1, length):
                if i <= 6:
                    # Consecutive bullish
                    prices.append(prices[-1] + np.random.uniform(1, 3))
                else:
                    # Random after
                    prices.append(prices[-1] + np.random.normal(0, 1))
                    
        elif pattern_type == "zigzag":
            # Create alternating up/down pattern
            base_price = 2000
            prices = [base_price]
            direction = 1
            for i in range(1, length):
                change = direction * np.random.uniform(1, 2)
                prices.append(prices[-1] + change)
                direction *= -1  # Alternate direction
                
        else:
            # Random walk
            base_price = 2000
            prices = [base_price]
            for i in range(1, length):
                change = np.random.normal(0, 2)
                prices.append(prices[-1] + change)
        
        # Create OHLC from prices
        opens = prices[:-1] + [prices[-1]]
        closes = prices
        highs = [max(o, c) + np.random.uniform(0, 1) for o, c in zip(opens, closes)]
        lows = [min(o, c) - np.random.uniform(0, 1) for o, c in zip(opens, closes)]
        
        df = pd.DataFrame({
            'datetime': dates,
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': [1000] * length
        })
        df.set_index('datetime', inplace=True)
        
        return df
    
    def test_swing_structure_accuracy(self):
        """Test swing structure detection against specifications"""
        print("\n🔍 TESTING SWING STRUCTURE ACCURACY")
        print("=" * 50)
        
        # Test 1: Strong uptrend should give 12 points
        df_uptrend = self.create_test_data("strong_uptrend", 100)
        swing_result = self.detector._analyze_swing_structure(df_uptrend)
        
        print(f"Uptrend Test: {swing_result}")
        if swing_result['regime'] == 'trending' and swing_result['points'] >= 8:
            print("✅ Uptrend detection: PASS")
        else:
            print("❌ Uptrend detection: FAIL")
            self.failed_tests.append("Swing Structure - Uptrend")
        
        # Test 2: Strong downtrend should give 12 points
        df_downtrend = self.create_test_data("strong_downtrend", 100)
        swing_result = self.detector._analyze_swing_structure(df_downtrend)
        
        print(f"Downtrend Test: {swing_result}")
        if swing_result['regime'] == 'trending' and swing_result['points'] >= 8:
            print("✅ Downtrend detection: PASS")
        else:
            print("❌ Downtrend detection: FAIL")
            self.failed_tests.append("Swing Structure - Downtrend")
        
        # Test 3: Ranging should give ranging points
        df_ranging = self.create_test_data("ranging", 100)
        swing_result = self.detector._analyze_swing_structure(df_ranging)
        
        print(f"Ranging Test: {swing_result}")
        if swing_result['regime'] == 'ranging' and swing_result['points'] >= 6:
            print("✅ Ranging detection: PASS")
        else:
            print("❌ Ranging detection: FAIL")
            self.failed_tests.append("Swing Structure - Ranging")
    
    def test_momentum_strength_accuracy(self):
        """Test momentum strength against specifications"""
        print("\n🔍 TESTING MOMENTUM STRENGTH ACCURACY")
        print("=" * 50)
        
        # Test 1: 6 consecutive bullish candles should give 10 points
        df_consecutive = self.create_test_data("consecutive_bullish", 50)
        momentum_result = self.detector._analyze_momentum_strength(df_consecutive)
        
        print(f"Consecutive Bullish Test: {momentum_result}")
        if momentum_result['regime'] == 'trending' and momentum_result['points'] == 10:
            print("✅ Consecutive momentum: PASS")
        else:
            print("❌ Consecutive momentum: FAIL")
            self.failed_tests.append("Momentum - Consecutive")
        
        # Test 2: Zigzag pattern should give 10 ranging points
        df_zigzag = self.create_test_data("zigzag", 50)
        momentum_result = self.detector._analyze_momentum_strength(df_zigzag)
        
        print(f"Zigzag Test: {momentum_result}")
        if momentum_result['regime'] == 'ranging' and momentum_result['points'] >= 7:
            print("✅ Zigzag momentum: PASS")
        else:
            print("❌ Zigzag momentum: FAIL")
            self.failed_tests.append("Momentum - Zigzag")
    
    def test_candle_body_analysis(self):
        """Test candle body analysis accuracy"""
        print("\n🔍 TESTING CANDLE BODY ANALYSIS")
        print("=" * 50)
        
        # Create test data with strong bodies (trending)
        dates = pd.date_range(start='2024-01-01', periods=20, freq='5T')
        
        # Strong trending candles (large bodies, small wicks)
        opens = [2000 + i for i in range(20)]
        closes = [2005 + i for i in range(20)]  # 5-point bodies
        highs = [c + 0.5 for c in closes]  # Small upper wicks
        lows = [o - 0.5 for o in opens]    # Small lower wicks
        
        df_strong = pd.DataFrame({
            'datetime': dates,
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': [1000] * 20
        })
        df_strong.set_index('datetime', inplace=True)
        
        # Calculate ATR for the test
        df_strong = self.detector._calculate_technical_indicators(df_strong)
        
        candle_result = self.detector._analyze_candle_bodies(df_strong)
        print(f"Strong Bodies Test: {candle_result}")
        
        if candle_result['regime'] == 'trending' and candle_result['points'] >= 5:
            print("✅ Strong candle bodies: PASS")
        else:
            print("❌ Strong candle bodies: FAIL")
            self.failed_tests.append("Candle Bodies - Strong")
    
    def test_complete_system_integration(self):
        """Test complete system with known patterns"""
        print("\n🔍 TESTING COMPLETE SYSTEM INTEGRATION")
        print("=" * 60)
        
        # Test with strong trending data
        df_trend = self.create_test_data("strong_uptrend", 200)
        regime, confidence, details = self.detector.detect_regime(df_trend)
        
        print(f"Strong Uptrend System Test:")
        print(f"  Regime: {regime}")
        print(f"  Confidence: {confidence:.2f}%")
        print(f"  Trending Score: {details['trending_score']}/98")
        print(f"  Ranging Score: {details['ranging_score']}/98")
        
        if regime in ['TRENDING', 'STRONG_TRENDING']:
            print("✅ System trending detection: PASS")
        else:
            print("❌ System trending detection: FAIL")
            self.failed_tests.append("System Integration - Trending")
        
        # Test with ranging data
        df_range = self.create_test_data("ranging", 200)
        regime, confidence, details = self.detector.detect_regime(df_range)
        
        print(f"\nRanging System Test:")
        print(f"  Regime: {regime}")
        print(f"  Confidence: {confidence:.2f}%")
        print(f"  Trending Score: {details['trending_score']}/98")
        print(f"  Ranging Score: {details['ranging_score']}/98")
        
        if regime in ['RANGING', 'STRONG_RANGING']:
            print("✅ System ranging detection: PASS")
        else:
            print("❌ System ranging detection: FAIL")
            self.failed_tests.append("System Integration - Ranging")
    
    def run_all_tests(self):
        """Run all accuracy tests"""
        print("🚀 ENHANCED REGIME DETECTOR - ACCURACY TESTING")
        print("=" * 70)
        
        self.test_swing_structure_accuracy()
        self.test_momentum_strength_accuracy()
        self.test_candle_body_analysis()
        self.test_complete_system_integration()
        
        # Summary
        print(f"\n📊 TEST SUMMARY")
        print("=" * 30)
        
        if len(self.failed_tests) == 0:
            print("🎉 ALL TESTS PASSED!")
            print("✅ System is accurate and ready for live trading")
        else:
            print(f"❌ {len(self.failed_tests)} TESTS FAILED:")
            for test in self.failed_tests:
                print(f"   • {test}")
            print("\n⚠️ System needs fixes before live trading")
        
        return len(self.failed_tests) == 0

def main():
    """Main testing function"""
    tester = RegimeDetectorTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎯 READY FOR INTEGRATION WITH FIXED_LIVE_TRADER.PY")
    else:
        print("\n⚠️ FIXES NEEDED BEFORE INTEGRATION")

if __name__ == "__main__":
    main()
