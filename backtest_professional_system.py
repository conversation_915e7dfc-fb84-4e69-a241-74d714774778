#!/usr/bin/env python3
"""
Backtest Professional Rule-Based Trading System
Validate the rule-based approach with historical data
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager
from final_trading_system import OptimizedSignalGenerator, OptimizedRiskManager

class ProfessionalBacktester:
    """Backtest the professional trading system"""
    
    def __init__(self, initial_balance=10000):
        self.mt5_manager = MT5Manager()
        self.signal_generator = OptimizedSignalGenerator()
        self.initial_balance = initial_balance
        self.risk_manager = OptimizedRiskManager(initial_balance)
        
    def get_historical_data(self, bars=5000):
        """Get historical data for backtesting"""
        print("📊 Getting historical data for backtesting...")
        
        if not self.mt5_manager.connect():
            return None
        
        df = self.mt5_manager.get_latest_data("XAUUSD!", "M5", bars)
        if df is None or len(df) < 1000:
            return None
        
        print(f"✅ Retrieved {len(df)} bars")
        print(f"📅 Date range: {df.index[0]} to {df.index[-1]}")
        print(f"💰 Price range: {df['close'].min():.2f} to {df['close'].max():.2f}")
        
        return df
    
    def run_backtest(self, df):
        """Run backtest on historical data"""
        print("\n🔄 Running backtest...")
        
        # Generate signals
        df = self.signal_generator.generate_signals(df)
        
        # Initialize tracking variables
        balance = self.initial_balance
        trades = []
        positions = []
        current_position = None
        
        # Track performance
        equity_curve = []
        drawdowns = []
        peak_balance = balance
        
        for i in range(100, len(df)):  # Start after indicators are calculated
            current_bar = df.iloc[i]
            current_time = df.index[i]
            
            # Update equity curve
            equity_curve.append(balance)
            
            # Calculate drawdown
            if balance > peak_balance:
                peak_balance = balance
            drawdown = (peak_balance - balance) / peak_balance * 100
            drawdowns.append(drawdown)
            
            # Check for exit conditions first
            if current_position:
                exit_trade = False
                exit_reason = ""
                exit_price = current_bar['close']
                
                # Stop loss hit
                if current_position['direction'] == 1:  # Long
                    if current_bar['low'] <= current_position['stop_loss']:
                        exit_trade = True
                        exit_reason = "Stop Loss"
                        exit_price = current_position['stop_loss']
                    elif current_bar['high'] >= current_position['take_profit']:
                        exit_trade = True
                        exit_reason = "Take Profit"
                        exit_price = current_position['take_profit']
                else:  # Short
                    if current_bar['high'] >= current_position['stop_loss']:
                        exit_trade = True
                        exit_reason = "Stop Loss"
                        exit_price = current_position['stop_loss']
                    elif current_bar['low'] <= current_position['take_profit']:
                        exit_trade = True
                        exit_reason = "Take Profit"
                        exit_price = current_position['take_profit']
                
                # Exit trade if conditions met
                if exit_trade:
                    # Calculate P&L
                    if current_position['direction'] == 1:  # Long
                        pnl = (exit_price - current_position['entry_price']) * current_position['position_size']
                    else:  # Short
                        pnl = (current_position['entry_price'] - exit_price) * current_position['position_size']
                    
                    balance += pnl
                    
                    # Record trade
                    trade_record = {
                        'entry_time': current_position['entry_time'],
                        'exit_time': current_time,
                        'direction': 'Long' if current_position['direction'] == 1 else 'Short',
                        'entry_price': current_position['entry_price'],
                        'exit_price': exit_price,
                        'position_size': current_position['position_size'],
                        'pnl': pnl,
                        'exit_reason': exit_reason,
                        'signal_strength': current_position['signal_strength']
                    }
                    trades.append(trade_record)
                    
                    # Clear position
                    current_position = None
                    
                    # Update risk manager balance
                    self.risk_manager.account_balance = balance
            
            # Check for new entry signals (only if no current position)
            if current_position is None and current_bar['signal'] != 0:
                # Calculate position size and levels
                entry_price = current_bar['close']
                atr_value = current_bar['atr']
                
                if pd.isna(atr_value) or atr_value <= 0:
                    continue
                
                position_size = self.risk_manager.calculate_position_size(entry_price, atr_value)
                stop_loss, take_profit = self.risk_manager.calculate_stops_targets(
                    entry_price, atr_value, current_bar['signal']
                )
                
                # Create new position
                current_position = {
                    'entry_time': current_time,
                    'entry_price': entry_price,
                    'direction': current_bar['signal'],
                    'position_size': position_size * 100,  # Convert lots to units
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'signal_strength': current_bar['signal_strength']
                }
        
        # Close any remaining position at the end
        if current_position:
            exit_price = df.iloc[-1]['close']
            if current_position['direction'] == 1:  # Long
                pnl = (exit_price - current_position['entry_price']) * current_position['position_size']
            else:  # Short
                pnl = (current_position['entry_price'] - exit_price) * current_position['position_size']
            
            balance += pnl
            
            trade_record = {
                'entry_time': current_position['entry_time'],
                'exit_time': df.index[-1],
                'direction': 'Long' if current_position['direction'] == 1 else 'Short',
                'entry_price': current_position['entry_price'],
                'exit_price': exit_price,
                'position_size': current_position['position_size'],
                'pnl': pnl,
                'exit_reason': 'End of Data',
                'signal_strength': current_position['signal_strength']
            }
            trades.append(trade_record)
        
        return trades, equity_curve, drawdowns, balance
    
    def analyze_results(self, trades, equity_curve, drawdowns, final_balance):
        """Analyze backtest results"""
        print("\n📊 BACKTEST RESULTS")
        print("=" * 60)
        
        if not trades:
            print("❌ No trades generated")
            return
        
        trades_df = pd.DataFrame(trades)
        
        # Basic statistics
        total_trades = len(trades)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        losing_trades = len(trades_df[trades_df['pnl'] < 0])
        win_rate = winning_trades / total_trades * 100
        
        # P&L statistics
        total_pnl = trades_df['pnl'].sum()
        avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
        
        # Performance metrics
        total_return = (final_balance - self.initial_balance) / self.initial_balance * 100
        max_drawdown = max(drawdowns) if drawdowns else 0
        
        # Risk metrics
        profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if losing_trades > 0 else float('inf')
        
        print(f"💰 Initial Balance: ${self.initial_balance:,.2f}")
        print(f"💰 Final Balance: ${final_balance:,.2f}")
        print(f"📈 Total Return: {total_return:.2f}%")
        print(f"📉 Maximum Drawdown: {max_drawdown:.2f}%")
        print()
        print(f"📊 Total Trades: {total_trades}")
        print(f"✅ Winning Trades: {winning_trades}")
        print(f"❌ Losing Trades: {losing_trades}")
        print(f"🎯 Win Rate: {win_rate:.1f}%")
        print()
        print(f"💵 Total P&L: ${total_pnl:,.2f}")
        print(f"📊 Average Win: ${avg_win:.2f}")
        print(f"📊 Average Loss: ${avg_loss:.2f}")
        print(f"⚖️ Profit Factor: {profit_factor:.2f}")
        
        # Trade distribution by direction
        long_trades = len(trades_df[trades_df['direction'] == 'Long'])
        short_trades = len(trades_df[trades_df['direction'] == 'Short'])
        print(f"\n📊 Trade Distribution:")
        print(f"   Long trades: {long_trades}")
        print(f"   Short trades: {short_trades}")
        
        # Exit reason analysis
        print(f"\n📊 Exit Reasons:")
        exit_reasons = trades_df['exit_reason'].value_counts()
        for reason, count in exit_reasons.items():
            print(f"   {reason}: {count} ({count/total_trades*100:.1f}%)")
        
        # Performance assessment
        print(f"\n🎯 PERFORMANCE ASSESSMENT:")
        if total_return > 20 and max_drawdown < 10 and win_rate > 60:
            print("🟢 EXCELLENT: High returns, low drawdown, good win rate")
        elif total_return > 10 and max_drawdown < 15 and win_rate > 50:
            print("🟡 GOOD: Decent performance with acceptable risk")
        elif total_return > 0 and max_drawdown < 20:
            print("🟠 FAIR: Profitable but needs improvement")
        else:
            print("🔴 POOR: Needs significant optimization")
        
        return trades_df
    
    def run_full_backtest(self):
        """Run complete backtest analysis"""
        print("🚀 PROFESSIONAL TRADING SYSTEM BACKTEST")
        print("=" * 60)
        print("Testing rule-based system on historical data")
        print("=" * 60)
        
        # Get data
        df = self.get_historical_data(5000)
        if df is None:
            print("❌ Failed to get historical data")
            return
        
        # Run backtest
        trades, equity_curve, drawdowns, final_balance = self.run_backtest(df)
        
        # Analyze results
        trades_df = self.analyze_results(trades, equity_curve, drawdowns, final_balance)
        
        # Save results
        if trades_df is not None and len(trades_df) > 0:
            os.makedirs('backtest_results', exist_ok=True)
            trades_df.to_csv('backtest_results/professional_system_trades.csv', index=False)
            print(f"\n💾 Results saved to backtest_results/professional_system_trades.csv")
        
        self.mt5_manager.disconnect()
        
        return trades_df

def main():
    """Main function"""
    backtester = ProfessionalBacktester(initial_balance=10000)
    results = backtester.run_full_backtest()

if __name__ == "__main__":
    main()
