"""
LSTM Market Prediction Plotter
Visualizes AI model predictions for trade entry analysis
"""

import numpy as np
import pandas as pd
import json
import pickle
import joblib
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Try to import plotting libraries
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.patches import Rectangle
    import seaborn as sns
    PLOTTING_AVAILABLE = True
    print("Matplotlib available for plotting")
except ImportError:
    PLOTTING_AVAILABLE = False
    print("Matplotlib not available, plotting disabled")

# Import our feature engineering classes
import sys
sys.path.append('.')
from fixed_live_trader import FixedFeatureEngineer, RegimeDetector

class PredictionPlotter:
    """
    Comprehensive prediction plotting system for LSTM market predictions
    """
    
    def __init__(self, model_path='models/best_lstm_model.h5', 
                 sequences_path='data/lstm_sequences/',
                 config_path='models/lstm_market_predictor_config.json'):
        """
        Initialize the prediction plotter
        
        Args:
            model_path: Path to trained LSTM model
            sequences_path: Path to sequence data and scalers
            config_path: Path to model configuration
        """
        self.model_path = model_path
        self.sequences_path = sequences_path
        self.config_path = config_path
        
        # Initialize components
        self.model = None
        self.feature_scaler = None
        self.target_scalers = {}
        self.metadata = None
        self.feature_engineer = FixedFeatureEngineer()
        self.regime_detector = RegimeDetector()
        
        # Load model and scalers
        self._load_model_components()
        
    def _load_model_components(self):
        """Load trained model, scalers, and metadata"""
        try:
            # Import tensorflow here to avoid loading if not needed
            import tensorflow as tf

            print(f"Loading model from {self.model_path}")

            # Load model with custom objects to handle legacy metrics
            custom_objects = {
                'mse': tf.keras.metrics.MeanSquaredError(),
                'mae': tf.keras.metrics.MeanAbsoluteError(),
                'accuracy': tf.keras.metrics.Accuracy()
            }

            self.model = tf.keras.models.load_model(self.model_path, custom_objects=custom_objects)
            print(f"✅ Model loaded successfully")
            
            # Load metadata
            metadata_path = os.path.join(self.sequences_path, 'metadata.json')
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    self.metadata = json.load(f)
                print(f"✅ Metadata loaded: {self.metadata['n_features']} features, {self.metadata['n_targets']} targets")
            
            # Load feature scaler
            feature_scaler_path = os.path.join(self.sequences_path, 'feature_scaler.pkl')
            if os.path.exists(feature_scaler_path):
                self.feature_scaler = joblib.load(feature_scaler_path)
                print(f"✅ Feature scaler loaded")

            # Load target scalers
            target_scalers_path = os.path.join(self.sequences_path, 'target_scalers.pkl')
            if os.path.exists(target_scalers_path):
                self.target_scalers = joblib.load(target_scalers_path)
                print(f"✅ Target scalers loaded: {list(self.target_scalers.keys())}")
                
        except Exception as e:
            print(f"❌ Error loading model components: {e}")
            raise
    
    def prepare_prediction_data(self, df, sequence_length=60):
        """
        Prepare data for prediction using same pipeline as training
        
        Args:
            df: DataFrame with OHLC data
            sequence_length: Length of sequence for LSTM input
            
        Returns:
            Scaled sequence ready for prediction
        """
        try:
            # Apply feature engineering (same as training)
            print("Applying feature engineering...")
            df_features = self.feature_engineer.create_technical_indicators(df.copy())

            # Add regime indicators (same as training)
            print("Adding regime indicators...")
            df_features = self.regime_detector.calculate_regime_indicators(df_features)

            # Add candlestick position analysis (same as training)
            print("Adding candle position analysis...")
            df_features = self.regime_detector.calculate_candle_position(df_features)
            
            # Encode categorical features (same as training)
            print("Encoding categorical features...")
            categorical_mappings = {
                'trend_direction': {'UP': 1, 'DOWN': 0},
                'accurate_trend_direction': {'UP': 1, 'DOWN': 0},
                'regime': {'TRENDING': 2, 'RANGING': 1, 'TRANSITIONAL': 0}
            }

            for col, mapping in categorical_mappings.items():
                if col in df_features.columns:
                    # Create encoded version
                    encoded_col = f"{col}_encoded"
                    df_features[encoded_col] = df_features[col].map(mapping).fillna(0)
                    print(f"  Encoded {col} -> {encoded_col}")
            
            # Select feature columns (same order as training)
            if self.metadata and 'feature_columns' in self.metadata:
                feature_columns = self.metadata['feature_columns']
                # Only select columns that exist in the dataframe
                available_columns = [col for col in feature_columns if col in df_features.columns]
                missing_columns = [col for col in feature_columns if col not in df_features.columns]

                if missing_columns:
                    print(f"⚠️  Missing columns: {missing_columns}")

                df_features = df_features[available_columns]
                print(f"Using {len(available_columns)} features for prediction")
            else:
                # Fallback: use all numeric columns except OHLC and categorical
                exclude_cols = ['open', 'high', 'low', 'close', 'volume', 'trend_direction', 'accurate_trend_direction', 'regime']
                feature_columns = [col for col in df_features.columns if col not in exclude_cols]
                df_features = df_features[feature_columns]
                print(f"Using {len(feature_columns)} features for prediction (fallback)")
            
            # Convert any remaining categorical columns to numeric
            for col in df_features.columns:
                if df_features[col].dtype == 'object':
                    # Convert UP/DOWN to 1/0
                    df_features[col] = df_features[col].map({'UP': 1, 'DOWN': 0}).fillna(0)

            # Handle NaN values
            df_features = df_features.fillna(method='ffill').fillna(0)
            
            # Take last sequence_length rows
            if len(df_features) < sequence_length:
                raise ValueError(f"Need at least {sequence_length} rows, got {len(df_features)}")
            
            sequence_data = df_features.tail(sequence_length).values
            
            # Scale features
            if self.feature_scaler:
                sequence_data = self.feature_scaler.transform(sequence_data)
            
            # Reshape for LSTM: (1, sequence_length, n_features)
            sequence_data = sequence_data.reshape(1, sequence_length, -1)
            
            print(f"✅ Prediction data prepared: {sequence_data.shape}")
            return sequence_data
            
        except Exception as e:
            print(f"❌ Error preparing prediction data: {e}")
            raise
    
    def make_predictions(self, sequence_data):
        """
        Make predictions using the trained model
        
        Args:
            sequence_data: Scaled sequence data for prediction
            
        Returns:
            Dictionary with predictions for each target group
        """
        try:
            print("Making predictions...")
            
            # Get raw predictions from model
            raw_predictions = self.model.predict(sequence_data, verbose=0)
            
            # Model outputs 3 arrays: [price_preds, direction_preds, change_preds]
            price_preds, direction_preds, change_preds = raw_predictions
            
            # Inverse transform predictions using target scalers
            predictions = {}
            
            # Price predictions (percentage-based)
            if 'price' in self.target_scalers:
                price_preds_scaled = self.target_scalers['price'].inverse_transform(price_preds)
                predictions['price_percentages'] = price_preds_scaled[0]  # Remove batch dimension
            else:
                predictions['price_percentages'] = price_preds[0]
            
            # Direction predictions (probabilities)
            predictions['direction_probs'] = direction_preds[0]
            predictions['direction_binary'] = (direction_preds[0] > 0.5).astype(int)
            
            # Change predictions (percentage-based)
            if 'change_pct' in self.target_scalers:
                change_preds_scaled = self.target_scalers['change_pct'].inverse_transform(change_preds)
                predictions['change_percentages'] = change_preds_scaled[0]
            else:
                predictions['change_percentages'] = change_preds[0]
            
            print(f"✅ Predictions made successfully")
            return predictions
            
        except Exception as e:
            print(f"❌ Error making predictions: {e}")
            raise
    
    def convert_predictions_to_prices(self, predictions, current_close):
        """
        Convert percentage-based predictions to actual price levels
        
        Args:
            predictions: Dictionary with percentage-based predictions
            current_close: Current close price to base calculations on
            
        Returns:
            Dictionary with actual price predictions
        """
        try:
            price_predictions = {}
            
            # Convert price percentages to actual prices
            price_percentages = predictions['price_percentages']
            
            # Price percentages are in order: open_1, high_1, low_1, close_1, open_3, high_3, low_3, close_3, open_5, high_5, low_5, close_5
            steps = [1, 3, 5]
            price_types = ['open', 'high', 'low', 'close']
            
            for i, step in enumerate(steps):
                step_prices = {}
                for j, price_type in enumerate(price_types):
                    idx = i * 4 + j  # Index in the flattened array
                    percentage = price_percentages[idx]
                    actual_price = current_close * (1 + percentage)
                    step_prices[price_type] = actual_price
                
                price_predictions[f'step_{step}'] = step_prices
            
            # Add direction and change predictions
            price_predictions['directions'] = {
                'step_1': predictions['direction_binary'][0],
                'step_3': predictions['direction_binary'][1], 
                'step_5': predictions['direction_binary'][2]
            }
            
            price_predictions['direction_probs'] = {
                'step_1': predictions['direction_probs'][0],
                'step_3': predictions['direction_probs'][1],
                'step_5': predictions['direction_probs'][2]
            }
            
            price_predictions['change_percentages'] = {
                'step_1': predictions['change_percentages'][0],
                'step_3': predictions['change_percentages'][1],
                'step_5': predictions['change_percentages'][2]
            }
            
            print(f"✅ Predictions converted to actual prices")
            return price_predictions
            
        except Exception as e:
            print(f"❌ Error converting predictions to prices: {e}")
            raise
    
    def create_prediction_plot(self, df, price_predictions, save_path=None, show_candles=100):
        """
        Create comprehensive prediction plot
        
        Args:
            df: Historical OHLC data
            price_predictions: Actual price predictions
            save_path: Path to save the plot (optional)
            show_candles: Number of historical candles to show
            
        Returns:
            Path to saved plot or None
        """
        if not PLOTTING_AVAILABLE:
            print("❌ Matplotlib not available, cannot create plots")
            return None
            
        try:
            print(f"Creating prediction plot...")
            
            # Prepare data
            df_plot = df.tail(show_candles).copy()
            df_plot['datetime'] = pd.to_datetime(df_plot.index)
            
            # Create figure with subplots
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12), 
                                         gridspec_kw={'height_ratios': [3, 1]})
            
            # Plot 1: Price chart with predictions
            self._plot_price_chart(ax1, df_plot, price_predictions)
            
            # Plot 2: Prediction details
            self._plot_prediction_details(ax2, price_predictions)
            
            # Overall formatting
            plt.tight_layout()
            plt.suptitle(f'LSTM Market Predictions - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}', 
                        fontsize=16, y=0.98)
            
            # Save plot
            if save_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = f'plots/prediction_{timestamp}.png'
            
            # Create plots directory if it doesn't exist
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✅ Plot saved to {save_path}")
            return save_path

        except Exception as e:
            print(f"❌ Error creating prediction plot: {e}")
            return None

    def _plot_price_chart(self, ax, df_plot, price_predictions):
        """Plot candlestick chart with prediction overlays"""
        try:
            # Plot historical candlesticks
            for i, (idx, row) in enumerate(df_plot.iterrows()):
                color = 'green' if row['close'] >= row['open'] else 'red'

                # Candlestick body
                body_height = abs(row['close'] - row['open'])
                body_bottom = min(row['open'], row['close'])

                ax.add_patch(Rectangle((i, body_bottom), 0.6, body_height,
                                     facecolor=color, alpha=0.7))

                # Wicks
                ax.plot([i+0.3, i+0.3], [row['low'], row['high']],
                       color=color, linewidth=1)

            # Plot predicted candles
            last_idx = len(df_plot) - 1
            current_close = df_plot.iloc[-1]['close']

            colors = ['blue', 'orange', 'purple']
            alphas = [0.8, 0.6, 0.4]

            for i, step in enumerate([1, 3, 5]):
                step_key = f'step_{step}'
                if step_key in price_predictions:
                    pred = price_predictions[step_key]
                    x_pos = last_idx + step

                    # Predicted candlestick
                    color = colors[i]
                    body_height = abs(pred['close'] - pred['open'])
                    body_bottom = min(pred['open'], pred['close'])

                    ax.add_patch(Rectangle((x_pos, body_bottom), 0.6, body_height,
                                         facecolor=color, alpha=alphas[i],
                                         label=f'Pred Step {step}'))

                    # Predicted wicks
                    ax.plot([x_pos+0.3, x_pos+0.3], [pred['low'], pred['high']],
                           color=color, linewidth=2, alpha=alphas[i])

                    # Direction arrow
                    direction = price_predictions['directions'][step_key]
                    arrow_y = pred['high'] + (pred['high'] - pred['low']) * 0.1
                    arrow_symbol = '↑' if direction == 1 else '↓'
                    arrow_color = 'green' if direction == 1 else 'red'

                    ax.text(x_pos+0.3, arrow_y, arrow_symbol,
                           fontsize=16, ha='center', color=arrow_color, weight='bold')

            # Formatting
            ax.set_title('Price Chart with LSTM Predictions', fontsize=14, weight='bold')
            ax.set_xlabel('Time Steps')
            ax.set_ylabel('Price')
            ax.grid(True, alpha=0.3)
            ax.legend()

            # Set x-axis limits to show predictions
            ax.set_xlim(-5, len(df_plot) + 8)

        except Exception as e:
            print(f"❌ Error plotting price chart: {e}")

    def _plot_prediction_details(self, ax, price_predictions):
        """Plot prediction details and confidence"""
        try:
            steps = [1, 3, 5]
            x_pos = np.arange(len(steps))

            # Direction probabilities
            direction_probs = [price_predictions['direction_probs'][f'step_{s}'] for s in steps]
            change_pcts = [price_predictions['change_percentages'][f'step_{s}'] * 100 for s in steps]

            # Create bar chart for direction probabilities
            bars = ax.bar(x_pos, direction_probs, alpha=0.7,
                         color=['blue', 'orange', 'purple'])

            # Add change percentage labels on bars
            for i, (bar, change_pct) in enumerate(zip(bars, change_pcts)):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{change_pct:.2f}%', ha='center', va='bottom', fontweight='bold')

            # Formatting
            ax.set_title('Prediction Confidence & Expected Change', fontsize=12, weight='bold')
            ax.set_xlabel('Prediction Steps')
            ax.set_ylabel('Direction Probability')
            ax.set_xticks(x_pos)
            ax.set_xticklabels([f'Step {s}' for s in steps])
            ax.set_ylim(0, 1.1)
            ax.grid(True, alpha=0.3)

            # Add horizontal line at 0.5 (neutral)
            ax.axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='Neutral (50%)')
            ax.legend()

        except Exception as e:
            print(f"❌ Error plotting prediction details: {e}")

    def predict_and_plot(self, df, save_path=None, show_candles=100):
        """
        Complete pipeline: prepare data, make predictions, create plot

        Args:
            df: Historical OHLC data
            save_path: Path to save plot (optional)
            show_candles: Number of historical candles to show

        Returns:
            Tuple of (price_predictions, plot_path)
        """
        try:
            print("=== LSTM PREDICTION & PLOTTING PIPELINE ===")

            # Prepare data for prediction
            sequence_data = self.prepare_prediction_data(df)

            # Make predictions
            predictions = self.make_predictions(sequence_data)

            # Convert to actual prices
            current_close = df.iloc[-1]['close']
            price_predictions = self.convert_predictions_to_prices(predictions, current_close)

            # Create plot
            plot_path = self.create_prediction_plot(df, price_predictions, save_path, show_candles)

            # Print summary
            self._print_prediction_summary(price_predictions, current_close)

            return price_predictions, plot_path

        except Exception as e:
            print(f"❌ Error in prediction pipeline: {e}")
            return None, None

    def _print_prediction_summary(self, price_predictions, current_close):
        """Print a summary of predictions"""
        print("\n=== PREDICTION SUMMARY ===")
        print(f"Current Close: ${current_close:.2f}")

        for step in [1, 3, 5]:
            step_key = f'step_{step}'
            if step_key in price_predictions:
                pred = price_predictions[step_key]
                direction = "UP" if price_predictions['directions'][step_key] == 1 else "DOWN"
                prob = price_predictions['direction_probs'][step_key]
                change_pct = price_predictions['change_percentages'][step_key] * 100

                print(f"\nStep {step} Prediction:")
                print(f"  Direction: {direction} ({prob:.1%} confidence)")
                print(f"  Expected Change: {change_pct:+.2f}%")
                print(f"  Predicted OHLC: O=${pred['open']:.2f} H=${pred['high']:.2f} L=${pred['low']:.2f} C=${pred['close']:.2f}")

        print("=" * 50)
