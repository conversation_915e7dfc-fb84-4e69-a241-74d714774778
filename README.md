# XAUUSD LSTM Trading System

A comprehensive automated trading system for XAUUSD (Gold) using LSTM neural networks with MetaTrader 5 integration.

## Features

- **LSTM Neural Network**: Deep learning model for price prediction
- **Comprehensive Feature Engineering**: 50+ technical indicators and market features
- **Risk Management**: 4% account risk per trade with ATR-based stop losses
- **Live Trading**: Real-time data processing and automated order execution
- **Modular Architecture**: Clean, maintainable, and extensible codebase
- **Comprehensive Testing**: Full test suite for all components
- **Performance Monitoring**: Detailed reporting and analysis

## System Requirements

- Python 3.8+
- MetaTrader 5 terminal
- Windows OS (for MT5 integration)
- Minimum 8GB RAM
- GPU recommended for training (optional)

## Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd freedomv3
```

2. **Create virtual environment**:
```bash
python -m venv venv
venv\Scripts\activate  # Windows
```

3. **Install dependencies**:
```bash
pip install -r requirements.txt
```

4. **Setup MetaTrader 5**:
   - Install MT5 terminal
   - Enable algorithmic trading
   - Allow DLL imports
   - Configure your trading account

## Project Structure

```
freedomv3/
├── config/
│   ├── __init__.py
│   └── config.py              # Configuration settings
├── src/
│   ├── __init__.py
│   ├── data_manager.py        # Data loading and management
│   ├── feature_engineering.py # Technical indicators and features
│   ├── data_preprocessing.py  # Dataset preparation
│   ├── lstm_model.py         # LSTM model architecture
│   ├── training_pipeline.py  # Model training pipeline
│   ├── model_evaluation.py   # Performance evaluation
│   ├── live_data_processor.py # Real-time data processing
│   ├── trading_logic.py      # Trading decision engine
│   ├── risk_management.py    # Risk management system
│   ├── mt5_integration.py    # MetaTrader 5 integration
│   └── live_trading_system.py # Main trading system
├── data/
│   └── XAU_5m_data.csv       # Historical XAUUSD data
├── models/                    # Trained models
├── reports/                   # Performance reports
├── logs/                      # System logs
├── tests/                     # Test files
├── main.py                    # Main execution script
├── test_system.py            # Comprehensive testing
└── README.md
```

## Usage

### 1. Test the System

Before training or trading, run the comprehensive test suite:

```bash
python test_system.py
```

This will validate all system components and ensure everything is working correctly.

### 2. Train the Model

Train the LSTM model using historical data:

```bash
python main.py train
```

This will:
- Load and validate historical XAUUSD data
- Create technical indicators and features
- Prepare balanced datasets
- Train the LSTM model with proper validation
- Generate comprehensive evaluation reports
- Save the trained model and preprocessor

### 3. Update Data (Optional)

Update the historical data with latest MT5 data:

```bash
python main.py update
```

You'll be prompted for MT5 credentials.

### 4. Run Live Trading

Start the live trading system:

```bash
python main.py trade --mt5-login YOUR_LOGIN --mt5-password YOUR_PASSWORD --mt5-server YOUR_SERVER
```

Or use the default model paths:

```bash
python main.py trade --model-path models/lstm_trading_model.h5 --preprocessor-path models/preprocessor.joblib
```

## Configuration

Edit `config/config.py` to customize:

- **Risk Management**: Risk percentage, ATR multiplier, position limits
- **Model Parameters**: LSTM architecture, training parameters
- **Trading Settings**: Signal thresholds, market sessions
- **Data Settings**: Timeframe, symbols, data sources

Key settings:
```python
RISK_PERCENT = 4.0          # Risk 4% per trade
ATR_MULTIPLIER = 1.5        # Stop loss = 1.5 * ATR
SIGNAL_THRESHOLD = 0.6      # Minimum prediction confidence
SEQUENCE_LENGTH = 60        # LSTM input sequence length
```

## Trading Logic

The system implements a sophisticated trading strategy:

1. **Signal Generation**: LSTM model predicts market direction (Hold/Buy/Sell)
2. **Signal Filtering**: Multiple filters ensure high-quality signals:
   - Confidence threshold (60%+)
   - Market regime analysis (trending vs ranging)
   - RSI overbought/oversold conditions
   - Time-based filters (avoid low liquidity periods)
3. **Risk Management**: 
   - Position sizing based on 4% account risk
   - ATR-based stop losses (1.5x ATR)
   - Maximum drawdown protection
   - Emergency stop conditions
4. **Position Management**: 
   - Trailing stops based on ATR
   - Automatic position closure on opposite signals

## Model Architecture

The LSTM model features:
- **Input**: 60 time steps of 50+ technical features
- **Architecture**: 3 LSTM layers (128, 64, 32 units) with dropout
- **Output**: 3-class classification (Hold, Buy, Sell)
- **Regularization**: L1/L2 regularization, batch normalization
- **Training**: Early stopping, learning rate reduction

## Risk Management

Comprehensive risk management includes:
- **Per-trade risk**: Maximum 4% of account balance
- **Daily loss limit**: 10% of account balance
- **Weekly loss limit**: 15% of account balance
- **Maximum drawdown**: 25% limit with emergency stop
- **Position limits**: Maximum 1 open position
- **Margin requirements**: Uses max 80% of available margin

## Performance Monitoring

The system provides detailed monitoring:
- **Real-time metrics**: Win rate, profit factor, Sharpe ratio
- **Risk metrics**: Drawdown, VaR, margin levels
- **Trade analysis**: Entry/exit analysis, signal quality
- **System health**: Error rates, data quality, connectivity

## Testing

Run specific test categories:

```bash
# Test all components
python test_system.py

# Test individual modules
python -m unittest test_system.TestDataManager
python -m unittest test_system.TestFeatureEngineering
python -m unittest test_system.TestLSTMModel
```

## Troubleshooting

### Common Issues

1. **MT5 Connection Failed**:
   - Ensure MT5 terminal is running
   - Check login credentials
   - Verify algorithmic trading is enabled

2. **Model Training Fails**:
   - Check data file exists and is valid
   - Ensure sufficient memory (8GB+ recommended)
   - Verify all dependencies are installed

3. **Low Model Performance**:
   - Increase training data (use more years)
   - Adjust feature engineering parameters
   - Tune model hyperparameters

4. **Trading System Errors**:
   - Check MT5 connection and permissions
   - Verify account has sufficient balance
   - Review risk management settings

### Logs

Check log files for detailed error information:
- `logs/live_trading.log`: Live trading system logs
- `reports/`: Performance and evaluation reports

## Safety Features

The system includes multiple safety mechanisms:
- **Emergency stop**: Automatic halt on excessive losses
- **Risk validation**: All trades validated before execution
- **Error handling**: Comprehensive error handling and recovery
- **Position monitoring**: Continuous position and risk monitoring
- **Graceful shutdown**: Clean shutdown on system signals

## Disclaimer

This trading system is for educational and research purposes. Trading involves substantial risk of loss. Past performance does not guarantee future results. Always test thoroughly on demo accounts before live trading.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review log files for error details
3. Run the test suite to identify issues
4. Ensure all requirements are met

## License

This project is licensed under the MIT License - see the LICENSE file for details.
