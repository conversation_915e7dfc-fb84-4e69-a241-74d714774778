#!/usr/bin/env python3
"""
Test the improved swing detection that selects the best (highest/lowest) swing point
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_best_swing_selection():
    """Test that the algorithm selects the highest swing high and lowest swing low"""
    print("🧪 TESTING BEST SWING POINT SELECTION")
    print("=" * 60)
    
    # Create trader instance
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test Scenario: Multiple swing point candidates where we need to pick the best
    print("\n📊 SCENARIO: Multiple Swing Point Candidates")
    dates = pd.date_range(start='2024-01-01', periods=15, freq='5min')
    
    # Create data with multiple potential swing points
    # - Traditional swing high at index 6 (4218.12) - HIGHEST
    # - Partial swing high at most recent (4216.40) - lower than 4218.12
    # - Traditional swing low at index 10 (4208.74) - LOWEST
    # - Partial swing low at most recent (4210.00) - higher than 4208.74
    
    highs = [
        4200.0, 4205.0, 4210.0, 4215.0, 4217.0,  # Rising to setup
        4217.5, 4218.12, 4216.0, 4214.0, 4212.0,  # Traditional swing HIGH at index 6 (4218.12)
        4210.0, 4208.74, 4211.0, 4213.0, 4216.40  # Traditional swing LOW at index 11, partial high at end
    ]
    
    lows = [
        4197.0, 4202.0, 4207.0, 4212.0, 4214.0,  # Rising
        4214.5, 4215.12, 4213.0, 4211.0, 4209.0,  # 
        4207.0, 4205.74, 4208.0, 4210.0, 4213.40  # Traditional swing LOW at index 11, partial low at end
    ]
    
    closes = [(h + l) / 2 for h, l in zip(highs, lows)]
    opens = closes.copy()
    
    df = pd.DataFrame({
        'time': dates,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'tick_volume': [100] * 15
    }).set_index('time')
    
    print("📈 DATA ANALYSIS:")
    print("Expected Results:")
    print(f"  🎯 Best Swing High: 4218.12 (index 6) - HIGHEST of all candidates")
    print(f"  🎯 Best Swing Low: 4205.74 (index 11) - LOWEST of all candidates")
    print()
    
    print("Potential Candidates:")
    print(f"  📊 Traditional High: 4218.12 (index 6)")
    print(f"  📊 Partial High: 4216.40 (most recent)")
    print(f"  📊 Traditional Low: 4205.74 (index 11)")
    print(f"  📊 Partial Low: 4213.40 (most recent)")
    print()
    
    print("Last 5 candles:")
    for i in range(5):
        idx = -(5-i)
        candle = df.iloc[idx]
        marker = ""
        if idx == -1:
            marker = " ← Most Recent"
        elif len(df) + idx == 6:
            marker = " ← Expected Best High (4218.12)"
        elif len(df) + idx == 11:
            marker = " ← Expected Best Low (4205.74)"
        print(f"  {len(df) + idx:2d}: H={candle['high']:8.2f} L={candle['low']:8.2f}{marker}")
    
    # Run swing detection
    print(f"\n🔍 RUNNING IMPROVED SWING DETECTION:")
    swing_points = trader.find_recent_swing_points(df)
    
    print(f"\n📋 RESULTS:")
    success = True
    
    if swing_points['recent_high']:
        detected_high = swing_points['recent_high']
        expected_high = 4218.12
        print(f"✅ Detected Swing High: {detected_high:.2f}")
        print(f"   📍 Expected: {expected_high:.2f}")
        print(f"   📅 Candles ago: {swing_points['recent_high_candles_ago']}")
        
        if abs(detected_high - expected_high) < 0.01:
            print(f"   🎉 CORRECT: Selected the highest swing point!")
        else:
            print(f"   ❌ INCORRECT: Should have selected {expected_high:.2f}")
            success = False
    else:
        print("❌ No swing high detected")
        success = False
        
    if swing_points['recent_low']:
        detected_low = swing_points['recent_low']
        expected_low = 4205.74
        print(f"✅ Detected Swing Low: {detected_low:.2f}")
        print(f"   📍 Expected: {expected_low:.2f}")
        print(f"   📅 Candles ago: {swing_points['recent_low_candles_ago']}")
        
        if abs(detected_low - expected_low) < 0.01:
            print(f"   🎉 CORRECT: Selected the lowest swing point!")
        else:
            print(f"   ❌ INCORRECT: Should have selected {expected_low:.2f}")
            success = False
    else:
        print("❌ No swing low detected")
        success = False
    
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}: Best swing point selection test")
    print("=" * 60)
    
    if success:
        print("🎉 The algorithm correctly selects the BEST swing points!")
        print("📊 It finds the HIGHEST swing high and LOWEST swing low from all candidates")
        print("🔍 This matches the real XAUUSD scenario you described")
    else:
        print("⚠️  The algorithm needs further adjustment")
        print("🔧 Check the candidate selection and comparison logic")
    
    return success

if __name__ == "__main__":
    test_best_swing_selection()
