#!/usr/bin/env python3
"""
Regime-Based Trading System for XAUUSD M5
Uses ATR + EMA Slope + Volatility to detect trending vs ranging markets
Reverses model signals in trends, follows them in ranges
"""

import os
import sys
import pandas as pd
import numpy as np
import time
from datetime import datetime
import logging
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager

# Import ML libraries
import joblib

class RegimeDetector:
    """Detect market regime: TRENDING, RANGING, or TRANSITIONAL"""
    
    def __init__(self):
        # XAUUSD M5 specific parameters (optimized for gold)
        self.atr_lookback = 100  # 100 periods = ~8 hours
        self.ema_periods = 21    # 21 periods = ~1.75 hours
        self.slope_lookback = 5  # 5 periods = 25 minutes
        self.bb_periods = 20     # Bollinger Bands period
        
        # Thresholds optimized for XAUUSD volatility
        self.trending_atr_threshold = 0.70    # ATR above 70th percentile
        self.ranging_atr_threshold = 0.30     # ATR below 30th percentile
        self.trending_slope_threshold = 0.0008  # EMA slope threshold (0.08%)
        self.ranging_slope_threshold = 0.0003   # EMA slope threshold (0.03%)
        self.bb_width_threshold = 0.35        # BB width percentile
        
    def calculate_regime_indicators(self, df):
        """Calculate all indicators needed for regime detection"""
        df = df.copy()
        
        # ATR (already calculated in main system)
        if 'atr' not in df.columns:
            df['tr1'] = df['high'] - df['low']
            df['tr2'] = abs(df['high'] - df['close'].shift(1))
            df['tr3'] = abs(df['low'] - df['close'].shift(1))
            df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
            df['atr'] = df['true_range'].rolling(14).mean()
        
        # ATR Percentile (key trending indicator)
        df['atr_percentile'] = df['atr'].rolling(self.atr_lookback).rank(pct=True)
        
        # EMA and Slope
        df['ema_21'] = df['close'].ewm(span=self.ema_periods).mean()
        df['ema_slope'] = (df['ema_21'] - df['ema_21'].shift(self.slope_lookback)) / df['ema_21'].shift(self.slope_lookback)
        df['ema_slope_abs'] = abs(df['ema_slope'])
        
        # Bollinger Bands Width (ranging indicator)
        df['bb_middle'] = df['close'].rolling(self.bb_periods).mean()
        bb_std = df['close'].rolling(self.bb_periods).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_width_percentile'] = df['bb_width'].rolling(self.atr_lookback).rank(pct=True)
        
        # Price volatility (additional confirmation)
        df['price_volatility'] = df['close'].rolling(20).std() / df['close']
        df['vol_percentile'] = df['price_volatility'].rolling(self.atr_lookback).rank(pct=True)
        
        # Trend consistency (how consistent is the trend)
        df['ema_direction'] = np.where(df['ema_21'] > df['ema_21'].shift(1), 1, -1)
        df['trend_consistency'] = df['ema_direction'].rolling(10).sum() / 10  # -1 to 1
        
        return df
    
    def detect_regime(self, df):
        """Detect current market regime with confidence score"""
        if len(df) < self.atr_lookback:
            return "INSUFFICIENT_DATA", 0.0, {}
        
        # Get latest values
        latest = df.iloc[-1]
        
        atr_pct = latest['atr_percentile']
        slope_abs = latest['ema_slope_abs']
        bb_width_pct = latest['bb_width_percentile']
        vol_pct = latest['vol_percentile']
        trend_consistency = abs(latest['trend_consistency'])
        
        # Regime scoring system
        trending_score = 0
        ranging_score = 0
        
        # ATR Analysis (most important for XAUUSD)
        if atr_pct > self.trending_atr_threshold:
            trending_score += 3
        elif atr_pct < self.ranging_atr_threshold:
            ranging_score += 3
        
        # EMA Slope Analysis
        if slope_abs > self.trending_slope_threshold:
            trending_score += 2
        elif slope_abs < self.ranging_slope_threshold:
            ranging_score += 2
        
        # Bollinger Band Width (tight bands = ranging)
        if bb_width_pct < self.bb_width_threshold:
            ranging_score += 2
        elif bb_width_pct > 0.65:
            trending_score += 1
        
        # Volatility confirmation
        if vol_pct > 0.7:
            trending_score += 1
        elif vol_pct < 0.3:
            ranging_score += 1
        
        # Trend consistency
        if trend_consistency > 0.6:
            trending_score += 1
        elif trend_consistency < 0.2:
            ranging_score += 1
        
        # Decision logic
        total_score = trending_score + ranging_score
        confidence = max(trending_score, ranging_score) / max(total_score, 1)
        
        if trending_score >= 4 and trending_score > ranging_score:
            regime = "TRENDING"
        elif ranging_score >= 4 and ranging_score > trending_score:
            regime = "RANGING"
        else:
            regime = "TRANSITIONAL"
        
        # Regime details for logging
        details = {
            'atr_percentile': atr_pct,
            'ema_slope': latest['ema_slope'],
            'bb_width_percentile': bb_width_pct,
            'trending_score': trending_score,
            'ranging_score': ranging_score,
            'confidence': confidence
        }
        
        return regime, confidence, details

class RegimeBasedTrader:
    """Main regime-based trading system"""
    
    def __init__(self):
        self.mt5_manager = MT5Manager()
        self.regime_detector = RegimeDetector()
        
        # Load ML model (same as before)
        self.model = None
        self.scaler = None
        self.selected_features = None
        
        # Trading parameters
        self.symbol = "XAUUSD!"
        self.timeframe = "M5"
        self.risk_percent = 4.0
        self.min_confidence = 0.30
        
        # Regime tracking
        self.current_regime = None
        self.regime_confidence = 0.0
        self.last_regime_change = None
        self.current_position = None
        
        self.is_running = False
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/regime_trading.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_model(self):
        """Load the ML model"""
        try:
            self.model = joblib.load('models_fixed/xgboost_model.pkl')
            self.scaler = joblib.load('models_fixed/feature_scaler.pkl')
            self.selected_features = joblib.load('models_fixed/selected_features.pkl')
            
            self.logger.info("✅ ML model loaded successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error loading model: {e}")
            return False
    
    def create_technical_indicators(self, df):
        """Create technical indicators (same as fixed_live_trader)"""
        df = df.copy()
        
        # Basic price features
        df['return_1'] = df['close'].pct_change(1)
        df['return_3'] = df['close'].pct_change(3)
        df['return_5'] = df['close'].pct_change(5)
        
        # Volatility
        df['high_low_pct'] = (df['high'] - df['low']) / df['close']
        df['close_open_pct'] = (df['close'] - df['open']) / df['open']
        
        # Moving averages
        df['sma_5'] = df['close'].rolling(5).mean()
        df['sma_10'] = df['close'].rolling(10).mean()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['sma_50'] = df['close'].rolling(50).mean()
        
        # Price relative to moving averages
        df['price_sma5_ratio'] = df['close'] / df['sma_5']
        df['price_sma20_ratio'] = df['close'] / df['sma_20']
        df['price_sma50_ratio'] = df['close'] / df['sma_50']
        
        # Bollinger Bands
        bb_period = 20
        bb_std = 2
        df['bb_middle'] = df['close'].rolling(bb_period).mean()
        bb_rolling_std = df['close'].rolling(bb_period).std()
        df['bb_upper'] = df['bb_middle'] + (bb_rolling_std * bb_std)
        df['bb_lower'] = df['bb_middle'] - (bb_rolling_std * bb_std)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # RSI
        def calculate_rsi(prices, period=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        df['rsi'] = calculate_rsi(df['close'])
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Rolling min/max
        df['highest_high_5'] = df['high'].rolling(5).max()
        df['lowest_low_5'] = df['low'].rolling(5).min()
        df['highest_high_10'] = df['high'].rolling(10).max()
        df['lowest_low_10'] = df['low'].rolling(10).min()
        df['highest_high_20'] = df['high'].rolling(20).max()
        df['lowest_low_20'] = df['low'].rolling(20).min()
        
        # Price position in recent range
        df['price_position_5'] = (df['close'] - df['lowest_low_5']) / (df['highest_high_5'] - df['lowest_low_5'])
        df['price_position_20'] = (df['close'] - df['lowest_low_20']) / (df['highest_high_20'] - df['lowest_low_20'])
        
        # Momentum indicators
        df['momentum_3'] = df['close'] / df['close'].shift(3)
        df['momentum_5'] = df['close'] / df['close'].shift(5)
        df['momentum_10'] = df['close'] / df['close'].shift(10)
        
        # Stochastic %K
        def calculate_stochastic(high, low, close, k_period=14):
            lowest_low = low.rolling(k_period).min()
            highest_high = high.rolling(k_period).max()
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            return k_percent
        
        df['stoch_k'] = calculate_stochastic(df['high'], df['low'], df['close'])
        df['stoch_d'] = df['stoch_k'].rolling(3).mean()
        
        # Williams %R
        df['williams_r'] = -100 * ((df['highest_high_20'] - df['close']) / (df['highest_high_20'] - df['lowest_low_20']))
        
        # Average True Range (ATR)
        df['tr1'] = df['high'] - df['low']
        df['tr2'] = abs(df['high'] - df['close'].shift(1))
        df['tr3'] = abs(df['low'] - df['close'].shift(1))
        df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
        df['atr'] = df['true_range'].rolling(14).mean()
        df['atr_ratio'] = df['atr'] / df['close']
        
        # Clean up
        df = df.drop(['tr1', 'tr2', 'tr3'], axis=1, errors='ignore')
        
        return df

    def get_model_prediction(self, df):
        """Get ML model prediction"""
        try:
            # Get latest features
            latest_features = df[self.selected_features].iloc[-1]
            latest_features = latest_features.fillna(latest_features.median())

            # Get ATR for position sizing
            latest_atr = df['atr'].iloc[-1]
            if pd.isna(latest_atr):
                latest_atr = df['atr'].dropna().iloc[-1] if len(df['atr'].dropna()) > 0 else 0.01

            # Scale features
            features_scaled = self.scaler.transform(latest_features.values.reshape(1, -1))

            # Make prediction
            pred_proba = self.model.predict_proba(features_scaled)[0, 1]
            confidence = abs(pred_proba - 0.5) * 2

            # Determine raw signal
            if pred_proba > 0.5:
                raw_signal = "BUY"
            else:
                raw_signal = "SELL"

            return raw_signal, confidence, pred_proba, latest_atr

        except Exception as e:
            self.logger.error(f"❌ Error getting model prediction: {e}")
            return None, 0, 0, None

    def apply_regime_logic(self, raw_signal, regime):
        """Apply regime-based signal modification"""
        if regime == "RANGING":
            # In ranging markets: follow model signals
            final_signal = raw_signal
            logic = "FOLLOW (ranging market)"
        elif regime == "TRENDING":
            # In trending markets: reverse model signals (fade the trend)
            final_signal = "SELL" if raw_signal == "BUY" else "BUY"
            logic = "REVERSE (trending market - fade)"
        else:  # TRANSITIONAL
            # In transitional markets: no trading
            final_signal = None
            logic = "NO TRADE (transitional market)"

        return final_signal, logic

    def check_regime_change(self, new_regime):
        """Check if regime has changed and handle position closure"""
        if self.current_regime is None:
            self.current_regime = new_regime
            return False

        if new_regime != self.current_regime:
            self.logger.info(f"🔄 REGIME CHANGE: {self.current_regime} → {new_regime}")

            # Close current position on regime change
            if self.current_position:
                self.logger.info("🔄 Closing position due to regime change")
                self.close_current_position("Regime Change")

            self.current_regime = new_regime
            self.last_regime_change = datetime.now()
            return True

        return False

    def check_current_positions(self):
        """Check if we have any open positions"""
        try:
            positions = self.mt5_manager.get_positions(self.symbol)
            if positions and len(positions) > 0:
                pos = positions[0]
                self.current_position = {
                    'type': 'BUY' if pos['type'] == 0 else 'SELL',
                    'ticket': pos['ticket'],
                    'time': datetime.fromtimestamp(pos['time']),
                    'volume': pos['volume'],
                    'price': pos['price_open']
                }
                return True
            else:
                self.current_position = None
                return False
        except Exception as e:
            self.logger.error(f"❌ Error checking positions: {e}")
            return False

    def close_current_position(self, reason="Manual"):
        """Close the current position"""
        try:
            if not self.current_position:
                return True

            self.logger.info(f"🔄 Closing current {self.current_position['type']} position - {reason}")

            result = self.mt5_manager.close_position(
                ticket=self.current_position['ticket'],
                symbol=self.symbol,
                volume=self.current_position['volume']
            )

            if result:
                self.logger.info("✅ Position closed successfully!")
                self.current_position = None
                return True
            else:
                self.logger.error("❌ Failed to close position")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error closing position: {e}")
            return False

    def calculate_position_size(self, balance, current_price, atr_value):
        """Calculate position size for 4% risk with 1 ATR stop loss"""
        try:
            risk_amount = balance * (self.risk_percent / 100)
            stop_loss_distance = atr_value * 1.0
            risk_per_lot = stop_loss_distance * 100
            lot_size = risk_amount / risk_per_lot
            lot_size = round(lot_size, 2)
            lot_size = max(0.01, min(lot_size, 10.0))
            return lot_size
        except Exception as e:
            self.logger.error(f"❌ Error calculating position size: {e}")
            return 0.01

    def execute_regime_trade(self, signal, confidence, balance, atr_value, regime, logic):
        """Execute trade with regime-based logic"""
        try:
            if signal is None:
                return False

            if confidence < self.min_confidence:
                self.logger.info(f"⚠️ Low confidence ({confidence:.3f}) - Skipping trade")
                return False

            # Check for current positions
            has_position = self.check_current_positions()

            if has_position and self.current_position:
                current_type = self.current_position['type']
                if (current_type == 'BUY' and signal == 'SELL') or (current_type == 'SELL' and signal == 'BUY'):
                    self.logger.info(f"🔄 Opposite signal: Current={current_type}, New={signal}")
                    if not self.close_current_position("Opposite Signal"):
                        return False
                elif current_type == signal:
                    self.logger.info(f"⚠️ Same signal ({signal}) - Already have {current_type} position")
                    return False
            elif has_position:
                self.logger.info(f"⚠️ Already have open position - Only 1 concurrent trade allowed")
                return False

            # Get current price
            tick = self.mt5_manager.get_symbol_info_tick(self.symbol)
            if not tick:
                self.logger.error("❌ Cannot get current price")
                return False

            current_price = tick['ask'] if signal == "BUY" else tick['bid']
            lot_size = self.calculate_position_size(balance, current_price, atr_value)

            # Set stop loss (1 ATR, no take profit)
            if signal == "BUY":
                price = tick['ask']
                stop_loss = price - (atr_value * 1.0)
            else:
                price = tick['bid']
                stop_loss = price + (atr_value * 1.0)

            # Execute order
            self.logger.info(f"📈 REGIME-BASED TRADE:")
            self.logger.info(f"   Signal: {signal} ({logic})")
            self.logger.info(f"   Regime: {regime}")
            self.logger.info(f"   Confidence: {confidence:.3f}")
            self.logger.info(f"   Price: {price:.5f}")
            self.logger.info(f"   Lot Size: {lot_size}")
            self.logger.info(f"   Stop Loss: {stop_loss:.5f} (1 ATR)")

            result = self.mt5_manager.place_order(
                symbol=self.symbol,
                order_type=signal,
                lot_size=lot_size,
                price=price,
                stop_loss=stop_loss,
                take_profit=None,
                comment=f"Regime_{regime}_{confidence:.2f}"
            )

            if result:
                self.logger.info("✅ Regime-based trade executed successfully!")
                self.current_position = {
                    'type': signal,
                    'ticket': result,
                    'time': datetime.now(),
                    'volume': lot_size,
                    'price': price
                }
                return True
            else:
                self.logger.error("❌ Failed to execute trade")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error executing regime trade: {e}")
            return False

    def run_regime_trading(self):
        """Main regime-based trading loop"""
        if not self.mt5_manager.connect():
            self.logger.error("❌ Failed to connect to MT5")
            return

        if not self.load_model():
            self.logger.error("❌ Failed to load ML model")
            return

        self.logger.info("🚀 REGIME-BASED TRADING SYSTEM STARTED")
        self.logger.info("=" * 60)
        self.logger.info("📊 XAUUSD M5 - ATR + EMA Slope Regime Detection")
        self.logger.info("🎯 RANGING: Follow model | TRENDING: Reverse model")
        self.logger.info("🔄 Auto-close on regime changes")
        self.logger.info("=" * 60)

        self.is_running = True

        try:
            while self.is_running:
                # Get account balance
                account_info = self.mt5_manager.get_account_info()
                if not account_info:
                    self.logger.error("❌ Cannot get account info")
                    time.sleep(300)
                    continue

                balance = account_info['balance']

                # Get market data
                df = self.mt5_manager.get_latest_data(self.symbol, self.timeframe, 200)
                if df is None or len(df) < 150:
                    self.logger.warning("⚠️ Insufficient market data")
                    time.sleep(300)
                    continue

                # Create technical indicators
                df = self.create_technical_indicators(df)

                # Add regime indicators
                df = self.regime_detector.calculate_regime_indicators(df)

                # Detect current regime
                regime, regime_conf, regime_details = self.regime_detector.detect_regime(df)

                # Check for regime change
                regime_changed = self.check_regime_change(regime)

                # Get model prediction
                raw_signal, confidence, pred_proba, atr_value = self.get_model_prediction(df)

                if raw_signal:
                    # Apply regime logic
                    final_signal, logic = self.apply_regime_logic(raw_signal, regime)

                    # Log current status
                    self.logger.info(f"📊 MARKET ANALYSIS ({datetime.now().strftime('%H:%M:%S')})")
                    self.logger.info(f"   Regime: {regime} (Confidence: {regime_conf:.2f})")
                    self.logger.info(f"   Raw Signal: {raw_signal} (Conf: {confidence:.3f})")
                    self.logger.info(f"   Final Signal: {final_signal} ({logic})")
                    self.logger.info(f"   ATR: {atr_value:.5f}")

                    # Execute trade if signal is valid
                    if final_signal and confidence >= self.min_confidence:
                        self.execute_regime_trade(final_signal, confidence, balance, atr_value, regime, logic)
                    elif final_signal is None:
                        self.logger.info("⚠️ No trade - Transitional market")
                    else:
                        self.logger.info(f"⚠️ Low confidence - No trade")

                # Wait 5 minutes before next analysis
                time.sleep(300)

        except KeyboardInterrupt:
            self.logger.info("🛑 Regime trading stopped by user")
        except Exception as e:
            self.logger.error(f"❌ Error in regime trading: {e}")
        finally:
            self.mt5_manager.disconnect()

def main():
    """Main function"""
    print("🚀 REGIME-BASED TRADING SYSTEM")
    print("=" * 60)
    print("📊 XAUUSD M5 - Smart Regime Detection")
    print("🎯 RANGING: Follow Model Signals")
    print("🔄 TRENDING: Reverse Model Signals (Fade)")
    print("🛑 TRANSITIONAL: No Trading")
    print("🔄 Auto-close on Regime Changes")
    print("💰 Risk: 4% | Stop: 1 ATR | No TP")
    print("=" * 60)

    trader = RegimeBasedTrader()
    trader.run_regime_trading()

if __name__ == "__main__":
    main()
