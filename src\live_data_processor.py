"""
Live Data Processing System
Handles real-time data processing for live trading predictions
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
import time
import threading
from queue import Queue
import joblib

from config.config import *
from src.mt5_integration import MT5Manager
from src.feature_engineering import FeatureEngineer
from src.lstm_model import LSTMTradingModel

# Set up logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

class LiveDataProcessor:
    """
    Processes live market data for real-time predictions
    """
    
    def __init__(self):
        self.mt5_manager = MT5Manager()
        self.feature_engineer = FeatureEngineer()
        self.model = None
        self.is_running = False
        self.data_queue = Queue()
        self.prediction_queue = Queue()
        self.current_data = pd.DataFrame()
        self.last_update = None
        
    def initialize(self, model_path: str, preprocessor_path: str,
                  login: Optional[int] = None, password: Optional[str] = None, 
                  server: Optional[str] = None) -> bool:
        """
        Initialize live data processor
        
        Args:
            model_path: Path to trained model
            preprocessor_path: Path to fitted preprocessor
            login: MT5 login
            password: MT5 password
            server: MT5 server
            
        Returns:
            bool: True if initialization successful
        """
        try:
            logger.info("Initializing live data processor...")
            
            # Connect to MT5
            if not self.mt5_manager.connect(login, password, server):
                logger.error("Failed to connect to MT5")
                return False
            
            # Load trained model
            self.model = LSTMTradingModel(input_shape=(SEQUENCE_LENGTH, 1), num_classes=3)
            self.model.load_model(model_path)
            
            if self.model.model is None:
                logger.error("Failed to load trained model")
                return False
            
            # Load fitted preprocessor
            try:
                preprocessor = joblib.load(preprocessor_path)
                self.feature_engineer = preprocessor.feature_engineer
                logger.info("Loaded fitted preprocessor")
            except Exception as e:
                logger.error(f"Failed to load preprocessor: {e}")
                return False
            
            # Initialize data buffer with historical data
            if not self._initialize_data_buffer():
                logger.error("Failed to initialize data buffer")
                return False
            
            logger.info("Live data processor initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing live data processor: {e}")
            return False
    
    def _initialize_data_buffer(self) -> bool:
        """Initialize data buffer with recent historical data"""
        try:
            # Get enough historical data to calculate features
            buffer_size = SEQUENCE_LENGTH + 100  # Extra data for feature calculation
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=buffer_size * 5 / 60)  # 5-minute bars
            
            historical_data = self.mt5_manager.get_historical_data(
                SYMBOL, TIMEFRAME, start_time, end_time
            )
            
            if historical_data is None or len(historical_data) < buffer_size:
                logger.error("Insufficient historical data for initialization")
                return False
            
            # Create features for historical data
            data_with_features = self.feature_engineer.create_technical_indicators(historical_data)
            
            # Store the processed data
            self.current_data = data_with_features
            self.last_update = self.current_data.index[-1]
            
            logger.info(f"Initialized data buffer with {len(self.current_data)} records")
            logger.info(f"Data range: {self.current_data.index[0]} to {self.current_data.index[-1]}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error initializing data buffer: {e}")
            return False
    
    def start_live_processing(self):
        """Start live data processing in background thread"""
        try:
            if self.is_running:
                logger.warning("Live processing already running")
                return
            
            self.is_running = True
            
            # Start data collection thread
            data_thread = threading.Thread(target=self._data_collection_loop, daemon=True)
            data_thread.start()
            
            # Start prediction thread
            prediction_thread = threading.Thread(target=self._prediction_loop, daemon=True)
            prediction_thread.start()
            
            logger.info("Live data processing started")
            
        except Exception as e:
            logger.error(f"Error starting live processing: {e}")
            self.is_running = False
    
    def stop_live_processing(self):
        """Stop live data processing"""
        self.is_running = False
        logger.info("Live data processing stopped")
    
    def _data_collection_loop(self):
        """Main data collection loop"""
        try:
            while self.is_running:
                try:
                    # Get latest data from MT5
                    latest_data = self.mt5_manager.get_latest_data(SYMBOL, TIMEFRAME, 1)
                    
                    if latest_data is not None and len(latest_data) > 0:
                        latest_timestamp = latest_data.index[-1]
                        
                        # Check if we have new data
                        if self.last_update is None or latest_timestamp > self.last_update:
                            # Update data buffer
                            self._update_data_buffer(latest_data)
                            
                            # Add to processing queue
                            self.data_queue.put(latest_timestamp)
                            
                            self.last_update = latest_timestamp
                            logger.debug(f"New data received: {latest_timestamp}")
                    
                    # Wait for next update
                    time.sleep(UPDATE_FREQUENCY)
                    
                except Exception as e:
                    logger.error(f"Error in data collection loop: {e}")
                    time.sleep(10)  # Wait before retrying
            
        except Exception as e:
            logger.error(f"Fatal error in data collection loop: {e}")
    
    def _update_data_buffer(self, new_data: pd.DataFrame):
        """Update the data buffer with new data"""
        try:
            # Add features to new data
            new_data_with_features = self.feature_engineer.create_technical_indicators(
                pd.concat([self.current_data.tail(100), new_data])
            ).tail(len(new_data))
            
            # Append to current data
            self.current_data = pd.concat([self.current_data, new_data_with_features])
            
            # Keep only necessary data (sequence length + buffer)
            max_rows = SEQUENCE_LENGTH + 200
            if len(self.current_data) > max_rows:
                self.current_data = self.current_data.tail(max_rows)
            
            logger.debug(f"Data buffer updated. Size: {len(self.current_data)}")
            
        except Exception as e:
            logger.error(f"Error updating data buffer: {e}")
    
    def _prediction_loop(self):
        """Main prediction loop"""
        try:
            while self.is_running:
                try:
                    # Wait for new data
                    if not self.data_queue.empty():
                        timestamp = self.data_queue.get()
                        
                        # Make prediction
                        prediction_result = self._make_prediction()
                        
                        if prediction_result:
                            prediction_result['timestamp'] = timestamp
                            self.prediction_queue.put(prediction_result)
                            
                            logger.info(f"Prediction made for {timestamp}: {prediction_result}")
                    
                    time.sleep(1)  # Check queue every second
                    
                except Exception as e:
                    logger.error(f"Error in prediction loop: {e}")
                    time.sleep(5)
            
        except Exception as e:
            logger.error(f"Fatal error in prediction loop: {e}")
    
    def _make_prediction(self) -> Optional[Dict]:
        """Make prediction using current data"""
        try:
            if len(self.current_data) < SEQUENCE_LENGTH:
                logger.warning("Insufficient data for prediction")
                return None
            
            # Prepare data for prediction
            feature_columns = self.feature_engineer.get_feature_importance_names()
            
            if not feature_columns:
                logger.error("No feature columns available")
                return None
            
            # Get latest sequence
            latest_data = self.current_data.tail(SEQUENCE_LENGTH + 50)  # Extra for feature calculation
            
            # Scale features
            scaled_data = self.feature_engineer.scale_features(
                latest_data, feature_columns, fit_scaler=False
            )
            
            if scaled_data is None or len(scaled_data) < SEQUENCE_LENGTH:
                logger.error("Failed to scale features for prediction")
                return None
            
            # Create sequence
            features = scaled_data[feature_columns].values
            if len(features) < SEQUENCE_LENGTH:
                logger.error("Insufficient features for sequence creation")
                return None
            
            sequence = features[-SEQUENCE_LENGTH:].reshape(1, SEQUENCE_LENGTH, len(feature_columns))
            
            # Make prediction
            prediction_proba = self.model.predict_proba(sequence)
            prediction_class = self.model.predict(sequence)
            
            if len(prediction_proba) == 0 or len(prediction_class) == 0:
                logger.error("Model prediction failed")
                return None
            
            # Get current market data for context
            current_price = self.current_data['close'].iloc[-1]
            current_atr = self.current_data['atr'].iloc[-1] if 'atr' in self.current_data.columns else 0
            
            # Prepare result
            result = {
                'prediction_class': int(prediction_class[0]),
                'prediction_probabilities': prediction_proba[0].tolist(),
                'confidence': float(np.max(prediction_proba[0])),
                'current_price': float(current_price),
                'current_atr': float(current_atr),
                'signal_strength': self._calculate_signal_strength(prediction_proba[0]),
                'market_context': self._get_market_context()
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error making prediction: {e}")
            return None
    
    def _calculate_signal_strength(self, probabilities: np.ndarray) -> str:
        """Calculate signal strength based on prediction confidence"""
        try:
            max_prob = np.max(probabilities)
            
            if max_prob > 0.8:
                return "STRONG"
            elif max_prob > 0.6:
                return "MODERATE"
            elif max_prob > 0.5:
                return "WEAK"
            else:
                return "NO_SIGNAL"
                
        except Exception as e:
            logger.error(f"Error calculating signal strength: {e}")
            return "UNKNOWN"
    
    def _get_market_context(self) -> Dict:
        """Get current market context"""
        try:
            if len(self.current_data) < 20:
                return {}
            
            recent_data = self.current_data.tail(20)
            
            context = {
                'trend_direction': 'UP' if recent_data['close'].iloc[-1] > recent_data['close'].iloc[-10] else 'DOWN',
                'volatility_regime': 'HIGH' if 'high_volatility' in recent_data.columns and recent_data['high_volatility'].iloc[-1] else 'LOW',
                'market_regime': 'TRENDING' if 'market_regime' in recent_data.columns and recent_data['market_regime'].iloc[-1] else 'RANGING',
                'rsi_level': float(recent_data['rsi'].iloc[-1]) if 'rsi' in recent_data.columns else None,
                'price_vs_ema': float(recent_data['price_vs_ema_fast'].iloc[-1]) if 'price_vs_ema_fast' in recent_data.columns else None
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting market context: {e}")
            return {}
    
    def get_latest_prediction(self) -> Optional[Dict]:
        """Get the latest prediction from queue"""
        try:
            if not self.prediction_queue.empty():
                return self.prediction_queue.get()
            return None
            
        except Exception as e:
            logger.error(f"Error getting latest prediction: {e}")
            return None
    
    def get_current_market_data(self) -> Optional[Dict]:
        """Get current market data summary"""
        try:
            if len(self.current_data) == 0:
                return None
            
            latest = self.current_data.iloc[-1]
            
            market_data = {
                'timestamp': latest.name,
                'open': float(latest['open']),
                'high': float(latest['high']),
                'low': float(latest['low']),
                'close': float(latest['close']),
                'volume': float(latest['volume']),
                'atr': float(latest['atr']) if 'atr' in latest else None,
                'rsi': float(latest['rsi']) if 'rsi' in latest else None,
                'ema_fast': float(latest['ema_fast']) if 'ema_fast' in latest else None,
                'ema_slow': float(latest['ema_slow']) if 'ema_slow' in latest else None
            }
            
            return market_data
            
        except Exception as e:
            logger.error(f"Error getting current market data: {e}")
            return None
    
    def is_market_open(self) -> bool:
        """Check if market is open for trading"""
        try:
            current_time = datetime.now()
            
            # Forex market is open 24/5 (Sunday 5 PM EST to Friday 5 PM EST)
            # Simplified check - avoid weekends
            if current_time.weekday() >= 5:  # Saturday = 5, Sunday = 6
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking market status: {e}")
            return False
    
    def get_system_status(self) -> Dict:
        """Get system status information"""
        try:
            status = {
                'is_running': self.is_running,
                'mt5_connected': self.mt5_manager.connected,
                'data_buffer_size': len(self.current_data),
                'last_update': self.last_update,
                'pending_predictions': self.prediction_queue.qsize(),
                'pending_data': self.data_queue.qsize(),
                'market_open': self.is_market_open()
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {}
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            self.stop_live_processing()
            self.mt5_manager.disconnect()
            logger.info("Live data processor cleaned up")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
