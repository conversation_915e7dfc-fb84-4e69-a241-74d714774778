#!/usr/bin/env python3
"""
Fixed Live Trading System
Uses the properly rebuilt model with all issues resolved
"""

import os
import sys
import pandas as pd
import numpy as np
import time
import threading
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Tuple, Optional
import logging
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager
from trade_logger import TradeLogger

# Import ML libraries
import joblib

# Import QQE Indicator
from qqe_indicator import QQEIndicator

# Import Simple Regression Channel
from enhanced_regime_detector import EnhancedRegimeDetector
from simple_regression_channel import SimpleRegressionChannel

class FixedFeatureEngineer:
    """Fixed feature engineering - same as rebuild system"""
    
    def create_technical_indicators(self, df):
        """Create technical indicators with proper calculations"""
        df = df.copy()
        
        # Basic price features
        df['return_1'] = df['close'].pct_change(1)
        df['return_3'] = df['close'].pct_change(3)
        df['return_5'] = df['close'].pct_change(5)
        
        # Volatility
        df['high_low_pct'] = (df['high'] - df['low']) / df['close']
        df['close_open_pct'] = (df['close'] - df['open']) / df['open']
        
        # Moving averages (MIDTERM: Longer periods)
        df['sma_5'] = df['close'].rolling(8).mean()   # Slightly longer
        df['sma_10'] = df['close'].rolling(13).mean() # Slightly longer
        df['sma_20'] = df['close'].rolling(30).mean() # Longer for midterm
        df['sma_50'] = df['close'].rolling(60).mean() # Longer for midterm
        
        # Price relative to moving averages
        df['price_sma5_ratio'] = df['close'] / df['sma_5']
        df['price_sma20_ratio'] = df['close'] / df['sma_20']
        df['price_sma50_ratio'] = df['close'] / df['sma_50']
        
        # Bollinger Bands (MIDTERM: Increased period for more stable bands)
        bb_period = 30
        bb_std = 2
        df['bb_middle'] = df['close'].rolling(bb_period).mean()
        bb_rolling_std = df['close'].rolling(bb_period).std()
        df['bb_upper'] = df['bb_middle'] + (bb_rolling_std * bb_std)
        df['bb_lower'] = df['bb_middle'] - (bb_rolling_std * bb_std)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # RSI
        def calculate_rsi(prices, period=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        df['rsi'] = calculate_rsi(df['close'])
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Rolling min/max (FIXED)
        df['highest_high_5'] = df['high'].rolling(5).max()
        df['lowest_low_5'] = df['low'].rolling(5).min()
        df['highest_high_10'] = df['high'].rolling(15).max()  # MIDTERM: Longer periods
        df['lowest_low_10'] = df['low'].rolling(15).min()
        df['highest_high_20'] = df['high'].rolling(30).max()
        df['lowest_low_20'] = df['low'].rolling(30).min()
        
        # Price position in recent range
        df['price_position_5'] = (df['close'] - df['lowest_low_5']) / (df['highest_high_5'] - df['lowest_low_5'])
        df['price_position_20'] = (df['close'] - df['lowest_low_20']) / (df['highest_high_20'] - df['lowest_low_20'])
        
        # Volume features (MIDTERM: Longer period) - Enhanced for regime detection
        if 'volume' in df.columns:
            df['volume_sma'] = df['volume'].rolling(30).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']

            # Additional volume indicators for regime detection
            df['volume_sma_short'] = df['volume'].rolling(10).mean()
            df['volume_trend'] = np.where(
                df['volume_sma_short'] > df['volume_sma_short'].shift(5), 1, -1
            )
            df['volume_momentum'] = df['volume'].pct_change(periods=3)
            df['volume_volatility'] = df['volume'].rolling(20).std() / df['volume_sma']
        else:
            # Default values if no volume data
            df['volume_sma'] = 1.0
            df['volume_ratio'] = 1.0
            df['volume_sma_short'] = 1.0
            df['volume_trend'] = 0
            df['volume_momentum'] = 0.0
            df['volume_volatility'] = 0.0
        
        # Momentum indicators
        df['momentum_3'] = df['close'] / df['close'].shift(3)
        df['momentum_5'] = df['close'] / df['close'].shift(5)
        df['momentum_10'] = df['close'] / df['close'].shift(10)
        
        # Stochastic %K
        def calculate_stochastic(high, low, close, k_period=14):
            lowest_low = low.rolling(k_period).min()
            highest_high = high.rolling(k_period).max()
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            return k_percent
        
        df['stoch_k'] = calculate_stochastic(df['high'], df['low'], df['close'])
        df['stoch_d'] = df['stoch_k'].rolling(3).mean()
        
        # Williams %R
        df['williams_r'] = -100 * ((df['highest_high_20'] - df['close']) / (df['highest_high_20'] - df['lowest_low_20']))
        
        # Average True Range (ATR)
        df['tr1'] = df['high'] - df['low']
        df['tr2'] = abs(df['high'] - df['close'].shift(1))
        df['tr3'] = abs(df['low'] - df['close'].shift(1))
        df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
        df['atr'] = df['true_range'].rolling(21).mean()  # MIDTERM: Longer ATR period
        df['atr_ratio'] = df['atr'] / df['close']
        
        # Clean up
        df = df.drop(['tr1', 'tr2', 'tr3'], axis=1, errors='ignore')
        
        return df

class RegimeDetector:
    """Enhanced Market Regime Detector - Optimized for 5M Short-term Trading"""

    def __init__(self):
        # Setup logging
        self.logger = logging.getLogger(__name__)

        # MIDTERM M5 parameters (optimized for midterm trend detection)
        self.atr_lookback = 100      # 100 periods = ~8.3 hours (more stable)
        self.ema_fast_periods = 21   # 21 periods = ~1.75 hours (slower, more stable)
        self.ema_slow_periods = 34   # 34 periods = ~2.8 hours (stronger confirmation)
        self.slope_lookback = 8      # 8 periods = 40 minutes (more stable slope)
        self.bb_periods = 30         # Bollinger Bands period (more stable bands)
        self.rsi_periods = 21        # RSI for momentum detection (less sensitive)

        # ENHANCED Thresholds (XAUUSD 5m optimized - more sensitive to detect actual trends)
        # RATIONALE: Previous thresholds were too high for XAUUSD 5-minute data, causing trending markets
        # to be classified as ranging. New thresholds are calibrated for XAUUSD price movements.
        self.trending_atr_threshold = 0.55    # ATR above 55th percentile (unchanged)
        self.ranging_atr_threshold = 0.40     # ATR below 40th percentile (unchanged)
        self.trending_slope_threshold = 0.0006  # EMA slope threshold (0.06% - was 0.12%, XAUUSD optimized)
        self.ranging_slope_threshold = 0.0002   # EMA slope threshold (0.02% - was 0.04%, XAUUSD optimized)
        self.bb_width_threshold = 0.40        # BB width percentile (unchanged)
        self.bb_squeeze_threshold = 0.25      # BB squeeze detection (unchanged)

        # ENHANCED: Trend detection thresholds (XAUUSD 5m optimized)
        self.fast_slope_threshold = 0.0004    # Fast EMA slope (0.04% - was 0.08%, XAUUSD optimized)
        self.momentum_threshold = 0.60        # RSI momentum threshold (unchanged)

        # NEW: Candlestick approval thresholds
        self.long_candle_threshold = 0.60     # Close above 60% of candle range for longs
        self.short_candle_threshold = 0.40    # Close below 40% of candle range for shorts

        # NEW: Bull/Bear acceleration tracking (Phase 1: Monitoring)
        self.bull_strength_history = []  # Track last 3 bull strength values
        self.bear_strength_history = []  # Track last 3 bear strength values
        self.bull_velocity_history = []  # Track last 2 velocity values
        self.bear_velocity_history = []  # Track last 2 velocity values

        # NEW: Swing point distance filtering
        self.swing_distance_atr_threshold = 1.5  # Maximum distance from recent swing point (in ATR)

    def calculate_regime_indicators(self, df):
        """ENHANCED: Calculate all indicators needed for regime detection with faster response"""
        df = df.copy()

        # ENHANCED: ATR Percentile with faster lookback
        df['atr_percentile'] = df['atr'].rolling(self.atr_lookback).rank(pct=True)

        # ENHANCED: Dual EMA system for faster trend detection
        df['ema_fast'] = df['close'].ewm(span=self.ema_fast_periods).mean()
        df['ema_slow'] = df['close'].ewm(span=self.ema_slow_periods).mean()

        # Fast EMA slope for early trend detection
        df['ema_fast_slope'] = (df['ema_fast'] - df['ema_fast'].shift(self.slope_lookback)) / df['ema_fast'].shift(self.slope_lookback)
        df['ema_fast_slope_abs'] = abs(df['ema_fast_slope'])

        # Slow EMA slope for confirmation
        df['ema_slow_slope'] = (df['ema_slow'] - df['ema_slow'].shift(self.slope_lookback)) / df['ema_slow'].shift(self.slope_lookback)
        df['ema_slow_slope_abs'] = abs(df['ema_slow_slope'])

        # Legacy compatibility
        df['ema_21'] = df['ema_slow']
        df['ema_slope'] = df['ema_slow_slope']
        df['ema_slope_abs'] = df['ema_slow_slope_abs']

        # NEW: RSI for momentum detection
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_periods).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_periods).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi_momentum'] = np.where(df['rsi'] > 50, 'UP', 'DOWN')

        # NEW: Price momentum over multiple timeframes
        df['momentum_3'] = (df['close'] - df['close'].shift(3)) / df['close'].shift(3)
        df['momentum_5'] = (df['close'] - df['close'].shift(5)) / df['close'].shift(5)
        df['momentum_8'] = (df['close'] - df['close'].shift(8)) / df['close'].shift(8)

        # Combined momentum score
        df['momentum_score'] = (
            np.where(df['momentum_3'] > 0, 1, -1) +
            np.where(df['momentum_5'] > 0, 1, -1) +
            np.where(df['momentum_8'] > 0, 1, -1)
        )

        # Bollinger Bands Width (ranging indicator)
        if 'bb_middle' not in df.columns:
            df['bb_middle'] = df['close'].rolling(self.bb_periods).mean()
            bb_std = df['close'].rolling(self.bb_periods).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
            df['bb_lower'] = df['bb_middle'] - (bb_std * 2)

        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_width_percentile'] = df['bb_width'].rolling(self.atr_lookback).rank(pct=True)

        # Price volatility (additional confirmation) - MIDTERM: Longer period
        df['price_volatility'] = df['close'].rolling(30).std() / df['close']
        df['vol_percentile'] = df['price_volatility'].rolling(self.atr_lookback).rank(pct=True)

        # NEW: Regression Channel Slope Analysis (20-period lookback for XAUUSD 5m)
        def calculate_regression_slope(prices, periods=20):
            """Calculate linear regression slope over specified periods with DEBUG"""
            slopes = []
            for i in range(len(prices)):
                if i < periods - 1:
                    slopes.append(np.nan)
                else:
                    # Get the last 'periods' prices
                    y_values = prices.iloc[i-periods+1:i+1].values
                    x_values = np.arange(periods)

                    # Calculate linear regression slope
                    if len(y_values) == periods and not np.any(np.isnan(y_values)):
                        # FIXED: Use manual slope calculation instead of polyfit
                        # Manual slope is more intuitive and matches visual chart analysis
                        first_price = y_values[0]
                        last_price = y_values[-1]

                        # Calculate slope as price change per time period
                        slope = (last_price - first_price) / (periods - 1)

                        # Normalize slope as percentage change per period
                        # Use the average price for normalization to avoid division by extreme values
                        avg_price = np.mean(y_values)
                        normalized_slope = (slope / avg_price) * 100 if avg_price != 0 else 0

                        # DEBUG: Log calculation for the last row only
                        if i == len(prices) - 1:
                            simple_change = ((last_price - first_price) / first_price) * 100
                            expected_slope_sign = "UP" if simple_change > 0 else "DOWN"
                            actual_slope_sign = "UP" if normalized_slope > 0 else "DOWN"

                            # REMOVED: Verbose regression debug logging

                        slopes.append(normalized_slope)
                    else:
                        slopes.append(np.nan)
            return pd.Series(slopes, index=prices.index)

        # Calculate regression slope (LONG-TERM: 20 candles)
        df['regression_slope'] = calculate_regression_slope(df['close'], periods=20)
        df['regression_slope_abs'] = abs(df['regression_slope'])

        # NEW: Calculate SHORT-TERM regression slope (10 candles)
        df['regression_slope_short'] = calculate_regression_slope(df['close'], periods=10)
        df['regression_slope_short_abs'] = abs(df['regression_slope_short'])

        # Regression slope strength classification for XAUUSD 5m (FURTHER OPTIMIZED)
        # Based on actual XAUUSD 5m data analysis: 0.005% = weak, 0.015% = moderate, 0.03%+ = strong
        df['regression_strength'] = np.where(
            df['regression_slope_abs'] >= 0.03, 'STRONG',     # Was 0.06% - now 0.03% (XAUUSD optimized)
            np.where(df['regression_slope_abs'] >= 0.015, 'MODERATE',  # Was 0.035% - now 0.015% (XAUUSD optimized)
                    np.where(df['regression_slope_abs'] >= 0.005, 'WEAK', 'FLAT'))  # Was 0.015% - now 0.005% (XAUUSD optimized)
        )

        # NEW: Short-term regression strength classification (same thresholds)
        df['regression_strength_short'] = np.where(
            df['regression_slope_short_abs'] >= 0.03, 'STRONG',
            np.where(df['regression_slope_short_abs'] >= 0.015, 'MODERATE',
                    np.where(df['regression_slope_short_abs'] >= 0.005, 'WEAK', 'FLAT'))
        )

        # NEW: Determine short-term trend direction
        df['regression_trend_short'] = np.where(df['regression_slope_short'] > 0, 'UP', 'DOWN')

        # NEW: Calculate 100-period regression slope for regime detection
        df['regression_slope_regime'] = calculate_regression_slope(df['close'], periods=100)
        df['regression_slope_regime_abs'] = abs(df['regression_slope_regime'])
        df['regression_trend_regime'] = np.where(df['regression_slope_regime'] > 0, 'UP', 'DOWN')

        # NEW: Regression Channel Bands for ranging market filtering
        def calculate_regression_channel(prices, periods=20):
            """Calculate regression channel with upper and lower bounds"""
            regression_lines = []
            upper_bounds = []
            lower_bounds = []
            positions = []

            for i in range(len(prices)):
                if i < periods - 1:
                    regression_lines.append(np.nan)
                    upper_bounds.append(np.nan)
                    lower_bounds.append(np.nan)
                    positions.append(np.nan)
                else:
                    # Get the last 'periods' prices
                    y_values = prices.iloc[i-periods+1:i+1].values
                    x_values = np.arange(periods)

                    if len(y_values) == periods and not np.any(np.isnan(y_values)):
                        # Calculate linear regression
                        slope, intercept = np.polyfit(x_values, y_values, 1)

                        # Calculate regression line value at current point
                        regression_value = slope * (periods - 1) + intercept
                        regression_lines.append(regression_value)

                        # Calculate residuals (distance from regression line)
                        regression_line_values = slope * x_values + intercept
                        residuals = y_values - regression_line_values
                        std_residual = np.std(residuals)

                        # Create channel bounds (±2 standard deviations)
                        upper_bound = regression_value + (2 * std_residual)
                        lower_bound = regression_value - (2 * std_residual)

                        upper_bounds.append(upper_bound)
                        lower_bounds.append(lower_bound)

                        # Calculate position within channel (0 = lower bound, 1 = upper bound)
                        current_price = y_values[-1]
                        if upper_bound != lower_bound:
                            position = (current_price - lower_bound) / (upper_bound - lower_bound)
                        else:
                            position = 0.5  # Middle if no range
                        positions.append(position)
                    else:
                        regression_lines.append(np.nan)
                        upper_bounds.append(np.nan)
                        lower_bounds.append(np.nan)
                        positions.append(np.nan)

            return (pd.Series(regression_lines, index=prices.index),
                    pd.Series(upper_bounds, index=prices.index),
                    pd.Series(lower_bounds, index=prices.index),
                    pd.Series(positions, index=prices.index))

        # Calculate regression channels (short-term, long-term, and regime-specific)
        df['regression_line'], df['regression_upper'], df['regression_lower'], df['regression_position'] = calculate_regression_channel(df['close'], periods=20)
        df['regression_line_short'], df['regression_upper_short'], df['regression_lower_short'], df['regression_position_short'] = calculate_regression_channel(df['close'], periods=10)

        # NEW: 100-period regression channel specifically for regime detection
        df['regression_line_regime'], df['regression_upper_regime'], df['regression_lower_regime'], df['regression_position_regime'] = calculate_regression_channel(df['close'], periods=100)

        # Bollinger Band Squeeze Detection
        df['bb_squeeze'] = df['bb_width_percentile'] < self.bb_squeeze_threshold

        # ENHANCED: Multi-factor trend direction
        df['ema_fast_trend'] = np.where(df['ema_fast_slope'] > 0, 'UP', 'DOWN')
        df['ema_slow_trend'] = np.where(df['ema_slow_slope'] > 0, 'UP', 'DOWN')
        df['ema_cross_trend'] = np.where(df['ema_fast'] > df['ema_slow'], 'UP', 'DOWN')

        # Price trend (higher highs/lower lows)
        df['price_trend_3'] = np.where(df['close'] > df['close'].shift(3), 'UP', 'DOWN')
        df['price_trend_5'] = np.where(df['close'] > df['close'].shift(5), 'UP', 'DOWN')
        df['price_trend_8'] = np.where(df['close'] > df['close'].shift(8), 'UP', 'DOWN')

        # Moving average trend
        df['ma_trend'] = np.where(df['sma_20'] > df['sma_20'].shift(3), 'UP', 'DOWN')

        # ENHANCED: Accurate trend direction with more factors (8 indicators - added regression slope)
        # NOTE: Regression slope only votes when NOT flat - flat slopes indicate ranging (no directional bias)
        regression_is_flat = (df['regression_strength'] == 'FLAT')
        regression_up_votes = np.where(
            regression_is_flat, 0,  # No vote when flat (ranging condition)
            (df['regression_slope'] > 0).astype(int) * 2  # 2 votes when trending
        )

        trend_up_votes = (
            (df['ema_fast_trend'] == 'UP').astype(int) +
            (df['ema_slow_trend'] == 'UP').astype(int) +
            (df['ema_cross_trend'] == 'UP').astype(int) +
            (df['price_trend_3'] == 'UP').astype(int) +
            (df['price_trend_5'] == 'UP').astype(int) +
            (df['rsi_momentum'] == 'UP').astype(int) +
            (df['momentum_score'] > 0).astype(int) +
            # NEW: Regression slope vote (weighted as 2 votes, but only when not flat)
            regression_up_votes
        )

        # Dynamic consensus based on whether regression slope is participating
        # When regression is flat: 4 out of 7 votes needed
        # When regression is trending: 5 out of 9 votes needed
        consensus_threshold = np.where(regression_is_flat, 4, 5)
        df['accurate_trend_direction'] = np.where(trend_up_votes >= consensus_threshold, 'UP', 'DOWN')

        # Legacy trend direction for backward compatibility
        df['trend_direction'] = np.where(df['ema_slope'] > 0, 'BUY', 'SELL')

        # ENHANCED: Multi-factor trend strength
        df['trend_strength'] = (
            df['ema_fast_slope_abs'] * 500 +  # Fast EMA contribution
            df['ema_slow_slope_abs'] * 500 +  # Slow EMA contribution
            abs(df['momentum_score']) * 100   # Momentum contribution
        )

        return df

    def calculate_candle_position(self, df):
        """FIXED: Calculate close position within full candle range (high to low)"""
        df = df.copy()

        # Calculate full candle range (high to low)
        df['candle_range'] = df['high'] - df['low']

        # Calculate close position within full candle range (0 = low, 1 = high)
        df['close_range_position'] = np.where(
            df['candle_range'] > 0,
            (df['close'] - df['low']) / df['candle_range'],
            0.5  # Default to middle if no range
        )

        # Also keep body analysis for additional info
        df['candle_body_high'] = np.maximum(df['open'], df['close'])
        df['candle_body_low'] = np.minimum(df['open'], df['close'])
        df['candle_body_height'] = df['candle_body_high'] - df['candle_body_low']
        df['close_body_position'] = np.where(
            df['candle_body_height'] > 0,
            (df['close'] - df['candle_body_low']) / df['candle_body_height'],
            0.5
        )

        return df

    def check_candle_approval(self, df, trade_direction):
        """FIXED: Check if LAST CLOSED candle approves trade based on close position in full candle range"""
        if len(df) < 2:
            return False, "Need at least 2 candles"

        # Use the LAST CLOSED candle (index -2), not current open candle (index -1)
        last_closed_candle = df.iloc[-2]
        close_position = last_closed_candle['close_range_position']  # Use full range position

        # Get candle details for logging
        candle_high = last_closed_candle['high']
        candle_low = last_closed_candle['low']
        candle_close = last_closed_candle['close']
        candle_range = candle_high - candle_low

        if trade_direction.upper() in ['BUY', 'LONG']:
            # For longs: close should be in top third (≥66.7% of range)
            approved = close_position >= self.long_candle_threshold
            reason = f"Last closed candle: Close {candle_close:.5f} at {close_position:.1%} of range [{candle_low:.5f}-{candle_high:.5f}] (need ≥{self.long_candle_threshold:.0%} for longs)"
        elif trade_direction.upper() in ['SELL', 'SHORT']:
            # For shorts: close should be in bottom third (≤33.3% of range)
            approved = close_position <= self.short_candle_threshold
            reason = f"Last closed candle: Close {candle_close:.5f} at {close_position:.1%} of range [{candle_low:.5f}-{candle_high:.5f}] (need ≤{self.short_candle_threshold:.0%} for shorts)"
        else:
            approved = False
            reason = f"Unknown trade direction: {trade_direction}"

        return approved, reason

    # is_significant_candle method removed per user request

    def check_candle_exit_confirmation(self, df, position_type):
        """Check if LAST CLOSED candle confirms exit for given position type"""
        if len(df) < 2:  # Need at least 2 candles to get the closed candle
            return False, "Need at least 2 candles", 0.0

        try:
            # Use LAST CLOSED candle (index -2), not current forming candle (index -1)
            closed_candle = df.iloc[-2]
            candle_high = closed_candle['high']
            candle_low = closed_candle['low']
            candle_close = closed_candle['close']
            candle_range = candle_high - candle_low

            if candle_range <= 0:
                return False, "No candle range", 0.5

            # ATR value retrieval (significance filtering removed)
            # Get ATR value from the same candle
            atr_value = closed_candle.get('atr', None)
            if atr_value is not None and atr_value > 0:
                candle_data = {
                    'high': candle_high,
                    'low': candle_low,
                    'open': closed_candle['open'],
                    'close': candle_close
                }

                # ATR-based candle significance filter removed per user request

            close_position = (candle_close - candle_low) / candle_range

            if position_type.upper() == 'BUY':
                # For BUY exits: close should be below 40% of range (bearish confirmation)
                confirmed = close_position <= 0.40
                reason = f"Current candle: Close {candle_close:.5f} at {close_position:.1%} of range [{candle_low:.5f}-{candle_high:.5f}] (need ≤40% to exit BUY)"
            elif position_type.upper() == 'SELL':
                # For SELL exits: close should be above 60% of range (bullish confirmation)
                confirmed = close_position >= 0.60
                reason = f"Current candle: Close {candle_close:.5f} at {close_position:.1%} of range [{candle_low:.5f}-{candle_high:.5f}] (need ≥60% to exit SELL)"
            else:
                return False, f"Unknown position type: {position_type}", 0.0

            return confirmed, reason, close_position

        except Exception as e:
            return False, f"Error checking candle confirmation: {e}", 0.0



    def calculate_recent_candle_strength(self, df, lookback=8):
        """MIDTERM: Calculate recent candle strength for logging (longer lookback)"""
        # CRITICAL FIX: Only use CLOSED candles, exclude current forming candle
        if len(df) < lookback + 1:  # Need +1 because we exclude the last (forming) candle
            return {
                'bullish_strength': 0.0,
                'bearish_strength': 0.0,
                'net_strength': 0.0,
                'dominant_bias': 'NEUTRAL',
                'candles_analyzed': len(df) - 1 if len(df) > 0 else 0
            }

        # Use df[:-1] to exclude the current forming candle, then take last 'lookback' closed candles
        closed_candles_df = df[:-1]  # Exclude current forming candle
        recent_candles = closed_candles_df.tail(lookback).copy()

        # Calculate individual candle strengths
        recent_candles['candle_range'] = recent_candles['high'] - recent_candles['low']
        recent_candles['candle_body'] = abs(recent_candles['close'] - recent_candles['open'])
        recent_candles['body_to_range_ratio'] = recent_candles['candle_body'] / recent_candles['candle_range']

        # Bullish/Bearish classification
        recent_candles['is_bullish'] = recent_candles['close'] > recent_candles['open']
        recent_candles['is_bearish'] = recent_candles['close'] < recent_candles['open']

        # Weight by body strength and ATR
        atr_recent = recent_candles['atr'].iloc[-1] if 'atr' in recent_candles.columns else recent_candles['candle_range'].mean()
        recent_candles['strength_weight'] = (recent_candles['candle_body'] / atr_recent) * recent_candles['body_to_range_ratio']

        # Calculate weighted strengths
        bullish_strength = (recent_candles['is_bullish'] * recent_candles['strength_weight']).sum()
        bearish_strength = (recent_candles['is_bearish'] * recent_candles['strength_weight']).sum()

        # Normalize to 0-1 scale
        total_strength = bullish_strength + bearish_strength
        if total_strength > 0:
            bullish_strength_norm = bullish_strength / total_strength
            bearish_strength_norm = bearish_strength / total_strength
        else:
            bullish_strength_norm = 0.5
            bearish_strength_norm = 0.5

        net_strength = bullish_strength_norm - bearish_strength_norm

        # Determine dominant bias
        if net_strength > 0.15:
            dominant_bias = 'BULLISH'
        elif net_strength < -0.15:
            dominant_bias = 'BEARISH'
        else:
            dominant_bias = 'NEUTRAL'

        # NEW: Calculate acceleration (Phase 1: Monitoring)
        acceleration_data = self.calculate_acceleration(bullish_strength_norm, bearish_strength_norm)

        return {
            'bullish_strength': bullish_strength_norm,
            'bearish_strength': bearish_strength_norm,
            'net_strength': net_strength,
            'dominant_bias': dominant_bias,
            'candles_analyzed': lookback,
            # NEW: Add acceleration data
            'bull_acceleration': acceleration_data['bull_acceleration'],
            'bear_acceleration': acceleration_data['bear_acceleration'],
            'bull_velocity': acceleration_data['bull_velocity'],
            'bear_velocity': acceleration_data['bear_velocity'],
            'acceleration_available': acceleration_data['acceleration_available']
        }

    def calculate_acceleration(self, current_bull_strength, current_bear_strength):
        """NEW: Calculate bull/bear acceleration (Phase 1: Monitoring)"""
        # NOTE: This method now receives strength values from CLOSED candles only
        # since calculate_recent_candle_strength was fixed to exclude forming candle

        # Update strength history (keep last 3 values)
        self.bull_strength_history.append(current_bull_strength)
        self.bear_strength_history.append(current_bear_strength)

        if len(self.bull_strength_history) > 3:
            self.bull_strength_history.pop(0)
        if len(self.bear_strength_history) > 3:
            self.bear_strength_history.pop(0)

        # Need at least 2 data points for velocity, 3 for acceleration
        if len(self.bull_strength_history) < 2:
            return {
                'bull_acceleration': 0.0,
                'bear_acceleration': 0.0,
                'bull_velocity': 0.0,
                'bear_velocity': 0.0,
                'acceleration_available': False
            }

        # Calculate current velocities (1st derivative)
        bull_velocity = (self.bull_strength_history[-1] - self.bull_strength_history[-2]) * 100  # Convert to percentage
        bear_velocity = (self.bear_strength_history[-1] - self.bear_strength_history[-2]) * 100

        # Update velocity history (keep last 2 values)
        self.bull_velocity_history.append(bull_velocity)
        self.bear_velocity_history.append(bear_velocity)

        if len(self.bull_velocity_history) > 2:
            self.bull_velocity_history.pop(0)
        if len(self.bear_velocity_history) > 2:
            self.bear_velocity_history.pop(0)

        # Calculate acceleration (2nd derivative) if we have enough data
        if len(self.bull_velocity_history) >= 2:
            bull_acceleration = self.bull_velocity_history[-1] - self.bull_velocity_history[-2]
            bear_acceleration = self.bear_velocity_history[-1] - self.bear_velocity_history[-2]
            acceleration_available = True
        else:
            bull_acceleration = 0.0
            bear_acceleration = 0.0
            acceleration_available = False

        return {
            'bull_acceleration': bull_acceleration,
            'bear_acceleration': bear_acceleration,
            'bull_velocity': bull_velocity,
            'bear_velocity': bear_velocity,
            'acceleration_available': acceleration_available
        }


    def detect_regime(self, df):
        """ENHANCED: Detect market regime with improved sensitivity for short-term trends"""
        if len(df) < self.atr_lookback:
            return "INSUFFICIENT_DATA", 0.0, {}, None, None

        # Get latest values
        latest = df.iloc[-1]

        atr_pct = latest['atr_percentile']
        fast_slope_abs = latest['ema_fast_slope_abs']
        slow_slope_abs = latest['ema_slow_slope_abs']
        bb_width_pct = latest['bb_width_percentile']
        vol_pct = latest['vol_percentile']
        trend_direction = latest['trend_direction']
        bb_squeeze = latest['bb_squeeze']
        rsi = latest['rsi']
        momentum_score = latest['momentum_score']

        # NEW: Regression channel slope values
        regression_slope = latest['regression_slope']
        regression_slope_abs = latest['regression_slope_abs']
        regression_strength = latest['regression_strength']

        # NEW: 100-period regime regression channel values
        regression_slope_regime = latest.get('regression_slope_regime', 0)
        regression_slope_regime_abs = abs(regression_slope_regime)

        # Calculate regime regression strength
        if regression_slope_regime_abs >= 0.02:  # >= 0.02% slope per period
            regression_strength_regime = 'STRONG'
        elif regression_slope_regime_abs >= 0.01:  # >= 0.01% slope per period
            regression_strength_regime = 'MODERATE'
        elif regression_slope_regime_abs >= 0.005:  # >= 0.005% slope per period
            regression_strength_regime = 'WEAK'
        else:
            regression_strength_regime = 'FLAT'

        # ENHANCED: Multi-factor regime scoring system with detailed reasoning
        trending_score = 0
        ranging_score = 0
        reasoning = []  # Track reasoning for each factor

        # ATR Analysis (Weight: 3 points) - More sensitive thresholds
        if atr_pct > self.trending_atr_threshold:
            trending_score += 3
            reasoning.append(f"ATR: HIGH volatility ({atr_pct:.1%} > {self.trending_atr_threshold:.1%}) → +3 TRENDING")
        elif atr_pct < self.ranging_atr_threshold:
            ranging_score += 3
            reasoning.append(f"ATR: LOW volatility ({atr_pct:.1%} < {self.ranging_atr_threshold:.1%}) → +3 RANGING")
        else:  # Middle zone - award to closer threshold
            if atr_pct > 0.5:
                trending_score += 1.5
                reasoning.append(f"ATR: MEDIUM-HIGH volatility ({atr_pct:.1%}) → +1.5 TRENDING")
            else:
                ranging_score += 1.5
                reasoning.append(f"ATR: MEDIUM-LOW volatility ({atr_pct:.1%}) → +1.5 RANGING")

        # ENHANCED: Fast EMA Slope Analysis (Weight: 2.5 points) - Early trend detection
        if fast_slope_abs > self.fast_slope_threshold:
            trending_score += 2.5
            reasoning.append(f"Fast EMA: STRONG slope ({fast_slope_abs:.4f} > {self.fast_slope_threshold:.4f}) → +2.5 TRENDING")
        elif fast_slope_abs < (self.fast_slope_threshold * 0.5):
            ranging_score += 2.5
            reasoning.append(f"Fast EMA: FLAT slope ({fast_slope_abs:.4f} < {self.fast_slope_threshold * 0.5:.4f}) → +2.5 RANGING")
        else:
            mid_threshold = self.fast_slope_threshold * 0.75
            if fast_slope_abs > mid_threshold:
                trending_score += 1
                reasoning.append(f"Fast EMA: MODERATE slope ({fast_slope_abs:.4f} > {mid_threshold:.4f}) → +1 TRENDING")
            else:
                ranging_score += 1
                reasoning.append(f"Fast EMA: WEAK slope ({fast_slope_abs:.4f} ≤ {mid_threshold:.4f}) → +1 RANGING")

        # Slow EMA Slope Analysis (Weight: 2 points) - Confirmation
        if slow_slope_abs > self.trending_slope_threshold:
            trending_score += 2
            reasoning.append(f"Slow EMA: STRONG slope ({slow_slope_abs:.4f} > {self.trending_slope_threshold:.4f}) → +2 TRENDING")
        elif slow_slope_abs < self.ranging_slope_threshold:
            ranging_score += 2
            reasoning.append(f"Slow EMA: FLAT slope ({slow_slope_abs:.4f} < {self.ranging_slope_threshold:.4f}) → +2 RANGING")
        else:
            mid_threshold = (self.trending_slope_threshold + self.ranging_slope_threshold) / 2
            if slow_slope_abs > mid_threshold:
                trending_score += 0.5
                reasoning.append(f"Slow EMA: MODERATE slope ({slow_slope_abs:.4f} > {mid_threshold:.4f}) → +0.5 TRENDING")
            else:
                ranging_score += 0.5
                reasoning.append(f"Slow EMA: WEAK slope ({slow_slope_abs:.4f} ≤ {mid_threshold:.4f}) → +0.5 RANGING")

        # NEW: RSI Momentum Analysis (Weight: 1.5 points)
        rsi_upper = 50 + self.momentum_threshold * 50
        rsi_lower = 50 - self.momentum_threshold * 50
        if rsi > rsi_upper:  # Strong momentum up
            trending_score += 1.5
            reasoning.append(f"RSI: STRONG bullish momentum ({rsi:.1f} > {rsi_upper:.1f}) → +1.5 TRENDING")
        elif rsi < rsi_lower:  # Strong momentum down
            trending_score += 1.5
            reasoning.append(f"RSI: STRONG bearish momentum ({rsi:.1f} < {rsi_lower:.1f}) → +1.5 TRENDING")
        elif 45 < rsi < 55:  # Neutral momentum = ranging
            ranging_score += 1.5
            reasoning.append(f"RSI: NEUTRAL momentum ({rsi:.1f} in 45-55 range) → +1.5 RANGING")
        else:  # Mild momentum
            trending_score += 0.5
            reasoning.append(f"RSI: MILD momentum ({rsi:.1f}) → +0.5 TRENDING")

        # NEW: Multi-timeframe Momentum Analysis (Weight: 1.5 points)
        if abs(momentum_score) >= 2:  # Strong directional momentum
            trending_score += 1.5
            reasoning.append(f"Momentum: STRONG directional ({momentum_score:+d}/3) → +1.5 TRENDING")
        elif momentum_score == 0:  # No clear momentum
            ranging_score += 1.5
            reasoning.append(f"Momentum: NO clear direction ({momentum_score}/3) → +1.5 RANGING")
        else:  # Weak momentum
            trending_score += 0.5
            reasoning.append(f"Momentum: WEAK directional ({momentum_score:+d}/3) → +0.5 TRENDING")

        # Bollinger Band Analysis (Weight: 2 points)
        if bb_squeeze:
            ranging_score += 2
            reasoning.append(f"BB: SQUEEZE detected (width {bb_width_pct:.1%} < {self.bb_squeeze_threshold:.1%}) → +2 RANGING")
        elif bb_width_pct > 0.65:  # More sensitive threshold
            trending_score += 2
            reasoning.append(f"BB: WIDE bands ({bb_width_pct:.1%} > 65%) → +2 TRENDING")
        elif bb_width_pct > 0.40:
            trending_score += 1
            reasoning.append(f"BB: MODERATE width ({bb_width_pct:.1%} > 40%) → +1 TRENDING")
        elif bb_width_pct < self.bb_width_threshold:
            ranging_score += 1
            reasoning.append(f"BB: NARROW bands ({bb_width_pct:.1%} < {self.bb_width_threshold:.1%}) → +1 RANGING")
        else:
            reasoning.append(f"BB: NEUTRAL width ({bb_width_pct:.1%}) → No points")

        # Volatility confirmation (Weight: 1 point)
        if vol_pct > 0.55:  # More sensitive threshold
            trending_score += 1
            reasoning.append(f"Volatility: HIGH ({vol_pct:.1%} > 55%) → +1 TRENDING")
        else:
            ranging_score += 1
            reasoning.append(f"Volatility: LOW ({vol_pct:.1%} ≤ 55%) → +1 RANGING")

        # NEW: Volume Analysis for Regime Detection (Weight: 2 points)
        volume_ratio = latest.get('volume_ratio', 1.0)
        volume_trend = latest.get('volume_trend', 0)
        volume_volatility = latest.get('volume_volatility', 0.0)

        # Volume activity level
        if volume_ratio > 1.3:  # High volume activity
            trending_score += 1.5
            reasoning.append(f"Volume Activity: HIGH ({volume_ratio:.2f} > 1.3) → +1.5 TRENDING")
        elif volume_ratio < 0.7:  # Low volume activity
            ranging_score += 1.5
            reasoning.append(f"Volume Activity: LOW ({volume_ratio:.2f} < 0.7) → +1.5 RANGING")
        elif volume_ratio > 1.1:  # Moderate high volume
            trending_score += 0.5
            reasoning.append(f"Volume Activity: MODERATE-HIGH ({volume_ratio:.2f} > 1.1) → +0.5 TRENDING")
        else:  # Normal to low volume
            ranging_score += 0.5
            reasoning.append(f"Volume Activity: NORMAL-LOW ({volume_ratio:.2f} ≤ 1.1) → +0.5 RANGING")

        # Volume trend consistency
        if volume_trend != 0:  # Volume has clear trend
            trending_score += 0.5
            trend_dir = "INCREASING" if volume_trend > 0 else "DECREASING"
            reasoning.append(f"Volume Trend: {trend_dir} → +0.5 TRENDING")
        else:  # No clear volume trend
            ranging_score += 0.5
            reasoning.append(f"Volume Trend: NEUTRAL → +0.5 RANGING")

        # NEW: 100-Period Regime Regression Channel Analysis (Weight: 3 points)
        # Primary regime detection channel - longer lookback for stable regime classification
        if not pd.isna(regression_slope_regime):
            direction_regime = "UP" if regression_slope_regime > 0 else "DOWN"

            if regression_strength_regime == 'FLAT':
                # FLAT 100-period channel - strong ranging signal
                ranging_score += 3
                reasoning.append(f"100-Period Regime Channel: FLAT slope ({regression_slope_regime:+.4f}% per period, 100 candles) → +3 RANGING")
            elif regression_strength_regime == 'WEAK':
                # WEAK trending - slight ranging bias
                ranging_score += 1
                reasoning.append(f"100-Period Regime Channel: WEAK {direction_regime} slope ({regression_slope_regime:+.4f}% per period, 100 candles) → +1 RANGING")
            elif regression_strength_regime == 'MODERATE':
                # MODERATE trending - trending bias
                trending_score += 2
                reasoning.append(f"100-Period Regime Channel: MODERATE {direction_regime} slope ({regression_slope_regime:+.4f}% per period, 100 candles) → +2 TRENDING")
            elif regression_strength_regime == 'STRONG':
                # STRONG trending - strong trending signal
                trending_score += 3
                reasoning.append(f"100-Period Regime Channel: STRONG {direction_regime} slope ({regression_slope_regime:+.4f}% per period, 100 candles) → +3 TRENDING")
        else:
            reasoning.append(f"100-Period Regime Channel: INSUFFICIENT DATA → +0 (no contribution)")

        # DYNAMIC Regression Channel Analysis (Weight: 4 points)
        # Uses either long-term (20-candle) or short-term (10-candle) based on context

        # Store regression values for consistent dual analysis logging
        self._regime_long_slope = None
        self._regime_long_strength = None
        self._regime_long_source = None

        # First, check if we should use short-term regression for consistency
        use_short_term_for_regime = False

        # Check if short-term regression would provide override (preview logic)
        if not pd.isna(latest.get('regression_slope_short', np.nan)):
            short_slope = latest['regression_slope_short']
            short_strength = latest['regression_strength_short']
            short_trend = latest['regression_trend_short']
            long_slope = latest['regression_slope']

            # Simulate the conditions that would trigger short-term override
            # (This mirrors the logic in apply_regime_logic)
            long_term_up = long_slope > 0
            short_term_up = short_slope > 0
            trends_opposite = long_term_up != short_term_up

            if trends_opposite and short_strength != 'FLAT':
                use_short_term_for_regime = True
                reasoning.append(f"🔄 DYNAMIC REGIME: Using short-term regression (conflicts with long-term)")

        # Initialize channel state variables (fix for variable scope issue)
        simple_channel_info = None
        channel_state = None

        # Apply the appropriate regression channel scoring
        if use_short_term_for_regime:
            # Use SHORT-TERM regression for regime scoring (10-candle)
            short_slope = latest['regression_slope_short']
            short_slope_abs = latest['regression_slope_short_abs']
            short_strength = latest['regression_strength_short']
            direction = "UP" if short_slope > 0 else "DOWN"

            # Store for dual analysis logging
            self._regime_long_slope = short_slope
            self._regime_long_strength = short_strength
            self._regime_long_source = "Short-term Override (10 candles)"

            # SYMMETRIC SCORING: Both trending and ranging get points
            if short_slope_abs <= 0.05:
                # FLAT channel - strong ranging signal
                channel_type = "FLAT_RANGING"
                ranging_score += 4
                reasoning.append(f"Short-term Channel: {channel_type} slope ({short_slope:+.4f}%, 10 candles) → +4 RANGING")
            elif short_slope_abs <= 0.15:
                # WEAK trending - moderate trending signal
                channel_type = "WEAK_TRENDING"
                trending_score += 2
                reasoning.append(f"Short-term Channel: {channel_type} {direction} slope ({short_slope:+.4f}%, 10 candles) → +2 TRENDING")
            else:
                # STRONG trending - strong trending signal
                channel_type = "STRONG_TRENDING"
                trending_score += 4
                reasoning.append(f"Short-term Channel: {channel_type} {direction} slope ({short_slope:+.4f}%, 10 candles) → +4 TRENDING")

            # Set channel state for short-term usage
            channel_state = 'SHORT_TERM_USED'

        else:
            # Use LONG-TERM regression for regime scoring (20-candle Simple Channel or fallback)
            # Note: simple_channel_info and channel_state already initialized above

            # Check if simple_regression is available (passed from FixedLiveTrader)
            if hasattr(self, '_simple_regression') and self._simple_regression is not None:
                simple_channel_info = self._simple_regression.update_channel_state(df)
                channel_state = simple_channel_info['state']

            if simple_channel_info and channel_state == 'ACTIVE_CHANNEL':
                # Use Simple Regression Channel (20-candle)
                channel_data = simple_channel_info['channel_data']
                if channel_data:
                    simple_slope = channel_data['slope']
                    simple_slope_abs = abs(simple_slope)
                    r_squared = channel_data['r_squared']
                    candle_count = channel_data['candle_count']
                    direction = "UP" if simple_slope > 0 else "DOWN"

                    # Store for dual analysis logging
                    self._regime_long_slope = simple_slope
                    if simple_slope_abs <= 0.05:
                        self._regime_long_strength = "FLAT_RANGING"
                    elif simple_slope_abs <= 0.15:
                        self._regime_long_strength = "WEAK_TRENDING"
                    else:
                        self._regime_long_strength = "STRONG_TRENDING"
                    self._regime_long_source = f"Simple Channel (R²={r_squared:.2f})"

                    # SYMMETRIC SCORING: Both trending and ranging get points
                    if simple_slope_abs <= 0.05:
                        # FLAT channel - strong ranging signal
                        channel_type = "FLAT_RANGING"
                        ranging_score += 4
                        reasoning.append(f"Long-term Channel: {channel_type} slope ({simple_slope:+.3f}, R²={r_squared:.2f}, {candle_count} candles) → +4 RANGING")
                    elif simple_slope_abs <= 0.15:
                        # WEAK trending - moderate trending signal
                        channel_type = "WEAK_TRENDING"
                        trending_score += 2
                        reasoning.append(f"Long-term Channel: {channel_type} {direction} slope ({simple_slope:+.3f}, R²={r_squared:.2f}, {candle_count} candles) → +2 TRENDING")
                    else:
                        # STRONG trending - strong trending signal
                        channel_type = "STRONG_TRENDING"
                        trending_score += 4
                        reasoning.append(f"Long-term Channel: {channel_type} {direction} slope ({simple_slope:+.3f}, R²={r_squared:.2f}, {candle_count} candles) → +4 TRENDING")
                else:
                    reasoning.append(f"Long-term Channel: ACTIVE but no data → No points")

            elif channel_state == 'BUILDING':
                # Building state (no channel yet) - use traditional regression
                reasoning.append(f"Long-term Channel: BUILDING new channel → Use traditional regression")

            # Fallback to traditional long-term regression if simple channel fails
            if channel_state != 'ACTIVE_CHANNEL' and not pd.isna(regression_slope_abs):
                direction = "UP" if regression_slope > 0 else "DOWN"

                # Store for dual analysis logging
                self._regime_long_slope = regression_slope
                self._regime_long_strength = regression_strength
                self._regime_long_source = "Traditional Regression (20 candles)"

                if regression_strength == 'STRONG':  # >= 0.03% slope
                    trending_score += 2
                    reasoning.append(f"Fallback Long-term Regression: STRONG {direction} slope ({regression_slope:+.3f}% per period, 20 candles) → +2 TRENDING")
                elif regression_strength == 'MODERATE':  # >= 0.015% slope
                    trending_score += 1.5
                    reasoning.append(f"Fallback Long-term Regression: MODERATE {direction} slope ({regression_slope:+.3f}% per period, 20 candles) → +1.5 TRENDING")
                elif regression_strength == 'WEAK':  # >= 0.005% slope
                    trending_score += 1
                    reasoning.append(f"Fallback Long-term Regression: WEAK {direction} slope ({regression_slope:+.3f}% per period, 20 candles) → +1 TRENDING")
                else:  # FLAT < 0.005%
                    ranging_score += 2
                    reasoning.append(f"Fallback Long-term Regression: FLAT slope ({regression_slope:+.3f}% per period, 20 candles) → +2 RANGING")

        # ENHANCED: Decision logic with more sensitive thresholds
        score_diff = abs(trending_score - ranging_score)
        # Updated max score calculation:
        # ATR(3) + Fast EMA(2.5) + Slow EMA(2) + RSI(1.5) + Momentum(1.5) + BB(2) + Volatility(1) + Volume(2) + 100-Period Regime Channel(3) + Simple Channel(4) = 22.5
        max_possible_score = 22.5
        confidence = max(trending_score, ranging_score) / max_possible_score

        # More sensitive regime classification
        min_score = 3.0  # Even less conservative
        min_score_diff = 1.0  # More sensitive to differences

        # Determine regime with detailed reasoning
        if trending_score >= min_score and score_diff >= min_score_diff and trending_score > ranging_score:
            regime = "TRENDING"
            regime_reason = f"TRENDING: Score {trending_score:.1f} > {ranging_score:.1f} (diff {score_diff:.1f} ≥ {min_score_diff})"
        elif ranging_score >= min_score and score_diff >= min_score_diff and ranging_score > trending_score:
            regime = "RANGING"
            regime_reason = f"RANGING: Score {ranging_score:.1f} > {trending_score:.1f} (diff {score_diff:.1f} ≥ {min_score_diff})"
        else:
            regime = "TRANSITIONAL"
            if score_diff < min_score_diff:
                regime_reason = f"TRANSITIONAL: Scores too close ({trending_score:.1f} vs {ranging_score:.1f}, diff {score_diff:.1f} < {min_score_diff})"
            else:
                regime_reason = f"TRANSITIONAL: Insufficient evidence (max score {max(trending_score, ranging_score):.1f} < {min_score})"

        # Enhanced regime details for logging with reasoning
        details = {
            'atr_percentile': atr_pct,
            'fast_ema_slope': latest['ema_fast_slope'],
            'fast_ema_slope_abs': fast_slope_abs,
            'slow_ema_slope': latest['ema_slow_slope'],
            'slow_ema_slope_abs': slow_slope_abs,
            'bb_width_percentile': bb_width_pct,
            'bb_squeeze': bb_squeeze,
            'rsi': rsi,
            'momentum_score': momentum_score,
            'trending_score': trending_score,
            'ranging_score': ranging_score,
            'score_diff': score_diff,
            'confidence': confidence,
            'trend_strength': latest['trend_strength'],
            'reasoning': reasoning,
            'regime_reason': regime_reason,
            'simple_channel_state': channel_state,
            'simple_channel_info': simple_channel_info,
            # Add regression data used in regime detection
            'regime_regression_data': {
                'slope': getattr(self, '_regime_long_slope', None),
                'strength': getattr(self, '_regime_long_strength', None),
                'source': getattr(self, '_regime_long_source', None)
            },
            # NEW: 100-period regime regression data
            'regime_regression_100': {
                'slope': regression_slope_regime,
                'slope_abs': regression_slope_regime_abs,
                'strength': regression_strength_regime,
                'trend': latest.get('regression_trend_regime', 'UNKNOWN')
            }
        }

        # Get accurate trend direction
        accurate_trend_direction = latest['accurate_trend_direction']

        # REMOVED: Verbose trend direction voting debug logging

        self.logger.info(f"   Final Decision: {accurate_trend_direction}")

        return regime, confidence, details, trend_direction, accurate_trend_direction

    def set_simple_regression(self, simple_regression):
        """Set the simple regression channel instance for regime detection"""
        self._simple_regression = simple_regression

class FixedLiveTrader:
    def __init__(self, symbol="XAUUSD!"):
        # Trading parameters
        self.symbol = symbol

        # Initialize components with symbol
        self.mt5_manager = MT5Manager(symbol=symbol)
        self.feature_engineer = FixedFeatureEngineer()
        # Keep original regime detector for utility methods
        self.regime_detector = RegimeDetector()
        # Add enhanced regime detector for improved regime detection with MTF and hybrid breakouts
        self.enhanced_regime_detector = EnhancedRegimeDetector(
            symbol=symbol,
            timeframe="M5",
            mtf_mode=True,  # Enable real multi-timeframe analysis
            breakout_mode="HYBRID"  # Use hybrid breakout logic for faster detection
        )
        self.trade_logger = TradeLogger("trade_log.csv")

        # Setup logging
        self.logger = logging.getLogger(__name__)

        # Load trained model
        self.model = None
        self.scaler = None
        self.selected_features = None

        # Symbol specifications for position sizing
        self.SYMBOL_SPECS = {
            "XAUUSD!": {"contract_size": 100, "name": "Gold"},      # 1 lot = 100 ounces
            "EURUSD!": {"contract_size": 100000, "name": "Euro"},   # 1 lot = 100,000 units
            "BTCUSD": {"contract_size": 1, "name": "Bitcoin"}       # 1 lot = 1 BTC
        }

        # Other trading parameters
        self.timeframe = "M5"
        self.risk_percent = 4.0
        self.min_confidence = 0.05  # Lowered to match ±5% candle strength thresholds

        # Validate symbol
        if self.symbol not in self.SYMBOL_SPECS:
            self.logger.warning(f"⚠️ Unknown symbol {self.symbol}. Using default contract size of 100.")
            self.SYMBOL_SPECS[self.symbol] = {"contract_size": 100, "name": "Unknown"}

        self.logger.info(f"🎯 Trading System initialized for {self.SYMBOL_SPECS[self.symbol]['name']} ({self.symbol})")
        # REMOVED: Contract size logging

        # Position tracking for single concurrent trade
        self.current_position = None  # {'type': 'BUY'/'SELL', 'ticket': ticket_id, 'time': datetime, 'volume': float, 'remaining_volume': float}
        self.last_signal = None  # Track last signal for opposite signal detection
        self.last_trade_time = None  # Track last trade time to prevent overtrading

        # Trailing stop tracking
        self.trailing_stop_data = None  # {'initial_sl': price, 'current_sl': price, 'original_sl_distance': distance, 'profit_sl_count': 0}

        # Pending same signal SL tracking (for price confirmation)
        self.pending_same_signal_data = None  # {'sl': price, 'signal_type': 'BUY'/'SELL', 'signal_candle_high': price, 'signal_candle_low': price, 'detected_time': datetime}

        # Real-time trailing stop monitor
        self.trailing_monitor_thread = None
        self.trailing_monitor_active = False
        self.trailing_monitor_lock = threading.Lock()  # Thread safety for shared data

        # NEW: Candle confirmation trailing stop tracking
        self.candle_confirmation_stop = None  # {'confirmation_sl': price, 'original_sl': price, 'candle_close_time': datetime, 'position_type': str}

        # NEW: Enable candle confirmation revert functionality
        self.disable_candle_confirmation_revert = False  # Set to False to enable reverting after one candle

        # NEW: Pending order tracking
        self.pending_orders = []  # List of {'ticket': int, 'type': str, 'price': float, 'created_time': datetime, 'candles_waited': int}

        # Regime tracking
        self.current_regime = None
        self.regime_confidence = 0.0
        self.last_regime_change = None

        # NEW: Previous regime tracking for transitional logic
        self.previous_regime = None

        # NEW: Pending regime change exit tracking
        self.pending_regime_exit = None  # {'reason': str, 'unfavorable_regime': str, 'position_type': str}

        # NEW: Iteration tracking for enhanced logging
        self.iteration_count = 0

        # NEW: Last analyzed candle tracking for latency handling
        self.last_analyzed_candle_time = None

        # NEW: Previous candle strength tracking for relative change signals
        self.previous_candle_strength = None

        # NEW: Store latest features_df for exit confirmations
        self.latest_features_df = None

        # NEW: QQE Indicator for primary signal generation with volume enhancement
        # FIXED: Match TradingView parameters exactly - USER'S ACTUAL SETTINGS
        self.qqe_indicator = QQEIndicator(
            rsi_period=7,  # RSI Length (USER'S TRADINGVIEW SETTING: 7)
            rsi_smoothing=5,  # RSI Smoothing (matches TradingView)
            qqe_factor=1.0,  # Fast QQE Factor (USER'S TRADINGVIEW SETTING: 1)
            threshold=10,  # Threshold (matches TradingView)
            volume_lookback=10,  # Volume analysis lookback period
            volume_divergence_lookback=8,  # Reduced for more responsive divergence detection
            divergence_strength_threshold=0.02  # Only filter strong divergences
        )

        # NEW: Swing point distance filtering
        self.swing_distance_atr_threshold = 1.5  # Maximum distance from recent swing point (in ATR)

        # Simple Regression Channel System
        self.simple_regression = SimpleRegressionChannel(
            periods=20,                       # Fixed 20-period lookback
            std_multiplier=2.0               # 2 standard deviations for channel bounds
        )

        # Pass simple regression to regime detector
        self.regime_detector.set_simple_regression(self.simple_regression)

    def is_weekend(self) -> bool:
        """Check if current time is weekend (Saturday or Sunday) in UTC"""
        current_time = datetime.now(timezone.utc)
        weekday = current_time.weekday()  # Monday=0, Sunday=6
        return weekday in [5, 6]  # Saturday=5, Sunday=6

    def sleep_until_monday(self):
        """Sleep until Monday 00:00 UTC with periodic logging"""
        current_time = datetime.now(timezone.utc)

        # Calculate next Monday 00:00 UTC
        days_until_monday = (7 - current_time.weekday()) % 7
        if days_until_monday == 0:  # If today is Monday
            days_until_monday = 7  # Wait until next Monday

        next_monday = current_time.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=days_until_monday)
        sleep_duration = (next_monday - current_time).total_seconds()

        self.logger.info(f"💤 WEEKEND MODE: Market closed (Saturday/Sunday)")
        self.logger.info(f"📅 Current Time: {current_time.strftime('%A, %Y-%m-%d %H:%M:%S UTC')}")
        self.logger.info(f"⏰ Market reopens: {next_monday.strftime('%A, %Y-%m-%d %H:%M:%S UTC')}")
        self.logger.info(f"⏳ Sleeping for {sleep_duration/3600:.1f} hours until Monday...")

        # Sleep in 1-hour chunks to allow for graceful shutdown
        sleep_chunk = 3600  # 1 hour
        total_slept = 0

        while total_slept < sleep_duration and self.is_running:
            chunk_sleep = min(sleep_chunk, sleep_duration - total_slept)
            time.sleep(chunk_sleep)
            total_slept += chunk_sleep

            # Log progress every 6 hours
            if total_slept % (6 * 3600) == 0 and total_slept < sleep_duration:
                remaining_hours = (sleep_duration - total_slept) / 3600
                self.logger.info(f"💤 Weekend sleep: {remaining_hours:.1f} hours remaining until Monday...")

        if self.is_running:
            self.logger.info(f"🌅 WEEKEND OVER: Resuming trading on Monday!")
        else:
            self.logger.info(f"🛑 Weekend sleep interrupted - system shutdown requested")

    def get_latest_data_safe(self):
        """Safely get latest features data for exit confirmations"""
        if hasattr(self, 'latest_features_df') and self.latest_features_df is not None and len(self.latest_features_df) > 0:
            return self.latest_features_df
        else:
            # Fallback: get fresh data if stored data not available
            try:
                df = self.mt5_manager.get_latest_data(self.symbol, self.timeframe, 50)
                if df is not None and len(df) > 0:
                    features_df = self.feature_engineer.create_technical_indicators(df)
                    features_df = self.regime_detector.calculate_regime_indicators(features_df)
                    features_df = self.regime_detector.calculate_candle_position(features_df)
                    return features_df
            except Exception as e:
                self.logger.error(f"❌ Error getting fallback data: {e}")
            return None

    def calculate_candle_pattern_signal(self, df):
        """
        ENHANCED: Multi-candle pattern logic with support/resistance confirmation and volume scaling

        Pattern Detection Priority:
        1. Single candle pattern (current vs previous)
        2. 2-candle combined pattern (current+previous vs candle[-2])
        3. 3-candle combined pattern (current+previous+previous-1 vs candle[-3])

        BUY signal criteria:
        - Combined candle low < reference candle low
        - Combined candle high < reference candle high
        - Close position: 66.67%+ (full volume) or 55-66% (half volume)
        - Near lower regression channel or EMA support

        SELL signal criteria:
        - Combined candle low > reference candle low
        - Combined candle high > reference candle high
        - Close position: 33.33%- (full volume) or 33-45% (half volume)
        - Near upper regression channel or EMA resistance
        """
        try:
            # ENHANCED: Need at least 4 candles for 3-candle pattern detection
            if len(df) < 4:
                return {
                    'candle_pattern_signal': 0,
                    'candle_pattern_strength': 0.0,
                    'volume_factor': 1.0,
                    'support_resistance_confluence': 0.0
                }

            # MULTI-CANDLE PATTERN DETECTION: Try 1-candle, then 2-candle, then 3-candle
            # Priority: Single candle → 2-candle combined → 3-candle combined

            # Try single candle pattern first (current vs previous)
            pattern_result = self._check_single_candle_pattern(df)
            if pattern_result['candle_pattern_signal'] != 0:
                return pattern_result

            # Try 2-candle combined pattern (current+previous vs candle[-2])
            pattern_result = self._check_multi_candle_pattern(df, candle_count=2)
            if pattern_result['candle_pattern_signal'] != 0:
                return pattern_result

            # Try 3-candle combined pattern (current+previous+previous-1 vs candle[-3])
            pattern_result = self._check_multi_candle_pattern(df, candle_count=3)
            return pattern_result

        except Exception as e:
            self.logger.error(f"Error calculating enhanced candle pattern signal: {e}")
            return {
                'candle_pattern_signal': 0,
                'candle_pattern_strength': 0.0,
                'volume_factor': 1.0,
                'support_resistance_confluence': 0.0
            }

    def _calculate_enhanced_strength_multi(self, candles_to_combine, ref_candle, position_penetration, confluence, signal_type, candle_count):
        """
        Calculate enhanced signal strength for multi-candle patterns
        """
        try:
            # Base strength from position penetration and confluence
            base_strength = min(0.8, position_penetration * 0.4 + confluence * 0.4)

            # Multi-candle bonus: More candles = slightly higher strength (but diminishing returns)
            candle_count_bonus = 1.0 + (candle_count - 1) * 0.1  # 1.0, 1.1, 1.2 for 1, 2, 3 candles

            # Combined range analysis (like higher timeframe)
            combined_range = candles_to_combine['high'].max() - candles_to_combine['low'].min()
            ref_range = ref_candle['high'] - ref_candle['low']

            # Range expansion factor (larger combined range = stronger signal)
            range_factor = min(1.5, combined_range / max(ref_range, combined_range * 0.1))

            # Volume analysis for combined candles
            combined_volume = candles_to_combine.get('volume', pd.Series([1] * len(candles_to_combine))).sum()
            ref_volume = ref_candle.get('volume', 1)
            volume_factor = min(1.3, combined_volume / max(ref_volume, 1))

            # Final strength calculation
            final_strength = base_strength * candle_count_bonus * range_factor * volume_factor

            return min(1.0, final_strength)

        except Exception as e:
            return 0.5  # Default moderate strength

    def _check_single_candle_pattern(self, df):
        """Check single candle pattern (current vs previous)"""
        return self._check_pattern_logic(df, candle_count=1)

    def _check_multi_candle_pattern(self, df, candle_count):
        """Check multi-candle combined pattern (2 or 3 candles)"""
        return self._check_pattern_logic(df, candle_count=candle_count)

    def _check_pattern_logic(self, df, candle_count):
        """
        Core pattern detection logic for single or multi-candle patterns
        candle_count: 1 = single candle, 2 = 2-candle combined, 3 = 3-candle combined
        """
        try:
            # Use last closed candles (exclude current forming candle)
            closed_candles = df[:-1]

            if len(closed_candles) < candle_count + 1:  # Need candle_count + reference candle
                return {
                    'candle_pattern_signal': 0,
                    'candle_pattern_strength': 0.0,
                    'volume_factor': 1.0,
                    'support_resistance_confluence': 0.0
                }

            # Get reference candle (the one to compare against)
            ref_candle = closed_candles.iloc[-(candle_count + 1)]

            # Get candles to combine
            candles_to_combine = closed_candles.tail(candle_count)

            # Calculate combined candle metrics
            combined_high = candles_to_combine['high'].max()
            combined_low = candles_to_combine['low'].min()
            combined_close = candles_to_combine.iloc[-1]['close']  # Last candle's close
            combined_range = combined_high - combined_low

            # Reference candle metrics
            ref_high = ref_candle['high']
            ref_low = ref_candle['low']

            # Avoid division by zero
            if combined_range <= 0:
                return {
                    'candle_pattern_signal': 0,
                    'candle_pattern_strength': 0.0,
                    'volume_factor': 1.0,
                    'support_resistance_confluence': 0.0
                }

            # Calculate close position within COMBINED candle range (like a higher timeframe candle)
            close_position = (combined_close - combined_low) / combined_range

            # Get support/resistance levels for confluence
            latest = df.iloc[-1]
            trend_direction = latest.get('accurate_trend_direction', 'UP')

            # Create current candle object with combined high/low for accurate confluence detection
            current_candle = {
                'high': combined_high,
                'low': combined_low,
                'close': combined_close
            }

            confluence_data = self._get_support_resistance_confluence(df, combined_close, trend_direction, current_candle)

            # Determine price action pattern
            buy_price_action = (combined_low < ref_low and combined_high < ref_high)
            sell_price_action = (combined_low > ref_low and combined_high > ref_high)

            signal = 0
            signal_strength = 0.0
            volume_factor = 1.0

            # BUY signal logic
            if buy_price_action:
                if close_position >= 0.6667:  # Top third (66.67%+)
                    if confluence_data['buy_confluence'] > 0.3:  # Strong confluence - Full volume
                        signal = 1
                        volume_factor = 1.0
                        top_third_penetration = (close_position - 0.6667) / 0.3333
                        signal_strength = self._calculate_enhanced_strength_multi(
                            candles_to_combine, ref_candle, top_third_penetration,
                            confluence_data['buy_confluence'], 'buy', candle_count
                        )
                    elif confluence_data['buy_confluence'] > 0.2:  # Moderate confluence - Half volume
                        signal = 1
                        volume_factor = 0.5
                        top_third_penetration = (close_position - 0.6667) / 0.3333
                        signal_strength = self._calculate_enhanced_strength_multi(
                            candles_to_combine, ref_candle, top_third_penetration,
                            confluence_data['buy_confluence'], 'buy', candle_count
                        ) * 0.8
                elif close_position >= 0.55:  # 55-66% range - Half volume
                    if confluence_data['buy_confluence'] > 0.2:
                        signal = 1
                        volume_factor = 0.5
                        mid_zone_penetration = (close_position - 0.55) / 0.1167
                        signal_strength = self._calculate_enhanced_strength_multi(
                            candles_to_combine, ref_candle, mid_zone_penetration * 0.7,
                            confluence_data['buy_confluence'], 'buy', candle_count
                        ) * 0.8

            # SELL signal logic
            elif sell_price_action:
                if close_position <= 0.3333:  # Bottom third (33.33%-)
                    if confluence_data['sell_confluence'] > 0.3:  # Strong confluence - Full volume
                        signal = -1
                        volume_factor = 1.0
                        bottom_third_penetration = (0.3333 - close_position) / 0.3333
                        signal_strength = self._calculate_enhanced_strength_multi(
                            candles_to_combine, ref_candle, bottom_third_penetration,
                            confluence_data['sell_confluence'], 'sell', candle_count
                        )
                    elif confluence_data['sell_confluence'] > 0.2:  # Moderate confluence - Half volume
                        signal = -1
                        volume_factor = 0.5
                        bottom_third_penetration = (0.3333 - close_position) / 0.3333
                        signal_strength = self._calculate_enhanced_strength_multi(
                            candles_to_combine, ref_candle, bottom_third_penetration,
                            confluence_data['sell_confluence'], 'sell', candle_count
                        ) * 0.8
                elif close_position <= 0.45:  # 33-45% range - Half volume
                    if confluence_data['sell_confluence'] > 0.2:
                        signal = -1
                        volume_factor = 0.5
                        mid_zone_penetration = (0.45 - close_position) / 0.12
                        signal_strength = self._calculate_enhanced_strength_multi(
                            candles_to_combine, ref_candle, mid_zone_penetration * 0.7,
                            confluence_data['sell_confluence'], 'sell', candle_count
                        ) * 0.8

            # NEW: 100-Period Regression Channel Position Sizing
            # Halve position size near boundaries: BUY in upper 1/5th (≥0.8), SELL in lower 1/5th (≤0.2)
            # Veto only if already at minimum size
            latest = df.iloc[-1]
            regression_position_regime = latest.get('regression_position_regime', 0.5)

            if not pd.isna(regression_position_regime):
                if signal == 1 and regression_position_regime >= 0.8:  # BUY signal near upper boundary
                    if volume_factor <= 0.5:  # Already at half size or smaller - veto trade
                        self.logger.info(f"🚫 BUY SIGNAL VETOED: Near 100-period regression upper boundary (position: {regression_position_regime:.3f} ≥ 0.8) and volume already at minimum ({volume_factor:.1f}x)")
                        signal = 0
                        signal_strength = 0.0
                        volume_factor = 1.0
                    else:  # Full size - halve it
                        volume_factor = 0.5
                        self.logger.info(f"📉 BUY POSITION HALVED: Near 100-period regression upper boundary (position: {regression_position_regime:.3f} ≥ 0.8) - Volume reduced to {volume_factor:.1f}x")
                elif signal == -1 and regression_position_regime <= 0.2:  # SELL signal near lower boundary
                    if volume_factor <= 0.5:  # Already at half size or smaller - veto trade
                        self.logger.info(f"🚫 SELL SIGNAL VETOED: Near 100-period regression lower boundary (position: {regression_position_regime:.3f} ≤ 0.2) and volume already at minimum ({volume_factor:.1f}x)")
                        signal = 0
                        signal_strength = 0.0
                        volume_factor = 1.0
                    else:  # Full size - halve it
                        volume_factor = 0.5
                        self.logger.info(f"📈 SELL POSITION HALVED: Near 100-period regression lower boundary (position: {regression_position_regime:.3f} ≤ 0.2) - Volume reduced to {volume_factor:.1f}x")

            # FIXED: Show the actual confluence that was calculated and potentially boosted
            # When signal == 0, we should show the higher of buy/sell confluence to see what was actually calculated
            if signal == 1:
                displayed_confluence = confluence_data['buy_confluence']
            elif signal == -1:
                displayed_confluence = confluence_data['sell_confluence']
            else:
                # No signal - show the higher confluence to see what was actually calculated
                displayed_confluence = max(confluence_data['buy_confluence'], confluence_data['sell_confluence'])

            return {
                'candle_pattern_signal': signal,
                'candle_pattern_strength': signal_strength,
                'volume_factor': volume_factor,
                'support_resistance_confluence': displayed_confluence
            }

        except Exception as e:
            self.logger.error(f"Error calculating enhanced candle pattern signal: {e}")
            return {
                'candle_pattern_signal': 0,
                'candle_pattern_strength': 0.0,
                'volume_factor': 1.0,
                'support_resistance_confluence': 0.0
            }

    def _get_support_resistance_confluence(self, df, current_price, trend_direction="UP", current_candle=None):
        """
        Calculate confluence with support/resistance levels
        - Dual regression channel bands
        - EMA 20 and EMA 10 levels
        - Uses candle high/low for more accurate support/resistance detection
        """
        try:
            confluence_data = {
                'buy_confluence': 0.0,
                'sell_confluence': 0.0
            }
            # print(f"DEBUG: Starting confluence calculation for price {current_price}")

            if len(df) < 2:
                return confluence_data

            # CRITICAL FIX: Use the same candle that current_price represents
            # Find which candle the current_price corresponds to
            price_candle = None
            for i in range(len(df)):
                candle = df.iloc[-(i+1)]  # Start from most recent
                if abs(candle.get('close', 0) - current_price) < 0.01:  # Match within 1 pip
                    price_candle = candle
                    break

            # Fallback to last closed candle if no exact match
            if price_candle is None:
                price_candle = df.iloc[-2]  # Last closed candle

            # Get EMA levels from the SAME candle as current_price
            ema_10 = price_candle.get('ema_10', current_price)
            ema_20 = price_candle.get('ema_20', current_price)

            # Get regression channel data from the SAME candle as current_price (10, 20, and 100-period)
            regression_upper = price_candle.get('regression_upper', None)
            regression_lower = price_candle.get('regression_lower', None)
            regression_upper_short = price_candle.get('regression_upper_short', None)
            regression_lower_short = price_candle.get('regression_lower_short', None)
            # NEW: 100-period regression channels for signal generation fallback
            regression_upper_regime = price_candle.get('regression_upper_regime', None)
            regression_lower_regime = price_candle.get('regression_lower_regime', None)

            # Calculate ATR for proximity threshold (use available candles)
            atr_values = []
            available_candles = min(14, len(df)-1)  # Skip current forming candle
            for i in range(available_candles):
                if i+2 < len(df):  # Ensure we have enough data
                    candle = df.iloc[-(i+2)]  # Skip current forming candle
                    high_low = candle['high'] - candle['low']
                    if i+3 < len(df):
                        prev_close = df.iloc[-(i+3)]['close']
                        high_close = abs(candle['high'] - prev_close)
                        low_close = abs(candle['low'] - prev_close)
                        atr_values.append(max(high_low, high_close, low_close))
                    else:
                        atr_values.append(high_low)

            atr = sum(atr_values) / len(atr_values) if atr_values else (current_price * 0.001)
            proximity_threshold = atr * 0.2  # BALANCED: Within 0.2 ATR is considered "near" (catches truly significant levels)
            tolerance = atr * 0.05  # Small tolerance for "at level" detection

            # COMPLETELY REWRITTEN: Accurate support/resistance classification
            # ALL LEVELS: Treat regression channels and EMAs as equally important

            support_levels = []
            resistance_levels = []

            # Get current candle data for high/low analysis FIRST
            current_candle_high = current_price  # Default fallback
            current_candle_low = current_price   # Default fallback

            if current_candle is not None:
                current_candle_high = current_candle.get('high', current_price)
                current_candle_low = current_candle.get('low', current_price)
            elif len(df) > 0:
                # Use the last closed candle if no current candle provided
                last_candle = df.iloc[-1]
                current_candle_high = last_candle.get('high', current_price)
                current_candle_low = last_candle.get('low', current_price)

            # ALL LEVELS: Both regression channels and EMAs are important
            all_levels = [
                ('RegL', regression_lower),
                ('RegU', regression_upper),
                ('RegLS', regression_lower_short),
                ('RegUS', regression_upper_short),
                ('RegL100', regression_lower_regime),
                ('RegU100', regression_upper_regime),
                ('EMA10', ema_10),
                ('EMA20', ema_20)
            ]

            # Step 1: Add regression channels based on their ACTUAL position relative to price
            # (EMAs will be handled separately by test-and-bounce logic)
            regression_levels = [
                ('RegL', regression_lower),
                ('RegU', regression_upper),
                ('RegLS', regression_lower_short),
                ('RegUS', regression_upper_short),
                ('RegL100', regression_lower_regime),
                ('RegU100', regression_upper_regime)
            ]

            for name, level in regression_levels:
                if level is None:
                    continue

                # Classify based on actual position, not assumed type
                if level < current_price:
                    support_levels.append((name, level))  # Below price = support
                elif level > current_price:
                    resistance_levels.append((name, level))  # Above price = resistance
                # If exactly at price, we'll handle it in the confluence calculation

            # Step 2: Add swing highs and lows as support/resistance levels
            try:
                # Get recent swing highs and lows from the last 20 candles
                swing_lookback = min(20, len(df))
                if swing_lookback >= 5:  # Need minimum data for swing detection
                    recent_data = df.tail(swing_lookback)

                    # Find swing highs (resistance levels)
                    for i in range(2, len(recent_data) - 2):  # Need 2 candles on each side
                        candle = recent_data.iloc[i]
                        prev2 = recent_data.iloc[i-2]
                        prev1 = recent_data.iloc[i-1]
                        next1 = recent_data.iloc[i+1]
                        next2 = recent_data.iloc[i+2]

                        # Swing high: higher than 2 candles on each side
                        if (candle['high'] > prev2['high'] and candle['high'] > prev1['high'] and
                            candle['high'] > next1['high'] and candle['high'] > next2['high']):
                            swing_high = candle['high']
                            if swing_high > current_price:  # Above current price = resistance
                                resistance_levels.append(('SwingH', swing_high))
                            elif swing_high < current_price:  # Below current price = support
                                support_levels.append(('SwingH', swing_high))

                        # Swing low: lower than 2 candles on each side
                        if (candle['low'] < prev2['low'] and candle['low'] < prev1['low'] and
                            candle['low'] < next1['low'] and candle['low'] < next2['low']):
                            swing_low = candle['low']
                            if swing_low < current_price:  # Below current price = support
                                support_levels.append(('SwingL', swing_low))
                            elif swing_low > current_price:  # Above current price = resistance
                                resistance_levels.append(('SwingL', swing_low))
            except Exception as e:
                self.logger.warning(f"Error detecting swing levels: {e}")

            # Step 3: Add EMAs as regular support/resistance levels (in addition to special test-and-bounce logic)
            ema_levels = [
                ('EMA10', ema_10),
                ('EMA20', ema_20)
            ]

            for name, level in ema_levels:
                if level is None:
                    continue

                # Classify EMAs based on actual position relative to price
                if level < current_price:
                    support_levels.append((name, level))  # Below price = support
                elif level > current_price:
                    resistance_levels.append((name, level))  # Above price = resistance

            # Step 4: Add EMA test-and-bounce detection (ADDITIONAL special logic)
            # Special logic for EMAs: detect when candle wicks test away from EMAs but close near EMAs
            # This indicates EMAs are acting as dynamic support/resistance

            # Check for EMA test-and-bounce patterns
            for name in ['EMA10', 'EMA20']:
                level = ema_10 if name == 'EMA10' else ema_20
                if level is None:
                    continue

                # Check if close is near EMA (within tolerance)
                close_to_ema_distance = abs(current_price - level)
                if close_to_ema_distance <= tolerance:
                    # Close is near EMA, check if candle tested away and bounced back

                    # ENHANCED: Check approach direction using previous candle
                    previous_price = df.iloc[-2]['close'] if len(df) >= 2 else current_price

                    # Check both BUY and SELL patterns, but prioritize the stronger one
                    buy_test_distance = abs(current_candle_low - level) if current_candle_low < level - tolerance else 0
                    sell_test_distance = abs(current_candle_high - level) if current_candle_high > level + tolerance else 0

                    # ENHANCED: Proper test-and-bounce logic with approach direction
                    # More restrictive logic: only allow the pattern that matches the approach direction

                    came_from_above = previous_price > level + tolerance
                    came_from_below = previous_price < level - tolerance
                    tested_below = current_candle_low < level - tolerance
                    tested_above = current_candle_high > level + tolerance
                    closed_near_ema = abs(current_price - level) <= tolerance

                    # BUY test-and-bounce: ONLY if came from above AND tested below (ignore if also tested above)
                    buy_pattern = (
                        came_from_above and  # Must come from above
                        not came_from_below and  # Must NOT come from below
                        tested_below and  # Must test below
                        closed_near_ema  # Must close near EMA
                    )

                    # SELL test-and-rejection: ONLY if came from below AND tested above (ignore if also tested below)
                    sell_pattern = (
                        came_from_below and  # Must come from below
                        not came_from_above and  # Must NOT come from above
                        tested_above and  # Must test above
                        closed_near_ema  # Must close near EMA
                    )

                    # ENHANCED: With proper approach direction, both patterns should rarely occur
                    # If both patterns exist, prioritize based on approach direction logic
                    if buy_pattern and sell_pattern:
                        # This should be rare with proper approach direction checking
                        # Choose based on which approach direction is more definitive
                        approach_from_above = previous_price > level + tolerance
                        approach_from_below = previous_price < level - tolerance

                        if approach_from_above and not approach_from_below:
                            # Definitive approach from above - prioritize BUY pattern
                            if (name, level) not in support_levels:
                                support_levels.append((name, level))
                            continue
                        elif approach_from_below and not approach_from_above:
                            # Definitive approach from below - prioritize SELL pattern
                            if (name, level) not in resistance_levels:
                                resistance_levels.append((name, level))
                            continue
                        else:
                            # Ambiguous approach - fall back to stronger test distance
                            if buy_test_distance >= sell_test_distance:
                                if (name, level) not in support_levels:
                                    support_levels.append((name, level))
                            else:
                                if (name, level) not in resistance_levels:
                                    resistance_levels.append((name, level))
                            continue
                    elif buy_pattern:
                        # Only BUY test-and-bounce pattern (came from above, tested below, bounced back)
                        if (name, level) not in support_levels:
                            support_levels.append((name, level))
                        continue
                    elif sell_pattern:
                        # Only SELL test-and-rejection pattern (came from below, tested above, rejected back)
                        if (name, level) not in resistance_levels:
                            resistance_levels.append((name, level))
                        continue

                # If no test-and-bounce pattern detected, add EMA based on normal position
                # Classify based on actual position, not assumed type
                if level < current_price:
                    support_levels.append((name, level))  # Below price = support
                elif level > current_price:
                    resistance_levels.append((name, level))  # Above price = resistance

            # Convert to simple level lists for confluence calculation
            support_level_values = [level[1] for level in support_levels]
            resistance_level_values = [level[1] for level in resistance_levels]

            # ENHANCED CONFLUENCE CALCULATION: Uses candle wicks for ALL levels (EMAs, Regression Channels, Swing Levels)

            # Calculate BUY confluence (proximity to support) - use candle LOW for ALL levels
            if support_level_values:
                max_buy_confluence = 0.0

                # Check EACH support level individually and take the maximum confluence
                for level_name, level_value in support_levels:
                    level_confluence = 0.0

                    # Distance from candle LOW to support level
                    distance_to_support = current_candle_low - level_value

                    # ENHANCED: All levels get wick-based detection (not just EMAs)
                    if level_name in ['EMA10', 'EMA20']:
                        # EMA test-and-bounce: Check if candle LOW tested the EMA support
                        low_to_ema_distance = abs(current_candle_low - level_value)
                        if low_to_ema_distance <= tolerance:
                            level_confluence = 1.0
                    else:
                        # Regression channels and swing levels: Use wick-based proximity
                        penetration_allowance = proximity_threshold * 1.5  # Allow some penetration
                        if distance_to_support >= -penetration_allowance and distance_to_support <= proximity_threshold:
                            # Wick tested support level
                            if distance_to_support <= 0:
                                # Candle LOW penetrated support - maximum confluence
                                level_confluence = 1.0
                            else:
                                # Candle LOW near support - scaled confluence
                                level_confluence = max(0.0, 1.0 - (distance_to_support / proximity_threshold))

                    # Track the highest confluence from any support level
                    max_buy_confluence = max(max_buy_confluence, level_confluence)

                confluence_data['buy_confluence'] = max_buy_confluence

            # Calculate SELL confluence (proximity to resistance) - use candle HIGH for ALL levels
            if resistance_level_values:
                max_sell_confluence = 0.0

                # Check EACH resistance level individually and take the maximum confluence
                for level_name, level_value in resistance_levels:
                    level_confluence = 0.0

                    # Distance from resistance level to candle HIGH
                    distance_to_resistance = level_value - current_candle_high

                    # ENHANCED: All levels get wick-based detection (not just EMAs)
                    if level_name in ['EMA10', 'EMA20']:
                        # EMA test-and-rejection: Check if candle HIGH tested the EMA resistance
                        high_to_ema_distance = abs(current_candle_high - level_value)
                        if high_to_ema_distance <= tolerance:
                            level_confluence = 1.0
                    else:
                        # Regression channels and swing levels: Use wick-based proximity
                        penetration_allowance = proximity_threshold * 1.5  # Allow some penetration
                        if distance_to_resistance >= -penetration_allowance and distance_to_resistance <= proximity_threshold:
                            # Wick tested resistance level
                            if distance_to_resistance <= 0:
                                # Candle HIGH penetrated resistance - maximum confluence
                                level_confluence = 1.0
                            else:
                                # Candle HIGH near resistance - scaled confluence
                                level_confluence = max(0.0, 1.0 - (distance_to_resistance / proximity_threshold))

                    # Track the highest confluence from any resistance level
                    max_sell_confluence = max(max_sell_confluence, level_confluence)

                confluence_data['sell_confluence'] = max_sell_confluence

            # ENHANCED: Store debug info with level classification details and distances
            debug_info = f"ATR:{atr:.4f}, Threshold:{proximity_threshold:.4f}, Tolerance:{tolerance:.4f}"
            debug_info += f", CandleH:{current_candle_high:.2f}, CandleL:{current_candle_low:.2f}"
            debug_info += f", EMA10:{ema_10:.2f}(d:{abs(current_price-ema_10):.2f}), EMA20:{ema_20:.2f}(d:{abs(current_price-ema_20):.2f})"

            if regression_lower:
                debug_info += f", RegL:{regression_lower:.2f}(d:{abs(current_price-regression_lower):.2f})"
            else:
                debug_info += ", RegL:None"

            if regression_upper:
                debug_info += f", RegU:{regression_upper:.2f}(d:{abs(current_price-regression_upper):.2f})"
            else:
                debug_info += ", RegU:None"

            if regression_lower_short:
                debug_info += f", RegLS:{regression_lower_short:.2f}(d:{abs(current_price-regression_lower_short):.2f})"
            else:
                debug_info += ", RegLS:None"

            if regression_upper_short:
                debug_info += f", RegUS:{regression_upper_short:.2f}(d:{abs(current_price-regression_upper_short):.2f})"
            else:
                debug_info += ", RegUS:None"

            debug_info += f", Price:{current_price:.2f}"

            # Show which levels are actually being used for confluence
            debug_info += f", S:{len(support_level_values)} levels"
            if support_level_values:
                support_types = [f"{level[0]}({level[1]:.2f})" for level in support_levels]
                debug_info += f"[{','.join(support_types)}]"
                closest_support = max(support_level_values)
                debug_info += f", Support@{closest_support:.4f}"

            debug_info += f", R:{len(resistance_level_values)} levels"
            if resistance_level_values:
                resistance_types = [f"{level[0]}({level[1]:.2f})" for level in resistance_levels]
                debug_info += f"[{','.join(resistance_types)}]"
                closest_resistance = min(resistance_level_values)
                debug_info += f", Resistance@{closest_resistance:.4f}"

            # NEW: 100-Period Regression Fallback Logic
            # Use 100-period regression when 10/20-period don't provide strong confluence but 100-period supports the signal
            confluence_data = self._apply_100_period_fallback(confluence_data, df, current_price, trend_direction)

            # FIXED: Add confluence values to debug info AFTER 100-period fallback is applied
            debug_info += f", BuyConf:{confluence_data['buy_confluence']:.3f}, SellConf:{confluence_data['sell_confluence']:.3f}"
            self._last_confluence_debug = debug_info

            return confluence_data

        except Exception as e:
            self.logger.error(f"Error calculating support/resistance confluence: {e}")
            return {'buy_confluence': 0.0, 'sell_confluence': 0.0}

    def _apply_100_period_fallback(self, confluence_data, df, current_price, trend_direction="UP"):
        """
        Apply 100-period regression fallback logic when 10/20-period don't align but 100-period supports signal

        Logic:
        - If signal + buyconf/sellconf aligns with 100-period direction BUT 10 and 20-period don't align
        - Then use 100-period regression to boost confluence

        Args:
            confluence_data: Current confluence data with buy_confluence and sell_confluence
            df: DataFrame with regression data
            current_price: Current market price
            trend_direction: Overall trend direction

        Returns:
            Updated confluence_data with 100-period fallback applied
        """
        try:
            if len(df) == 0:
                return confluence_data

            latest = df.iloc[-1]

            # Get regression slopes and trends for all periods
            slope_20 = latest.get('regression_slope', 0)
            slope_10 = latest.get('regression_slope_short', 0)
            slope_100 = latest.get('regression_slope_regime', 0)

            trend_20 = latest.get('regression_trend', 'UNKNOWN')  # UP/DOWN from 20-period
            trend_10 = latest.get('regression_trend_short', 'UNKNOWN')  # UP/DOWN from 10-period
            trend_100 = latest.get('regression_trend_regime', 'UNKNOWN')  # UP/DOWN from 100-period

            # Get regression channel positions
            position_20 = latest.get('regression_position', 0.5)
            position_10 = latest.get('regression_position_short', 0.5)
            position_100 = latest.get('regression_position_regime', 0.5)

            # Check if 10 and 20-period regressions disagree or provide weak signals
            short_long_disagree = (trend_10 != trend_20) or (abs(slope_10) < 0.005 and abs(slope_20) < 0.005)

            # Check if current confluence is weak (below 0.4 threshold)
            buy_confluence_weak = confluence_data['buy_confluence'] < 0.4
            sell_confluence_weak = confluence_data['sell_confluence'] < 0.4

            # Apply 100-period fallback for BUY signals
            if (buy_confluence_weak and short_long_disagree and
                trend_100 == 'UP' and abs(slope_100) >= 0.005):  # 100-period supports UP trend

                # Calculate 100-period confluence boost based on channel position and slope strength
                slope_strength = min(1.0, abs(slope_100) / 0.03)  # Normalize slope strength (0.03 = strong)

                # For BUY: boost when price is in lower half of 100-period channel (good entry point)
                if position_100 <= 0.6:  # Lower 60% of channel
                    position_boost = (0.6 - position_100) / 0.6  # Higher boost when lower in channel
                    fallback_confluence = 0.5 + (slope_strength * 0.3) + (position_boost * 0.2)

                    # Use the higher of current confluence or fallback
                    if fallback_confluence > confluence_data['buy_confluence']:
                        confluence_data['buy_confluence'] = min(1.0, fallback_confluence)
                        # REMOVED: 100-period fallback logging

            # Apply 100-period fallback for SELL signals
            if (sell_confluence_weak and short_long_disagree and
                trend_100 == 'DOWN' and abs(slope_100) >= 0.005):  # 100-period supports DOWN trend

                # Calculate 100-period confluence boost based on channel position and slope strength
                slope_strength = min(1.0, abs(slope_100) / 0.03)  # Normalize slope strength

                # For SELL: boost when price is in upper half of 100-period channel (good entry point)
                if position_100 >= 0.4:  # Upper 60% of channel
                    position_boost = (position_100 - 0.4) / 0.6  # Higher boost when higher in channel
                    fallback_confluence = 0.5 + (slope_strength * 0.3) + (position_boost * 0.2)

                    # Use the higher of current confluence or fallback
                    if fallback_confluence > confluence_data['sell_confluence']:
                        confluence_data['sell_confluence'] = min(1.0, fallback_confluence)
                        # REMOVED: 100-period fallback logging

            return confluence_data

        except Exception as e:
            self.logger.error(f"Error applying 100-period fallback: {e}")
            return confluence_data

    def _calculate_enhanced_strength(self, curr_candle, prev_candle, position_penetration, confluence, signal_type):
        """
        Calculate enhanced signal strength with confluence factors
        """
        try:
            curr_range = curr_candle['high'] - curr_candle['low']

            # Base strength from position penetration (0.3 base + 0.3 for penetration)
            base_strength = 0.3 + (position_penetration * 0.3)

            # Price movement strength
            low_movement = abs(curr_candle['low'] - prev_candle['low']) / prev_candle['low'] if prev_candle['low'] != 0 else 0
            high_movement = abs(curr_candle['high'] - prev_candle['high']) / prev_candle['high'] if prev_candle['high'] != 0 else 0
            movement_strength = min((low_movement + high_movement) * 10, 0.2)  # Cap at 0.2

            # Body strength relative to range
            body_size = abs(curr_candle['close'] - curr_candle['open'])
            body_strength = min(body_size / curr_range, 0.2) if curr_range > 0 else 0  # Cap at 0.2

            # Confluence bonus (up to 0.3)
            confluence_bonus = confluence * 0.3

            # Combined strength
            total_strength = min(base_strength + movement_strength + body_strength + confluence_bonus, 1.0)

            return total_strength

        except Exception as e:
            self.logger.error(f"Error calculating enhanced strength: {e}")
            return 0.5

    def find_recent_swing_points(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Find the most recent Swing High and Swing Low in the last 20 candles.

        A Swing High is a candle whose high is greater than the highs of at least one candle on both sides.
        A Swing Low is a candle whose low is lower than the lows of at least one candle on both sides.

        Returns dict with recent swing points and distances
        """
        try:
            if len(df) < 3:  # Need at least 3 candles for swing detection
                return {
                    'recent_high': None,
                    'recent_low': None,
                    'recent_high_distance': None,
                    'recent_low_distance': None,
                    'swing_points_available': False
                }

            # Use last 20 candles as specified, but exclude the current forming candle
            lookback_candles = min(20, len(df) - 1)  # Exclude current forming candle
            if lookback_candles < 3:
                return {
                    'recent_high': None,
                    'recent_low': None,
                    'recent_high_distance': None,
                    'recent_low_distance': None,
                    'swing_points_available': False
                }

            # Get only closed candles (exclude the last one which might be forming)
            closed_data = df.iloc[:-1].tail(lookback_candles).copy()
            current_price = df.iloc[-1]['close']

            self.logger.info(f"🔍 SWING POINT SEARCH: Analyzing last {lookback_candles} closed candles")

            recent_high = None
            recent_low = None
            recent_high_index = None
            recent_low_index = None

            # Search for swing highs - collect all candidates then choose the highest
            swing_high_candidates = []

            # Method 1: Check most recent closed candle with partial confirmation logic
            if len(closed_data) >= 3:  # Need at least 3 candles for partial confirmation
                most_recent_candle = closed_data.iloc[-1]
                most_recent_high = most_recent_candle['high']

                # Calculate left-side confirmation strength for most recent candle
                left_confirmation_strength = self.calculate_left_side_strength_high(closed_data, len(closed_data) - 1)

                if left_confirmation_strength >= 2:  # Strong left-side confirmation
                    swing_high_candidates.append({
                        'price': most_recent_high,
                        'index': len(closed_data) - 1,
                        'method': 'PARTIAL',
                        'strength': left_confirmation_strength
                    })
                    # REMOVED: Verbose swing high candidate logging

            # Method 2: Traditional method for older candles - STRICT SWING DETECTION
            for i in range(len(closed_data) - 2, 0, -1):  # Exclude most recent and oldest candles
                current_candle = closed_data.iloc[i]
                current_high = current_candle['high']

                # STRICT SWING HIGH: Must be higher than IMMEDIATE neighbors AND at least one further candle on each side
                is_valid_swing_high = False

                # Check immediate neighbors first (must be higher than both)
                left_neighbor_high = closed_data.iloc[i-1]['high'] if i > 0 else float('inf')
                right_neighbor_high = closed_data.iloc[i+1]['high'] if i < len(closed_data) - 1 else float('inf')

                if current_high > left_neighbor_high and current_high > right_neighbor_high:
                    # Now check for additional confirmation on both sides (within 3 candles)
                    has_lower_left = False
                    has_lower_right = False

                    # Check left side (up to 3 candles back)
                    for j in range(max(0, i-3), i):
                        if closed_data.iloc[j]['high'] < current_high:
                            has_lower_left = True
                            break

                    # Check right side (up to 3 candles forward)
                    for j in range(i + 1, min(len(closed_data), i + 4)):
                        if closed_data.iloc[j]['high'] < current_high:
                            has_lower_right = True
                            break

                    # Valid swing high if higher than immediate neighbors AND has additional confirmation
                    is_valid_swing_high = has_lower_left and has_lower_right

                # If this is a valid swing high, add it as candidate
                if is_valid_swing_high:
                    swing_high_candidates.append({
                        'price': current_high,
                        'index': i,
                        'method': 'TRADITIONAL',
                        'strength': 'both_sides'
                    })
                    candles_ago = len(closed_data) - 1 - i
                    # REMOVED: Verbose swing high candidate logging

            # Choose the BEST swing high from all candidates (prioritize recent if close in price)
            if swing_high_candidates:
                best_candidate = self.select_best_swing_high(swing_high_candidates, closed_data)
                recent_high = best_candidate['price']
                recent_high_index = best_candidate['index']
                candles_ago = len(closed_data) - 1 - recent_high_index
                self.logger.info(f"✅ BEST SWING HIGH SELECTED: {recent_high:.5f} ({best_candidate['method']}) at index {recent_high_index} ({candles_ago} candles ago)")

            # Search for swing lows - collect all candidates then choose the lowest
            swing_low_candidates = []

            # Method 1: Check most recent closed candle with partial confirmation logic
            if len(closed_data) >= 3:  # Need at least 3 candles for partial confirmation
                most_recent_candle = closed_data.iloc[-1]
                most_recent_low = most_recent_candle['low']

                # Calculate left-side confirmation strength for most recent candle
                left_confirmation_strength = self.calculate_left_side_strength_low(closed_data, len(closed_data) - 1)

                if left_confirmation_strength >= 2:  # Strong left-side confirmation
                    swing_low_candidates.append({
                        'price': most_recent_low,
                        'index': len(closed_data) - 1,
                        'method': 'PARTIAL',
                        'strength': left_confirmation_strength
                    })
                    # REMOVED: Verbose swing low candidate logging

            # Method 2: Traditional method for older candles - STRICT SWING DETECTION
            for i in range(len(closed_data) - 2, 0, -1):  # Exclude most recent and oldest candles
                current_candle = closed_data.iloc[i]
                current_low = current_candle['low']

                # STRICT SWING LOW: Must be lower than IMMEDIATE neighbors AND at least one further candle on each side
                is_valid_swing_low = False

                # Check immediate neighbors first (must be lower than both)
                left_neighbor_low = closed_data.iloc[i-1]['low'] if i > 0 else float('-inf')
                right_neighbor_low = closed_data.iloc[i+1]['low'] if i < len(closed_data) - 1 else float('-inf')

                if current_low < left_neighbor_low and current_low < right_neighbor_low:
                    # Now check for additional confirmation on both sides (within 3 candles)
                    has_higher_left = False
                    has_higher_right = False

                    # Check left side (up to 3 candles back)
                    for j in range(max(0, i-3), i):
                        if closed_data.iloc[j]['low'] > current_low:
                            has_higher_left = True
                            break

                    # Check right side (up to 3 candles forward)
                    for j in range(i + 1, min(len(closed_data), i + 4)):
                        if closed_data.iloc[j]['low'] > current_low:
                            has_higher_right = True
                            break

                    # Valid swing low if lower than immediate neighbors AND has additional confirmation
                    is_valid_swing_low = has_higher_left and has_higher_right

                # If this is a valid swing low, add it as candidate
                if is_valid_swing_low:
                    swing_low_candidates.append({
                        'price': current_low,
                        'index': i,
                        'method': 'TRADITIONAL',
                        'strength': 'both_sides'
                    })
                    candles_ago = len(closed_data) - 1 - i
                    # REMOVED: Verbose swing low candidate logging

            # Choose the BEST swing low from all candidates (prioritize recent if close in price)
            if swing_low_candidates:
                best_candidate = self.select_best_swing_low(swing_low_candidates, closed_data)
                recent_low = best_candidate['price']
                recent_low_index = best_candidate['index']
                candles_ago = len(closed_data) - 1 - recent_low_index
                self.logger.info(f"✅ BEST SWING LOW SELECTED: {recent_low:.5f} ({best_candidate['method']}) at index {recent_low_index} ({candles_ago} candles ago)")

            # Log results
            if recent_high is None and recent_low is None:
                self.logger.info(f"❌ No swing points found in last {lookback_candles} closed candles")
            else:
                if recent_high:
                    candles_ago = len(closed_data) - 1 - recent_high_index
                    self.logger.info(f"📍 Recent Swing High: {recent_high:.5f} ({candles_ago} candles ago)")
                if recent_low:
                    candles_ago = len(closed_data) - 1 - recent_low_index
                    self.logger.info(f"📍 Recent Swing Low: {recent_low:.5f} ({candles_ago} candles ago)")

            # Calculate distances from current price
            recent_high_distance = abs(current_price - recent_high) if recent_high else None
            recent_low_distance = abs(current_price - recent_low) if recent_low else None

            return {
                'recent_high': recent_high,
                'recent_low': recent_low,
                'recent_high_distance': recent_high_distance,
                'recent_low_distance': recent_low_distance,
                'swing_points_available': recent_high is not None or recent_low is not None,
                'recent_high_candles_ago': len(closed_data) - 1 - recent_high_index if recent_high_index is not None else None,
                'recent_low_candles_ago': len(closed_data) - 1 - recent_low_index if recent_low_index is not None else None,
                'search_depth': lookback_candles
            }

        except Exception as e:
            self.logger.error(f"❌ Error in swing point detection: {e}")
            return {
                'recent_high': None,
                'recent_low': None,
                'recent_high_distance': None,
                'recent_low_distance': None,
                'swing_points_available': False
            }

    def calculate_left_side_strength_high(self, data, candle_index):
        """
        Calculate how strong the left-side confirmation is for a potential swing high
        STRICT VERSION: Must be higher than immediate left neighbor first

        Args:
            data: DataFrame of candle data
            candle_index: Index of the candle to check

        Returns:
            int: Number of candles on the left that confirm this as a swing high (0 if not valid)
        """
        try:
            if candle_index <= 0:
                return 0

            current_high = data.iloc[candle_index]['high']

            # STRICT CHECK: Must be higher than immediate left neighbor
            if candle_index > 0:
                immediate_left_high = data.iloc[candle_index - 1]['high']
                if current_high <= immediate_left_high:
                    return 0  # Not a valid swing high if not higher than immediate neighbor

            confirmation_count = 0

            # Check up to 5 candles to the left (older candles)
            lookback = min(5, candle_index)
            for j in range(candle_index - lookback, candle_index):
                if data.iloc[j]['high'] < current_high:
                    confirmation_count += 1

            return confirmation_count

        except Exception as e:
            self.logger.error(f"❌ Error calculating left-side strength for high: {e}")
            return 0

    def calculate_left_side_strength_low(self, data, candle_index):
        """
        Calculate how strong the left-side confirmation is for a potential swing low
        STRICT VERSION: Must be lower than immediate left neighbor first

        Args:
            data: DataFrame of candle data
            candle_index: Index of the candle to check

        Returns:
            int: Number of candles on the left that confirm this as a swing low (0 if not valid)
        """
        try:
            if candle_index <= 0:
                return 0

            current_low = data.iloc[candle_index]['low']

            # STRICT CHECK: Must be lower than immediate left neighbor
            if candle_index > 0:
                immediate_left_low = data.iloc[candle_index - 1]['low']
                if current_low >= immediate_left_low:
                    return 0  # Not a valid swing low if not lower than immediate neighbor

            confirmation_count = 0

            # Check up to 5 candles to the left (older candles)
            lookback = min(5, candle_index)
            for j in range(candle_index - lookback, candle_index):
                if data.iloc[j]['low'] > current_low:
                    confirmation_count += 1

            return confirmation_count

        except Exception as e:
            self.logger.error(f"❌ Error calculating left-side strength for low: {e}")
            return 0

    def select_best_swing_high(self, candidates, data):
        """
        Select the best swing high from candidates using RECENCY-FIRST approach

        Priority:
        1. Find the MOST RECENT valid swing high (closest to current candle)
        2. Only look for older/higher swings if no recent swing found
        """
        try:
            if not candidates:
                return None

            # Sort candidates by recency (most recent first)
            candidates_by_recency = sorted(candidates, key=lambda x: len(data) - 1 - x['index'])

            # REMOVED: Verbose swing high analysis logging

            # PURE RECENCY-FIRST APPROACH: Simply take the most recent swing high
            most_recent = candidates_by_recency[0]  # First in sorted list = most recent
            most_recent_candles_ago = len(data) - 1 - most_recent['index']

            self.logger.info(f"🎯 SELECTED MOST RECENT SWING HIGH: {most_recent['price']:.5f} ({most_recent_candles_ago} candles ago)")
            return most_recent

        except Exception as e:
            self.logger.error(f"❌ Error selecting best swing high: {e}")
            # Fallback to most recent candidate
            if candidates:
                candidates_by_recency = sorted(candidates, key=lambda x: len(data) - 1 - x['index'])
                return candidates_by_recency[0]
            return None

    def select_best_swing_low(self, candidates, data):
        """
        Select the best swing low from candidates using RECENCY-FIRST approach

        Priority:
        1. Find the MOST RECENT valid swing low (closest to current candle)
        2. Only look for older/lower swings if no recent swing found
        """
        try:
            if not candidates:
                return None

            # Sort candidates by recency (most recent first)
            candidates_by_recency = sorted(candidates, key=lambda x: len(data) - 1 - x['index'])

            # REMOVED: Verbose swing low analysis logging

            # PURE RECENCY-FIRST APPROACH: Simply take the most recent swing low
            most_recent = candidates_by_recency[0]  # First in sorted list = most recent
            most_recent_candles_ago = len(data) - 1 - most_recent['index']

            self.logger.info(f"🎯 SELECTED MOST RECENT SWING LOW: {most_recent['price']:.5f} ({most_recent_candles_ago} candles ago)")
            return most_recent

        except Exception as e:
            self.logger.error(f"❌ Error selecting best swing low: {e}")
            # Fallback to most recent candidate
            if candidates:
                candidates_by_recency = sorted(candidates, key=lambda x: len(data) - 1 - x['index'])
                return candidates_by_recency[0]
            return None

    def set_candle_confirmation_trailing_stop(self, confirmation_candle_data, position_type):
        """
        ENHANCED: Set trailing stop loss 30 pips above/below confirmation candle instead of closing trade
        Now includes profit/loss checking per user requirements

        Args:
            confirmation_candle_data: Dict with candle high/low/close data
            position_type: 'BUY' or 'SELL'
        """
        try:
            if not self.current_position:
                self.logger.warning("⚠️ No current position to set candle confirmation stop")
                return False

            # ATR value retrieval (significance filtering removed)
            # Get SL distance from trailing data (no longer need ATR)
            original_sl_distance = 1.50  # Fixed 150-point distance
            if hasattr(self, 'trailing_stop_data') and self.trailing_stop_data:
                original_sl_distance = self.trailing_stop_data.get('original_sl_distance', 1.50)

            # ATR-based candle significance filter removed per user request

            # ENHANCED: Check if candle confirmation trailing should be allowed
            should_allow, allow_reason = self.should_allow_trailing("CANDLE")
            if not should_allow:
                self.logger.info(f"🚫 CANDLE CONFIRMATION TRAILING BLOCKED: {allow_reason}")
                return False

            # Get current position info
            positions = self.mt5_manager.get_positions(self.symbol)
            if not positions:
                self.logger.warning("⚠️ No positions found for candle confirmation stop")
                return False

            position = positions[0]  # We only have one position
            current_sl = position.get('sl', 0)

            # Calculate new stop loss 30 pips above/below confirmation candle (ENHANCED: More breathing room)
            pip_size = self.mt5_manager.get_pip_size(self.symbol)

            if position_type == 'BUY':
                # For BUY: set stop 30 pips below confirmation candle low
                new_sl = confirmation_candle_data['low'] - (pip_size * 30)
                self.logger.info(f"🎯 CANDLE CONFIRMATION TRAILING STOP (BUY): Current SL={current_sl:.5f} → New SL={new_sl:.5f} (30 pips below confirmation candle low {confirmation_candle_data['low']:.5f})")
            else:  # SELL
                # For SELL: set stop 30 pips above confirmation candle high
                new_sl = confirmation_candle_data['high'] + (pip_size * 30)
                self.logger.info(f"🎯 CANDLE CONFIRMATION TRAILING STOP (SELL): Current SL={current_sl:.5f} → New SL={new_sl:.5f} (30 pips above confirmation candle high {confirmation_candle_data['high']:.5f})")

            # Check if new stop loss is different from current (prevent "No changes" error)
            if abs(new_sl - current_sl) < pip_size * 1.0:  # Less than 1 pip difference
                self.logger.warning(f"⚠️ CANDLE CONFIRMATION TRAILING STOP: New SL {new_sl:.5f} too close to current SL {current_sl:.5f} - skipping modification")
                return False

            # Modify the stop loss
            if self.mt5_manager.modify_position(position['ticket'], stop_loss=new_sl):
                # Store confirmation stop data
                self.candle_confirmation_stop = {
                    'confirmation_sl': new_sl,
                    'original_sl': current_sl,
                    'candle_close_time': datetime.now(),
                    'position_type': position_type,
                    'confirmation_candle': confirmation_candle_data
                }

                self.logger.info(f"✅ CANDLE CONFIRMATION STOP SET: New SL={new_sl:.5f}, Original SL={current_sl:.5f}")
                return True
            else:
                self.logger.error(f"❌ Failed to set candle confirmation trailing stop")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error setting candle confirmation trailing stop: {e}")
            return False

    def check_candle_confirmation_stop_revert(self):
        """
        Check if we should revert candle confirmation stop back to original level
        Called when a new candle closes and trade wasn't stopped out
        """
        try:
            # DISABLED: Check if candle confirmation reverting is disabled
            if self.disable_candle_confirmation_revert:
                if self.candle_confirmation_stop:
                    self.logger.info(f"🚫 CANDLE CONFIRMATION REVERT DISABLED: Reverting functionality is disabled")
                    self.logger.info(f"   Candle confirmation stop will remain active (no automatic revert)")
                    # Clear the confirmation stop data since we're not reverting
                    self.candle_confirmation_stop = None
                return False

            if not self.candle_confirmation_stop:
                # No candle confirmation stop active
                return False

            # Enhanced debugging for revert logic
            current_time = datetime.now()
            confirmation_time = self.candle_confirmation_stop['candle_close_time']
            time_since_confirmation = (current_time - confirmation_time).total_seconds()

            self.logger.info(f"🔍 CANDLE CONFIRMATION REVERT CHECK:")
            self.logger.info(f"   Confirmation set at: {confirmation_time}")
            self.logger.info(f"   Current time: {current_time}")
            self.logger.info(f"   Time elapsed: {time_since_confirmation:.1f} seconds")
            self.logger.info(f"   Revert threshold: 300 seconds (5 minutes)")

            # USER REQUESTED: Revert after exactly one candle (5 minutes) if trade didn't close
            # Use 290 seconds (4:50) to account for timing variations in candle close detection
            should_revert = time_since_confirmation >= 290  # One candle = 5 minutes

            if should_revert:
                self.logger.info(f"⏰ ONE CANDLE REVERT: Trailing stop active for {time_since_confirmation:.1f}s (≥4:50) - reverting to original position")
                # Check if position still exists (wasn't stopped out)
                positions = self.mt5_manager.get_positions(self.symbol)
                if positions and self.current_position:
                    position = positions[0]
                    original_sl = self.candle_confirmation_stop['original_sl']
                    current_sl = position.get('sl', 0)

                    self.logger.info(f"🔄 REVERT CONDITIONS MET: Time elapsed ≥ 5 minutes")
                    self.logger.info(f"   Position exists: ✓")
                    self.logger.info(f"   Original SL: {original_sl:.5f}")
                    self.logger.info(f"   Current SL: {current_sl:.5f}")
                    self.logger.info(f"   Position type: {self.current_position['type']}")

                    # USER REQUESTED: Always revert to EXACTLY where it was before trailing
                    original_sl = self.candle_confirmation_stop['original_sl']
                    position_type = self.current_position['type']

                    # Get current price for validation
                    tick_info = self.mt5_manager.get_symbol_info_tick(self.symbol)
                    if tick_info:
                        current_price = tick_info['bid'] if position_type == 'SELL' else tick_info['ask']

                        self.logger.info(f"🔄 REVERTING TO EXACT ORIGINAL POSITION:")
                        self.logger.info(f"   Original SL: {original_sl:.5f}")
                        self.logger.info(f"   Current Price: {current_price:.5f}")
                        self.logger.info(f"   Position Type: {position_type}")

                        # Validate that original_sl is on the correct side
                        valid_revert_sl = original_sl
                        if position_type == 'BUY' and original_sl >= current_price:
                            # BUY position SL must be below current price
                            valid_revert_sl = current_price - 1.50  # 150 points below current price
                            self.logger.warning(f"⚠️ INVALID ORIGINAL SL: BUY position original SL {original_sl:.5f} >= current price {current_price:.5f}")
                            self.logger.info(f"   Using safe SL: {valid_revert_sl:.5f} (150 points below current price)")
                        elif position_type == 'SELL' and original_sl <= current_price:
                            # SELL position SL must be above current price
                            valid_revert_sl = current_price + 1.50  # 150 points above current price
                            self.logger.warning(f"⚠️ INVALID ORIGINAL SL: SELL position original SL {original_sl:.5f} <= current price {current_price:.5f}")
                            self.logger.info(f"   Using safe SL: {valid_revert_sl:.5f} (150 points above current price)")

                        if self.mt5_manager.modify_position(position['ticket'], stop_loss=valid_revert_sl):
                            self.logger.info(f"✅ TRAILING STOP REVERTED: Back to original SL={valid_revert_sl:.5f}")
                            self.logger.info(f"   Trade didn't close after one candle - reverted to exact original position")

                            # Clear confirmation stop data
                            self.candle_confirmation_stop = None
                            return True
                        else:
                            self.logger.error(f"❌ Failed to revert trailing stop to original position {valid_revert_sl:.5f}")
                    else:
                        self.logger.error(f"❌ Could not get current price for revert analysis")
                else:
                    # Position was closed (likely by stop loss)
                    self.logger.info(f"✅ Position was closed by candle confirmation stop")
                    self.candle_confirmation_stop = None

            return False

        except Exception as e:
            self.logger.error(f"❌ Error checking candle confirmation stop revert: {e}")
            return False

    def _calculate_swing_based_stop_loss(self, signal, confirmation_candle_data, features_df):
        """
        Calculate stop loss based on swing levels + 150 points buffer

        Args:
            signal: 'BUY' or 'SELL'
            confirmation_candle_data: Dict with candle high/low data
            features_df: DataFrame with market data for swing detection

        Returns:
            tuple: (sl_price, sl_method_description)
        """
        try:
            # Get swing points using existing method
            swing_data = self.find_recent_swing_points(features_df)

            if signal == 'BUY':
                # For BUY: Use swing low as base, add 150 points buffer
                if swing_data['recent_low'] is not None:
                    # Use swing low - 150 points
                    sl_price = swing_data['recent_low'] - 1.50
                    sl_method = f"Swing Low {swing_data['recent_low']:.5f} - 150 points"
                else:
                    # Fallback to signal candle low - 150 points
                    sl_price = confirmation_candle_data['low'] - 1.50
                    sl_method = f"Signal Candle Low {confirmation_candle_data['low']:.5f} - 150 points (no swing low found)"

            else:  # SELL
                # For SELL: Use swing high as base, add 150 points buffer
                if swing_data['recent_high'] is not None:
                    # Use swing high + 150 points
                    sl_price = swing_data['recent_high'] + 1.50
                    sl_method = f"Swing High {swing_data['recent_high']:.5f} + 150 points"
                else:
                    # Fallback to signal candle high + 150 points
                    sl_price = confirmation_candle_data['high'] + 1.50
                    sl_method = f"Signal Candle High {confirmation_candle_data['high']:.5f} + 150 points (no swing high found)"

            return sl_price, sl_method

        except Exception as e:
            self.logger.error(f"❌ Error calculating swing-based stop loss: {e}")
            # Fallback to original logic
            if signal == 'BUY':
                sl_price = confirmation_candle_data['low'] - 1.50
                sl_method = f"FALLBACK: Signal Candle Low {confirmation_candle_data['low']:.5f} - 150 points"
            else:
                sl_price = confirmation_candle_data['high'] + 1.50
                sl_method = f"FALLBACK: Signal Candle High {confirmation_candle_data['high']:.5f} + 150 points"
            return sl_price, sl_method

    def place_pending_order_from_confirmation_candle(self, signal, confirmation_candle_data, volume, atr_value, features_df=None):
        """
        Place pending order 1 pip above/below confirmation candle instead of market execution
        Removes any existing pending orders first to avoid multiple pending orders

        Args:
            signal: 'BUY' or 'SELL'
            confirmation_candle_data: Dict with candle high/low/close data
            volume: Position size
            atr_value: ATR value (used for take profit calculation only)
        """
        try:
            # ATR-based candle significance filter removed per user request

            # FIRST: Remove any existing pending orders before placing new one
            if self.pending_orders:
                self.logger.info(f"🗑️ REMOVING EXISTING PENDING ORDERS: Found {len(self.pending_orders)} pending orders")
                for pending_order in list(self.pending_orders):  # Use list() to avoid modification during iteration
                    ticket = pending_order['ticket']
                    if self.mt5_manager.cancel_order(ticket):
                        self.logger.info(f"✅ Cancelled existing pending order: {ticket}")
                        self.pending_orders.remove(pending_order)
                    else:
                        self.logger.warning(f"⚠️ Failed to cancel pending order: {ticket}")

                # Clear the list if any orders couldn't be cancelled
                if self.pending_orders:
                    self.logger.warning(f"⚠️ Some pending orders couldn't be cancelled, clearing list anyway")
                    self.pending_orders.clear()
            pip_size = self.mt5_manager.get_pip_size(self.symbol)

            # ENHANCED: Get current market price for validation
            tick_info = self.mt5_manager.get_symbol_info_tick(self.symbol)
            if not tick_info:
                self.logger.error("❌ Cannot get current market price for pending order validation")
                return False

            current_bid = tick_info['bid']
            current_ask = tick_info['ask']

            if signal == 'BUY':
                # For BUY: place buy stop 1 pip above confirmation candle high
                entry_price = confirmation_candle_data['high'] + pip_size
                order_type = 'BUY_STOP'

                # ENHANCED: Use swing low + 150 points for stop loss
                sl_price, sl_method = self._calculate_swing_based_stop_loss(signal, confirmation_candle_data, features_df)

                # ENHANCED: Validate BUY STOP price (must be above current ask)
                if entry_price <= current_ask:
                    # Price moved above our intended entry - adjust entry price
                    entry_price = current_ask + (pip_size * 2)  # 2 pips above current ask
                    # Keep SL based on original calculation, not adjusted entry
                    self.logger.warning(f"⚠️ PRICE MOVED: Adjusted BUY STOP from {confirmation_candle_data['high'] + pip_size:.5f} to {entry_price:.5f} (current ask: {current_ask:.5f})")

                self.logger.info(f"🎯 PLACING BUY STOP ORDER: Entry={entry_price:.5f} (target: 1 pip above confirmation high {confirmation_candle_data['high']:.5f})")
                self.logger.info(f"🛡️ SWING-BASED SL: SL={sl_price:.5f} ({sl_method})")
            else:  # SELL
                # For SELL: place sell stop 1 pip below confirmation candle low
                entry_price = confirmation_candle_data['low'] - pip_size
                order_type = 'SELL_STOP'

                # ENHANCED: Use swing high + 150 points for stop loss
                sl_price, sl_method = self._calculate_swing_based_stop_loss(signal, confirmation_candle_data, features_df)

                # ENHANCED: Validate SELL STOP price (must be below current bid)
                if entry_price >= current_bid:
                    # Price moved below our intended entry - adjust entry price
                    entry_price = current_bid - (pip_size * 2)  # 2 pips below current bid
                    # Keep SL based on original calculation, not adjusted entry
                    self.logger.warning(f"⚠️ PRICE MOVED: Adjusted SELL STOP from {confirmation_candle_data['low'] - pip_size:.5f} to {entry_price:.5f} (current bid: {current_bid:.5f})")

                self.logger.info(f"🎯 PLACING SELL STOP ORDER: Entry={entry_price:.5f} (target: 1 pip below confirmation low {confirmation_candle_data['low']:.5f})")
                self.logger.info(f"🛡️ SWING-BASED SL: SL={sl_price:.5f} ({sl_method})")

            # Calculate take profit (if needed)
            tp_price = None  # We'll use trailing stops instead

            # Place the pending order
            ticket = self.mt5_manager.place_order(
                symbol=self.symbol,
                order_type=order_type,
                volume=volume,
                price=entry_price,
                sl=sl_price,
                tp=tp_price,
                comment=f"P{signal}"
            )

            if ticket:
                # Track the pending order
                pending_order = {
                    'ticket': ticket,
                    'type': signal,
                    'price': entry_price,
                    'created_time': datetime.now(),
                    'candles_waited': 0,
                    'confirmation_candle': confirmation_candle_data
                }
                self.pending_orders.append(pending_order)

                self.logger.info(f"✅ PENDING ORDER PLACED: Ticket={ticket}, Type={order_type}, Entry={entry_price:.5f}, SL={sl_price:.5f}")
                return True
            else:
                self.logger.error(f"❌ Failed to place pending order")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error placing pending order: {e}")
            return False

    def check_and_remove_expired_pending_orders(self):
        """
        Check pending orders and remove those that haven't been filled within a few candles
        """
        try:
            if not self.pending_orders:
                return

            current_time = datetime.now()
            orders_to_remove = []

            for pending_order in self.pending_orders:
                # Check if order still exists (might have been filled)
                order_info = self.mt5_manager.get_order_info(pending_order['ticket'])

                if not order_info:
                    # Order doesn't exist anymore (likely filled or cancelled)
                    orders_to_remove.append(pending_order)
                    self.logger.info(f"📋 PENDING ORDER FILLED/CANCELLED: Ticket={pending_order['ticket']}")
                    continue

                # Check if order has expired (more than 3 candles = 15 minutes)
                time_elapsed = (current_time - pending_order['created_time']).total_seconds()
                candles_elapsed = time_elapsed / 300  # 300 seconds = 5 minutes per candle

                if candles_elapsed >= 3:  # 3 candles = 15 minutes
                    # Cancel the expired order
                    if self.mt5_manager.cancel_order(pending_order['ticket']):
                        self.logger.info(f"🗑️ EXPIRED PENDING ORDER CANCELLED: Ticket={pending_order['ticket']} (waited {candles_elapsed:.1f} candles)")
                        orders_to_remove.append(pending_order)
                    else:
                        self.logger.error(f"❌ Failed to cancel expired pending order: Ticket={pending_order['ticket']}")
                else:
                    self.logger.info(f"⏳ PENDING ORDER WAITING: Ticket={pending_order['ticket']} ({candles_elapsed:.1f}/3 candles)")

            # Remove processed orders from tracking
            for order in orders_to_remove:
                self.pending_orders.remove(order)

        except Exception as e:
            self.logger.error(f"❌ Error checking pending orders: {e}")

    def check_pending_orders_filled(self):
        """
        Check if any pending orders were filled and update position tracking
        """
        try:
            if not self.pending_orders:
                return

            # Check current positions to see if any match our pending orders
            positions = self.mt5_manager.get_positions(self.symbol)

            if positions:
                for position in positions:
                    # Check if this position matches any of our pending orders
                    for pending_order in self.pending_orders[:]:  # Use slice to avoid modification during iteration
                        # Check if order was filled by looking for matching position
                        order_info = self.mt5_manager.get_order_info(pending_order['ticket'])

                        if not order_info:  # Order no longer exists (likely filled)
                            # Check if we have a new position that matches
                            if (position.get('type') == pending_order['type'] and
                                abs(position.get('price_open', 0) - pending_order['price']) < 0.0001):  # Price match within 1 pip

                                # Pending order was filled!
                                self.logger.info(f"✅ PENDING ORDER FILLED: Ticket={pending_order['ticket']}")
                                self.logger.info(f"   Position opened: {position.get('type')} at {position.get('price_open'):.5f}")

                                # Update current position tracking
                                self.current_position = {
                                    'type': position.get('type'),
                                    'ticket': position.get('ticket'),
                                    'time': datetime.now(),
                                    'volume': position.get('volume'),
                                    'remaining_volume': position.get('volume'),
                                    'price': position.get('price_open'),
                                    'sl': position.get('sl'),
                                    'take_profit': position.get('tp', 0.0),  # Include TP data
                                    'original_sl': position.get('sl')  # NEW: Store original SL for trailing calculations
                                }

                                # Initialize trailing stop data
                                entry_price = position.get('price_open')
                                initial_sl = position.get('sl')

                                # ENHANCED: Calculate SL distance in POINTS for XAUUSD consistency
                                raw_sl_distance = abs(initial_sl - entry_price) if initial_sl and entry_price else 0.001

                                # UNIT CONVERSION FIX: Convert to points for XAUUSD
                                if self.symbol == "XAUUSD!":
                                    # For XAUUSD: SL distance conversion factor ~45.9 (based on analysis)
                                    # This converts price difference to points for SL distance calculation
                                    original_sl_distance = raw_sl_distance * 45.9
                                    self.logger.info(f"🔧 XAUUSD SL CONVERSION: {raw_sl_distance:.2f} price → {original_sl_distance:.1f} points")
                                else:
                                    original_sl_distance = raw_sl_distance
                                    self.logger.info(f"🔧 SL DISTANCE: {original_sl_distance:.5f} (no conversion)")

                                self.trailing_stop_data = {
                                    'initial_sl': initial_sl,
                                    'current_sl': initial_sl,
                                    'original_sl_distance': original_sl_distance,
                                    'raw_sl_distance': raw_sl_distance,  # Store both for debugging
                                    'profit_sl_count': 0  # Track profit in SL distance units
                                }

                                self.logger.info(f"📊 TRAILING STOP INITIALIZED:")
                                self.logger.info(f"   Entry: {entry_price:.5f}, SL: {initial_sl:.5f}")
                                self.logger.info(f"   Raw Distance: {raw_sl_distance:.5f}, Points Distance: {original_sl_distance:.1f}")
                                self.logger.info(f"🔧 DEBUG: profit_sl_count INITIALIZED to {self.trailing_stop_data['profit_sl_count']}")

                                # START REAL-TIME TRAILING MONITOR
                                self.start_real_time_trailing_monitor()

                                # Remove from pending orders
                                self.pending_orders.remove(pending_order)

                                self.logger.info(f"🎯 POSITION TRACKING UPDATED:")
                                self.logger.info(f"   Type: {self.current_position['type']}")
                                self.logger.info(f"   Volume: {self.current_position['volume']}")
                                self.logger.info(f"   Entry: {self.current_position['price']:.5f}")
                                self.logger.info(f"   Stop Loss: {position.get('sl'):.5f}")

                                break

        except Exception as e:
            self.logger.error(f"❌ Error checking filled pending orders: {e}")

    def check_swing_distance_filter(self, signal: str, swing_points: Dict[str, Any], atr_value: float) -> Tuple[bool, str, float]:
        """
        Check if trade entry is within acceptable distance from recent swing points
        Now returns dynamic position sizing based on distance

        Args:
            signal: "BUY" or "SELL"
            swing_points: Dict from find_recent_swing_points()
            atr_value: Current ATR value

        Returns:
            Tuple of (approved: bool, reason: str, position_size_factor: float)
            position_size_factor: 1.0 = full size, 0.5 = half size, 0.0 = blocked
        """
        try:
            if not swing_points.get('swing_points_available', False):
                # No swing points found - allow full size trade
                return True, "No recent swing points found", 1.0

            # Define distance thresholds
            close_distance_atr = 1.5  # Full size
            medium_distance_atr = 3.0  # Half size
            max_distance_atr = 6.0     # Block trades beyond this

            def calculate_position_size_factor(distance_atr: float) -> Tuple[bool, str, float]:
                """Calculate position size factor based on distance in ATR"""
                if distance_atr <= close_distance_atr:
                    # Close to swing point - full size
                    return True, f"FULL SIZE: {distance_atr:.2f} ATR (≤{close_distance_atr} ATR)", 1.0
                elif distance_atr <= medium_distance_atr:
                    # Medium distance - half size
                    return True, f"HALF SIZE: {distance_atr:.2f} ATR (>{close_distance_atr} but ≤{medium_distance_atr} ATR)", 0.5
                elif distance_atr <= max_distance_atr:
                    # Far distance - scaled size using 1.5/X formula
                    size_factor = 1.5 / distance_atr
                    size_factor = max(0.1, min(size_factor, 1.0))  # Clamp between 10% and 100%
                    return True, f"SCALED SIZE: {distance_atr:.2f} ATR → {size_factor:.2f}x size (1.5/{distance_atr:.2f} formula)", size_factor
                else:
                    # Too far - block trade
                    return False, f"BLOCKED: {distance_atr:.2f} ATR (>{max_distance_atr} ATR - too far from swing point)", 0.0

            if signal == "BUY":
                # For LONG trades, check distance from recent low
                recent_low = swing_points.get('recent_low')
                if recent_low is not None:
                    distance = swing_points.get('recent_low_distance')
                    if distance is not None:
                        distance_atr = distance / atr_value if atr_value > 0 else 0
                        approved, size_reason, size_factor = calculate_position_size_factor(distance_atr)
                        reason = f"BUY {size_reason} - Distance from recent low {recent_low:.5f}: {distance:.5f} points"
                        return approved, reason, size_factor
                # No recent low found - allow full size trade
                return True, "BUY approved: No recent low found", 1.0

            elif signal == "SELL":
                # For SHORT trades, check distance from recent high
                recent_high = swing_points.get('recent_high')
                if recent_high is not None:
                    distance = swing_points.get('recent_high_distance')
                    if distance is not None:
                        distance_atr = distance / atr_value if atr_value > 0 else 0
                        approved, size_reason, size_factor = calculate_position_size_factor(distance_atr)
                        reason = f"SELL {size_reason} - Distance from recent high {recent_high:.5f}: {distance:.5f} points"
                        return approved, reason, size_factor
                # No recent high found - allow full size trade
                return True, "SELL approved: No recent high found", 1.0

            # Unknown signal type - reject
            return False, f"Unknown signal type: {signal}", 0.0

        except Exception as e:
            self.logger.error(f"❌ Error in swing distance filter: {e}")
            # On error, allow full size trade to avoid blocking system
            return True, f"Error in swing filter - allowing full size trade: {e}", 1.0

    def start_trading(self):
        self.is_running = True
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/fixed_live_trading.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def is_position_profitable(self):
        """Check if current position is in profit"""
        try:
            if not self.current_position:
                return False, 0, "No position"

            # Get current market price
            tick_info = self.mt5_manager.get_symbol_info_tick(self.symbol)
            if not tick_info:
                return False, 0, "No tick data"

            entry_price = self.current_position['price']
            position_type = self.current_position['type']

            # Use appropriate price for position type
            current_price = tick_info['ask'] if position_type == 'BUY' else tick_info['bid']

            # Calculate profit
            if position_type == 'BUY':
                profit_points = current_price - entry_price
                is_profitable = current_price > entry_price
            else:  # SELL
                profit_points = entry_price - current_price
                is_profitable = current_price < entry_price

            return is_profitable, profit_points, f"{position_type}: {entry_price:.5f} → {current_price:.5f}"

        except Exception as e:
            self.logger.error(f"❌ Error checking position profit: {e}")
            return False, 0, f"Error: {e}"

    def load_model(self):
        """DISABLED: Model loading (using candle strength instead)"""
        try:
            # Model loading disabled - using candle strength decision instead
            self.model = None
            self.scaler = None
            self.selected_features = None

            # REMOVED: Verbose model loading logs
            return True

        except Exception as e:
            self.logger.error(f"❌ Error in model setup: {e}")
            return False
    
    def get_live_prediction(self):
        """Get live prediction using fixed model with regime detection"""
        try:
            # Get live data
            df = self.mt5_manager.get_latest_data(self.symbol, self.timeframe, 200)
            if df is None or len(df) < 100:
                return None, 0, "No data", None, None, None, {}, {}

            # Calculate features
            features_df = self.feature_engineer.create_technical_indicators(df)

            # Add regime indicators
            features_df = self.regime_detector.calculate_regime_indicators(features_df)

            # NEW: Add candlestick position analysis
            features_df = self.regime_detector.calculate_candle_position(features_df)

            # Store latest features_df for exit confirmations
            self.latest_features_df = features_df

            # Detect current regime using BOTH detectors for comparison
            # Original regime detector (for compatibility)
            orig_regime, orig_regime_conf, orig_regime_details, trend_direction, accurate_trend_direction = self.regime_detector.detect_regime(features_df)

            # Enhanced regime detector (for improved accuracy) - FIXED: Pass raw OHLC data
            enhanced_regime, enhanced_conf, enhanced_details = self.enhanced_regime_detector.detect_regime(df)

            # Use enhanced detector results but keep original variables for compatibility
            regime = enhanced_regime
            regime_conf = enhanced_conf
            regime_details = enhanced_details

            # Log comparison for monitoring
            self.logger.info(f"REGIME COMPARISON - Original: {orig_regime} ({orig_regime_conf:.1f}%) | Enhanced: {enhanced_regime} ({enhanced_conf:.1f}%)")

            # Safe access to enhanced details with fallback
            trending_score = enhanced_details.get('trending_score', 0)
            ranging_score = enhanced_details.get('ranging_score', 0)
            self.logger.info(f"Enhanced Scores - Trending: {trending_score}/98 | Ranging: {ranging_score}/98")

            # NEW: Calculate recent candle strength for logging
            candle_strength = self.regime_detector.calculate_recent_candle_strength(features_df)

            # REMOVED: Verbose candle strength debug logging

            # Get ATR for stop loss calculation (no longer need ML features)
            latest_atr = features_df['atr'].iloc[-1]
            if pd.isna(latest_atr):
                latest_atr = features_df['atr'].dropna().iloc[-1] if len(features_df['atr'].dropna()) > 0 else 0.01

            # TEMPORARY: Candle pattern-based primary signal generation with candle strength voting
            candle_net_strength = candle_strength['net_strength'] * 100  # Convert to percentage

            # Calculate QQE indicators for primary signal generation
            features_df = self.qqe_indicator.calculate_qqe_bands(features_df)
            features_df = self.qqe_indicator.generate_qqe_signals(features_df)
            qqe_analysis = self.qqe_indicator.get_qqe_analysis(features_df)

            # SWITCHED: Use QQE signal as primary signal generator
            qqe_signal = qqe_analysis.get('qqe_signal', 0)  # 1 for buy, -1 for sell, 0 for no signal
            qqe_strength = qqe_analysis.get('qqe_signal_strength', 0.0)  # QQE signal strength
            qqe_trend_signal = qqe_analysis.get('qqe_trend_signal', 0)  # Continuous trend signal

            # QQE Volume analysis
            volume_confirmation = qqe_analysis.get('volume_confirmation', 1.0)
            volume_strength = qqe_analysis.get('volume_strength', 'NORMAL')
            divergence_filter = qqe_analysis.get('divergence_filter', False)

            # Set volume factor based on QQE volume analysis
            pattern_volume_factor = volume_confirmation  # Use QQE volume confirmation as volume factor

            # Calculate support/resistance confluence for additional confirmation
            candle_pattern_analysis = self.calculate_candle_pattern_signal(features_df)
            confluence_score = candle_pattern_analysis.get('support_resistance_confluence', 0.0)  # S/R confluence

            # DEBUG: Log QQE analysis
            volume_text = "HIGH" if volume_confirmation > 1.2 else "NORMAL" if volume_confirmation >= 0.8 else "LOW"
            trend_text = "BULLISH" if qqe_trend_signal > 0 else "BEARISH" if qqe_trend_signal < 0 else "NEUTRAL"
            self.logger.info(f"🎯 QQE SIGNAL ANALYSIS: Signal={qqe_signal}, Strength={qqe_strength:.3f}, Trend={trend_text}, Volume={volume_text}({volume_confirmation:.2f}x), Confluence={confluence_score:.3f}")
            if len(features_df) >= 2:
                curr_candle = features_df.iloc[-2]  # Last closed candle

                # Log QQE technical details
                rsi = qqe_analysis.get('rsi', 0)
                rsi_ma = qqe_analysis.get('rsi_ma', 0)
                fast_atr_rsi_tl = qqe_analysis.get('fast_atr_rsi_tl', 0)
                qqe_long_count = qqe_analysis.get('qqe_long_count', 0)
                qqe_short_count = qqe_analysis.get('qqe_short_count', 0)

                self.logger.info(f"   📊 QQE TECHNICAL: RSI={rsi:.1f}, RSI_MA={rsi_ma:.1f}, FastATR={fast_atr_rsi_tl:.1f}")
                self.logger.info(f"   📊 QQE COUNTS: Long={qqe_long_count}, Short={qqe_short_count}")
                self.logger.info(f"   📊 VOLUME ANALYSIS: Strength={volume_strength}, Confirmation={volume_confirmation:.2f}x, Divergence={divergence_filter}")

                # Log signal detection details
                if qqe_signal != 0:
                    signal_text = "BUY" if qqe_signal > 0 else "SELL"
                    signal_type = "FRESH" if (qqe_long_count == 1 and qqe_signal > 0) or (qqe_short_count == 1 and qqe_signal < 0) else "TREND"
                    self.logger.info(f"   ✅ QQE SIGNAL DETECTED: {signal_text} {signal_type} signal with {qqe_strength:.3f} strength")

                # QQE signal validation with volume and confluence
                if qqe_signal != 0:
                    signal_text = "BUY" if qqe_signal > 0 else "SELL"

                    # Check volume confirmation
                    if volume_confirmation >= 1.2:
                        self.logger.info(f"   ✅ VOLUME CONFIRMED: {signal_text} signal with HIGH volume confirmation ({volume_confirmation:.2f}x)")
                    elif volume_confirmation >= 0.8:
                        self.logger.info(f"   ✅ VOLUME NORMAL: {signal_text} signal with NORMAL volume confirmation ({volume_confirmation:.2f}x)")
                    else:
                        self.logger.info(f"   ⚠️ VOLUME LOW: {signal_text} signal with LOW volume confirmation ({volume_confirmation:.2f}x)")

                    # Check divergence filter
                    if divergence_filter:
                        self.logger.info(f"   ❌ DIVERGENCE FILTER: {signal_text} signal blocked by volume divergence")

                    # Check confluence
                    if confluence_score > 0.3:
                        self.logger.info(f"   ✅ HIGH CONFLUENCE: {signal_text} signal with strong S/R confluence ({confluence_score:.3f})")
                    elif confluence_score > 0.2:
                        self.logger.info(f"   ✅ MODERATE CONFLUENCE: {signal_text} signal with moderate S/R confluence ({confluence_score:.3f})")
                    else:
                        self.logger.info(f"   ⚠️ LOW CONFLUENCE: {signal_text} signal with weak S/R confluence ({confluence_score:.3f})")
                else:
                    self.logger.info(f"   ⚪ NO QQE SIGNAL: RSI_MA={rsi_ma:.1f}, FastATR={fast_atr_rsi_tl:.1f} (no crossover condition met)")

                    # Log support/resistance levels
                    if hasattr(self, '_last_confluence_debug'):
                        self.logger.info(f"   Support/Resistance: {self._last_confluence_debug}")

            # VOLUME DIVERGENCE FILTERING (for trend-following system)
            volume_divergence_filter = qqe_analysis.get('divergence_filter', False)
            divergence_type = qqe_analysis.get('divergence_type', 'NONE')
            volume_confirmation = qqe_analysis.get('volume_confirmation', 1.0)
            volume_ratio = qqe_analysis.get('volume_ratio', 1.0)

            # VOTING SYSTEM: QQE + Candle Strength + Volume Analysis
            raw_signal = None
            base_confidence = 0.0
            signal_reason = ""
            volume_filter_reason = ""

            # Check volume divergence filter first (trend-following system avoids divergence)
            if volume_divergence_filter:
                volume_filter_reason = f"VOLUME_DIVERGENCE_FILTER: {divergence_type} detected - avoiding potential reversal"
                self.logger.info(f"🚫 {volume_filter_reason}")

            if qqe_signal != 0 and not volume_divergence_filter:
                # PURE QQE SIGNAL GENERATION - No candle voting/veto
                if qqe_signal == 1:  # QQE Long
                    raw_signal = "BUY"
                    # Pure QQE confidence with volume enhancement only
                    base_confidence = min(qqe_strength + (volume_confirmation - 1.0) * 0.2, 1.0)
                    signal_reason = f"QQE_LONG: QQE {qqe_strength:.3f}, Vol {volume_confirmation:.2f}x"

                elif qqe_signal == -1:  # QQE Short
                    raw_signal = "SELL"
                    # Pure QQE confidence with volume enhancement only
                    base_confidence = min(qqe_strength + (volume_confirmation - 1.0) * 0.2, 1.0)
                    signal_reason = f"QQE_SHORT: QQE {qqe_strength:.3f}, Vol {volume_confirmation:.2f}x"
            elif not volume_divergence_filter:
                # No QQE signal - respect the QQE analysis decision
                # The QQE system has already analyzed RSI, momentum, and volume
                # If it says no signal, we should not override with the old candle strength system
                raw_signal = None
                base_confidence = 0.0
                signal_reason = f"NO_SIGNAL: QQE analysis found no valid signal (no crossover condition met)"
            else:
                # Volume divergence detected - no signal
                raw_signal = None
                base_confidence = 0.0
                signal_reason = f"NO_SIGNAL: {volume_filter_reason}"

            # Apply acceleration-based confidence weighting if available
            ml_confidence = base_confidence
            if raw_signal and candle_strength.get('acceleration_available', False):
                bull_accel = candle_strength.get('bull_acceleration', 0)
                bear_accel = candle_strength.get('bear_acceleration', 0)

                # Calculate acceleration factor
                if raw_signal == "BUY":
                    accel_factor = max(0.5, min(1.5, 1.0 + (bull_accel / 100)))
                    signal_reason += f" | Bull Accel: {bull_accel:+.1f}% (factor: {accel_factor:.2f})"
                else:  # SELL
                    accel_factor = max(0.5, min(1.5, 1.0 + (bear_accel / 100)))
                    signal_reason += f" | Bear Accel: {bear_accel:+.1f}% (factor: {accel_factor:.2f})"

                # Apply acceleration factor to confidence
                ml_confidence = min(base_confidence * accel_factor, 1.0)

            # Update previous strength for next iteration (keep for logging)
            self.previous_candle_strength = candle_net_strength

            # NEW: Apply swing point distance filtering with dynamic position sizing
            swing_position_size_factor = 1.0  # Default to full size
            if raw_signal is not None:
                swing_points = self.find_recent_swing_points(features_df)
                swing_approved, swing_reason, swing_position_size_factor = self.check_swing_distance_filter(raw_signal, swing_points, latest_atr)
                if not swing_approved:
                    raw_signal = None
                    signal_reason = f"SWING_DISTANCE_VETO: {swing_reason}"
                else:
                    # Log swing distance position sizing decision
                    if swing_position_size_factor < 1.0:
                        signal_reason = f"SWING_DISTANCE_SIZING: {swing_reason}"

            # Store for logging
            self.current_ml_signal = raw_signal
            self.current_trend_direction = accurate_trend_direction
            self.regime_confidence = regime_conf

            # ENHANCED: Check for opposite signal in existing position - bypass all vetos for SL updates
            bypass_vetos_for_opposite_signal = False
            if self.current_position and raw_signal:
                current_type = self.current_position.get('type')
                is_opposite_signal = ((current_type == 'BUY' and raw_signal == 'SELL') or
                                    (current_type == 'SELL' and raw_signal == 'BUY'))

                if is_opposite_signal:
                    bypass_vetos_for_opposite_signal = True
                    final_signal = raw_signal  # Bypass all vetos for opposite signal SL updates
                    logic = f"OPPOSITE_SIGNAL_SL_UPDATE({current_type}_position_gets_{raw_signal}_signal)"
                    half_size = False
                    self.logger.info(f"🔄 OPPOSITE SIGNAL DETECTED: {current_type} position with {raw_signal} signal - BYPASSING ALL VETOS for SL update")

            # Apply regime-based logic with trend direction filtering and BB position (HYBRID APPROACH) - unless bypassed
            if not bypass_vetos_for_opposite_signal:
                final_signal, logic, half_size = self.apply_regime_logic(raw_signal, regime, trend_direction, accurate_trend_direction, features_df, orig_regime)

            # RE-ENABLED: Apply candlestick approval filter - unless bypassed for opposite signal SL updates
            if final_signal is not None and not bypass_vetos_for_opposite_signal:
                candle_approved, candle_reason = self.regime_detector.check_candle_approval(features_df, final_signal)
                if not candle_approved:
                    final_signal = None
                    logic = f"CANDLE_VETO({candle_reason})"
            elif bypass_vetos_for_opposite_signal:
                self.logger.info(f"🔄 CANDLE APPROVAL BYPASSED: Opposite signal for SL update - no candle veto applied")

            # Enhanced detailed info with new features (using relative change signals)
            candle_info = f"Candle:{candle_strength['dominant_bias']}({candle_strength['net_strength']:+.2f})"
            candle_strength_pct = candle_net_strength
            regime_reason_brief = regime_details.get('regime_reason', '').split(':')[0] if regime_details.get('regime_reason') else ''

            # Add signal reason and volume analysis to details
            if 'signal_reason' in locals():
                signal_info = f"Signal:{signal_reason}"
            else:
                signal_info = f"Current:{candle_strength_pct:+.1f}%"

            # Add volume analysis info
            volume_info = f"Vol:{volume_ratio:.2f}x({qqe_analysis.get('volume_strength', 'NORMAL')})"
            if volume_divergence_filter:
                volume_info += f"|DIV:{divergence_type}"

            details = f"QQE:{raw_signal if raw_signal else 'NONE'} | {signal_info} | {volume_info} | Regime:{regime}({regime_conf:.2f}) | Trend:{accurate_trend_direction} | {candle_info} | Logic:{logic} | Reason:{regime_reason_brief}"

            return final_signal, ml_confidence, details, latest_atr, regime, logic, regime_details, candle_strength, qqe_analysis, features_df, half_size, swing_position_size_factor, candle_pattern_analysis

        except Exception as e:
            self.logger.error(f"❌ Error getting prediction: {e}")
            return None, 0, str(e), None, None, None, {}, {}, {}, None, False, 1.0, {}

    def apply_regime_logic(self, raw_signal, regime, trend_direction, accurate_trend_direction, features_df=None, orig_regime=None):
        """ENHANCED: Dual regression channel logic with short-term override capability + HYBRID regime filtering"""
        if raw_signal is None:
            return None, "NO_SIGNAL_FROM_CANDLE_STRENGTH", False

        if regime in ["TRENDING", "STRONG_TRENDING"]:
            # TRENDING: Allow signals aligned with trend direction
            signal_aligns_with_trend = (
                (accurate_trend_direction == "UP" and raw_signal == "BUY") or
                (accurate_trend_direction == "DOWN" and raw_signal == "SELL")
            )

            if signal_aligns_with_trend:
                return raw_signal, f"TREND_ALIGNED({accurate_trend_direction})", False
            else:
                # NEW: Check short-term regression channel override before vetoing
                if features_df is not None and len(features_df) > 0:
                    latest = features_df.iloc[-1]

                    # Get short-term regression data
                    short_trend = latest.get('regression_trend_short', None)
                    short_strength = latest.get('regression_strength_short', 'FLAT')
                    short_slope = latest.get('regression_slope_short', 0)
                    long_slope = latest.get('regression_slope', 0)

                    # Check if short-term trend confirms the signal
                    short_term_confirms_signal = (
                        (short_trend == "UP" and raw_signal == "BUY") or
                        (short_trend == "DOWN" and raw_signal == "SELL")
                    )

                    # Override conditions:
                    # 1. Short-term trend confirms the signal
                    # 2. Short-term regression is not FLAT (has some directional bias)
                    # 3. Long-term and short-term trends are opposite (conflict scenario)
                    long_term_up = accurate_trend_direction == "UP"
                    short_term_up = short_trend == "UP"
                    trends_opposite = long_term_up != short_term_up

                    if short_term_confirms_signal and short_strength != 'FLAT' and trends_opposite:
                        self.logger.info(f"🔄 DUAL REGRESSION OVERRIDE ACTIVATED:")
                        self.logger.info(f"   Long-term trend: {accurate_trend_direction} (slope: {long_slope:+.4f}%)")
                        self.logger.info(f"   Short-term trend: {short_trend} (slope: {short_slope:+.4f}%, strength: {short_strength})")
                        self.logger.info(f"   Signal: {raw_signal} - Short-term confirms, long-term vetoes")
                        self.logger.info(f"   Decision: ALLOW with HALF POSITION SIZE")

                        return raw_signal, f"SHORT_TERM_OVERRIDE({short_trend}_vs_{accurate_trend_direction})", True
                    else:
                        # Log why override was not applied
                        if not short_term_confirms_signal:
                            reason = f"Short-term trend {short_trend} doesn't confirm {raw_signal}"
                        elif short_strength == 'FLAT':
                            reason = f"Short-term regression is FLAT (no directional bias)"
                        elif not trends_opposite:
                            reason = f"Long-term and short-term trends agree ({accurate_trend_direction} vs {short_trend})"
                        else:
                            reason = "Unknown reason"

                        self.logger.info(f"🚫 DUAL REGRESSION OVERRIDE NOT APPLIED: {reason}")
                        self.logger.info(f"   Long-term: {accurate_trend_direction} (slope: {long_slope:+.4f}%)")
                        self.logger.info(f"   Short-term: {short_trend} (slope: {short_slope:+.4f}%, strength: {short_strength})")

                # VETO signals that go against the trend in TRENDING markets
                return None, f"TREND_VETO({accurate_trend_direction}_blocks_{raw_signal})", False

        elif regime in ["RANGING", "STRONG_RANGING"]:
            # RANGING: Block all signals - no trading in ranging markets
            return None, f"RANGING_BLOCKED_TRENDING_ONLY", False

        else:  # TRANSITIONAL
            # HYBRID APPROACH: Use original regime detector as QQE signal gate in TRANSITIONAL periods
            # Enhanced detector provides primary regime classification, original detector gates QQE signals

            if orig_regime == "TRENDING":
                # Original detector sees trending conditions - allow QQE signals with trend alignment
                signal_aligns_with_trend = (
                    (accurate_trend_direction == "UP" and raw_signal == "BUY") or
                    (accurate_trend_direction == "DOWN" and raw_signal == "SELL")
                )

                if signal_aligns_with_trend:
                    self.logger.info(f"🎯 HYBRID REGIME FILTER ACTIVATED:")
                    self.logger.info(f"   Enhanced: TRANSITIONAL | Original: {orig_regime}")
                    self.logger.info(f"   QQE Signal: {raw_signal} | Trend: {accurate_trend_direction}")
                    self.logger.info(f"   Decision: ALLOW - Original detector confirms trending conditions for QQE")
                    return raw_signal, f"HYBRID_TRANSITIONAL_TRENDING({accurate_trend_direction})", False
                else:
                    self.logger.info(f"🚫 HYBRID REGIME FILTER - SIGNAL MISALIGNMENT:")
                    self.logger.info(f"   Enhanced: TRANSITIONAL | Original: {orig_regime}")
                    self.logger.info(f"   QQE Signal: {raw_signal} | Trend: {accurate_trend_direction}")
                    self.logger.info(f"   Decision: BLOCK - Signal doesn't align with trend direction")
                    return None, f"HYBRID_TRANSITIONAL_MISALIGNED({raw_signal}_vs_{accurate_trend_direction})", False
            else:
                # Original detector doesn't see trending - block QQE signals
                self.logger.info(f"🚫 HYBRID REGIME FILTER - ORIGINAL NOT TRENDING:")
                self.logger.info(f"   Enhanced: TRANSITIONAL | Original: {orig_regime}")
                self.logger.info(f"   QQE Signal: {raw_signal}")
                self.logger.info(f"   Decision: BLOCK - Original detector doesn't confirm trending conditions")
                return None, f"HYBRID_TRANSITIONAL_BLOCKED(orig_{orig_regime})", False

    def check_regime_change(self, new_regime):
        """Check if regime has changed and handle position closure (smart logic)"""
        if self.current_regime is None:
            self.current_regime = new_regime
            self.previous_regime = None  # No previous regime on first run
            return False

        if new_regime != self.current_regime:
            self.logger.info(f"🔄 REGIME CHANGE: {self.current_regime} → {new_regime}")

            # Store previous regime before updating current
            self.previous_regime = self.current_regime

            # Smart regime change logic
            if self.current_position:
                # Check if position is profitable
                current_positions = self.mt5_manager.get_positions()
                is_profitable = False
                profit = 0

                if current_positions:
                    for pos in current_positions:
                        if pos['ticket'] == self.current_position['ticket']:
                            profit = pos.get('profit', 0)
                            is_profitable = profit > 0
                            break

                # SMART REGIME CHANGE LOGIC: Preserve profitable trades in key transitions
                should_close = False
                should_remove_tp = False

                if self.current_regime == "TRANSITIONAL" and new_regime == "TRENDING":
                    # TRANSITIONAL → TRENDING: Only close if NOT profitable
                    if not is_profitable:
                        should_close = True
                        reason = "TRANSITIONAL→TRENDING (Not Profitable)"
                    else:
                        self.logger.info(f"💰 TRANSITIONAL→TRENDING but position is PROFITABLE (${profit:.2f}) - Keeping position open")
                        should_remove_tp = True  # Remove TP to use trailing stops

                elif self.current_regime == "RANGING" and new_regime == "TRANSITIONAL":
                    # RANGING → TRANSITIONAL: Only close if NOT profitable (sign of going to trend)
                    if not is_profitable:
                        should_close = True
                        reason = "RANGING→TRANSITIONAL (Not Profitable)"
                    else:
                        self.logger.info(f"💰 RANGING→TRANSITIONAL but position is PROFITABLE (${profit:.2f}) - Keeping position open (trend forming)")
                        should_remove_tp = True  # Remove TP to prepare for trending

                elif self.current_regime == "RANGING" and new_regime == "TRENDING":
                    # RANGING → TRENDING: Close with candle confirmation
                    should_close = True
                    reason = "RANGING→TRENDING (Direct)"

                elif self.current_regime == "TRENDING" and new_regime in ["RANGING", "TRANSITIONAL"]:
                    # TRENDING → other: Close with candle confirmation (trend ending)
                    should_close = True
                    reason = f"TRENDING→{new_regime} (Trend Ending)"

                # NEW: Apply candle confirmation to regime change exits
                if should_close:
                    # Get latest data for candle confirmation
                    try:
                        latest_data = self.get_latest_data_safe()
                        if latest_data is not None and len(latest_data) > 0:
                            candle_confirmed, candle_reason, close_position = self.regime_detector.check_candle_exit_confirmation(
                                latest_data, self.current_position['type']
                            )

                            if candle_confirmed:
                                # Instead of closing immediately, set trailing stop 10 pips from confirmation candle
                                closed_candle = latest_data.iloc[-2]  # Last closed candle
                                confirmation_candle_data = {
                                    'high': closed_candle['high'],
                                    'low': closed_candle['low'],
                                    'close': closed_candle['close']
                                }

                                # TEMPORARILY DISABLED: Regime change-based trailing stops
                                # if self.set_candle_confirmation_trailing_stop(confirmation_candle_data, self.current_position['type']):
                                #     self.logger.info(f"🎯 REGIME CHANGE TRAILING STOP SET: {reason}")
                                #     self.logger.info(f"   Candle confirmation: {candle_reason}")
                                #     self.logger.info(f"   Stop will revert to original level if not hit within one candle")
                                # else:
                                #     # FIXED: Don't close position just because trailing stop failed
                                #     # Continue monitoring with existing stop loss
                                #     self.logger.warning(f"⚠️ REGIME CHANGE TRAILING STOP FAILED: {reason}")
                                #     self.logger.info(f"   Candle confirmation: {candle_reason}")
                                #     self.logger.info(f"🔄 Position remains open with existing stop loss - will retry trailing on next analysis")

                                # Keep analysis active but disable trailing stop action
                                self.logger.info(f"🚫 REGIME CHANGE TRAILING STOP DISABLED: {reason}")
                                self.logger.info(f"   Candle confirmation: {candle_reason}")
                                self.logger.info(f"🔄 Regime change analysis active but trailing stop action temporarily disabled")

                                self.pending_regime_exit = None  # Clear any pending exit
                            else:
                                # Regime change would close position but candle doesn't confirm - track it
                                self.pending_regime_exit = {
                                    'reason': reason,
                                    'unfavorable_regime': new_regime,
                                    'position_type': self.current_position['type']
                                }
                                self.logger.info(f"⚠️ REGIME CHANGE EXIT BLOCKED: {reason}")
                                self.logger.info(f"   Candle confirmation: {candle_reason}")
                                self.logger.info(f"   Will monitor for candle confirmation in subsequent loops")
                        else:
                            # Fallback to immediate close if candle data unavailable
                            self.logger.info(f"🔄 REGIME CHANGE EXIT (FALLBACK): {reason} [No candle data]")
                            self.close_current_position(f"Regime Change - {reason}")
                    except Exception as e:
                        self.logger.error(f"❌ Error in regime change candle confirmation: {e}")
                        # Fallback to immediate close
                        self.logger.info(f"🔄 REGIME CHANGE EXIT (FALLBACK): {reason}")
                        self.close_current_position(f"Regime Change - {reason}")
                elif should_remove_tp:
                    # Remove take profit to use trailing stops in trending environment
                    self.remove_take_profit(f"Regime Change - {self.current_regime}→{new_regime}")

            self.current_regime = new_regime
            self.last_regime_change = datetime.now()
            return True

        return False

    def log_enhanced_regime_analysis(self, regime_details, candle_strength, qqe_analysis=None, features_df=None, atr_value=None, qqe_signal=0, qqe_strength=0.0):
        """ENHANCED: Log detailed regime analysis with specific reasoning and QQE data"""
        # REMOVED: Verbose detailed regime analysis logging

        # FIXED: Log dual regression channel analysis using EXACT SAME data as regime detection
        if features_df is not None and len(features_df) > 0:
            latest = features_df.iloc[-1]

            # Use the EXACT values that were used in regime detection (passed via regime_details)
            regime_regression = regime_details.get('regime_regression_data', {})
            long_slope = regime_regression.get('slope')
            long_strength = regime_regression.get('strength', 'UNKNOWN')
            long_source = regime_regression.get('source', 'UNKNOWN')

            # Fallback to traditional regression if no passed values (shouldn't happen)
            if long_slope is None:
                long_slope = latest.get('regression_slope', 0)
                long_strength = latest.get('regression_strength', 'UNKNOWN')
                long_source = "Traditional Regression (FALLBACK)"
                self.logger.info(f"⚠️ WARNING: No regime regression data passed, using fallback")

            # Short-term regression (always from traditional calculation)
            short_slope = latest.get('regression_slope_short', 0)
            short_strength = latest.get('regression_strength_short', 'UNKNOWN')
            short_trend = latest.get('regression_trend_short', 'UNKNOWN')

            # NEW: Get 100-period regime regression data
            regime_regression_100 = regime_details.get('regime_regression_100', {})
            regime_slope = regime_regression_100.get('slope', 0)
            regime_strength = regime_regression_100.get('strength', 'UNKNOWN')
            regime_trend = regime_regression_100.get('trend', 'UNKNOWN')

            self.logger.info("📊 TRIPLE REGRESSION CHANNEL ANALYSIS:")
            self.logger.info(f"   Regime (100 candles): {regime_slope:+.4f}% per period ({regime_strength}) → {regime_trend}")
            self.logger.info(f"   Long-term (20 candles): {long_slope:+.4f}% per period ({long_strength}) - {long_source}")
            self.logger.info(f"   Short-term (10 candles): {short_slope:+.4f}% per period ({short_strength}) → {short_trend}")

            # Show if trends are aligned or conflicting
            regime_trend_up = regime_slope > 0
            long_trend_up = long_slope > 0
            short_trend_up = short_slope > 0

            # Count aligned trends
            trends_up = sum([regime_trend_up, long_trend_up, short_trend_up])
            if trends_up == 3 or trends_up == 0:
                alignment = "ALL_ALIGNED"
            elif trends_up == 2:
                alignment = "MAJORITY_ALIGNED"
            else:
                alignment = "CONFLICTING"
            self.logger.info(f"   Trend Alignment: {alignment} ({trends_up}/3 upward)")

            # UPDATED: Show transitional trade eligibility (now blocks all transitions)
            if regime_details.get('regime_reason', '').startswith('TRANSITIONAL'):
                if self.previous_regime == "RANGING":
                    self.logger.info(f"   Transitional Trade Eligibility: RANGING→TRANSITIONAL - ALL trades BLOCKED")
                elif self.previous_regime == "TRENDING":
                    self.logger.info(f"   Transitional Trade Eligibility: TRENDING→TRANSITIONAL - ALL trades BLOCKED")
                else:
                    self.logger.info(f"   Transitional Trade Eligibility: {self.previous_regime}→TRANSITIONAL - ALL trades BLOCKED")

        self.logger.info("🕯️ CANDLE STRENGTH ANALYSIS:")
        self.logger.info(f"   Dominant Bias: {candle_strength['dominant_bias']}")
        self.logger.info(f"   Net Strength: {candle_strength['net_strength']:+.2f}")
        self.logger.info(f"   Bullish Strength: {candle_strength['bullish_strength']:.1%}")
        self.logger.info(f"   Bearish Strength: {candle_strength['bearish_strength']:.1%}")
        self.logger.info(f"   Candles Analyzed: {candle_strength['candles_analyzed']}")

        # NEW: Display acceleration data (Phase 1: Monitoring)
        if candle_strength.get('acceleration_available', False):
            self.logger.info("⚡ ACCELERATION ANALYSIS (BASED ON CLOSED CANDLES ONLY):")
            self.logger.info(f"   Bull Velocity: {candle_strength['bull_velocity']:+.2f}%")
            self.logger.info(f"   Bear Velocity: {candle_strength['bear_velocity']:+.2f}%")
            self.logger.info(f"   Bull Acceleration: {candle_strength['bull_acceleration']:+.2f}%")
            self.logger.info(f"   Bear Acceleration: {candle_strength['bear_acceleration']:+.2f}%")

            # FIXED: Interpret acceleration based on velocity (momentum direction) first
            bull_accel = candle_strength['bull_acceleration']
            bear_accel = candle_strength['bear_acceleration']
            bull_velocity = candle_strength['bull_velocity']
            bear_velocity = candle_strength['bear_velocity']

            # Determine dominant momentum direction from velocity SIGN (not magnitude)
            if bull_velocity > 0:
                # Bullish momentum (positive bull velocity)
                if bull_accel > 5:
                    accel_interpretation = "🚀 BULLISH ACCELERATION"
                elif bull_accel < -5:
                    accel_interpretation = "🛑 BULLISH DECELERATION"
                else:
                    accel_interpretation = "➡️ BULLISH MOMENTUM STABLE"
            else:
                # Bearish momentum (negative bull velocity = positive bear velocity)
                if bear_accel > 5:
                    accel_interpretation = "🚀 BEARISH ACCELERATION"
                elif bear_accel < -5:
                    accel_interpretation = "🛑 BEARISH DECELERATION"
                else:
                    accel_interpretation = "➡️ BEARISH MOMENTUM STABLE"

            self.logger.info(f"   Interpretation: {accel_interpretation}")
        else:
            self.logger.info("⚡ ACCELERATION ANALYSIS: Insufficient data (need 3+ periods)")

        # NEW: QQE Analysis Logging
        if qqe_analysis:
            self.logger.info("📈 QQE SIGNAL ANALYSIS (primary signal generator):")
            self.logger.info(f"   QQE Signal: {int(qqe_signal):+d}")
            self.logger.info(f"   QQE Strength: {qqe_strength:.3f}")
            self.logger.info("📈 QQE TECHNICAL DETAILS:")
            self.logger.info(f"   RSI: {qqe_analysis.get('rsi', 0):.1f}")
            self.logger.info(f"   RSI MA: {qqe_analysis.get('rsi_ma', 0):.1f}")
            self.logger.info(f"   Fast ATR RSI TL: {qqe_analysis.get('fast_atr_rsi_tl', 0):.1f}")
            self.logger.info(f"   QQE Trend: {int(qqe_analysis.get('trend', 0)):+d}")
            self.logger.info(f"   QQE Trend Signal: {int(qqe_analysis.get('qqe_trend_signal', 0)):+d}")
            self.logger.info(f"   QQE Long Count: {qqe_analysis.get('qqe_long_count', 0)}")
            self.logger.info(f"   QQE Short Count: {qqe_analysis.get('qqe_short_count', 0)}")
            self.logger.info(f"   Volume Confirmation: {qqe_analysis.get('volume_confirmation', 1.0):.2f}x")
            self.logger.info(f"   Divergence Filter: {qqe_analysis.get('divergence_filter', False)}")

            # Volume Analysis Logging
            self.logger.info("📊 VOLUME ANALYSIS:")
            self.logger.info(f"   Volume Ratio: {qqe_analysis.get('volume_ratio', 1.0):.2f}x (current/average)")
            self.logger.info(f"   Volume Strength: {qqe_analysis.get('volume_strength', 'NORMAL')}")
            self.logger.info(f"   Volume Confirmation: {qqe_analysis.get('volume_confirmation', 1.0):.2f}x")
            self.logger.info(f"   Volume Trend: {qqe_analysis.get('volume_trend', 0):+d} (1=increasing, -1=decreasing)")
            self.logger.info(f"   Volume Momentum: {qqe_analysis.get('volume_momentum', 0):+.1%}")
            self.logger.info(f"   Divergence Type: {qqe_analysis.get('divergence_type', 'NONE')}")
            if qqe_analysis.get('divergence_filter', False):
                self.logger.info(f"   🚫 DIVERGENCE FILTER ACTIVE: {qqe_analysis.get('divergence_type', 'NONE')} - Avoiding potential reversal")
        else:
            self.logger.info("📈 QQE INDICATOR ANALYSIS: Not available")

        # Simple Regression Channel Analysis Logging
        if hasattr(self, 'simple_regression'):
            try:
                channel_status = self.simple_regression.get_status_summary()
                # REMOVED: Verbose simple regression channel logging

            except Exception as e:
                self.logger.error(f"❌ Error in simple channel logging: {e}")
        # REMOVED: Simple regression channel logging

        # NEW: Swing Point Analysis Logging
        if features_df is not None and atr_value is not None:
            try:
                swing_points = self.find_recent_swing_points(features_df)
                # REMOVED: Verbose swing point analysis logging
            except Exception as e:
                self.logger.error(f"❌ Error in swing point logging: {e}")
        # REMOVED: Swing point analysis logging

    def get_swing_for_wick_analysis(self, signal: str, swing_points: Dict[str, Any], df: pd.DataFrame) -> Tuple[Optional[float], Optional[Dict], str]:
        """
        Get the appropriate swing point for wick analysis based on signal direction

        Args:
            signal: 'BUY' or 'SELL'
            swing_points: Dict from find_recent_swing_points()
            df: DataFrame with candle data

        Returns:
            Tuple of (swing_price, swing_candle_data, reason)
        """
        try:
            if not swing_points.get('swing_points_available', False):
                return None, None, "No swing points available"

            if signal == 'SELL':
                # For SELL signals, use swing high
                swing_price = swing_points.get('recent_high')
                candles_ago = swing_points.get('recent_high_candles_ago', 0)

                if swing_price is None:
                    return None, None, "No recent swing high found"

                # If current closed candle is the swing (0 candles ago), try to get prior swing high
                if candles_ago == 0:
                    self.logger.info("🔍 WICK ANALYSIS: Current candle is swing high, looking for prior swing high")
                    # Find prior swing high by re-running swing detection on data excluding the most recent candle
                    if len(df) >= 3:
                        prior_df = df.iloc[:-2]  # Exclude last 2 candles (current forming + most recent closed)
                        prior_swing_points = self.find_recent_swing_points(prior_df)
                        if prior_swing_points.get('recent_high'):
                            swing_price = prior_swing_points['recent_high']
                            candles_ago = prior_swing_points.get('recent_high_candles_ago', 0) + 1  # Add 1 because we excluded a candle
                            self.logger.info(f"✅ WICK ANALYSIS: Using prior swing high {swing_price:.5f} ({candles_ago + 1} candles ago)")
                        else:
                            return None, None, "No prior swing high found"
                    else:
                        return None, None, "Insufficient data for prior swing high"

            else:  # BUY
                # For BUY signals, use swing low
                swing_price = swing_points.get('recent_low')
                candles_ago = swing_points.get('recent_low_candles_ago', 0)

                if swing_price is None:
                    return None, None, "No recent swing low found"

                # If current closed candle is the swing (0 candles ago), try to get prior swing low
                if candles_ago == 0:
                    self.logger.info("🔍 WICK ANALYSIS: Current candle is swing low, looking for prior swing low")
                    # Find prior swing low by re-running swing detection on data excluding the most recent candle
                    if len(df) >= 3:
                        prior_df = df.iloc[:-2]  # Exclude last 2 candles (current forming + most recent closed)
                        prior_swing_points = self.find_recent_swing_points(prior_df)
                        if prior_swing_points.get('recent_low'):
                            swing_price = prior_swing_points['recent_low']
                            candles_ago = prior_swing_points.get('recent_low_candles_ago', 0) + 1  # Add 1 because we excluded a candle
                            self.logger.info(f"✅ WICK ANALYSIS: Using prior swing low {swing_price:.5f} ({candles_ago + 1} candles ago)")
                        else:
                            return None, None, "No prior swing low found"
                    else:
                        return None, None, "Insufficient data for prior swing low"

            # Get the candle data for the swing point
            swing_candle_index = len(df) - 2 - candles_ago  # -2 to exclude current forming candle, then subtract candles_ago
            if swing_candle_index < 0 or swing_candle_index >= len(df) - 1:  # Ensure we don't access current forming candle
                return None, None, f"Invalid swing candle index: {swing_candle_index}"

            swing_candle_data = df.iloc[swing_candle_index].to_dict()

            return swing_price, swing_candle_data, f"Using swing {signal.lower()} at {swing_price:.5f} ({candles_ago} candles ago)"

        except Exception as e:
            self.logger.error(f"❌ Error getting swing for wick analysis: {e}")
            return None, None, f"Error: {e}"

    def analyze_wick_interaction(self, signal: str, swing_points: Dict[str, Any], df: pd.DataFrame, atr_value: float, current_price: float) -> Tuple[bool, str, float]:
        """
        Analyze if current price interacts with extreme wick area and determine volume adjustment

        Args:
            signal: 'BUY' or 'SELL'
            swing_points: Dict from find_recent_swing_points()
            df: DataFrame with candle data
            atr_value: Current ATR value
            current_price: Current market price

        Returns:
            Tuple of (should_halve_volume, reason, volume_factor)
            volume_factor: 1.0 = full size, 0.5 = half size, 0.0 = skip trade
        """
        try:
            # Get the appropriate swing for analysis
            swing_price, swing_candle_data, swing_reason = self.get_swing_for_wick_analysis(signal, swing_points, df)

            if swing_price is None or swing_candle_data is None:
                return False, f"No wick analysis - {swing_reason}", 1.0

            self.logger.info(f"🔍 WICK ANALYSIS: {swing_reason}")

            # Extract candle OHLC data
            candle_high = swing_candle_data['high']
            candle_low = swing_candle_data['low']
            candle_open = swing_candle_data['open']
            candle_close = swing_candle_data['close']

            # Calculate wick sizes
            if signal == 'SELL':
                # For SELL signals, analyze lower wick of swing high candle
                body_bottom = min(candle_open, candle_close)
                lower_wick_size = body_bottom - candle_low
                wick_area_top = body_bottom  # Top of wick area (body bottom)
                wick_area_bottom = candle_low  # Bottom of wick area (candle low)

                self.logger.info(f"📊 SWING HIGH CANDLE ANALYSIS:")
                self.logger.info(f"   High: {candle_high:.5f}, Low: {candle_low:.5f}")
                self.logger.info(f"   Open: {candle_open:.5f}, Close: {candle_close:.5f}")
                self.logger.info(f"   Body Bottom: {body_bottom:.5f}")
                self.logger.info(f"   Lower Wick Size: {lower_wick_size:.5f}")
                self.logger.info(f"   Lower Wick Area: {wick_area_bottom:.5f} to {wick_area_top:.5f}")

                # Check if wick is too large (> 1 ATR)
                if lower_wick_size > atr_value:
                    return False, f"Lower wick too large ({lower_wick_size:.5f} > {atr_value:.5f} ATR) - Skipping extreme", 1.0

                # Check if current price interacts with lower wick area
                price_in_wick_area = wick_area_bottom <= current_price <= wick_area_top

                if price_in_wick_area:
                    return True, f"SELL at lower wick extreme - Current price {current_price:.5f} in wick area [{wick_area_bottom:.5f}-{wick_area_top:.5f}]", 0.5
                else:
                    return False, f"Current price {current_price:.5f} not in lower wick area [{wick_area_bottom:.5f}-{wick_area_top:.5f}]", 1.0

            else:  # BUY
                # For BUY signals, analyze upper wick of swing low candle
                body_top = max(candle_open, candle_close)
                upper_wick_size = candle_high - body_top
                wick_area_bottom = body_top  # Bottom of wick area (body top)
                wick_area_top = candle_high  # Top of wick area (candle high)

                self.logger.info(f"📊 SWING LOW CANDLE ANALYSIS:")
                self.logger.info(f"   High: {candle_high:.5f}, Low: {candle_low:.5f}")
                self.logger.info(f"   Open: {candle_open:.5f}, Close: {candle_close:.5f}")
                self.logger.info(f"   Body Top: {body_top:.5f}")
                self.logger.info(f"   Upper Wick Size: {upper_wick_size:.5f}")
                self.logger.info(f"   Upper Wick Area: {wick_area_bottom:.5f} to {wick_area_top:.5f}")

                # Check if wick is too large (> 1 ATR)
                if upper_wick_size > atr_value:
                    return False, f"Upper wick too large ({upper_wick_size:.5f} > {atr_value:.5f} ATR) - Skipping extreme", 1.0

                # Check if current price interacts with upper wick area
                price_in_wick_area = wick_area_bottom <= current_price <= wick_area_top

                if price_in_wick_area:
                    return True, f"BUY at upper wick extreme - Current price {current_price:.5f} in wick area [{wick_area_bottom:.5f}-{wick_area_top:.5f}]", 0.5
                else:
                    return False, f"Current price {current_price:.5f} not in upper wick area [{wick_area_bottom:.5f}-{wick_area_top:.5f}]", 1.0

        except Exception as e:
            self.logger.error(f"❌ Error in wick interaction analysis: {e}")
            return False, f"Error in wick analysis: {e}", 1.0

    def calculate_position_size(self, balance, current_price, atr_value, half_size=False, swing_size_factor=1.0, signal=None, signal_candle_data=None):
        """Calculate position size for 4% risk with ACTUAL stop loss distance - Multi-symbol support with dynamic sizing"""
        try:
            # Base risk percent (4%)
            base_risk_percent = self.risk_percent

            # Apply half-size reduction if needed (regime-based)
            if half_size:
                base_risk_percent = base_risk_percent / 2

            # Apply swing distance scaling
            final_risk_percent = base_risk_percent * swing_size_factor

            risk_amount = balance * (final_risk_percent / 100)

            # FIXED: Calculate ACTUAL stop loss distance based on signal candle (not fixed 1.50)
            stop_loss_distance = 1.50  # Default fallback

            try:
                # ENHANCED: Use provided signal candle data first, then try to get from market data
                signal_candle = None

                if signal_candle_data is not None:
                    # Use provided signal candle data
                    signal_candle = signal_candle_data
                    self.logger.info(f"📊 Using provided signal candle data for SL calculation")
                else:
                    # Try to get from market data
                    latest_data = self.get_latest_data_safe()
                    if latest_data is not None and len(latest_data) >= 2:
                        signal_candle = latest_data.iloc[-2]  # Last closed candle (signal candle)
                        self.logger.info(f"📊 Using market data signal candle for SL calculation")

                if signal_candle is not None and signal is not None:
                    # ENHANCED: Use swing-based stop loss calculation for position sizing
                    try:
                        # Get latest market data for swing detection
                        latest_data = self.get_latest_data_safe()
                        if latest_data is not None:
                            actual_sl, sl_method = self._calculate_swing_based_stop_loss(signal, signal_candle, latest_data)
                            stop_loss_distance = abs(current_price - actual_sl)
                            self.logger.info(f"📏 SWING-BASED SL DISTANCE: {stop_loss_distance:.2f} points ({sl_method})")
                        else:
                            # Fallback to original logic if no market data
                            if signal == "BUY":
                                actual_sl = signal_candle['low'] - 1.50
                                stop_loss_distance = abs(current_price - actual_sl)
                            elif signal == "SELL":
                                actual_sl = signal_candle['high'] + 1.50
                                stop_loss_distance = abs(actual_sl - current_price)
                            self.logger.info(f"📏 FALLBACK SL DISTANCE: {stop_loss_distance:.2f} points (Signal candle: H:{signal_candle['high']:.2f} L:{signal_candle['low']:.2f})")
                    except Exception as e:
                        # Fallback to original logic on error
                        if signal == "BUY":
                            actual_sl = signal_candle['low'] - 1.50
                            stop_loss_distance = abs(current_price - actual_sl)
                        elif signal == "SELL":
                            actual_sl = signal_candle['high'] + 1.50
                            stop_loss_distance = abs(actual_sl - current_price)
                        self.logger.warning(f"⚠️ Error in swing-based SL calculation, using fallback: {e}")
                        self.logger.info(f"📏 FALLBACK SL DISTANCE: {stop_loss_distance:.2f} points (Signal candle: H:{signal_candle['high']:.2f} L:{signal_candle['low']:.2f})")
                else:
                    self.logger.warning(f"⚠️ Using fallback SL distance: {stop_loss_distance:.2f} points (no signal candle data available)")
            except Exception as e:
                self.logger.warning(f"⚠️ Error calculating actual SL distance, using fallback: {e}")

            # Get symbol-specific contract size
            contract_size = self.SYMBOL_SPECS[self.symbol]["contract_size"]
            symbol_name = self.SYMBOL_SPECS[self.symbol]["name"]

            # Risk per lot = stop_loss_distance * contract_size
            risk_per_lot = stop_loss_distance * contract_size

            # Calculate lot size: Risk Amount / Risk per Lot
            lot_size = risk_amount / risk_per_lot

            # Round to 2 decimal places and apply limits
            lot_size = round(lot_size, 2)
            lot_size = max(0.01, min(lot_size, 10.0))

            # Determine position type and reason
            sizing_reasons = []
            if half_size:
                sizing_reasons.append("regime-based half-size")
            if swing_size_factor < 1.0:
                sizing_reasons.append(f"swing distance scaling ({swing_size_factor:.2f}x)")

            if sizing_reasons:
                position_type = f"SCALED SIZE ({', '.join(sizing_reasons)})"
            else:
                position_type = "FULL SIZE"

            self.logger.info(f"💰 Position Sizing - {position_type} ({symbol_name} - {self.symbol}):")
            self.logger.info(f"   Balance: ${balance:.2f}")
            self.logger.info(f"   Base Risk: {self.risk_percent:.1f}%")
            if half_size:
                self.logger.info(f"   Half-size Factor: 0.5x (regime-based)")
            if swing_size_factor != 1.0:
                self.logger.info(f"   Swing Distance Factor: {swing_size_factor:.2f}x")
            self.logger.info(f"   Final Risk Percent: {final_risk_percent:.2f}%")
            self.logger.info(f"   Risk Amount: ${risk_amount:.2f}")
            self.logger.info(f"   Stop Distance: {stop_loss_distance:.5f}")
            self.logger.info(f"   Contract Size: {contract_size}")
            self.logger.info(f"   Risk per Lot: ${risk_per_lot:.2f}")
            self.logger.info(f"   Calculated Lot Size: {lot_size}")

            return lot_size

        except Exception as e:
            self.logger.error(f"❌ Error calculating position size: {e}")
            return 0.01

    def get_position_profit_loss_info(self):
        """
        Get detailed position profit/loss information
        Returns: (is_profitable, profit_points, profit_percentage, position_risk_value)
        """
        try:
            if not self.current_position:
                return False, 0, 0, 0

            # Get current market price
            tick_info = self.mt5_manager.get_symbol_info_tick(self.symbol)
            if not tick_info:
                return False, 0, 0, 0

            entry_price = self.current_position['price']
            position_type = self.current_position['type']
            volume = self.current_position.get('remaining_volume', self.current_position.get('volume', 0))

            # Use appropriate price for position type
            current_price = tick_info['ask'] if position_type == 'BUY' else tick_info['bid']

            # Calculate profit in points
            if position_type == 'BUY':
                profit_points = current_price - entry_price
                is_profitable = current_price > entry_price
            else:  # SELL
                profit_points = entry_price - current_price
                is_profitable = current_price < entry_price

            # Calculate position risk value (SL distance * volume * contract_size)
            if self.current_position.get('stop_loss'):
                sl_distance = abs(entry_price - self.current_position['stop_loss'])
                contract_size = self.mt5_manager.get_contract_size(self.symbol)
                position_risk_value = sl_distance * volume * contract_size
            else:
                sl_distance = 0
                position_risk_value = 0

            # Calculate profit percentage relative to position risk
            profit_percentage = (profit_points / sl_distance * 100) if sl_distance > 0 else 0

            return is_profitable, profit_points, profit_percentage, position_risk_value

        except Exception as e:
            self.logger.error(f"❌ Error getting position P&L info: {e}")
            return False, 0, 0, 0

    def should_allow_trailing(self, trailing_type="SL_DISTANCE", current_price=None):
        """
        Check if trailing should be allowed based on position profit/loss status

        Args:
            trailing_type: "SL_DISTANCE" for SL distance-based trailing, "CANDLE" for candle confirmation trailing
            current_price: Optional current price to use instead of fetching from MT5

        Returns:
            (should_allow, reason)
        """
        try:
            if current_price is not None and self.current_position:
                # Use provided current price to calculate profit
                entry_price = self.current_position['price']
                position_type = self.current_position['type']

                if position_type == 'BUY':
                    profit_points = current_price - entry_price
                    is_profitable = current_price > entry_price
                else:  # SELL
                    profit_points = entry_price - current_price
                    is_profitable = current_price < entry_price

                # Calculate profit percentage relative to SL distance
                if self.current_position.get('stop_loss'):
                    sl_distance = abs(entry_price - self.current_position['stop_loss'])
                    profit_percentage = (profit_points / sl_distance * 100) if sl_distance > 0 else 0
                else:
                    profit_percentage = 0
            else:
                # Fall back to MT5 data
                is_profitable, profit_points, profit_percentage, position_risk_value = self.get_position_profit_loss_info()

            if trailing_type == "SL_DISTANCE" or trailing_type == "ATR":
                # SL distance trailing: Only allow if position is profitable
                if is_profitable:
                    return True, f"Position profitable ({profit_points:+.5f} points, {profit_percentage:+.1f}%)"
                else:
                    return False, f"Position not profitable ({profit_points:+.5f} points, {profit_percentage:+.1f}%) - SL distance trailing blocked"

            elif trailing_type == "CANDLE":
                # ENHANCED: Candle confirmation trailing logic per user requirements:
                # 1. If position is in profit → Allow trailing (protect profits)
                # 2. If position is NOT in profit → Only allow if loss is 50% or MORE of position risk (cut big losses)
                # 3. If position has small loss (< 50%) → Block trailing (let it recover)

                if is_profitable:
                    return True, f"Position profitable ({profit_points:+.5f} points, {profit_percentage:+.1f}%) - Candle trailing allowed"
                elif profit_percentage <= -50:  # Position is 50% or MORE in loss
                    return True, f"Position 50%+ in loss ({profit_points:+.5f} points, {profit_percentage:+.1f}% ≤ -50%) - Candle trailing allowed to cut losses"
                else:
                    # Position is not profitable but loss is less than 50% - Block trailing (let it recover)
                    return False, f"Position not profitable and loss < 50% ({profit_points:+.5f} points, {profit_percentage:+.1f}% > -50%) - Candle trailing blocked (let position recover)"

            return False, "Unknown trailing type"

        except Exception as e:
            self.logger.error(f"❌ Error checking trailing allowance: {e}")
            return False, f"Error checking trailing: {e}"

    def get_original_sl_distance(self):
        """
        Get the original stop loss distance from stored trailing data
        Returns the distance that should be used for trailing
        """
        if not self.trailing_stop_data:
            return None

        # FIXED: Use stored original distance instead of recalculating
        # This prevents the distance from becoming zero after trailing stops are applied
        stored_distance = self.trailing_stop_data.get('original_sl_distance')

        if stored_distance and stored_distance > 0.00001:
            return stored_distance

        # Fallback: calculate from current position if stored distance not available
        if not self.current_position:
            return None

        entry_price = self.current_position.get('price', 0)
        original_sl = self.current_position.get('original_sl', self.current_position.get('sl', 0))
        calculated_distance = abs(original_sl - entry_price)

        # FIXED: Check for zero or very small distance to prevent division by zero
        if calculated_distance <= 0.00001:  # Less than 0.1 pip for most symbols
            self.logger.warning(f"⚠️ Original SL distance too small or zero: {calculated_distance:.8f}")
            self.logger.warning(f"   Entry Price: {entry_price:.5f}, Original SL: {original_sl:.5f}")
            return None

        return calculated_distance

    def start_real_time_trailing_monitor(self):
        """Start the real-time trailing stop monitor thread"""
        if self.trailing_monitor_thread and self.trailing_monitor_thread.is_alive():
            self.logger.info("🔄 REAL-TIME TRAILING MONITOR: Already running")
            return  # Already running

        self.trailing_monitor_active = True
        self.trailing_monitor_thread = threading.Thread(
            target=self._real_time_trailing_monitor,
            daemon=True,
            name="TrailingStopMonitor"
        )
        self.trailing_monitor_thread.start()
        self.logger.info("🔄 REAL-TIME TRAILING MONITOR: Started")

        # Log current position and trailing data for debugging
        if self.current_position and self.trailing_stop_data:
            self.logger.info(f"📊 MONITOR CONTEXT: Position={self.current_position['type']}, Entry={self.current_position['price']:.5f}")
            self.logger.info(f"📊 TRAILING DATA: SL={self.trailing_stop_data['current_sl']:.5f}, Distance={self.trailing_stop_data['original_sl_distance']:.2f}")
        else:
            self.logger.warning("⚠️ MONITOR STARTED BUT NO POSITION/TRAILING DATA")

    def stop_real_time_trailing_monitor(self):
        """Stop the real-time trailing stop monitor thread"""
        self.trailing_monitor_active = False

        # Check if we're trying to stop from within the monitor thread itself
        current_thread_name = threading.current_thread().name
        if current_thread_name == "TrailingStopMonitor":
            # Don't try to join from within the same thread - just signal to stop
            self.logger.info("🛑 REAL-TIME TRAILING MONITOR: Stopping from within thread")
            return

        # Safe to join from external thread
        if self.trailing_monitor_thread and self.trailing_monitor_thread.is_alive():
            self.trailing_monitor_thread.join(timeout=5.0)  # Wait up to 5 seconds
        self.logger.info("🛑 REAL-TIME TRAILING MONITOR: Stopped")

    def _real_time_trailing_monitor(self):
        """Real-time trailing stop monitor - runs in separate thread"""
        self.logger.info("🚀 REAL-TIME TRAILING MONITOR: Thread started")

        while self.trailing_monitor_active:
            try:
                # Check if we have a position to monitor
                with self.trailing_monitor_lock:
                    has_position = bool(self.current_position and self.trailing_stop_data)

                if not has_position:
                    time.sleep(2.0)  # Check every 2 seconds when no position
                    continue

                # Get current price
                tick_info = self.mt5_manager.get_symbol_info_tick(self.symbol)
                if not tick_info:
                    time.sleep(5.0)  # Wait longer if can't get price
                    continue

                # Determine current price based on position type
                with self.trailing_monitor_lock:
                    if not self.current_position:  # Double-check after lock
                        continue

                    position_type = self.current_position['type']
                    current_price = tick_info['bid'] if position_type == 'SELL' else tick_info['ask']

                # Get SL distance (no longer need ATR for trailing)
                original_sl_distance = 1.50  # Default 150-point distance
                if self.trailing_stop_data and 'original_sl_distance' in self.trailing_stop_data:
                    original_sl_distance = self.trailing_stop_data['original_sl_distance']

                # ENHANCED: Check for pending same signal SL with price confirmation FIRST
                same_signal_applied = self.check_pending_same_signal_sl(current_price)

                # Attempt trailing stop update
                trailing_updated = self.update_trailing_stop(current_price, original_sl_distance)

                if trailing_updated:
                    self.logger.info(f"⚡ REAL-TIME TRAILING: Updated at price {current_price:.5f}")
                elif same_signal_applied:
                    self.logger.info(f"⚡ REAL-TIME SAME SIGNAL: Applied at price {current_price:.5f}")
                else:
                    # Log why trailing didn't happen (but only occasionally to avoid spam)
                    if hasattr(self, '_last_trailing_debug_time'):
                        if time.time() - self._last_trailing_debug_time > 30:  # Every 30 seconds
                            self.logger.info(f"🔍 TRAILING DEBUG: No update at price {current_price:.5f}, SL distance {original_sl_distance:.2f}")
                            self._last_trailing_debug_time = time.time()
                    else:
                        self._last_trailing_debug_time = time.time()

                # Sleep for monitoring interval
                time.sleep(10.0)  # Check every 10 seconds for trailing opportunities

            except Exception as e:
                self.logger.error(f"❌ Real-time trailing monitor error: {e}")
                time.sleep(15.0)  # Wait longer on error

        self.logger.info("🏁 REAL-TIME TRAILING MONITOR: Thread ended")

        # Clean up thread reference when ending from within the thread
        if threading.current_thread().name == "TrailingStopMonitor":
            self.trailing_monitor_thread = None

    def update_trailing_stop(self, current_price, original_sl_distance):
        """Update trailing stop loss every 1 SL distance in profit - ENHANCED with profit/loss checking and thread safety"""
        try:
            # Thread safety: Use lock when accessing shared data
            with self.trailing_monitor_lock:
                if not self.current_position or not self.trailing_stop_data:
                    return False

                # ENHANCED: Check if SL distance trailing should be allowed
                should_allow, allow_reason = self.should_allow_trailing("SL_DISTANCE", current_price)
                if not should_allow:
                    # Only log if called from main thread (avoid spam from real-time monitor)
                    if threading.current_thread().name != "TrailingStopMonitor":
                        self.logger.info(f"🚫 SL DISTANCE TRAILING BLOCKED: {allow_reason}")
                    return False

            position_type = self.current_position['type']
            entry_price = self.current_position['price']
            current_sl = self.trailing_stop_data['current_sl']

            # Use the provided original SL distance (passed as parameter)
            if original_sl_distance is None or original_sl_distance <= 0:
                self.logger.warning("⚠️ Cannot calculate original SL distance for trailing - invalid or zero distance")
                return False

            # Calculate profit in original SL distance units
            if position_type == 'BUY':
                raw_profit_points = current_price - entry_price

                # UNIT CONVERSION FIX: Convert profit to same units as SL distance
                if self.symbol == "XAUUSD!":
                    # For XAUUSD: Profit conversion factor ~93.0 (based on analysis)
                    # This converts price difference to points for profit calculation
                    profit_points = raw_profit_points * 93.0
                    self.logger.info(f"🔧 XAUUSD PROFIT CONVERSION: {raw_profit_points:.2f} price → {profit_points:.1f} points")
                else:
                    profit_points = raw_profit_points

                # FIXED: Additional safety check to prevent division by zero
                if original_sl_distance <= 0:
                    self.logger.error("❌ Division by zero prevented: original_sl_distance is zero")
                    return False
                profit_sl_units = profit_points / original_sl_distance

                # Log current profit status for debugging (only from background thread to avoid spam)
                if threading.current_thread().name == "TrailingStopMonitor":
                    if not hasattr(self, '_last_profit_log_time') or time.time() - self._last_profit_log_time > 30:
                        self.logger.info(f"📊 TRAILING PROFIT CHECK (BUY): Entry={entry_price:.5f}, Current={current_price:.5f}")
                        self.logger.info(f"   Raw Profit: {raw_profit_points:.2f} price → Profit: {profit_points:.1f} points")
                        self.logger.info(f"   SL Distance: {original_sl_distance:.1f} points, SL Units: {profit_sl_units:.4f}")
                        self.logger.info(f"   Current profit_sl_count: {self.trailing_stop_data['profit_sl_count']}")
                        self.logger.info(f"   Need: {self.trailing_stop_data['profit_sl_count'] + 1} units to trigger trailing")
                        self._last_profit_log_time = time.time()

                # Check if we've gained another SL distance unit in profit
                if profit_sl_units >= (self.trailing_stop_data['profit_sl_count'] + 1):
                    # NEW: Move stop loss up by original SL distance
                    new_sl = current_sl + original_sl_distance

                    # CRITICAL FIX: Ensure BUY stop loss stays BELOW current price
                    if new_sl >= current_price:
                        # Calculate maximum allowed SL (current price - 1 pip)
                        pip_size = self.mt5_manager.get_pip_size(self.symbol)
                        max_allowed_sl = current_price - (pip_size * 2)  # 2 pips below current price for safety

                        self.logger.warning(f"⚠️ TRAILING STOP VIOLATION DETECTED (BUY):")
                        self.logger.warning(f"   Calculated SL: {new_sl:.5f} >= Current Price: {current_price:.5f}")
                        self.logger.warning(f"   Adjusting to safe level: {max_allowed_sl:.5f}")

                        new_sl = max_allowed_sl

                    self.logger.info(f"🔄 NEW TRAILING STOP UPDATE (BUY):")
                    self.logger.info(f"   Profit: {profit_sl_units:.2f} SL distance units")
                    self.logger.info(f"   Original SL Distance: {original_sl_distance:.5f}")
                    self.logger.info(f"   Old SL: {current_sl:.5f}")
                    self.logger.info(f"   New SL: {new_sl:.5f} (+{original_sl_distance:.5f})")
                    self.logger.info(f"   Current Price: {current_price:.5f}")
                    self.logger.info(f"   Allowance: {allow_reason}")

                    # Check if new stop loss is significantly different (prevent "No changes" error)
                    pip_size = self.mt5_manager.get_pip_size(self.symbol)
                    if abs(new_sl - current_sl) < pip_size * 1.0:  # Less than 1 pip difference
                        self.logger.warning(f"⚠️ SL DISTANCE TRAILING STOP: New SL {new_sl:.5f} too close to current SL {current_sl:.5f} - skipping")
                        return False

                    # Update tracking data before MT5 modification
                    old_count = self.trailing_stop_data['profit_sl_count']
                    self.trailing_stop_data['profit_sl_count'] += 1
                    self.trailing_stop_data['current_sl'] = new_sl
                    self.logger.info(f"🔧 DEBUG: profit_sl_count INCREMENTED from {old_count} to {self.trailing_stop_data['profit_sl_count']}")

                    # Round stop loss to appropriate decimal places for MT5
                    new_sl_rounded = round(new_sl, 2)  # XAUUSD typically uses 2 decimal places

                    # Update stop loss in MT5
                    success = self.mt5_manager.modify_position(
                        ticket=self.current_position['ticket'],
                        stop_loss=new_sl_rounded
                    )

                    # ALWAYS attempt partial exit when trailing is triggered, even if SL modification has minor issues
                    # This ensures partial exits happen even with floating-point precision differences
                    if success or True:  # Force partial exit attempt
                        # ENHANCED: Check if position is at minimum volume before partial close
                        positions = self.mt5_manager.get_positions(self.symbol)
                        if positions:
                            current_volume = positions[0]['volume']
                            # Only do partial close if volume > minimum (0.01)
                            # If at minimum, let regime/velocity/candle systems handle final exit
                            if current_volume > 0.01:
                                partial_success, partial_msg = self.close_partial_position(close_fraction=1/3)
                                if partial_success:
                                    self.logger.info(f"💰 PROFIT TAKING: {partial_msg}")
                                else:
                                    self.logger.warning(f"⚠️ Partial close failed: {partial_msg}")
                            else:
                                self.logger.info(f"📍 MINIMUM VOLUME REACHED: {current_volume:.2f} lots - Only trailing SL, letting regime/velocity/candle systems handle final exit")

                        # Log trailing stop update
                        if self.current_position and 'trade_id' in self.current_position:
                            self.trade_logger.update_trailing_stop(self.current_position['trade_id'])

                    # Return success based on whether we achieved the main goal (trailing stop update)
                    # Even if verification failed due to minor precision differences, consider it successful
                    # if MT5 reported success initially
                    return True  # Always return True when trailing conditions are met

            else:  # SELL position
                raw_profit_points = entry_price - current_price

                # UNIT CONVERSION FIX: Convert profit to same units as SL distance
                if self.symbol == "XAUUSD!":
                    # For XAUUSD: Profit conversion factor ~93.0 (based on analysis)
                    # This converts price difference to points for profit calculation
                    profit_points = raw_profit_points * 93.0
                    self.logger.info(f"🔧 XAUUSD PROFIT CONVERSION: {raw_profit_points:.2f} price → {profit_points:.1f} points")
                else:
                    profit_points = raw_profit_points

                # FIXED: Additional safety check to prevent division by zero
                if original_sl_distance <= 0:
                    self.logger.error("❌ Division by zero prevented: original_sl_distance is zero")
                    return False
                profit_sl_units = profit_points / original_sl_distance

                # Log current profit status for debugging (only from background thread to avoid spam)
                if threading.current_thread().name == "TrailingStopMonitor":
                    if not hasattr(self, '_last_profit_log_time_sell') or time.time() - self._last_profit_log_time_sell > 30:
                        self.logger.info(f"📊 TRAILING PROFIT CHECK (SELL): Entry={entry_price:.5f}, Current={current_price:.5f}")
                        self.logger.info(f"   Raw Profit: {raw_profit_points:.2f} price → Profit: {profit_points:.1f} points")
                        self.logger.info(f"   SL Distance: {original_sl_distance:.1f} points, SL Units: {profit_sl_units:.4f}")
                        self.logger.info(f"   Need: {self.trailing_stop_data['profit_sl_count'] + 1} units to trigger trailing")
                        self._last_profit_log_time_sell = time.time()

                # Check if we've gained another SL distance unit in profit
                if profit_sl_units >= (self.trailing_stop_data['profit_sl_count'] + 1):
                    # NEW: Move stop loss down by original SL distance
                    new_sl = current_sl - original_sl_distance

                    # CRITICAL FIX: Ensure SELL stop loss stays ABOVE current price
                    if new_sl <= current_price:
                        # Calculate minimum allowed SL (current price + 1 pip)
                        pip_size = self.mt5_manager.get_pip_size(self.symbol)
                        min_allowed_sl = current_price + (pip_size * 2)  # 2 pips above current price for safety

                        self.logger.warning(f"⚠️ TRAILING STOP VIOLATION DETECTED (SELL):")
                        self.logger.warning(f"   Calculated SL: {new_sl:.5f} <= Current Price: {current_price:.5f}")
                        self.logger.warning(f"   Adjusting to safe level: {min_allowed_sl:.5f}")

                        new_sl = min_allowed_sl

                    self.logger.info(f"🔄 NEW TRAILING STOP UPDATE (SELL):")
                    self.logger.info(f"   Profit: {profit_sl_units:.2f} SL distance units")
                    self.logger.info(f"   Original SL Distance: {original_sl_distance:.5f}")
                    self.logger.info(f"   Old SL: {current_sl:.5f}")
                    self.logger.info(f"   New SL: {new_sl:.5f} (-{original_sl_distance:.5f})")
                    self.logger.info(f"   Current Price: {current_price:.5f}")

                    # Check if new stop loss is significantly different (prevent "No changes" error)
                    pip_size = self.mt5_manager.get_pip_size(self.symbol)
                    if abs(new_sl - current_sl) < pip_size * 1.0:  # Less than 1 pip difference
                        self.logger.warning(f"⚠️ SL DISTANCE TRAILING STOP: New SL {new_sl:.5f} too close to current SL {current_sl:.5f} - skipping")
                        return False

                    # Update tracking data before MT5 modification
                    old_count = self.trailing_stop_data['profit_sl_count']
                    self.trailing_stop_data['profit_sl_count'] += 1
                    self.trailing_stop_data['current_sl'] = new_sl
                    self.logger.info(f"🔧 DEBUG: profit_sl_count INCREMENTED from {old_count} to {self.trailing_stop_data['profit_sl_count']}")

                    # Round stop loss to appropriate decimal places for MT5
                    new_sl_rounded = round(new_sl, 2)  # XAUUSD typically uses 2 decimal places

                    # Update stop loss in MT5
                    success = self.mt5_manager.modify_position(
                        ticket=self.current_position['ticket'],
                        stop_loss=new_sl_rounded
                    )

                    # ALWAYS attempt partial exit when trailing is triggered, even if SL modification has minor issues
                    # This ensures partial exits happen even with floating-point precision differences
                    if success or True:  # Force partial exit attempt
                        # ENHANCED: Check if position is at minimum volume before partial close
                        positions = self.mt5_manager.get_positions(self.symbol)
                        if positions:
                            current_volume = positions[0]['volume']
                            # Only do partial close if volume > minimum (0.01)
                            # If at minimum, let regime/velocity/candle systems handle final exit
                            if current_volume > 0.01:
                                partial_success, partial_msg = self.close_partial_position(close_fraction=1/3)
                                if partial_success:
                                    self.logger.info(f"💰 PROFIT TAKING: {partial_msg}")
                                else:
                                    self.logger.warning(f"⚠️ Partial close failed: {partial_msg}")
                            else:
                                self.logger.info(f"📍 MINIMUM VOLUME REACHED: {current_volume:.2f} lots - Only trailing SL, letting regime/velocity/candle systems handle final exit")

                        # Log trailing stop update
                        if self.current_position and 'trade_id' in self.current_position:
                            self.trade_logger.update_trailing_stop(self.current_position['trade_id'])

                    # Return success based on whether we achieved the main goal (trailing stop update)
                    # Even if verification failed due to minor precision differences, consider it successful
                    # if MT5 reported success initially
                    return True  # Always return True when trailing conditions are met

            return False

        except Exception as e:
            self.logger.error(f"❌ Error updating trailing stop: {e}")
            return False

    def check_pending_same_signal_sl(self, current_price):
        """Check and apply pending same signal SL with price confirmation - ENHANCED for background monitor"""
        try:
            # Thread safety: Use lock when accessing shared data
            with self.trailing_monitor_lock:
                if not self.pending_same_signal_data or not self.current_position or not self.trailing_stop_data:
                    return False

                pending_data = self.pending_same_signal_data
                signal_type = pending_data['signal_type']
                pending_sl = pending_data['sl']
                signal_candle_high = pending_data['signal_candle_high']
                signal_candle_low = pending_data['signal_candle_low']
                detected_time = pending_data['detected_time']

            # Check price confirmation first
            price_confirmed = False
            if signal_type == 'BUY':
                price_confirmed = current_price > signal_candle_high
                price_condition = f"Current price {current_price:.5f} > Signal candle high {signal_candle_high:.5f}"
            else:  # SELL
                price_confirmed = current_price < signal_candle_low
                price_condition = f"Current price {current_price:.5f} < Signal candle low {signal_candle_low:.5f}"

            if not price_confirmed:
                # Only log occasionally to avoid spam
                if not hasattr(self, '_last_same_signal_debug_time') or time.time() - self._last_same_signal_debug_time > 60:
                    self.logger.info(f"🔍 SAME SIGNAL WAITING: Price confirmation pending for {signal_type}")
                    self.logger.info(f"   {price_condition} = {price_confirmed}")
                    self._last_same_signal_debug_time = time.time()
                return False

            # Price confirmed! Now check profit and SL improvement conditions
            is_profitable, profit_points, profit_info = self.is_position_profitable()

            if not is_profitable:
                self.logger.info(f"⚠️ SAME SIGNAL PRICE CONFIRMED BUT BLOCKED: Position not profitable ({profit_points:+.5f} points)")
                self.logger.info(f"   Same signal SL updates only allowed when position is in profit")
                # Clear pending data since price confirmed but conditions not met
                with self.trailing_monitor_lock:
                    self.pending_same_signal_data = None
                return False

            # Check if new SL is better than current SL
            current_sl = self.current_position.get('stop_loss', 0)
            if self.trailing_stop_data:
                current_sl = self.trailing_stop_data.get('current_sl', current_sl)

            sl_is_better = False
            if signal_type == "BUY":
                sl_is_better = pending_sl > current_sl  # Higher SL is better for BUY
                comparison = f"{pending_sl:.5f} > {current_sl:.5f}"
            else:  # SELL
                sl_is_better = pending_sl < current_sl  # Lower SL is better for SELL
                comparison = f"{pending_sl:.5f} < {current_sl:.5f}"

            if not sl_is_better:
                self.logger.info(f"⚠️ SAME SIGNAL PRICE CONFIRMED BUT BLOCKED: New SL not better than current")
                self.logger.info(f"   {signal_type} position: New SL {pending_sl:.5f} vs Current SL {current_sl:.5f}")
                self.logger.info(f"   For {signal_type}: Need {comparison} to be beneficial")
                # Clear pending data since price confirmed but SL not better
                with self.trailing_monitor_lock:
                    self.pending_same_signal_data = None
                return False

            # ALL CONDITIONS MET: Apply the same signal SL update
            self.logger.info(f"🎯 SAME SIGNAL CONDITIONS MET - APPLYING SL UPDATE:")
            self.logger.info(f"   ✅ Price confirmed: {price_condition}")
            self.logger.info(f"   ✅ Position profitable: {profit_points:+.5f} points")
            self.logger.info(f"   ✅ SL improvement: {current_sl:.5f} → {pending_sl:.5f} ({comparison})")

            # Update current position's stop loss
            success = self.mt5_manager.modify_position(
                ticket=self.current_position['ticket'],
                stop_loss=round(pending_sl, 2)  # Round for XAUUSD
            )

            if success:
                self.logger.info(f"✅ SAME SIGNAL SL APPLIED: Updated {signal_type} position SL to {pending_sl:.5f}")

                # Update trailing stop data
                with self.trailing_monitor_lock:
                    if self.trailing_stop_data:
                        self.trailing_stop_data['current_sl'] = pending_sl
                        # Recalculate original distance if needed
                        if 'price' in self.current_position:
                            entry_price = self.current_position['price']
                            original_sl_distance = abs(pending_sl - entry_price)
                            self.trailing_stop_data['original_sl_distance'] = original_sl_distance
                            self.logger.info(f"📊 Updated trailing data: SL={pending_sl:.5f}, Distance={original_sl_distance:.5f}")

                    # Clear pending data after successful application
                    self.pending_same_signal_data = None

                return True
            else:
                self.logger.error(f"❌ Failed to apply same signal SL update")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error checking pending same signal SL: {e}")
            return False

    def close_partial_position(self, close_fraction=1/3):
        """Close a fraction of the current position with MT5-valid volume"""
        try:
            if not self.current_position:
                return False, "No position to close"

            # Get current position details from MT5
            positions = self.mt5_manager.get_positions(self.symbol)
            if not positions:
                return False, "Position not found in MT5"

            current_pos = positions[0]
            current_volume = current_pos['volume']

            # Debug logging for volume calculation
            self.logger.info(f"🔍 PARTIAL CLOSE DEBUG:")
            self.logger.info(f"   Current volume: {current_volume}")
            self.logger.info(f"   Close fraction: {close_fraction}")

            # Validate current volume
            if current_volume <= 0:
                return False, f"Invalid current volume: {current_volume}"

            # Calculate volume to close (1/3 of remaining)
            volume_to_close = current_volume * close_fraction
            self.logger.info(f"   Raw volume to close: {volume_to_close}")

            # Get symbol info for volume step and minimum volume
            symbol_info = self.mt5_manager.symbol_info
            if not symbol_info:
                return False, "Cannot get symbol info"

            volume_step = symbol_info.volume_step
            volume_min = symbol_info.volume_min

            self.logger.info(f"   Volume step: {volume_step}, Min volume: {volume_min}")

            # Round to valid MT5 volume (must be multiple of volume_step)
            if volume_step > 0:
                volume_to_close = round(volume_to_close / volume_step) * volume_step
            else:
                volume_to_close = round(volume_to_close, 2)  # Fallback to 2 decimal places

            self.logger.info(f"   Rounded volume to close: {volume_to_close}")

            # Ensure minimum volume requirements
            if volume_to_close < volume_min:
                # Try to close minimum volume instead of failing
                if current_volume >= volume_min:
                    volume_to_close = volume_min
                    self.logger.warning(f"⚠️ Volume too small, using minimum: {volume_min}")
                else:
                    return False, f"Volume too small: {volume_to_close} < {volume_min}, current: {current_volume}"

            # Ensure we don't close more than available
            if volume_to_close >= current_volume:
                volume_to_close = current_volume  # Close entire position

            self.logger.info(f"💰 PARTIAL CLOSE: Closing {volume_to_close:.2f} of {current_volume:.2f} lots ({close_fraction:.1%})")

            # Close partial position
            success = self.mt5_manager.close_position(
                ticket=self.current_position['ticket'],
                symbol=self.symbol,
                volume=volume_to_close
            )

            if success:
                # Update remaining volume tracking
                remaining_volume = current_volume - volume_to_close
                self.current_position['remaining_volume'] = remaining_volume

                self.logger.info(f"✅ Partial close successful. Remaining: {remaining_volume:.2f} lots")

                # If position is fully closed, clear tracking
                # FIXED: Use < instead of <= to avoid stopping monitor when position is at minimum volume
                if remaining_volume <= volume_min:
                    self.logger.info("🔚 Position fully closed")
                    # STOP REAL-TIME TRAILING MONITOR (safe from any thread)
                    self.stop_real_time_trailing_monitor()
                    self.current_position = None
                    self.trailing_stop_data = None
                else:
                    self.logger.info(f"📊 Position still active: {remaining_volume:.2f} lots remaining - Trailing monitor continues")

                return True, f"Closed {volume_to_close:.2f} lots"
            else:
                return False, "MT5 close failed"

        except Exception as e:
            self.logger.error(f"❌ Error closing partial position: {e}")
            return False, str(e)

    def check_current_positions(self):
        """Check if we have any open positions"""
        try:
            positions = self.mt5_manager.get_positions(self.symbol)
            if positions and len(positions) > 0:
                # We have an open position
                pos = positions[0]  # Get first position
                self.current_position = {
                    'type': 'BUY' if pos['type'] == 0 else 'SELL',
                    'ticket': pos['ticket'],
                    'time': datetime.fromtimestamp(pos['time']),
                    'volume': pos['volume'],
                    'remaining_volume': pos['volume'],  # Track remaining volume for partial closes
                    'price': pos['price_open'],
                    'sl': pos.get('sl', 0),
                    'take_profit': pos.get('tp', 0.0),  # Include TP data
                    'original_sl': pos.get('sl', 0)  # NEW: Store original SL for trailing calculations
                }

                # Initialize trailing stop data if not already set
                if not self.trailing_stop_data:
                    entry_price = pos['price_open']
                    initial_sl = pos.get('sl', 0)

                    # ENHANCED: Calculate SL distance in POINTS for XAUUSD consistency
                    raw_sl_distance = abs(initial_sl - entry_price) if initial_sl and entry_price else 0.001

                    # UNIT CONVERSION FIX: Convert to points for XAUUSD
                    if self.symbol == "XAUUSD!":
                        # For XAUUSD: SL distance conversion factor ~45.9 (based on analysis)
                        # This converts price difference to points for SL distance calculation
                        original_sl_distance = raw_sl_distance * 45.9
                        self.logger.info(f"🔧 XAUUSD SL CONVERSION: {raw_sl_distance:.2f} price → {original_sl_distance:.1f} points")
                    else:
                        original_sl_distance = raw_sl_distance
                        self.logger.info(f"🔧 SL DISTANCE: {original_sl_distance:.5f} (no conversion)")

                    self.trailing_stop_data = {
                        'initial_sl': initial_sl,
                        'current_sl': initial_sl,
                        'original_sl_distance': original_sl_distance,
                        'raw_sl_distance': raw_sl_distance,  # Store both for debugging
                        'profit_sl_count': 0  # Track profit in SL distance units
                    }

                    self.logger.info(f"🔄 TRAILING STOP INITIALIZED:")
                    self.logger.info(f"   Entry: {entry_price:.5f}, SL: {initial_sl:.5f}")
                    self.logger.info(f"   Raw Distance: {raw_sl_distance:.5f}, Points Distance: {original_sl_distance:.1f}")
                    self.logger.info(f"🔧 DEBUG: profit_sl_count INITIALIZED to {self.trailing_stop_data['profit_sl_count']}")

                # START REAL-TIME TRAILING MONITOR for recovered position
                self.start_real_time_trailing_monitor()

                return True
            else:
                # STOP REAL-TIME TRAILING MONITOR
                self.stop_real_time_trailing_monitor()
                self.current_position = None
                return False
        except Exception as e:
            self.logger.error(f"❌ Error checking positions: {e}")
            return False

    def close_current_position(self, reason="Opposite Signal"):
        """Close the current position"""
        try:
            if not self.current_position:
                return True

            self.logger.info(f"🔄 Closing current {self.current_position['type']} position - {reason}")

            # Close position
            result = self.mt5_manager.close_position(
                ticket=self.current_position['ticket'],
                symbol=self.symbol,
                volume=self.current_position['volume']
            )

            if result:
                self.logger.info("✅ Position closed successfully!")

                # LOG TRADE RESULT
                if self.current_position and 'trade_id' in self.current_position:
                    # Get current price for exit price
                    tick_info = self.mt5_manager.get_symbol_info_tick(self.symbol)
                    if tick_info:
                        exit_price = tick_info['bid'] if self.current_position['type'] == 'SELL' else tick_info['ask']

                        # Calculate max excursion
                        max_excursion = self.trade_logger.calculate_max_excursion(
                            ticket=self.current_position['ticket'],
                            entry_price=self.current_position['price'],
                            direction=self.current_position['type'],
                            entry_time=self.current_position['time'],
                            exit_time=datetime.now()
                        )

                        result_data = {
                            'exit_price': exit_price,
                            'exit_reason': reason,
                            'max_favorable_pips': max_excursion['max_favorable_pips'],
                            'max_adverse_pips': max_excursion['max_adverse_pips']
                        }

                        self.trade_logger.log_trade_result(self.current_position['trade_id'], result_data)

                # STOP REAL-TIME TRAILING MONITOR
                self.stop_real_time_trailing_monitor()
                self.current_position = None
                self.trailing_stop_data = None  # Clear trailing stop data
                return True
            else:
                self.logger.error("❌ Failed to close position")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error closing position: {e}")
            return False

    def remove_take_profit(self, reason="Regime Change"):
        """Remove take profit from current position to use trailing stops"""
        try:
            if not self.current_position:
                return True

            # Get current position data from MT5 to check actual TP
            positions = self.mt5_manager.get_positions(self.symbol)
            if not positions:
                self.logger.warning(f"⚠️ Position not found in MT5 - cannot remove TP")
                return False

            current_pos = positions[0]
            current_tp = current_pos.get('tp', 0.0)

            # Check if position already has no take profit
            if current_tp == 0.0:
                self.logger.info(f"✅ Take Profit already removed from {self.current_position['type']} position - {reason}")
                return True

            self.logger.info(f"🎯 Removing Take Profit from {self.current_position['type']} position - {reason}")
            self.logger.info(f"   Current TP: {current_tp:.5f} → Target TP: 0.0")

            # Modify position to remove take profit (set to 0)
            success = self.mt5_manager.modify_position(
                ticket=self.current_position['ticket'],
                take_profit=0.0  # Remove take profit
            )

            if success:
                self.logger.info(f"✅ Take Profit removed - Now using trailing stops only")
                # Update our position data to reflect the change
                self.current_position['take_profit'] = 0.0
                return True
            else:
                self.logger.error(f"❌ Failed to remove Take Profit")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error removing take profit: {e}")
            return False

    def execute_trade(self, signal, confidence, balance, atr_value, regime, logic, half_size=False, swing_size_factor=1.0, features_df=None):
        """Execute trade with regime-based single position management"""
        try:
            if signal is None:
                self.logger.info(f"⚠️ No signal - {logic}")
                return False

            if confidence < self.min_confidence:
                self.logger.info(f"⚠️ Low confidence ({confidence:.3f}) - Skipping trade")
                return False

            # OVERTRADING PROTECTION: Minimum 30 seconds between trades
            current_time = datetime.now()
            if self.last_trade_time and (current_time - self.last_trade_time).seconds < 30:
                self.logger.info(f"⚠️ Trade too soon - Last trade {(current_time - self.last_trade_time).seconds}s ago (min 30s)")
                return False

            if atr_value is None or atr_value <= 0:
                self.logger.error(f"❌ Invalid ATR value: {atr_value}")
                return False

            # Check for current positions
            has_position = self.check_current_positions()

            # PRIORITY: Check for sensitive closing conditions FIRST (regardless of new signal)
            if has_position and self.current_position:
                current_type = self.current_position['type']

                # Use the candle strength from the main prediction (avoid recalculating)
                try:
                    # Get the candle strength from the main analysis
                    result = self.get_live_prediction()
                    if result and len(result) >= 13:
                        _, _, _, _, _, _, _, candle_strength_data, _, _, _, _, _ = result
                        candle_net_strength = candle_strength_data['net_strength'] * 100

                        # SENSITIVE CLOSING LOGIC (PRIORITY - works regardless of signal)
                        should_close = False
                        close_reason = ""

                        # DISABLED: Sensitive closing logic (candle strength sign change exits)
                        # This logic would close SELL when strength > 0 and BUY when strength < 0
                        # Currently disabled per user request

                        # Placeholder for future sensitive exit logic
                        should_close = False
                        close_reason = ""

                        # NEW: Phase 2 - Acceleration-based early exits
                        if not should_close and candle_strength_data.get('acceleration_available', False):
                            bull_accel = candle_strength_data.get('bull_acceleration', 0)
                            bear_accel = candle_strength_data.get('bear_acceleration', 0)
                            bull_velocity = candle_strength_data.get('bull_velocity', 0)
                            bear_velocity = candle_strength_data.get('bear_velocity', 0)

                            self.logger.info(f"🔍 ACCELERATION EXIT CHECK: {current_type} position")
                            self.logger.info(f"   Bull Velocity: {bull_velocity:+.1f}% | Bear Velocity: {bear_velocity:+.1f}%")
                            self.logger.info(f"   Bull Accel: {bull_accel:+.1f}% | Bear Accel: {bear_accel:+.1f}%")

                            # ENHANCED: Check if position allows velocity/acceleration exits (profit OR 50%+ loss)
                            is_profitable, profit_points, profit_info = self.is_position_profitable()

                            # Calculate profit percentage relative to SL distance (same logic as should_allow_trailing)
                            entry_price = self.current_position['price']
                            if self.current_position.get('stop_loss'):
                                sl_distance = abs(entry_price - self.current_position['stop_loss'])
                                profit_percentage = (profit_points / sl_distance * 100) if sl_distance > 0 else 0
                            else:
                                profit_percentage = 0

                            self.logger.info(f"💰 PROFIT CHECK: {profit_info} | Profitable: {is_profitable} | Points: {profit_points:+.5f} | Percentage: {profit_percentage:+.1f}%")

                            # ENHANCED: Allow velocity/acceleration exits for profit protection OR cutting big losses
                            should_allow_velocity_accel_exits = False
                            velocity_accel_reason = ""

                            if is_profitable:
                                should_allow_velocity_accel_exits = True
                                velocity_accel_reason = f"Position profitable ({profit_points:+.5f} points, {profit_percentage:+.1f}%) - Velocity/Acceleration exits allowed to protect profits"
                            elif profit_percentage <= -50:  # Position is 50% or MORE in loss
                                should_allow_velocity_accel_exits = True
                                velocity_accel_reason = f"Position 50%+ in loss ({profit_points:+.5f} points, {profit_percentage:+.1f}% ≤ -50%) - Velocity/Acceleration exits allowed to cut big losses"
                            else:
                                should_allow_velocity_accel_exits = False
                                velocity_accel_reason = f"Position not profitable and loss < 50% ({profit_points:+.5f} points, {profit_percentage:+.1f}% > -50%) - Velocity/Acceleration exits blocked (let position recover)"

                            if not should_allow_velocity_accel_exits:
                                self.logger.info(f"⚠️ VELOCITY/ACCELERATION EXITS BLOCKED: {velocity_accel_reason}")
                                self.logger.info(f"   Bull Velocity: {bull_velocity:+.1f}% | Bear Velocity: {bear_velocity:+.1f}%")
                                self.logger.info(f"   Bull Accel: {bull_accel:+.1f}% | Bear Accel: {bear_accel:+.1f}%")
                            else:
                                self.logger.info(f"✅ VELOCITY/ACCELERATION EXITS ALLOWED: {velocity_accel_reason}")
                                # ENHANCED: Phase 2.5 - Momentum direction change exits (PROFIT OR 50%+ LOSS)
                                if current_type == 'BUY' and bull_velocity < 0:
                                    # Check if new signal aligns with current position before closing
                                    signal_aligns = signal and signal == 'BUY'
                                    if signal_aligns:
                                        self.logger.info(f"⚠️ VELOCITY EXIT BLOCKED: New {signal} signal aligns with current {current_type} position")
                                        self.logger.info(f"   Bull velocity: {bull_velocity:+.1f}% < 0% (would trigger exit but signal reinforces position)")
                                    else:
                                        # This is the old logic - should use candle confirmation + trailing stops
                                        # But this path should not be reached since we have the enhanced logic below
                                        self.logger.info(f"⚠️ OLD MOMENTUM EXIT PATH: This should use candle confirmation logic below")
                                        should_close = False  # Let the enhanced logic below handle it
                                elif current_type == 'SELL' and bear_velocity < 0:
                                    # Check if new signal aligns with current position before closing
                                    signal_aligns = signal and signal == 'SELL'
                                    if signal_aligns:
                                        self.logger.info(f"⚠️ VELOCITY EXIT BLOCKED: New {signal} signal aligns with current {current_type} position")
                                        self.logger.info(f"   Bear velocity: {bear_velocity:+.1f}% < 0% (would trigger exit but signal reinforces position)")
                                    else:
                                        # This is the old logic - should use candle confirmation + trailing stops
                                        # But this path should not be reached since we have the enhanced logic below
                                        self.logger.info(f"⚠️ OLD MOMENTUM EXIT PATH: This should use candle confirmation logic below")
                                        should_close = False  # Let the enhanced logic below handle it
                                # Original Phase 2 - Acceleration threshold exits (ONLY IN PROFIT)
                                elif current_type == 'BUY' and bull_accel < -10:
                                    # Check if new signal aligns with current position before closing
                                    signal_aligns = signal and signal == 'BUY'
                                    if signal_aligns:
                                        self.logger.info(f"⚠️ ACCELERATION EXIT BLOCKED: New {signal} signal aligns with current {current_type} position")
                                        self.logger.info(f"   Bull acceleration: {bull_accel:+.1f}% < -10% (would trigger exit but signal reinforces position)")
                                    else:
                                        # This is the old logic - should use candle confirmation + trailing stops
                                        # But this path should not be reached since we have the enhanced logic below
                                        self.logger.info(f"⚠️ OLD ACCELERATION EXIT PATH: This should use candle confirmation logic below")
                                        should_close = False  # Let the enhanced logic below handle it
                                elif current_type == 'SELL' and bear_accel < -10:
                                    # Check if new signal aligns with current position before closing
                                    signal_aligns = signal and signal == 'SELL'
                                    if signal_aligns:
                                        self.logger.info(f"⚠️ ACCELERATION EXIT BLOCKED: New {signal} signal aligns with current {current_type} position")
                                        self.logger.info(f"   Bear acceleration: {bear_accel:+.1f}% < -10% (would trigger exit but signal reinforces position)")
                                    else:
                                        # This is the old logic - should use candle confirmation + trailing stops
                                        # But this path should not be reached since we have the enhanced logic below
                                        self.logger.info(f"⚠️ OLD ACCELERATION EXIT PATH: This should use candle confirmation logic below")
                                        should_close = False  # Let the enhanced logic below handle it
                                else:
                                    self.logger.info(f"   No acceleration exit: All thresholds safe (position in profit)")
                        elif not should_close:
                            accel_available = candle_strength_data.get('acceleration_available', False)
                            self.logger.info(f"🔍 ACCELERATION EXIT SKIPPED: acceleration_available={accel_available}")

                        if should_close:
                            self.logger.info(f"🔄 SENSITIVE CLOSING: {close_reason}")
                            if not self.close_current_position("Sensitive Candle Strength"):
                                return False
                            # Position closed - can now potentially open new position
                            has_position = False  # Update status
                        else:
                            # No sensitive closing needed - check for same/opposite signals
                            if signal and current_type == signal:
                                # ENHANCED: Store same signal SL for price confirmation by background monitor
                                self.logger.info(f"🔄 Same signal ({signal}) detected - Storing for price confirmation")

                                # Calculate what the new signal's stop loss would be using swing-based logic
                                latest_data = self.get_latest_data_safe()
                                if latest_data is not None and len(latest_data) >= 2:
                                    signal_candle = latest_data.iloc[-2]  # Last closed candle (signal candle)
                                    try:
                                        new_signal_sl, sl_method = self._calculate_swing_based_stop_loss(signal, signal_candle, latest_data)
                                        self.logger.info(f"🔄 Same signal SL calculation: {sl_method}")
                                    except Exception as e:
                                        # Fallback to original logic
                                        if signal == "BUY":
                                            new_signal_sl = signal_candle['low'] - 1.50  # 150 points below signal candle low
                                        else:  # SELL
                                            new_signal_sl = signal_candle['high'] + 1.50  # 150 points above signal candle high
                                        self.logger.warning(f"⚠️ Same signal SL fallback used: {e}")

                                    # Store same signal data for background monitor to handle with price confirmation
                                    self.pending_same_signal_data = {
                                        'sl': new_signal_sl,
                                        'signal_type': signal,
                                        'signal_candle_high': signal_candle['high'],
                                        'signal_candle_low': signal_candle['low'],
                                        'detected_time': datetime.now()
                                    }

                                    self.logger.info(f"📦 SAME SIGNAL STORED FOR PRICE CONFIRMATION:")
                                    self.logger.info(f"   Signal: {signal}")
                                    self.logger.info(f"   Pending SL: {new_signal_sl:.5f}")
                                    self.logger.info(f"   Signal Candle High: {signal_candle['high']:.5f}")
                                    self.logger.info(f"   Signal Candle Low: {signal_candle['low']:.5f}")
                                    if signal == "BUY":
                                        self.logger.info(f"   Price Confirmation: Current price must go above {signal_candle['high']:.5f}")
                                    else:  # SELL
                                        self.logger.info(f"   Price Confirmation: Current price must go below {signal_candle['low']:.5f}")
                                    self.logger.info(f"   Background monitor will apply SL when price confirms + position profitable + SL better")

                                    return False  # Don't open new position, stored for background processing
                            elif signal and ((current_type == 'BUY' and signal == 'SELL') or (current_type == 'SELL' and signal == 'BUY')):
                                # DISABLED: No automatic closure for opposite signals - only SL update
                                self.logger.info(f"🔄 Opposite signal detected: Current={current_type}, New={signal} - SL will be updated later, no closure")
                                # Signal will be handled by the main opposite signal logic (SL update only)
                                return False  # Keep current position, don't open new one
                            elif signal is None:
                                # No new signal and no sensitive closing - keep position
                                return False
                    else:
                        # Fallback to traditional logic if can't get candle strength
                        if signal and ((current_type == 'BUY' and signal == 'SELL') or (current_type == 'SELL' and signal == 'BUY')):
                            # DISABLED: No automatic closure for opposite signals - only SL update
                            self.logger.info(f"🔄 Opposite signal detected: Current={current_type}, New={signal} - SL will be updated later, no closure")
                            # Signal will be handled by the main opposite signal logic (SL update only)
                            return False  # Keep current position, don't open new one
                        elif signal and current_type == signal:
                            # ENHANCED: Store same signal SL for price confirmation by background monitor
                            self.logger.info(f"🔄 Same signal ({signal}) detected - Storing for price confirmation")

                            # Calculate what the new signal's stop loss would be using swing-based logic
                            latest_data = self.get_latest_data_safe()
                            if latest_data is not None and len(latest_data) >= 2:
                                signal_candle = latest_data.iloc[-2]  # Last closed candle (signal candle)
                                try:
                                    new_signal_sl, sl_method = self._calculate_swing_based_stop_loss(signal, signal_candle, latest_data)
                                    self.logger.info(f"🔄 Same signal SL calculation: {sl_method}")
                                except Exception as e:
                                    # Fallback to original logic
                                    if signal == "BUY":
                                        new_signal_sl = signal_candle['low'] - 1.50  # 150 points below signal candle low
                                    else:  # SELL
                                        new_signal_sl = signal_candle['high'] + 1.50  # 150 points above signal candle high
                                    self.logger.warning(f"⚠️ Same signal SL fallback used: {e}")

                                # Store same signal data for background monitor to handle with price confirmation
                                self.pending_same_signal_data = {
                                    'sl': new_signal_sl,
                                    'signal_type': signal,
                                    'signal_candle_high': signal_candle['high'],
                                    'signal_candle_low': signal_candle['low'],
                                    'detected_time': datetime.now()
                                }

                                self.logger.info(f"📦 SAME SIGNAL STORED FOR PRICE CONFIRMATION:")
                                self.logger.info(f"   Signal: {signal}")
                                self.logger.info(f"   Pending SL: {new_signal_sl:.5f}")
                                self.logger.info(f"   Signal Candle High: {signal_candle['high']:.5f}")
                                self.logger.info(f"   Signal Candle Low: {signal_candle['low']:.5f}")
                                if signal == "BUY":
                                    self.logger.info(f"   Price Confirmation: Current price must go above {signal_candle['high']:.5f}")
                                else:  # SELL
                                    self.logger.info(f"   Price Confirmation: Current price must go below {signal_candle['low']:.5f}")
                                self.logger.info(f"   Background monitor will apply SL when price confirms + position profitable + SL better")

                                return False  # Don't open new position, stored for background processing
                        elif signal is None:
                            return False

                except Exception as e:
                    self.logger.error(f"❌ Error in sensitive closing logic: {e}")
                    # Fallback to traditional logic
                    if signal and ((current_type == 'BUY' and signal == 'SELL') or (current_type == 'SELL' and signal == 'BUY')):
                        # DISABLED: No automatic closure for opposite signals - only SL update
                        self.logger.info(f"🔄 Opposite signal detected: Current={current_type}, New={signal} - SL will be updated later, no closure")
                        # Signal will be handled by the main opposite signal logic (SL update only)
                        return False  # Keep current position, don't open new one
                    elif signal and current_type == signal:
                        # ENHANCED: Store same signal SL for price confirmation by background monitor
                        self.logger.info(f"🔄 Same signal ({signal}) detected - Storing for price confirmation")

                        # Calculate what the new signal's stop loss would be using swing-based logic
                        # Need to get the signal candle data to calculate consistent SL
                        latest_data = self.get_latest_data_safe()
                        if latest_data is not None and len(latest_data) >= 2:
                            signal_candle = latest_data.iloc[-2]  # Last closed candle (signal candle)
                            try:
                                new_signal_sl, sl_method = self._calculate_swing_based_stop_loss(signal, signal_candle, latest_data)
                                self.logger.info(f"🔄 Same signal SL calculation: {sl_method}")
                            except Exception as e:
                                # Fallback to original logic
                                if signal == "BUY":
                                    new_signal_sl = signal_candle['low'] - 1.50  # 150 points below signal candle low
                                else:  # SELL
                                    new_signal_sl = signal_candle['high'] + 1.50  # 150 points above signal candle high
                                self.logger.warning(f"⚠️ Same signal SL fallback used: {e}")

                            # Store same signal data for background monitor to handle with price confirmation
                            self.pending_same_signal_data = {
                                'sl': new_signal_sl,
                                'signal_type': signal,
                                'signal_candle_high': signal_candle['high'],
                                'signal_candle_low': signal_candle['low'],
                                'detected_time': datetime.now()
                            }

                            self.logger.info(f"📦 SAME SIGNAL STORED FOR PRICE CONFIRMATION:")
                            self.logger.info(f"   Signal: {signal}")
                            self.logger.info(f"   Pending SL: {new_signal_sl:.5f}")
                            self.logger.info(f"   Signal Candle High: {signal_candle['high']:.5f}")
                            self.logger.info(f"   Signal Candle Low: {signal_candle['low']:.5f}")
                            if signal == "BUY":
                                self.logger.info(f"   Price Confirmation: Current price must go above {signal_candle['high']:.5f}")
                            else:  # SELL
                                self.logger.info(f"   Price Confirmation: Current price must go below {signal_candle['low']:.5f}")
                            self.logger.info(f"   Background monitor will apply SL when price confirms + position profitable + SL better")

                            return True  # Allow continuation, stored for background processing
                    elif signal is None:
                        return False
            elif has_position:
                self.logger.info(f"⚠️ Already have open position - Only 1 concurrent trade allowed")
                return False

            # Get current price
            tick = self.mt5_manager.get_symbol_info_tick(self.symbol)
            if not tick:
                self.logger.error("❌ Cannot get current price")
                return False

            # NEW: Get confirmation candle data first for accurate position sizing
            try:
                latest_data = self.get_latest_data_safe()
                if latest_data is not None and len(latest_data) >= 2:
                    closed_candle = latest_data.iloc[-2]  # Last closed candle
                    confirmation_candle_data = {
                        'high': closed_candle['high'],
                        'low': closed_candle['low'],
                        'close': closed_candle['close']
                    }

                    # ENHANCED: Calculate position size with actual signal candle data for accurate SL distance
                    current_price = tick['ask'] if signal == "BUY" else tick['bid']
                    lot_size = self.calculate_position_size(
                        balance, current_price, atr_value, half_size, swing_size_factor, signal, confirmation_candle_data
                    )

                    # Set stop loss and take profit based on regime
                    # NOTE: These values are for logging only - actual SL will be set by pending order logic
                    if signal == "BUY":
                        price = tick['ask']
                        stop_loss = price - 1.50  # 150 points (will be overridden by pending order with signal candle logic)
                        # NEW: Set 1:1 take profit for RANGING markets (using SL distance)
                        if regime == "RANGING":
                            take_profit = price + 1.50  # 1:1 risk-reward (150 points TP = SL distance)
                        else:
                            take_profit = None  # No take profit for TRENDING/TRANSITIONAL
                    else:
                        price = tick['bid']
                        stop_loss = price + 1.50  # 150 points (will be overridden by pending order with signal candle logic)
                        # NEW: Set 1:1 take profit for RANGING markets (using SL distance)
                        if regime == "RANGING":
                            take_profit = price - 1.50  # 1:1 risk-reward (150 points TP = SL distance)
                        else:
                            take_profit = None  # No take profit for TRENDING/TRANSITIONAL

                    # Execute order
                    self.logger.info(f"📈 REGIME-BASED TRADE:")
                    self.logger.info(f"   Signal: {signal} ({logic})")
                    self.logger.info(f"   Regime: {regime}")
                    self.logger.info(f"   Confidence: {confidence:.3f}")
                    self.logger.info(f"   Price: {price:.5f}")
                    self.logger.info(f"   Lot Size: {lot_size}")
                    self.logger.info(f"   Stop Loss: {stop_loss:.5f} (150 points - will be set by pending order based on signal candle)")
                    self.logger.info(f"   ATR: {atr_value:.5f}")
                    if take_profit:
                        self.logger.info(f"   Take Profit: {take_profit:.5f} (1:1 R:R - RANGING)")
                    else:
                        self.logger.info(f"   Take Profit: None (TRENDING/TRANSITIONAL)")

                    # Place pending order 1 pip above/below confirmation candle
                    result = self.place_pending_order_from_confirmation_candle(
                        signal, confirmation_candle_data, lot_size, atr_value, features_df
                    )

                    if result:
                        self.logger.info("✅ Pending order placed successfully!")
                        self.logger.info(f"   Will be filled if price breaks confirmation candle level")
                        self.logger.info(f"   Order will expire if not filled within 3 candles (15 minutes)")

                        self.last_signal = signal
                        self.last_trade_time = datetime.now()  # Update for overtrading protection
                        return True
                    else:
                        self.logger.error("❌ Failed to place pending order")
                        return False
                else:
                    self.logger.error("❌ Cannot get confirmation candle data for pending order")
                    return False

            except Exception as e:
                self.logger.error(f"❌ Error in pending order placement: {e}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error executing trade: {e}")
            return False
    
    def wait_for_candle_close(self):
        """Wait until the next 5-minute candle closes (at times like 11:00, 11:05, 11:10, etc.)"""
        current_time = datetime.now()

        # Calculate seconds since the hour started
        seconds_since_hour = current_time.minute * 60 + current_time.second

        # Calculate which 5-minute interval we're in (0, 5, 10, 15, etc.)
        current_5min_interval = (current_time.minute // 5) * 5

        # Calculate the next 5-minute mark
        next_5min_interval = current_5min_interval + 5
        if next_5min_interval >= 60:
            # Next interval is in the next hour
            next_candle_close = current_time.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
        else:
            # Next interval is in the same hour
            next_candle_close = current_time.replace(minute=next_5min_interval, second=0, microsecond=0)

        # Calculate wait time
        wait_seconds = (next_candle_close - current_time).total_seconds()

        self.logger.info(f"⏰ Current time: {current_time.strftime('%H:%M:%S')}")
        self.logger.info(f"⏰ Next candle closes at: {next_candle_close.strftime('%H:%M:%S')}")
        self.logger.info(f"⏰ Waiting {wait_seconds:.1f} seconds for candle close...")

        return wait_seconds, next_candle_close

    def trading_loop(self):
        """Main trading loop synchronized with 5-minute candle closes"""
        self.logger.info("🚀 Starting fixed live trading loop (synchronized with 5-minute candle closes)...")

        last_analysis_time = None

        while self.is_running:
            try:
                # WEEKEND CHECK: Sleep during Saturday and Sunday when markets are closed
                if self.is_weekend():
                    self.sleep_until_monday()
                    if not self.is_running:  # Check if shutdown was requested during weekend sleep
                        break
                    continue  # After weekend, restart the loop

                current_time = datetime.now()

                # Check if we should run analysis (exactly when candles close)
                should_analyze = False

                # Check if we're at a 5-minute boundary (within 10 seconds tolerance)
                seconds_since_hour = current_time.minute * 60 + current_time.second
                is_at_5min_boundary = (seconds_since_hour % 300) <= 10  # Within 10 seconds of 5-min mark

                if last_analysis_time is None:
                    # First run - check if we're at a boundary
                    if is_at_5min_boundary:
                        should_analyze = True
                        last_analysis_time = current_time
                        self.logger.info(f"🎯 FIRST ANALYSIS: Running at candle close {current_time.strftime('%H:%M:%S')}")
                    else:
                        # Wait for next candle close
                        wait_seconds, next_candle_close = self.wait_for_candle_close()
                        time.sleep(min(wait_seconds, 30))  # Sleep in chunks of max 30 seconds
                        continue
                else:
                    # Check if enough time has passed since last analysis (at least 4:50)
                    time_since_last = (current_time - last_analysis_time).total_seconds()

                    if is_at_5min_boundary and time_since_last >= 290:  # At boundary and enough time passed
                        should_analyze = True
                        last_analysis_time = current_time
                        self.logger.info(f"🎯 CANDLE CLOSE ANALYSIS: Running at {current_time.strftime('%H:%M:%S')} ({time_since_last:.0f}s since last)")
                    else:
                        # Not time yet, sleep and continue
                        if is_at_5min_boundary and time_since_last < 290:
                            self.logger.info(f"⏰ At boundary but too soon: {time_since_last:.0f}s < 290s since last analysis")
                        time.sleep(10)
                        continue

                if should_analyze:
                    # Increment iteration counter
                    self.iteration_count += 1

                    # LATENCY HANDLING: Check if we got fresh candle data
                    retry_count = 0
                    max_retries = 3

                    # Time-based fallback: if we've been waiting too long, force analysis
                    analysis_start_time = datetime.now()

                    while retry_count < max_retries:
                        # Get latest data to check candle freshness
                        latest_data = self.get_latest_data_safe()
                        if latest_data is not None and len(latest_data) >= 2:
                            # Get the last closed candle time
                            last_closed_candle = latest_data.iloc[-2]  # -2 because -1 is forming candle
                            current_candle_time = pd.to_datetime(last_closed_candle.name)

                            # Enhanced debugging for stuck candle issue
                            self.logger.info(f"🔍 CANDLE DEBUG: Current={current_candle_time}, Last={self.last_analyzed_candle_time}")
                            self.logger.info(f"🔍 CANDLE DEBUG: Data shape={latest_data.shape}, Last 2 timestamps={list(latest_data.index[-2:])}")

                            # Check if this is the same candle we analyzed before
                            if self.last_analyzed_candle_time is not None and current_candle_time == self.last_analyzed_candle_time:
                                retry_count += 1
                                elapsed_time = (datetime.now() - analysis_start_time).total_seconds()

                                self.logger.warning(f"⏰ LATENCY DETECTED: Same candle as last analysis ({current_candle_time}), waiting 1s... (retry {retry_count}/{max_retries})")

                                # TIME-BASED FALLBACK: If we've been trying for more than 10 seconds, force proceed
                                if elapsed_time > 10:
                                    self.logger.warning(f"🔄 TIME-BASED FALLBACK: Been waiting {elapsed_time:.1f}s, forcing analysis to prevent infinite loop")
                                    # Set to a slightly different time to break the loop
                                    self.last_analyzed_candle_time = current_candle_time + pd.Timedelta(seconds=1)
                                    break

                                # RETRY-BASED FALLBACK: If we've hit max retries, force proceed
                                if retry_count >= max_retries:
                                    self.logger.warning(f"🔄 RETRY-BASED FALLBACK: Max retries reached, forcing analysis to prevent infinite loop")
                                    # Set to a slightly different time to break the loop
                                    self.last_analyzed_candle_time = current_candle_time + pd.Timedelta(seconds=1)
                                    break

                                time.sleep(1)
                                continue
                            else:
                                # Fresh candle data - proceed with analysis
                                self.last_analyzed_candle_time = current_candle_time
                                self.logger.info(f"✅ FRESH CANDLE DATA: Analyzing candle closed at {current_candle_time}")
                                break
                        else:
                            retry_count += 1
                            self.logger.warning(f"⚠️ Could not get candle data, retrying... ({retry_count}/{max_retries})")
                            time.sleep(1)

                    if retry_count >= max_retries and self.last_analyzed_candle_time is None:
                        self.logger.error(f"❌ Failed to get fresh candle data after {max_retries} retries, skipping analysis")
                        time.sleep(10)
                        continue

                    # Get account info
                    account_info = self.mt5_manager.get_account_info()
                    if not account_info:
                        self.logger.error("❌ Cannot get account info")
                        time.sleep(60)
                        continue

                    balance = account_info['balance']

                    # Check current position status
                    has_position = self.check_current_positions()
                    position_info = ""
                    if has_position and self.current_position:
                        position_info = f" | Current: {self.current_position['type']} @ {self.current_position['price']:.2f}"

                    # Get prediction with regime detection
                    signal, confidence, details, atr_value, regime, logic, regime_details, candle_strength, qqe_analysis, features_df, half_size, swing_size_factor, candle_pattern_analysis = self.get_live_prediction()

                    # PRIORITY: Check for position closing BEFORE processing new signals
                    if has_position and self.current_position and candle_strength:
                        current_type = self.current_position['type']
                        candle_net_strength = candle_strength['net_strength'] * 100

                        # Check for sensitive closing and acceleration exits
                        should_close = False
                        close_reason = ""

                        # DISABLED: Sensitive closing logic (candle strength sign change exits)
                        # This logic would close SELL when strength > 0 and BUY when strength < 0
                        # Currently disabled per user request

                        # Skip sensitive closing - go directly to other exit checks
                        should_close = False
                        close_reason = ""

                        # Acceleration-based exits
                        if not should_close and candle_strength.get('acceleration_available', False):
                            bull_velocity = candle_strength.get('bull_velocity', 0)
                            bear_velocity = candle_strength.get('bear_velocity', 0)
                            bull_accel = candle_strength.get('bull_acceleration', 0)
                            bear_accel = candle_strength.get('bear_acceleration', 0)

                            self.logger.info(f"🔍 ACCELERATION EXIT CHECK: {current_type} position")
                            self.logger.info(f"   Bull Velocity: {bull_velocity:+.1f}% | Bear Velocity: {bear_velocity:+.1f}%")
                            self.logger.info(f"   Bull Accel: {bull_accel:+.1f}% | Bear Accel: {bear_accel:+.1f}%")

                            # ENHANCED: Check if position allows velocity/acceleration exits (profit OR 50%+ loss)
                            is_profitable, profit_points, profit_info = self.is_position_profitable()

                            # Calculate profit percentage relative to SL distance (same logic as should_allow_trailing)
                            entry_price = self.current_position['price']
                            if self.current_position.get('stop_loss'):
                                sl_distance = abs(entry_price - self.current_position['stop_loss'])
                                profit_percentage = (profit_points / sl_distance * 100) if sl_distance > 0 else 0
                            else:
                                profit_percentage = 0

                            self.logger.info(f"💰 PROFIT CHECK: {profit_info} | Profitable: {is_profitable} | Points: {profit_points:+.5f} | Percentage: {profit_percentage:+.1f}%")

                            # ENHANCED: Allow velocity/acceleration exits for profit protection OR cutting big losses
                            should_allow_velocity_accel_exits = False
                            velocity_accel_reason = ""

                            if is_profitable:
                                should_allow_velocity_accel_exits = True
                                velocity_accel_reason = f"Position profitable ({profit_points:+.5f} points, {profit_percentage:+.1f}%) - Velocity/Acceleration exits allowed to protect profits"
                            elif profit_percentage <= -50:  # Position is 50% or MORE in loss
                                should_allow_velocity_accel_exits = True
                                velocity_accel_reason = f"Position 50%+ in loss ({profit_points:+.5f} points, {profit_percentage:+.1f}% ≤ -50%) - Velocity/Acceleration exits allowed to cut big losses"
                            else:
                                should_allow_velocity_accel_exits = False
                                velocity_accel_reason = f"Position not profitable and loss < 50% ({profit_points:+.5f} points, {profit_percentage:+.1f}% > -50%) - Velocity/Acceleration exits blocked (let position recover)"

                            if not should_allow_velocity_accel_exits:
                                self.logger.info(f"⚠️ VELOCITY/ACCELERATION EXITS BLOCKED: {velocity_accel_reason}")
                                # Skip all velocity/acceleration exit logic
                                velocity_exit_triggered = False
                            else:
                                self.logger.info(f"✅ VELOCITY/ACCELERATION EXITS ALLOWED: {velocity_accel_reason}")
                                # ENHANCED Phase 2.5 - Momentum direction change exits with candle confirmation (PROFIT OR 50%+ LOSS)
                                velocity_exit_triggered = False
                            candle_confirmation = False

                            # Get current candle data for confirmation
                            try:
                                # Get latest data for candle confirmation
                                latest_data = self.get_latest_data_safe()
                                if latest_data is not None and len(latest_data) >= 2:  # Need at least 2 candles to get closed candle
                                    # Use LAST CLOSED candle (index -2), not current forming candle (index -1)
                                    closed_candle = latest_data.iloc[-2]
                                    candle_high = closed_candle['high']
                                    candle_low = closed_candle['low']
                                    candle_close = closed_candle['close']
                                    candle_range = candle_high - candle_low

                                    if candle_range > 0:
                                        close_position = (candle_close - candle_low) / candle_range

                                        # Check velocity exit conditions with candle confirmation (ONLY IN PROFIT)
                                        if current_type == 'BUY' and bull_velocity < 0:
                                            velocity_exit_triggered = True
                                            candle_confirmation = close_position <= 0.40  # Below 40% for BUY exits
                                            self.logger.info(f"🔍 BUY VELOCITY EXIT: bull_velocity {bull_velocity:+.1f}% < 0 [IN PROFIT: {profit_points:+.5f}]")
                                            self.logger.info(f"   Candle confirmation (LAST CLOSED): Close at {close_position:.1%} of range (need ≤40% to exit)")

                                        elif current_type == 'SELL' and bear_velocity < 0:
                                            velocity_exit_triggered = True
                                            candle_confirmation = close_position >= 0.60  # Above 60% for SELL exits
                                            self.logger.info(f"🔍 SELL VELOCITY EXIT: bear_velocity {bear_velocity:+.1f}% < 0 [IN PROFIT: {profit_points:+.5f}]")
                                            self.logger.info(f"   Candle confirmation (LAST CLOSED): Close at {close_position:.1%} of range (need ≥60% to exit)")

                                        # Only exit if both velocity changed AND candle confirms
                                        if velocity_exit_triggered and candle_confirmation:
                                            # Instead of closing immediately, set trailing stop 10 pips from confirmation candle
                                            confirmation_candle_data = {
                                                'high': candle_high,
                                                'low': candle_low,
                                                'close': candle_close
                                            }
                                            if current_type == 'BUY':
                                                close_reason = f"BUY Close: Momentum turned bearish (bull velocity {bull_velocity:+.1f}% < 0%) + Candle confirmation (last closed at {close_position:.1%} ≤ 40%) [PROFIT: {profit_points:+.5f}] - TRAILING STOP SET"
                                            else:
                                                close_reason = f"SELL Close: Momentum turned bullish (bear velocity {bear_velocity:+.1f}% < 0%) + Candle confirmation (last closed at {close_position:.1%} ≥ 60%) [PROFIT: {profit_points:+.5f}] - TRAILING STOP SET"

                                            # TEMPORARILY DISABLED: Velocity-based trailing stops
                                            # if self.set_candle_confirmation_trailing_stop(confirmation_candle_data, current_type):
                                            #     self.logger.info(f"🎯 VELOCITY TRAILING STOP SET: {close_reason}")
                                            # else:
                                            #     # FIXED: Don't close position just because trailing stop failed
                                            #     # Continue monitoring with existing stop loss
                                            #     close_reason = close_reason.replace("TRAILING STOP SET", "TRAILING STOP FAILED - CONTINUING WITH CURRENT SL")
                                            #     self.logger.warning(f"⚠️ VELOCITY TRAILING STOP FAILED: {close_reason}")
                                            #     self.logger.info(f"🔄 Position remains open with existing stop loss - will retry trailing on next analysis")

                                            # Keep analysis active but disable trailing stop action
                                            close_reason = close_reason.replace("TRAILING STOP SET", "TRAILING STOP DISABLED")
                                            self.logger.info(f"🚫 VELOCITY TRAILING STOP DISABLED: {close_reason}")
                                            self.logger.info(f"🔄 Velocity analysis active but trailing stop action temporarily disabled")
                                        elif velocity_exit_triggered and not candle_confirmation:
                                            self.logger.info(f"⚠️ VELOCITY EXIT BLOCKED: Momentum changed but last closed candle doesn't confirm (close at {close_position:.1%})")

                            except Exception as e:
                                self.logger.error(f"❌ Error in candle confirmation for velocity exit: {e}")
                                # Fallback to original logic if candle data unavailable
                                if current_type == 'BUY' and bull_velocity < 0:
                                    should_close = True
                                    close_reason = f"BUY Close: Momentum turned bearish (bull velocity {bull_velocity:+.1f}% < 0%) [No candle confirmation]"
                                    self.logger.info(f"🚨 MOMENTUM DIRECTION EXIT (FALLBACK): {close_reason}")
                                elif current_type == 'SELL' and bear_velocity < 0:
                                    should_close = True
                                    close_reason = f"SELL Close: Momentum turned bullish (bear velocity {bear_velocity:+.1f}% < 0%) [No candle confirmation]"
                                    self.logger.info(f"🚨 MOMENTUM DIRECTION EXIT (FALLBACK): {close_reason}")
                            # Phase 2 - Acceleration threshold exits WITH candle confirmation (consistent logic) - ONLY IN PROFIT
                            if not should_close and is_profitable:  # Only check if velocity exit didn't trigger AND position is profitable
                                acceleration_exit_triggered = False
                                accel_candle_confirmation = False

                                # Check acceleration exit conditions (ONLY IN PROFIT)
                                if current_type == 'BUY' and bull_accel < -10:
                                    # Check if new signal aligns with current position before triggering exit
                                    signal_aligns = signal and signal == 'BUY'
                                    if signal_aligns:
                                        self.logger.info(f"⚠️ ACCELERATION EXIT BLOCKED: New {signal} signal aligns with current {current_type} position")
                                        self.logger.info(f"   Bull acceleration: {bull_accel:+.1f}% < -10% (would trigger exit but signal reinforces position)")
                                        acceleration_exit_triggered = False
                                    else:
                                        acceleration_exit_triggered = True
                                        accel_exit_reason = f"BUY Close: Bull acceleration deceleration ({bull_accel:+.1f}% < -10%) [IN PROFIT: {profit_points:+.5f}]"
                                elif current_type == 'SELL' and bear_accel < -10:
                                    # Check if new signal aligns with current position before triggering exit
                                    signal_aligns = signal and signal == 'SELL'
                                    if signal_aligns:
                                        self.logger.info(f"⚠️ ACCELERATION EXIT BLOCKED: New {signal} signal aligns with current {current_type} position")
                                        self.logger.info(f"   Bear acceleration: {bear_accel:+.1f}% < -10% (would trigger exit but signal reinforces position)")
                                        acceleration_exit_triggered = False
                                    else:
                                        acceleration_exit_triggered = True
                                        accel_exit_reason = f"SELL Close: Bear acceleration deceleration ({bear_accel:+.1f}% < -10%) [IN PROFIT: {profit_points:+.5f}]"

                                # Apply candle confirmation to acceleration exits
                                if acceleration_exit_triggered:
                                    try:
                                        latest_data = self.get_latest_data_safe()
                                        if latest_data is not None and len(latest_data) >= 2:  # Need at least 2 candles to get closed candle
                                            # Use LAST CLOSED candle (index -2), not current forming candle (index -1)
                                            closed_candle = latest_data.iloc[-2]
                                            candle_high = closed_candle['high']
                                            candle_low = closed_candle['low']
                                            candle_close = closed_candle['close']
                                            candle_range = candle_high - candle_low

                                            if candle_range > 0:
                                                close_position = (candle_close - candle_low) / candle_range

                                                # Same candle confirmation logic as velocity exits
                                                if current_type == 'BUY' and close_position <= 0.40:
                                                    accel_candle_confirmation = True
                                                elif current_type == 'SELL' and close_position >= 0.60:
                                                    accel_candle_confirmation = True

                                                if accel_candle_confirmation:
                                                    # Instead of closing immediately, set trailing stop 10 pips from confirmation candle
                                                    confirmation_candle_data = {
                                                        'high': candle_high,
                                                        'low': candle_low,
                                                        'close': candle_close
                                                    }
                                                    # TEMPORARILY DISABLED: Acceleration-based trailing stops
                                                    # if self.set_candle_confirmation_trailing_stop(confirmation_candle_data, current_type):
                                                    #     close_reason = f"{accel_exit_reason} + Candle confirmation (close at {close_position:.1%}) - TRAILING STOP SET"
                                                    #     self.logger.info(f"🎯 ACCELERATION TRAILING STOP SET: {close_reason}")
                                                    # else:
                                                    #     # FIXED: Don't close position just because trailing stop failed
                                                    #     # Continue monitoring with existing stop loss
                                                    #     close_reason = f"{accel_exit_reason} + Candle confirmation (close at {close_position:.1%}) - TRAILING STOP FAILED - CONTINUING WITH CURRENT SL"
                                                    #     self.logger.warning(f"⚠️ ACCELERATION TRAILING STOP FAILED: {close_reason}")
                                                    #     self.logger.info(f"🔄 Position remains open with existing stop loss - will retry trailing on next analysis")

                                                    # Keep analysis active but disable trailing stop action
                                                    close_reason = f"{accel_exit_reason} + Candle confirmation (close at {close_position:.1%}) - TRAILING STOP DISABLED"
                                                    self.logger.info(f"🚫 ACCELERATION TRAILING STOP DISABLED: {close_reason}")
                                                    self.logger.info(f"🔄 Acceleration analysis active but trailing stop action temporarily disabled")
                                                else:
                                                    self.logger.info(f"⚠️ ACCELERATION EXIT BLOCKED: {accel_exit_reason}")
                                                    self.logger.info(f"   Candle confirmation: Close at {close_position:.1%} (BUY needs ≤40%, SELL needs ≥60%)")
                                            else:
                                                # No candle range - fallback to original logic
                                                should_close = True
                                                close_reason = f"{accel_exit_reason} [No candle range - fallback]"
                                                self.logger.info(f"🚨 ACCELERATION EXIT (FALLBACK): {close_reason}")
                                        else:
                                            # No candle data - fallback to original logic
                                            should_close = True
                                            close_reason = f"{accel_exit_reason} [No candle data - fallback]"
                                            self.logger.info(f"🚨 ACCELERATION EXIT (FALLBACK): {close_reason}")
                                    except Exception as e:
                                        self.logger.error(f"❌ Error in acceleration exit candle confirmation: {e}")
                                        # Fallback to original logic
                                        should_close = True
                                        close_reason = f"{accel_exit_reason} [Error - fallback]"
                                        self.logger.info(f"🚨 ACCELERATION EXIT (FALLBACK): {close_reason}")
                                else:
                                    self.logger.info(f"   No acceleration exit: All thresholds safe (position in profit)")
                            elif not should_close and not is_profitable:
                                # Log that acceleration exits are also blocked for unprofitable positions
                                if (current_type == 'BUY' and bull_accel < -10) or (current_type == 'SELL' and bear_accel < -10):
                                    self.logger.info(f"⚠️ ACCELERATION EXIT BLOCKED: Position not in profit")
                                    if current_type == 'BUY':
                                        self.logger.info(f"   Bull acceleration: {bull_accel:+.1f}% < -10% (would trigger exit if profitable)")
                                    else:
                                        self.logger.info(f"   Bear acceleration: {bear_accel:+.1f}% < -10% (would trigger exit if profitable)")
                                else:
                                    self.logger.info(f"   No acceleration exit: All thresholds safe (position not profitable)")

                        # Check for opposite signals - ENHANCED: Only update SL, no automatic closure
                        if not should_close and signal and ((current_type == 'BUY' and signal == 'SELL') or (current_type == 'SELL' and signal == 'BUY')):
                            # ENHANCED: Set current position's SL exactly where new pending order would be placed
                            try:
                                latest_data = self.get_latest_data_safe()
                                if latest_data is not None and len(latest_data) >= 2:
                                    closed_candle = latest_data.iloc[-2]  # Last closed candle
                                    pip_size = self.mt5_manager.get_pip_size(self.symbol)

                                    # Calculate where new pending order would be placed
                                    if signal == 'BUY':
                                        # BUY pending would be 1 pip above confirmation candle high
                                        new_sl_for_current_sell = closed_candle['high'] + pip_size
                                        self.logger.info(f"🔄 OPPOSITE SIGNAL ENHANCEMENT: Setting SELL position SL to {new_sl_for_current_sell:.5f} (where BUY pending would be)")
                                    else:  # signal == 'SELL'
                                        # SELL pending would be 1 pip below confirmation candle low
                                        new_sl_for_current_buy = closed_candle['low'] - pip_size
                                        self.logger.info(f"🔄 OPPOSITE SIGNAL ENHANCEMENT: Setting BUY position SL to {new_sl_for_current_buy:.5f} (where SELL pending would be)")

                                    # Modify current position's SL
                                    if 'ticket' in self.current_position:
                                        if signal == 'BUY':
                                            success = self.mt5_manager.modify_position(
                                                ticket=self.current_position['ticket'],
                                                stop_loss=new_sl_for_current_sell
                                            )
                                        else:
                                            success = self.mt5_manager.modify_position(
                                                ticket=self.current_position['ticket'],
                                                stop_loss=new_sl_for_current_buy
                                            )

                                        if success:
                                            self.logger.info(f"✅ SEAMLESS TRANSITION: Current position SL updated to match new pending entry - POSITION KEPT")
                                            self.logger.info(f"🎯 OPPOSITE SIGNAL HANDLED: SL protection set, no automatic closure")
                                            # Block new signal execution since we're keeping current position
                                            signal = None
                                        else:
                                            self.logger.warning(f"⚠️ Failed to update current position SL for seamless transition")

                            except Exception as e:
                                self.logger.error(f"❌ Error in opposite signal enhancement: {e}")

                            # REMOVED: No automatic closure for opposite signals
                            # Position is now protected by updated SL and will be stopped out if price reaches that level

                        # NEW: Check for pending regime change exits
                        if not should_close and self.pending_regime_exit and self.current_position:
                            pending_exit = self.pending_regime_exit

                            # Check if unfavorable regime persists
                            if regime == pending_exit['unfavorable_regime']:
                                # Regime still unfavorable, check candle confirmation
                                try:
                                    latest_data = self.get_latest_data_safe()
                                    if latest_data is not None and len(latest_data) > 0:
                                        candle_confirmed, candle_reason, close_position = self.regime_detector.check_candle_exit_confirmation(
                                            latest_data, pending_exit['position_type']
                                        )

                                        if candle_confirmed:
                                            # Instead of closing immediately, set trailing stop 10 pips from confirmation candle
                                            closed_candle = latest_data.iloc[-2]  # Last closed candle
                                            confirmation_candle_data = {
                                                'high': closed_candle['high'],
                                                'low': closed_candle['low'],
                                                'close': closed_candle['close']
                                            }

                                            # TEMPORARILY DISABLED: Pending regime exit-based trailing stops
                                            # if self.set_candle_confirmation_trailing_stop(confirmation_candle_data, pending_exit['position_type']):
                                            #     self.logger.info(f"🎯 PENDING REGIME EXIT TRAILING STOP SET: {pending_exit['reason']}")
                                            #     self.logger.info(f"   Candle confirmation: {candle_reason}")
                                            #     self.logger.info(f"   Stop will revert to original level if not hit within one candle")
                                            # else:
                                            #     # FIXED: Don't close position just because trailing stop failed
                                            #     # Continue monitoring with existing stop loss
                                            #     self.logger.warning(f"⚠️ PENDING REGIME EXIT TRAILING STOP FAILED: {pending_exit['reason']}")
                                            #     self.logger.info(f"   Candle confirmation: {candle_reason}")
                                            #     self.logger.info(f"🔄 Position remains open with existing stop loss - will retry trailing on next analysis")

                                            # Keep analysis active but disable trailing stop action
                                            self.logger.info(f"🚫 PENDING REGIME EXIT TRAILING STOP DISABLED: {pending_exit['reason']}")
                                            self.logger.info(f"   Candle confirmation: {candle_reason}")
                                            self.logger.info(f"🔄 Pending regime exit analysis active but trailing stop action temporarily disabled")

                                            self.pending_regime_exit = None  # Clear pending exit
                                        else:
                                            self.logger.info(f"🔍 PENDING REGIME EXIT: Still waiting for candle confirmation")
                                            self.logger.info(f"   Unfavorable regime persists: {regime}")
                                            self.logger.info(f"   Candle status: {candle_reason}")
                                except Exception as e:
                                    self.logger.error(f"❌ Error checking pending regime exit: {e}")
                            else:
                                # Regime changed back to favorable, clear pending exit
                                self.logger.info(f"✅ PENDING REGIME EXIT CLEARED: Regime changed back to {regime} (favorable)")
                                self.pending_regime_exit = None

                        # NEW: Check if new signal aligns with current position - if so, don't close
                        if should_close and signal and "Opposite signal" not in close_reason:
                            # Check if new signal aligns with current position
                            signal_aligns = (current_type == 'BUY' and signal == 'BUY') or (current_type == 'SELL' and signal == 'SELL')
                            if signal_aligns:
                                should_close = False
                                self.logger.info(f"🔄 KEEPING POSITION: New {signal} signal aligns with current {current_type} position")
                                self.logger.info(f"   Original close reason: {close_reason}")
                                self.logger.info(f"   Decision: Keep position open since new signal confirms direction")

                        # Close position if any condition is met
                        if should_close:
                            self.logger.info(f"🔄 CLOSING POSITION: {close_reason}")
                            if self.close_current_position(close_reason):
                                has_position = False
                                # STOP REAL-TIME TRAILING MONITOR
                                self.stop_real_time_trailing_monitor()
                                self.current_position = None
                                self.trailing_stop_data = None

                    # DISABLED: Main loop trailing - Background real-time monitor handles all trailing
                    # The background trailing monitor runs every 10 seconds independently
                    # and handles all trailing stops and partial closes
                    if has_position and self.current_position:
                        self.logger.info(f"📊 POSITION STATUS: {self.current_position['type']} position active - Background trailing monitor handling updates")
                        # Ensure background monitor is running
                        if not (self.trailing_monitor_thread and self.trailing_monitor_thread.is_alive()):
                            self.logger.warning("⚠️ Background trailing monitor not running - restarting")
                            self.start_real_time_trailing_monitor()

                    # CRITICAL FIX: Check and revert candle confirmation stops EVERY candle close
                    # This must happen BEFORE signal processing and EVERY iteration
                    if has_position and self.current_position:
                        if self.candle_confirmation_stop:
                            self.logger.info(f"🔍 CHECKING CANDLE CONFIRMATION REVERT: Active confirmation stop detected")
                        revert_result = self.check_candle_confirmation_stop_revert()
                        if revert_result:
                            self.logger.info(f"✅ CANDLE CONFIRMATION REVERT: Successfully processed")

                    # Check and remove expired pending orders
                    self.check_and_remove_expired_pending_orders()

                    # Check if any pending orders were filled and update position tracking
                    if not has_position:  # Only check if we don't already have a position
                        self.check_pending_orders_filled()

                    if signal is not None or regime:  # Show info even if no signal
                        # Check for regime change
                        if regime:
                            regime_changed = self.check_regime_change(regime)

                        self.logger.info(f"🎯 REGIME ANALYSIS: {details}{position_info}")

                        # NEW: Log enhanced analysis every iteration for detailed monitoring
                        if regime_details and candle_strength:
                            # Pass QQE values for logging
                            qqe_signal_val = qqe_analysis.get('qqe_signal', 0) if qqe_analysis else 0
                            qqe_strength_val = qqe_analysis.get('qqe_signal_strength', 0.0) if qqe_analysis else 0.0
                            self.log_enhanced_regime_analysis(regime_details, candle_strength, qqe_analysis, features_df, atr_value, qqe_signal_val, qqe_strength_val)

                        # Execute trade if signal is valid and confidence is high enough
                        if signal and confidence >= self.min_confidence:
                            # Combine regime half_size with pattern volume factor
                            pattern_volume_factor = candle_pattern_analysis.get('volume_factor', 1.0) if candle_pattern_analysis else 1.0
                            combined_half_size = half_size or (pattern_volume_factor < 1.0)  # Half size if either regime or pattern requires it
                            combined_swing_factor = swing_size_factor * pattern_volume_factor  # Multiply swing factor by pattern volume factor

                            # NEW: WICK INTERACTION ANALYSIS - Check if trading at extreme wick area
                            wick_volume_factor = 1.0  # Default to full size
                            wick_should_halve = False
                            wick_reason = "No wick analysis"

                            try:
                                if features_df is not None and atr_value is not None:
                                    # Get current market price for wick analysis
                                    tick_info = self.mt5_manager.get_symbol_info_tick(self.symbol)
                                    if tick_info:
                                        current_market_price = tick_info['ask'] if signal == 'BUY' else tick_info['bid']

                                        # Get swing points for wick analysis
                                        swing_points = self.find_recent_swing_points(features_df)

                                        # Analyze wick interaction
                                        wick_should_halve, wick_reason, wick_volume_factor = self.analyze_wick_interaction(
                                            signal, swing_points, features_df, atr_value, current_market_price
                                        )

                                        self.logger.info(f"🔍 WICK INTERACTION ANALYSIS: {wick_reason}")

                                        # Check if we should skip the trade (volume already minimum and needs halving)
                                        if wick_should_halve and wick_volume_factor == 0.5:
                                            # Get signal candle data for accurate position sizing
                                            signal_candle_data = None
                                            if len(features_df) >= 2:
                                                signal_candle = features_df.iloc[-2]  # Last closed candle (signal candle)
                                                signal_candle_data = {
                                                    'high': signal_candle['high'],
                                                    'low': signal_candle['low'],
                                                    'close': signal_candle['close']
                                                }

                                            # Calculate what the final lot size would be
                                            test_lot_size = self.calculate_position_size(
                                                balance, current_market_price, atr_value, combined_half_size, combined_swing_factor, signal, signal_candle_data
                                            )
                                            final_test_lot_size = test_lot_size * wick_volume_factor

                                            if final_test_lot_size < 0.01:  # Below minimum lot size
                                                self.logger.warning(f"⚠️ WICK EXTREME TRADE SKIPPED: Final lot size {final_test_lot_size:.3f} < 0.01 minimum")
                                                self.logger.info(f"   Base lot size: {test_lot_size:.3f}")
                                                self.logger.info(f"   Wick factor: {wick_volume_factor:.1f}x")
                                                self.logger.info(f"   Reason: {wick_reason}")
                                                # Skip this trade
                                                signal = None
                                                logic = f"WICK_EXTREME_SKIP: {wick_reason}"

                            except Exception as e:
                                self.logger.error(f"❌ Error in wick interaction analysis: {e}")
                                wick_volume_factor = 1.0
                                wick_reason = f"Wick analysis error: {e}"

                            # Apply wick volume factor to combined swing factor
                            final_combined_swing_factor = combined_swing_factor * wick_volume_factor

                            volume_reason = ""
                            if pattern_volume_factor < 1.0:
                                volume_reason = f" | PatternVol:{pattern_volume_factor:.1f}x"
                            if half_size:
                                volume_reason += " | RegimeHalf"
                            if combined_swing_factor < 1.0:
                                volume_reason += f" | SwingFactor:{combined_swing_factor:.2f}x"
                            if wick_volume_factor < 1.0:
                                volume_reason += f" | WickExtreme:{wick_volume_factor:.1f}x"

                            if volume_reason:
                                self.logger.info(f"📊 VOLUME SCALING: {volume_reason.strip(' |')}")

                            # Execute trade only if signal is still valid (not skipped due to wick extreme)
                            if signal:
                                self.execute_trade(signal, confidence, balance, atr_value, regime, logic, combined_half_size, final_combined_swing_factor, features_df)
                        elif signal is None:
                            self.logger.info(f"⚠️ No trade - {logic}")
                        else:
                            self.logger.info(f"⚠️ Confidence too low ({confidence:.3f} < {self.min_confidence}) - No trade")
                    else:
                        self.logger.warning(f"⚠️ Failed to get prediction: {details}{position_info}")
                    
                    last_prediction_time = current_time

                # After analysis, wait for next exact candle boundary to prevent timing drift
                wait_seconds, next_candle_close = self.wait_for_candle_close()
                self.logger.info(f"⏰ Analysis complete. Next candle closes at: {next_candle_close.strftime('%H:%M:%S')}")
                self.logger.info(f"⏰ Waiting {wait_seconds:.1f} seconds for next candle close...")
                time.sleep(min(wait_seconds, 30))  # Sleep in chunks of max 30 seconds
                
            except KeyboardInterrupt:
                self.logger.info("🛑 Trading stopped by user")
                break
            except Exception as e:
                self.logger.error(f"❌ Error in trading loop: {e}")
                time.sleep(60)
    
    def start_trading(self):
        """Start the fixed live trading system"""
        symbol_name = self.SYMBOL_SPECS[self.symbol]["name"]
        self.logger.info(f"🚀 Starting Fixed {symbol_name} ({self.symbol}) Live Trading System...")

        # Load model
        if not self.load_model():
            self.logger.error("❌ Failed to load model")
            return False

        # Connect to MT5
        if not self.mt5_manager.connect():
            self.logger.error("❌ Failed to connect to MT5")
            return False

        account_info = self.mt5_manager.get_account_info()
        if account_info:
            self.logger.info(f"✅ Connected - Account: {account_info['login']}, Balance: ${account_info['balance']:.2f}")

        # Start trading
        self.is_running = True

        try:
            self.trading_loop()
        except KeyboardInterrupt:
            self.logger.info("🛑 Trading stopped by user")
        finally:
            self.is_running = False
            self.mt5_manager.disconnect()
            self.logger.info("✅ Trading system shutdown complete")

        return True
    
    def stop_trading(self):
        """Stop the trading system"""
        self.is_running = False

    # is_significant_candle_backup method removed per user request

def main():
    """Main function with multi-symbol support"""
    import sys

    # Default symbol
    symbol = "XAUUSD!"

    # Check for command line argument
    if len(sys.argv) > 1:
        symbol = sys.argv[1].upper()
        # Ensure proper format
        if symbol == "BTCUSD":
            symbol = "BTCUSD"
        elif symbol == "EURUSD":
            symbol = "EURUSD!"
        elif symbol == "XAUUSD":
            symbol = "XAUUSD!"

    # Create trader with specified symbol
    trader = FixedLiveTrader(symbol=symbol)
    symbol_name = trader.SYMBOL_SPECS[symbol]["name"]

    # REMOVED: Verbose startup banner logging

    # Start trading
    try:
        trader.start_trading()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down trading system...")
        trader.stop_trading()

if __name__ == "__main__":
    main()
