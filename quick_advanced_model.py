#!/usr/bin/env python3
"""
Quick Advanced Model - Focus on Random Forest and XGBoost
Skip LSTM for now to get faster results
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_selection import mutual_info_classif
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from data_manager import DataManager
from feature_engineering import FeatureEngineer

class QuickAdvancedModel:
    def __init__(self):
        self.data_manager = DataManager()
        self.feature_engineer = FeatureEngineer()
        
    def load_and_prepare_data(self):
        """Load and prepare data"""
        print("🔄 Loading and preparing data...")
        
        df = self.data_manager.load_historical_data()
        recent_data = df.tail(50000).copy()  # Use 50k records for speed
        print(f"📊 Using {len(recent_data)} recent records")
        
        features_df = self.feature_engineer.create_technical_indicators(recent_data)
        return features_df
    
    def create_better_targets(self, df):
        """Create better balanced targets"""
        print("🎯 Creating better targets...")
        
        # Calculate 3-period forward return
        df['future_return'] = (df['close'].shift(-3) - df['close']) / df['close']
        
        # Use percentiles for balanced classes
        up_threshold = df['future_return'].quantile(0.65)    # Top 35%
        down_threshold = df['future_return'].quantile(0.35)  # Bottom 35%
        
        print(f"📊 Thresholds: Up > {up_threshold:.4f}, Down < {down_threshold:.4f}")
        
        # Create binary target
        df['target'] = np.where(df['future_return'] > up_threshold, 1,
                               np.where(df['future_return'] < down_threshold, 0, np.nan))
        
        # Remove middle 30% (unclear signals)
        clear_df = df.dropna(subset=['target']).copy()
        clear_df['target'] = clear_df['target'].astype(int)
        
        print(f"📊 Target distribution:")
        print(f"   Total samples: {len(clear_df)}")
        print(f"   Up: {sum(clear_df['target'] == 1)} ({sum(clear_df['target'] == 1)/len(clear_df)*100:.1f}%)")
        print(f"   Down: {sum(clear_df['target'] == 0)} ({sum(clear_df['target'] == 0)/len(clear_df)*100:.1f}%)")
        
        return clear_df
    
    def select_features(self, X, y, top_k=15):
        """Advanced feature selection"""
        print(f"🔍 Selecting top {top_k} features...")
        
        # Remove high-NaN features
        nan_ratios = X.isnull().sum() / len(X)
        valid_features = nan_ratios[nan_ratios < 0.1].index.tolist()
        X_clean = X[valid_features].fillna(X[valid_features].median())
        
        # Mutual information feature selection
        mi_scores = mutual_info_classif(X_clean, y, random_state=42)
        feature_scores = pd.Series(mi_scores, index=X_clean.columns).sort_values(ascending=False)
        
        top_features = feature_scores.head(top_k).index.tolist()
        
        print(f"🎯 Top {len(top_features)} features:")
        for i, (feature, score) in enumerate(feature_scores.head(10).items()):
            print(f"   {i+1}. {feature}: {score:.4f}")
        
        return X_clean[top_features], top_features, feature_scores
    
    def walk_forward_split(self, X, y, n_splits=4):
        """Time series walk-forward validation"""
        print(f"📈 Creating {n_splits} walk-forward splits...")
        
        n_samples = len(X)
        test_size = n_samples // n_splits
        
        splits = []
        for i in range(n_splits):
            train_end = n_samples - (n_splits - i) * test_size
            test_start = train_end
            test_end = min(test_start + test_size, n_samples)
            
            if train_end > 1000:  # Minimum training size
                splits.append({
                    'train_idx': list(range(0, train_end)),
                    'test_idx': list(range(test_start, test_end))
                })
        
        return splits
    
    def train_models(self, X, y, splits):
        """Train Random Forest and XGBoost models"""
        print("🤖 Training models...")
        
        results = {}
        
        # Random Forest
        print("   Training Random Forest...")
        rf_scores = []
        rf_predictions = []
        
        for i, split in enumerate(splits):
            X_train, X_test = X.iloc[split['train_idx']], X.iloc[split['test_idx']]
            y_train, y_test = y.iloc[split['train_idx']], y.iloc[split['test_idx']]
            
            rf = RandomForestClassifier(
                n_estimators=200,
                max_depth=12,
                min_samples_split=20,
                min_samples_leaf=10,
                random_state=42,
                n_jobs=-1,
                class_weight='balanced'
            )
            
            rf.fit(X_train, y_train)
            y_pred_proba = rf.predict_proba(X_test)[:, 1]
            score = roc_auc_score(y_test, y_pred_proba)
            rf_scores.append(score)
            
            rf_predictions.extend(list(zip(y_test.values, y_pred_proba)))
            
            print(f"     Split {i+1}: AUC = {score:.4f}")
        
        results['RandomForest'] = {
            'scores': rf_scores,
            'mean_auc': np.mean(rf_scores),
            'std_auc': np.std(rf_scores),
            'predictions': rf_predictions
        }
        
        # XGBoost
        print("   Training XGBoost...")
        xgb_scores = []
        xgb_predictions = []
        
        for i, split in enumerate(splits):
            X_train, X_test = X.iloc[split['train_idx']], X.iloc[split['test_idx']]
            y_train, y_test = y.iloc[split['train_idx']], y.iloc[split['test_idx']]
            
            # Calculate class balance
            scale_pos_weight = sum(y_train == 0) / sum(y_train == 1)
            
            xgb_model = xgb.XGBClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                scale_pos_weight=scale_pos_weight,
                random_state=42,
                n_jobs=-1,
                eval_metric='logloss'
            )
            
            xgb_model.fit(X_train, y_train)
            y_pred_proba = xgb_model.predict_proba(X_test)[:, 1]
            score = roc_auc_score(y_test, y_pred_proba)
            xgb_scores.append(score)
            
            xgb_predictions.extend(list(zip(y_test.values, y_pred_proba)))
            
            print(f"     Split {i+1}: AUC = {score:.4f}")
        
        results['XGBoost'] = {
            'scores': xgb_scores,
            'mean_auc': np.mean(xgb_scores),
            'std_auc': np.std(xgb_scores),
            'predictions': xgb_predictions
        }
        
        return results
    
    def evaluate_models(self, results):
        """Evaluate and compare models"""
        print("\n📊 Model Evaluation Results:")
        print("=" * 50)
        
        best_model = None
        best_auc = 0
        
        for model_name, result in results.items():
            mean_auc = result['mean_auc']
            std_auc = result['std_auc']
            
            print(f"\n{model_name}:")
            print(f"  Mean AUC: {mean_auc:.4f} ± {std_auc:.4f}")
            print(f"  Individual scores: {[f'{s:.3f}' for s in result['scores']]}")
            
            if mean_auc > best_auc:
                best_auc = mean_auc
                best_model = model_name
        
        print(f"\n🏆 Best Model: {best_model} (AUC: {best_auc:.4f})")
        
        # Create evaluation plots
        self.create_evaluation_plots(results, best_model)
        
        return best_model, best_auc
    
    def create_evaluation_plots(self, results, best_model):
        """Create evaluation visualizations"""
        print("📊 Creating evaluation plots...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # Plot 1: AUC Comparison
        models = list(results.keys())
        mean_aucs = [results[model]['mean_auc'] for model in models]
        std_aucs = [results[model]['std_auc'] for model in models]
        
        bars = axes[0,0].bar(models, mean_aucs, yerr=std_aucs, capsize=5, 
                            color=['lightblue', 'lightcoral'])
        axes[0,0].set_title('Model AUC Comparison')
        axes[0,0].set_ylabel('AUC Score')
        axes[0,0].set_ylim(0.5, max(mean_aucs) + 0.1)
        
        # Add value labels
        for bar, auc in zip(bars, mean_aucs):
            axes[0,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                          f'{auc:.3f}', ha='center', va='bottom')
        
        # Plot 2: ROC Curves
        for model_name, result in results.items():
            y_true = [pred[0] for pred in result['predictions']]
            y_scores = [pred[1] for pred in result['predictions']]
            
            fpr, tpr, _ = roc_curve(y_true, y_scores)
            auc_score = roc_auc_score(y_true, y_scores)
            
            axes[0,1].plot(fpr, tpr, label=f'{model_name} (AUC={auc_score:.3f})', linewidth=2)
        
        axes[0,1].plot([0, 1], [0, 1], 'k--', alpha=0.5)
        axes[0,1].set_xlabel('False Positive Rate')
        axes[0,1].set_ylabel('True Positive Rate')
        axes[0,1].set_title('ROC Curves')
        axes[0,1].legend()
        axes[0,1].grid(alpha=0.3)
        
        # Plot 3: Prediction Distribution (Best Model)
        best_predictions = results[best_model]['predictions']
        y_scores = [pred[1] for pred in best_predictions]
        
        axes[1,0].hist(y_scores, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[1,0].axvline(x=0.5, color='red', linestyle='--', label='Decision Threshold')
        axes[1,0].set_xlabel('Predicted Probability')
        axes[1,0].set_ylabel('Frequency')
        axes[1,0].set_title(f'{best_model} - Prediction Distribution')
        axes[1,0].legend()
        
        # Plot 4: Confusion Matrix (Best Model)
        y_true = [pred[0] for pred in best_predictions]
        y_pred = [1 if pred[1] > 0.5 else 0 for pred in best_predictions]
        
        cm = confusion_matrix(y_true, y_pred)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[1,1])
        axes[1,1].set_xlabel('Predicted')
        axes[1,1].set_ylabel('Actual')
        axes[1,1].set_title(f'{best_model} - Confusion Matrix')
        
        plt.tight_layout()
        
        # Save plots
        os.makedirs('reports_v2', exist_ok=True)
        plt.savefig('reports_v2/advanced_model_evaluation.png', dpi=300, bbox_inches='tight')
        print("💾 Evaluation plots saved to reports_v2/advanced_model_evaluation.png")
        
        return fig
    
    def trading_simulation(self, results, best_model):
        """Realistic trading simulation"""
        print("💰 Running trading simulation...")
        
        predictions = results[best_model]['predictions']
        
        # Trading parameters
        initial_balance = 10000
        balance = initial_balance
        position_size_pct = 0.04  # 4% risk per trade
        transaction_cost = 0.0006  # 0.06% total cost (spread + commission + slippage)
        
        trades = []
        
        for y_true, y_pred_proba in predictions:
            confidence = abs(y_pred_proba - 0.5) * 2  # 0-1 confidence
            
            # Only trade with high confidence
            if confidence > 0.3:  # 30% minimum confidence
                trade_size = balance * position_size_pct
                
                # Simulate trade outcome
                if y_pred_proba > 0.5:  # Buy signal
                    if y_true == 1:  # Correct prediction
                        profit = trade_size * 0.015 - trade_size * transaction_cost  # 1.5% profit minus costs
                        balance += profit
                        trades.append({'type': 'BUY', 'result': 'WIN', 'profit': profit, 'confidence': confidence})
                    else:  # Wrong prediction
                        loss = trade_size * 0.015 + trade_size * transaction_cost  # 1.5% loss plus costs
                        balance -= loss
                        trades.append({'type': 'BUY', 'result': 'LOSS', 'profit': -loss, 'confidence': confidence})
                else:  # Sell signal
                    if y_true == 0:  # Correct prediction
                        profit = trade_size * 0.015 - trade_size * transaction_cost
                        balance += profit
                        trades.append({'type': 'SELL', 'result': 'WIN', 'profit': profit, 'confidence': confidence})
                    else:  # Wrong prediction
                        loss = trade_size * 0.015 + trade_size * transaction_cost
                        balance -= loss
                        trades.append({'type': 'SELL', 'result': 'LOSS', 'profit': -loss, 'confidence': confidence})
        
        # Calculate metrics
        total_return = (balance - initial_balance) / initial_balance * 100
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['result'] == 'WIN'])
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        
        avg_win = np.mean([t['profit'] for t in trades if t['result'] == 'WIN']) if winning_trades > 0 else 0
        avg_loss = np.mean([abs(t['profit']) for t in trades if t['result'] == 'LOSS']) if (total_trades - winning_trades) > 0 else 0
        
        print(f"\n💰 Trading Simulation Results:")
        print(f"   Model: {best_model}")
        print(f"   Initial Balance: ${initial_balance:,.2f}")
        print(f"   Final Balance: ${balance:,.2f}")
        print(f"   Total Return: {total_return:.2f}%")
        print(f"   Total Trades: {total_trades}")
        print(f"   Winning Trades: {winning_trades}")
        print(f"   Win Rate: {win_rate:.2f}%")
        print(f"   Average Win: ${avg_win:.2f}")
        print(f"   Average Loss: ${avg_loss:.2f}")
        print(f"   Transaction Cost: {transaction_cost*100:.3f}% per trade")
        
        return {
            'initial_balance': initial_balance,
            'final_balance': balance,
            'total_return': total_return,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss
        }

def main():
    """Main function"""
    print("🚀 Quick Advanced Model Training...")
    
    model = QuickAdvancedModel()
    
    # Load data
    df = model.load_and_prepare_data()
    
    # Create better targets
    df_clean = model.create_better_targets(df)
    
    # Feature selection
    feature_columns = [col for col in df_clean.columns 
                      if col not in ['target', 'future_return', 'close', 'open', 'high', 'low', 'volume']]
    X = df_clean[feature_columns]
    y = df_clean['target']
    
    X_selected, top_features, feature_scores = model.select_features(X, y, top_k=15)
    
    # Walk-forward validation
    splits = model.walk_forward_split(X_selected, y, n_splits=4)
    
    # Train models
    results = model.train_models(X_selected, y, splits)
    
    # Evaluate models
    best_model, best_auc = model.evaluate_models(results)
    
    # Trading simulation
    trading_results = model.trading_simulation(results, best_model)
    
    # Final assessment
    print(f"\n🎯 FINAL ASSESSMENT:")
    print("=" * 50)
    
    if best_auc > 0.6:
        print("✅ GOOD MODEL: AUC > 0.60 - Has predictive power")
    elif best_auc > 0.55:
        print("⚠️  MARGINAL MODEL: AUC 0.55-0.60 - Weak but usable")
    else:
        print("❌ POOR MODEL: AUC < 0.55 - Not suitable for trading")
    
    if trading_results['total_return'] > 5 and trading_results['win_rate'] > 55:
        print("✅ PROFITABLE: Good returns with decent win rate")
    elif trading_results['total_return'] > 0:
        print("⚠️  MARGINAL: Small positive returns")
    else:
        print("❌ UNPROFITABLE: Negative returns after costs")
    
    print("\n✅ Quick advanced model training completed!")

if __name__ == "__main__":
    main()
