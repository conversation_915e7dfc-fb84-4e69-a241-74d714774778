#!/usr/bin/env python3

import sys
sys.path.append('.')
from enhanced_regime_detector import EconomicCalendarParser
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')

def test_parser():
    print("Testing Economic Calendar Parser...")
    
    # Test the parser
    parser = EconomicCalendarParser('economic_calendar.txt')
    success = parser.load_calendar()
    print(f'Parser success: {success}')
    print(f'Total events loaded: {len(parser.events)}')
    
    # Show first few events
    print("\nFirst 10 events:")
    for i, event in enumerate(parser.events[:10]):
        print(f'{i+1}. {event["time"]} - {event["currency"]} {event["event"]} ({event["impact"]})')
    
    # Get next event
    next_event = parser.get_next_event()
    if next_event:
        print(f'\nNext event: {next_event["currency"]} {next_event["event"]} at {next_event["time"]} ({next_event["impact"]})')
        
        # Calculate time until event
        from datetime import datetime
        current_time = datetime.now(parser.amsterdam_tz)
        time_until = next_event['time'] - current_time
        hours = int(time_until.total_seconds() // 3600)
        minutes = int((time_until.total_seconds() % 3600) // 60)
        print(f'Time until event: {hours}h {minutes}m')
    else:
        print('\nNo next event found')
    
    # Show events for today
    from datetime import datetime, date
    today = date.today()
    today_events = [e for e in parser.events if e['time'].date() == today]
    print(f'\nEvents for today ({today}): {len(today_events)}')
    for event in today_events:
        print(f'  {event["time"].strftime("%H:%M")} - {event["currency"]} {event["event"]} ({event["impact"]})')

if __name__ == "__main__":
    test_parser()
