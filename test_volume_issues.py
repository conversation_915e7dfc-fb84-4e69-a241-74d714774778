#!/usr/bin/env python3
"""
Test Volume Issues Analysis
Demonstrate and verify fixes for volume trend and divergence calculation
"""

import sys
import pandas as pd
import numpy as np
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_volume_trend_test_data():
    """Create test data to demonstrate volume trend issue"""
    logger.info("📊 Creating volume trend test data...")
    
    # Create scenario where volume is clearly increasing but trend shows -1
    data = []
    volumes = [
        # Periods 1-6: Lower volume
        100, 150, 200, 250, 300, 350,
        # Periods 7-12: Higher volume (clearly increasing trend)
        400, 500, 600, 700, 800, 900
    ]
    
    for i, volume in enumerate(volumes):
        price = 2000 + i * 0.1  # Slight price increase
        
        data.append({
            'datetime': pd.Timestamp('2024-01-01') + pd.Timedelta(minutes=5*i),
            'open': price,
            'high': price + 0.1,
            'low': price - 0.1,
            'close': price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('datetime', inplace=True)
    
    logger.info(f"✅ Created {len(df)} periods")
    logger.info(f"   Volume pattern: {volumes[:6]} → {volumes[6:]}")
    logger.info(f"   Expected: Volume trend should be +1 (increasing) in later periods")
    
    return df

def create_divergence_test_data():
    """Create test data to demonstrate divergence calculation"""
    logger.info("📊 Creating divergence test data...")
    
    # Create BEARISH divergence scenario: Price UP, Volume DOWN
    data = []
    
    for i in range(15):
        if i < 10:
            # First 10 periods: Lower prices, Higher volume
            price = 2000 + i * 0.05  # Gradual price increase
            volume = 1000 + i * 50   # Volume increasing
        else:
            # Last 5 periods: Higher prices, Lower volume (BEARISH DIVERGENCE)
            price = 2000.5 + (i-10) * 0.1  # Prices continue up
            volume = 1450 - (i-10) * 200   # Volume decreasing more aggressively
        
        data.append({
            'datetime': pd.Timestamp('2024-01-01') + pd.Timedelta(minutes=5*i),
            'open': price,
            'high': price + 0.05,
            'low': price - 0.05,
            'close': price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('datetime', inplace=True)
    
    logger.info(f"✅ Created {len(df)} periods")
    logger.info(f"   Price: {df.iloc[0]['close']:.2f} → {df.iloc[-1]['close']:.2f} (UP)")
    logger.info(f"   Volume: {df.iloc[0]['volume']} → {df.iloc[-1]['volume']} (DOWN)")
    logger.info(f"   Expected: BEARISH_DIV (price up, volume down)")
    
    return df

def test_volume_trend_logic():
    """Test volume trend calculation logic"""
    logger.info("\n🔧 TESTING VOLUME TREND LOGIC...")
    
    try:
        from qqe_indicator import QQEIndicator
        
        # Create QQE indicator
        qqe = QQEIndicator()
        
        # Get test data
        df = create_volume_trend_test_data()
        
        # Calculate volume indicators
        df_with_volume = qqe.calculate_volume_indicators(df)
        
        logger.info(f"\n   📊 VOLUME TREND ANALYSIS:")
        
        # Show last few periods
        for i in range(-4, 0):  # Last 4 periods
            period = len(df) + i
            volume = df.iloc[i]['volume']
            volume_trend = df_with_volume.iloc[i]['volume_trend']
            
            # Calculate 3-period averages for explanation
            if period >= 3:
                recent_avg = df.iloc[i-2:i+1]['volume'].mean()  # Last 3 periods
                if period >= 6:
                    previous_avg = df.iloc[i-5:i-2]['volume'].mean()  # Previous 3 periods
                    logger.info(f"      Period {period}: Volume={volume}, Trend={volume_trend:+d}")
                    logger.info(f"        Recent avg (3p): {recent_avg:.1f}, Previous avg (3p): {previous_avg:.1f}")
                else:
                    logger.info(f"      Period {period}: Volume={volume}, Trend={volume_trend:+d} (insufficient data for comparison)")
        
        # Check if trend makes sense
        last_trend = df_with_volume.iloc[-1]['volume_trend']
        last_volume = df.iloc[-1]['volume']
        first_volume = df.iloc[0]['volume']
        
        logger.info(f"\n   📊 TREND ANALYSIS:")
        logger.info(f"      First volume: {first_volume}")
        logger.info(f"      Last volume: {last_volume}")
        logger.info(f"      Overall change: {((last_volume/first_volume-1)*100):+.1f}%")
        logger.info(f"      Final trend: {last_trend:+d} ({'increasing' if last_trend > 0 else 'decreasing'})")
        
        if last_volume > first_volume and last_trend > 0:
            logger.info("      ✅ CORRECT: Volume increased and trend is positive")
            return True
        elif last_volume < first_volume and last_trend < 0:
            logger.info("      ✅ CORRECT: Volume decreased and trend is negative")
            return True
        else:
            logger.error(f"      ❌ ISSUE: Volume trend ({last_trend:+d}) doesn't match actual change ({((last_volume/first_volume-1)*100):+.1f}%)")
            return False
        
    except Exception as e:
        logger.error(f"❌ Volume Trend Test: FAILED - {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

def test_divergence_calculation():
    """Test divergence calculation logic"""
    logger.info("\n🔧 TESTING DIVERGENCE CALCULATION...")
    
    try:
        from qqe_indicator import QQEIndicator
        
        # Create QQE indicator
        qqe = QQEIndicator(volume_divergence_lookback=10)
        
        # Get test data
        df = create_divergence_test_data()
        
        # Calculate QQE with divergence
        df_with_qqe = qqe.calculate_qqe_bands(df)
        
        logger.info(f"\n   📊 DIVERGENCE ANALYSIS:")
        
        # Show divergence details
        last_row = df_with_qqe.iloc[-1]
        
        price_start = df.iloc[0]['close']
        price_end = df.iloc[-1]['close']
        price_direction = last_row.get('price_direction', 0)
        
        volume_start = df.iloc[0]['volume']
        volume_end = df.iloc[-1]['volume']
        volume_direction = last_row.get('volume_direction', 0)
        
        divergence_type = last_row.get('divergence_type', 'NONE')
        
        logger.info(f"      Price: {price_start:.2f} → {price_end:.2f} (direction: {int(price_direction):+d})")
        logger.info(f"      Volume: {volume_start} → {volume_end} (direction: {int(volume_direction):+d})")
        logger.info(f"      Divergence Type: {divergence_type}")
        
        # Verify divergence logic
        price_up = price_end > price_start
        volume_down = volume_end < volume_start
        
        logger.info(f"\n   📊 DIVERGENCE VERIFICATION:")
        logger.info(f"      Price trend: {'UP' if price_up else 'DOWN'}")
        logger.info(f"      Volume trend: {'UP' if not volume_down else 'DOWN'}")
        logger.info(f"      Expected divergence: {'BEARISH_DIV' if price_up and volume_down else 'NONE'}")
        logger.info(f"      Actual divergence: {divergence_type}")
        
        if price_up and volume_down and divergence_type == 'BEARISH_DIV':
            logger.info("      ✅ CORRECT: BEARISH_DIV detected (price up, volume down)")
            return True
        elif not (price_up and volume_down) and divergence_type == 'NONE':
            logger.info("      ✅ CORRECT: No divergence detected")
            return True
        else:
            logger.error(f"      ❌ ISSUE: Expected BEARISH_DIV but got {divergence_type}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Divergence Test: FAILED - {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

def explain_divergence_concept():
    """Explain how divergence calculation works"""
    logger.info("\n📚 DIVERGENCE CALCULATION EXPLANATION:")
    logger.info("=" * 60)
    
    logger.info("🔍 WHAT IS VOLUME DIVERGENCE?")
    logger.info("   Volume divergence occurs when price and volume move in opposite directions")
    logger.info("   over a specified lookback period (default: 10 periods)")
    
    logger.info("\n📊 HOW IT'S CALCULATED:")
    logger.info("   1. Price Direction: Compare current close vs close 10 periods ago")
    logger.info("      - If close_now > close_10_ago: Price Direction = +1 (UP)")
    logger.info("      - If close_now < close_10_ago: Price Direction = -1 (DOWN)")
    
    logger.info("\n   2. Volume Direction: Compare current volume SMA vs volume SMA 10 periods ago")
    logger.info("      - If volume_sma_now > volume_sma_10_ago: Volume Direction = +1 (UP)")
    logger.info("      - If volume_sma_now < volume_sma_10_ago: Volume Direction = -1 (DOWN)")
    
    logger.info("\n   3. Divergence Detection:")
    logger.info("      - BEARISH_DIV: Price Direction = +1, Volume Direction = -1")
    logger.info("        (Prices rising but volume declining - potential reversal)")
    logger.info("      - BULLISH_DIV: Price Direction = -1, Volume Direction = +1")
    logger.info("        (Prices falling but volume increasing - potential reversal)")
    logger.info("      - NONE: Price and Volume directions agree")
    
    logger.info("\n🎯 WHY THIS MATTERS FOR TRADING:")
    logger.info("   - BEARISH_DIV: Rising prices with declining volume = weak uptrend")
    logger.info("   - BULLISH_DIV: Falling prices with increasing volume = potential bottom")
    logger.info("   - Uses TICK VOLUME (number of price changes) as proxy for real volume")
    logger.info("   - Based on CLOSE PRICES vs VOLUME SMA, not high/low ranges")

def main():
    """Run volume issues analysis"""
    logger.info("🧪 TESTING VOLUME ISSUES ANALYSIS")
    logger.info("=" * 70)
    
    # First explain the concepts
    explain_divergence_concept()
    
    tests = [
        ("Volume Trend Logic", test_volume_trend_logic),
        ("Divergence Calculation", test_divergence_calculation)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            logger.error(f"❌ {test_name}: FAILED with exception - {e}")
    
    logger.info("\n" + "=" * 70)
    logger.info("🏁 VOLUME ISSUES TEST SUMMARY")
    logger.info(f"   ✅ Passed: {passed}")
    logger.info(f"   ❌ Failed: {failed}")
    logger.info(f"   📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
