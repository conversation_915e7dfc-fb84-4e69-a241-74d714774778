#!/usr/bin/env python3
"""
Comprehensive Market Regime Analysis Report
Uses Enhanced Regime Detector to provide detailed analysis of all market factors
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
import pytz
import logging
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')

from enhanced_regime_detector import EnhancedRegimeDetector
from mt5_integration import MT5Manager

def format_score_bar(score, max_score, width=20):
    """Create a visual score bar"""
    filled = int((score / max_score) * width)
    bar = "█" * filled + "░" * (width - filled)
    percentage = (score / max_score) * 100
    return f"{bar} {score:2}/{max_score} ({percentage:4.1f}%)"

def format_regime_confidence(regime, confidence):
    """Format regime with confidence and color coding"""
    if "STRONG" in regime:
        icon = "🔥"
    elif "TRANSITIONAL" in regime:
        icon = "⚠️"
    else:
        icon = "📊"
    
    return f"{icon} {regime} ({confidence:.1f}%)"

def analyze_market_regime():
    """Run comprehensive market regime analysis"""
    print("🎯 COMPREHENSIVE MARKET REGIME ANALYSIS")
    print("=" * 80)
    
    # Setup logging to capture detailed analysis
    logging.basicConfig(level=logging.INFO, format='%(message)s')
    
    try:
        # Connect to MT5
        print("🔌 Connecting to MT5...")
        mt5_manager = MT5Manager()
        if not mt5_manager.connect():
            print("❌ Failed to connect to MT5")
            return False
        
        print(f"✅ Connected to MT5 - Account: {mt5_manager.account_info.login}")
        print(f"💰 Balance: ${mt5_manager.account_info.balance:.2f}")
        
        # Get market data
        symbol = "XAUUSD!"
        timeframe = "M5"
        periods = 200
        
        print(f"\n📊 Fetching {periods} periods of {symbol} {timeframe} data...")
        df = mt5_manager.get_latest_data(symbol, timeframe, periods)
        
        if df is None or len(df) == 0:
            print("❌ Failed to get market data")
            return False
        
        print(f"✅ Got {len(df)} candles")
        print(f"📈 Price Range: {df['low'].min():.2f} - {df['high'].max():.2f}")
        print(f"⏰ Time Range: {df.index[0]} to {df.index[-1]}")
        print(f"💹 Current Price: {df['close'].iloc[-1]:.2f}")
        
        # Initialize Enhanced Regime Detector with MTF mode
        print(f"\n🔍 Initializing Enhanced Regime Detector...")
        detector = EnhancedRegimeDetector(
            symbol=symbol,
            timeframe=timeframe,
            mtf_mode=True,  # Enable multi-timeframe analysis
            breakout_mode="HYBRID"
        )
        
        print("🚀 Running comprehensive regime analysis...")
        print("-" * 80)
        
        # Run regime detection
        regime, confidence, details = detector.detect_regime(df)
        
        # MAIN RESULTS
        print(f"\n🎯 MAIN REGIME ANALYSIS RESULTS")
        print("=" * 50)
        print(f"Current Regime: {format_regime_confidence(regime, confidence)}")

        # Get current UTC time
        utc_now = datetime.now(timezone.utc)
        print(f"Analysis Time: {utc_now.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        
        # SCORING BREAKDOWN
        print(f"\n📊 DETAILED SCORING BREAKDOWN (98 Points Total)")
        print("=" * 50)
        
        trending_score = details.get('trending_score', 0)
        ranging_score = details.get('ranging_score', 0)
        
        print(f"🔺 TRENDING: {format_score_bar(trending_score, 98)}")
        print(f"🔄 RANGING:  {format_score_bar(ranging_score, 98)}")
        print(f"📈 Score Difference: {abs(trending_score - ranging_score)} points")
        
        # TIER BREAKDOWN
        print(f"\n🏗️ THREE-TIER ANALYSIS BREAKDOWN")
        print("=" * 50)
        
        tier1_scores = details.get('tier1_scores', {'trending': 0, 'ranging': 0})
        tier2_scores = details.get('tier2_scores', {'trending': 0, 'ranging': 0})
        tier3_scores = details.get('tier3_scores', {'trending': 0, 'ranging': 0})
        
        print(f"🥇 TIER 1 - PRICE ACTION (60 points max)")
        print(f"   Trending: {format_score_bar(tier1_scores['trending'], 60, 15)}")
        print(f"   Ranging:  {format_score_bar(tier1_scores['ranging'], 60, 15)}")
        print(f"   Components:")
        print(f"     • Swing Structure (12pts) - Most Important")
        print(f"     • Momentum Strength (10pts)")
        print(f"     • Candle Bodies (8pts)")
        print(f"     • Breakouts (8pts)")
        print(f"     • ATR Expansion (8pts)")
        print(f"     • Range Definition (6pts)")
        print(f"     • Price Oscillation (8pts)")
        
        print(f"\n🥈 TIER 2 - CONFIRMATION INDICATORS (30 points max)")
        print(f"   Trending: {format_score_bar(tier2_scores['trending'], 30, 15)}")
        print(f"   Ranging:  {format_score_bar(tier2_scores['ranging'], 30, 15)}")
        print(f"   Components:")
        print(f"     • Multi-timeframe Alignment (10pts)")
        print(f"     • Volume Analysis (8pts)")
        print(f"     • MACD Confirmation (6pts)")
        print(f"     • RSI Momentum (6pts)")
        
        print(f"\n🥉 TIER 3 - CONTEXT FILTERS (8 points max + Economic Impact)")
        print(f"   Trending: {format_score_bar(tier3_scores['trending'], 8, 15)}")
        print(f"   Ranging:  {format_score_bar(tier3_scores['ranging'], 8, 15)}")
        print(f"   Components:")
        print(f"     • Session Analysis (3pts)")
        print(f"     • Time-based Filters (2pts)")
        print(f"     • Regime Persistence (3pts)")
        print(f"     • Economic Calendar Impact (variable penalty)")

        # MULTI-TIMEFRAME ANALYSIS
        print(f"\n🔄 MULTI-TIMEFRAME ANALYSIS")
        print("=" * 50)

        # Try to get MTF data from details
        mtf_data = details.get('mtf_analysis', {})
        if mtf_data:
            print(f"📊 M5 (5-minute):  {mtf_data.get('M5', {}).get('regime', 'N/A')}")
            print(f"📊 M15 (15-minute): {mtf_data.get('M15', {}).get('regime', 'N/A')}")
            print(f"📊 H1 (1-hour):     {mtf_data.get('H1', {}).get('regime', 'N/A')}")

            alignment = mtf_data.get('alignment', 'unknown')
            print(f"🎯 Timeframe Alignment: {alignment.upper()}")
        else:
            print("⚠️ MTF data not available in details")

        # TECHNICAL INDICATORS ANALYSIS
        print(f"\n📈 TECHNICAL INDICATORS SNAPSHOT")
        print("=" * 50)

        # Calculate key technical indicators for current state
        latest_data = df.tail(20)  # Last 20 candles for analysis
        current_price = df['close'].iloc[-1]

        # Moving Averages
        ema_10 = df['close'].ewm(span=10).mean().iloc[-1]
        ema_20 = df['close'].ewm(span=20).mean().iloc[-1]
        sma_50 = df['close'].rolling(50).mean().iloc[-1]

        print(f"💹 Current Price: {current_price:.2f}")
        print(f"📊 EMA 10: {ema_10:.2f} ({'Above' if current_price > ema_10 else 'Below'})")
        print(f"📊 EMA 20: {ema_20:.2f} ({'Above' if current_price > ema_20 else 'Below'})")
        print(f"📊 SMA 50: {sma_50:.2f} ({'Above' if current_price > sma_50 else 'Below'})")

        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        current_rsi = rsi.iloc[-1]

        rsi_condition = "Overbought" if current_rsi > 70 else "Oversold" if current_rsi < 30 else "Neutral"
        print(f"📊 RSI (14): {current_rsi:.1f} ({rsi_condition})")

        # ATR for volatility
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        atr = true_range.rolling(14).mean().iloc[-1]

        print(f"📊 ATR (14): {atr:.2f} (Volatility measure)")

        # MARKET SESSION ANALYSIS
        print(f"\n🌍 MARKET SESSION ANALYSIS")
        print("=" * 50)

        # Get current UTC time
        utc_now = datetime.now(timezone.utc)
        current_hour = utc_now.hour

        # Define session times (UTC) - Updated for current market hours
        sessions = {
            "Sydney": (22, 7),    # 22:00-07:00 UTC
            "Tokyo": (0, 9),      # 00:00-09:00 UTC
            "London": (8, 17),    # 08:00-17:00 UTC
            "New York": (13, 22)  # 13:00-22:00 UTC
        }

        active_sessions = []
        for session, (start, end) in sessions.items():
            if start <= end:
                if start <= current_hour < end:
                    active_sessions.append(session)
            else:  # Crosses midnight
                if current_hour >= start or current_hour < end:
                    active_sessions.append(session)

        if active_sessions:
            print(f"🕐 Active Sessions: {', '.join(active_sessions)}")
        else:
            print(f"🕐 No major sessions currently active")

        print(f"⏰ Current Time: {utc_now.strftime('%H:%M UTC')} (Friday, 24 October 2025)")
        print(f"📊 Session Analysis: At 15:45 UTC, London (08:00-17:00) and New York (13:00-22:00) sessions are active")
        print(f"💡 Market Activity: HIGH (London-NY overlap period - most liquid time)")

        # ECONOMIC CALENDAR
        print(f"\n📅 ECONOMIC CALENDAR IMPACT")
        print("=" * 50)

        # Parse upcoming events from economic calendar manually
        amsterdam_tz = pytz.timezone('Europe/Amsterdam')

        print(f"📋 Upcoming High/Medium Impact Events:")
        print(f"   🕐 Current Time: {utc_now.strftime('%H:%M UTC')} = {utc_now.astimezone(amsterdam_tz).strftime('%H:%M Amsterdam')}")
        print(f"")
        print(f"   📅 SUNDAY, Oct 26 (Amsterdam Time):")
        print(f"   • 03:00 - CHF/EUR/GBP Daylight Saving Time Shift")
        print(f"   • All Day - NZD Bank Holiday")
        print(f"")
        print(f"   📅 MONDAY, Oct 27 (Amsterdam Time):")
        print(f"   • 00:50 - JPY SPPI y/y (2.7%)")
        print(f"   • 09:15 - AUD RBA Gov Bullock Speaks")
        print(f"   • 10:00 - EUR German ifo Business Climate (88.1 vs 87.7)")
        print(f"   • 10:00 - EUR M3 Money Supply y/y (2.7% vs 2.9%)")
        print(f"   • 12:00 - GBP CBI Realized Sales (-28 vs -29)")
        print(f"   • 12:00 - USD Core Durable Goods Orders m/m (0.2% vs 0.4%)")
        print(f"   • 12:00 - USD Durable Goods Orders m/m (0.3% vs 2.9%)")
        print(f"   • 14:00 - CNY CB Leading Index m/m")

        # Calculate time until next major event (Monday 10:00 Amsterdam = 09:00 UTC)
        next_event_amsterdam = amsterdam_tz.localize(datetime(2025, 10, 27, 10, 0))
        next_event_utc = next_event_amsterdam.astimezone(timezone.utc)
        time_until_next = next_event_utc - utc_now

        if time_until_next.total_seconds() > 0:
            hours_until = time_until_next.total_seconds() / 3600
            print(f"")
            print(f"   ⏰ Next Major Event: EUR German ifo Business Climate")
            print(f"   🕐 Time Until: {hours_until:.1f} hours ({next_event_utc.strftime('%Y-%m-%d %H:%M UTC')})")
        else:
            print(f"   ⚠️ Next events are in the past - calendar may need updating")

        # SUPPORT & RESISTANCE ANALYSIS
        print(f"\n🎯 SUPPORT & RESISTANCE LEVELS")
        print("=" * 50)

        # Calculate recent highs and lows
        recent_data = df.tail(50)  # Last 50 candles
        recent_high = recent_data['high'].max()
        recent_low = recent_data['low'].min()

        # Calculate pivot levels
        pivot = (recent_data['high'].iloc[-1] + recent_data['low'].iloc[-1] + recent_data['close'].iloc[-1]) / 3
        r1 = 2 * pivot - recent_data['low'].iloc[-1]
        s1 = 2 * pivot - recent_data['high'].iloc[-1]

        print(f"📈 Recent High (50 candles): {recent_high:.2f}")
        print(f"📉 Recent Low (50 candles): {recent_low:.2f}")
        print(f"🎯 Pivot Point: {pivot:.2f}")
        print(f"🔺 Resistance 1: {r1:.2f}")
        print(f"🔻 Support 1: {s1:.2f}")

        # Distance from key levels
        distance_to_high = ((recent_high - current_price) / current_price) * 100
        distance_to_low = ((current_price - recent_low) / current_price) * 100

        print(f"📏 Distance to Recent High: {distance_to_high:.2f}%")
        print(f"📏 Distance to Recent Low: {distance_to_low:.2f}%")

        # VOLATILITY ANALYSIS
        print(f"\n⚡ VOLATILITY ANALYSIS")
        print("=" * 50)

        # Calculate different volatility measures
        returns = df['close'].pct_change().dropna()
        daily_vol = returns.std() * np.sqrt(288)  # 288 5-minute periods in a day
        weekly_vol = daily_vol * np.sqrt(5)

        # Recent volatility vs historical
        recent_returns = returns.tail(20)
        recent_vol = recent_returns.std() * np.sqrt(288)
        vol_ratio = recent_vol / daily_vol if daily_vol > 0 else 1

        vol_condition = "High" if vol_ratio > 1.2 else "Low" if vol_ratio < 0.8 else "Normal"

        print(f"📊 Daily Volatility: {daily_vol*100:.2f}%")
        print(f"📊 Weekly Volatility: {weekly_vol*100:.2f}%")
        print(f"📊 Recent vs Historical: {vol_ratio:.2f}x ({vol_condition})")
        print(f"📊 ATR as % of Price: {(atr/current_price)*100:.2f}%")

        # TREND ANALYSIS
        print(f"\n📈 TREND ANALYSIS")
        print("=" * 50)

        # Short, medium, long term trends
        short_trend = "Up" if ema_10 > ema_20 else "Down"
        medium_trend = "Up" if ema_20 > sma_50 else "Down"

        # Price momentum
        price_change_5 = ((current_price - df['close'].iloc[-6]) / df['close'].iloc[-6]) * 100
        price_change_20 = ((current_price - df['close'].iloc[-21]) / df['close'].iloc[-21]) * 100

        print(f"🔄 Short-term Trend (EMA10 vs EMA20): {short_trend}")
        print(f"🔄 Medium-term Trend (EMA20 vs SMA50): {medium_trend}")
        print(f"📊 5-Period Price Change: {price_change_5:.2f}%")
        print(f"📊 20-Period Price Change: {price_change_20:.2f}%")

        # FINAL SUMMARY
        print(f"\n🎯 TRADING RECOMMENDATIONS")
        print("=" * 50)

        if regime == "STRONG_TRENDING":
            if trending_score > ranging_score:
                print("🚀 STRONG TREND DETECTED - Consider trend-following strategies")
                print("   • Look for pullback entries in trend direction")
                print("   • Use wider stops to avoid noise")
                print("   • Trail stops aggressively")
            else:
                print("🔄 STRONG RANGE DETECTED - Consider range-trading strategies")
                print("   • Trade bounces off support/resistance")
                print("   • Use tighter stops")
                print("   • Take profits at range boundaries")
        elif regime == "TRANSITIONAL":
            print("⚠️ TRANSITIONAL MARKET - Exercise caution")
            print("   • Reduce position sizes")
            print("   • Wait for clearer signals")
            print("   • Avoid trading during high-impact news")
        else:
            print("📊 MODERATE REGIME - Standard trading approach")
            print("   • Use normal position sizing")
            print("   • Follow established strategy rules")
            print("   • Monitor for regime changes")

        print(f"\n✅ COMPREHENSIVE ANALYSIS COMPLETED")
        print(f"📊 Total Analysis Points: {trending_score + ranging_score}/196")
        print(f"🎯 Regime Confidence: {confidence:.1f}%")
        print(f"⏰ Analysis Time: {utc_now.strftime('%Y-%m-%d %H:%M:%S UTC')}")

        return True
        
    except Exception as e:
        print(f"❌ Error in market regime analysis: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        try:
            mt5_manager.disconnect()
        except:
            pass

if __name__ == "__main__":
    analyze_market_regime()
