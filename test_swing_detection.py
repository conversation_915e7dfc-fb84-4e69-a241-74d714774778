#!/usr/bin/env python3
"""
Test script to validate the new swing point detection logic
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager
from fixed_live_trader import FixedLiveTrader

def test_swing_detection():
    """Test the swing point detection with real data"""
    print("🔍 Testing Swing Point Detection...")
    
    # Create trader instance
    trader = FixedLiveTrader("XAUUSD!")
    
    # Connect to MT5 and get data
    if not trader.mt5_manager.connect():
        print("❌ Failed to connect to MT5")
        return
    
    # Get recent data
    df = trader.mt5_manager.get_latest_data("XAUUSD!", "M5", 50)
    if df is None or len(df) < 20:
        print("❌ Failed to get sufficient data")
        return
    
    print(f"📊 Got {len(df)} candles of data")
    print(f"📊 Date range: {df.index[0]} to {df.index[-1]}")
    
    # Test swing point detection
    swing_points = trader.find_recent_swing_points(df)
    
    print("\n🎯 SWING POINT DETECTION RESULTS:")
    print("=" * 50)
    
    if swing_points['swing_points_available']:
        if swing_points['recent_high']:
            print(f"✅ Recent Swing High: {swing_points['recent_high']:.5f}")
            print(f"   📍 Distance from current: {swing_points['recent_high_distance']:.5f}")
            print(f"   📅 Candles ago: {swing_points['recent_high_candles_ago']}")
        else:
            print("❌ No recent swing high found")
            
        if swing_points['recent_low']:
            print(f"✅ Recent Swing Low: {swing_points['recent_low']:.5f}")
            print(f"   📍 Distance from current: {swing_points['recent_low_distance']:.5f}")
            print(f"   📅 Candles ago: {swing_points['recent_low_candles_ago']}")
        else:
            print("❌ No recent swing low found")
    else:
        print("❌ No swing points found in the data")
    
    print(f"\n📊 Search depth: {swing_points['search_depth']} candles")
    print(f"📊 Current price: {df.iloc[-1]['close']:.5f}")
    
    # Show last few candles for context
    print("\n📈 LAST 10 CANDLES (for context):")
    print("=" * 70)
    print("Index | Time                | High     | Low      | Close    ")
    print("-" * 70)
    
    for i in range(max(0, len(df)-10), len(df)):
        candle = df.iloc[i]
        time_str = candle.name.strftime("%Y-%m-%d %H:%M")
        print(f"{i:5d} | {time_str} | {candle['high']:8.5f} | {candle['low']:8.5f} | {candle['close']:8.5f}")
    
    trader.mt5_manager.disconnect()
    print("\n✅ Test completed!")

if __name__ == "__main__":
    test_swing_detection()
