# Partial Position Closing Feature

## Overview
Implemented automatic partial position closing that triggers every time the trailing stop loss gets modified (trailed). This helps lock in profits progressively as the trade moves in favor.

## How It Works

### 1. **Trigger Condition**
- **When**: Every time the trailing stop loss gets updated (moved 1 ATR in profit)
- **Action**: Automatically close 1/3 of the remaining position volume

### 2. **MT5-Valid Volume Calculation**
```python
def close_partial_position(self, close_fraction=1/3):
    # Get current position volume from MT5
    current_volume = current_pos['volume']
    
    # Calculate 1/3 of remaining volume
    volume_to_close = current_volume * close_fraction
    
    # Round to MT5-valid volume (must be multiple of volume_step)
    volume_to_close = round(volume_to_close / volume_step) * volume_step
    
    # Ensure minimum volume requirements
    if volume_to_close < volume_min:
        return False, "Volume too small"
```

### 3. **Progressive Profit Taking Example**
```
Initial Position: 1.00 lots
1st Trail (1 ATR profit): Close 0.33 lots → Remaining: 0.67 lots
2nd Trail (2 ATR profit): Close 0.22 lots → Remaining: 0.45 lots  
3rd Trail (3 ATR profit): Close 0.15 lots → Remaining: 0.30 lots
4th Trail (4 ATR profit): Close 0.10 lots → Remaining: 0.20 lots
```

### 4. **Integration with Trailing Stop**
- **BUY Positions**: When SL moves up by 1 ATR → Close 1/3
- **SELL Positions**: When SL moves down by 1 ATR → Close 1/3
- **Logging**: Detailed logs for both SL update and partial close

### 5. **Safety Features**
- **Volume Validation**: Ensures MT5-compatible volume (rounded to volume_step)
- **Minimum Volume Check**: Won't close if volume < minimum required
- **Position Tracking**: Updates remaining_volume after each partial close
- **Full Close Detection**: Clears position tracking when fully closed
- **Error Handling**: Graceful handling of MT5 errors

## Technical Implementation

### Position Tracking Enhanced
```python
self.current_position = {
    'type': 'BUY'/'SELL',
    'ticket': ticket_id,
    'time': datetime,
    'volume': original_volume,
    'remaining_volume': current_volume,  # NEW: Tracks remaining after partials
    'price': entry_price
}
```

### Logging Output
```
🔄 TRAILING STOP UPDATE (BUY):
   Profit: 2.15 ATR
   Old SL: 2650.123
   New SL: 2651.456 (+1 ATR)
💰 PARTIAL CLOSE: Closing 0.33 of 1.00 lots (33.3%)
✅ Partial close successful. Remaining: 0.67 lots
💰 PROFIT TAKING: Closed 0.33 lots
```

## Benefits

### 1. **Risk Management**
- **Locks in Profits**: Secures gains as trade moves favorably
- **Reduces Exposure**: Decreases position size progressively
- **Protects Against Reversals**: Less capital at risk on pullbacks

### 2. **Psychological Benefits**
- **Removes Emotion**: Automatic profit-taking eliminates manual decisions
- **Consistent Strategy**: Same approach for all profitable trades
- **Stress Reduction**: Knowing profits are being secured automatically

### 3. **Performance Enhancement**
- **Improved Risk-Reward**: Better overall trade outcomes
- **Capital Preservation**: Protects against giving back all profits
- **Compound Growth**: Freed capital can be used for new opportunities

## Configuration

### Customizable Parameters
- **Close Fraction**: Currently set to 1/3 (33.3%), easily adjustable
- **Trigger Frequency**: Every 1 ATR trail (can be modified)
- **Minimum Volume**: Respects MT5 symbol specifications

### Symbol-Specific Settings
- **Volume Step**: Automatically retrieved from MT5 symbol info
- **Minimum Volume**: Automatically retrieved from MT5 symbol info
- **Precision**: Handles different lot size requirements per symbol

## Usage in Trading Loop

The feature is fully integrated into the existing trading system:

1. **Position Opens**: Tracks initial volume and remaining volume
2. **Trailing Updates**: Every ATR trail triggers partial close
3. **Volume Management**: Automatically handles MT5 volume requirements
4. **Position Cleanup**: Clears tracking when position fully closed

This creates a systematic approach to profit-taking that works automatically without manual intervention, helping to lock in gains while still allowing for further profit potential.
