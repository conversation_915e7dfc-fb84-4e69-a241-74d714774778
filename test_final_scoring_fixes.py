#!/usr/bin/env python3
"""
Test the FINAL scoring fixes for regime detection
"""

def test_volatility_fix():
    """Test Issue #2: Volatility Full Weight Fix"""
    print("🧪 TESTING VOLATILITY FULL WEIGHT FIX")
    print("=" * 60)
    
    vol_percentiles = [0.10, 0.25, 0.40, 0.50, 0.60, 0.70, 0.80, 0.90]
    
    print("Volatility Percentile → Points Awarded:")
    for vol_pct in vol_percentiles:
        if vol_pct > 0.60:  # Trending bias
            points = "+1 trending"
        else:  # Ranging bias
            points = "+1 ranging"
        
        print(f"   {vol_pct:.2f} → {points}")
    
    print(f"\n📊 Distribution:")
    print(f"   Trending (>60th percentile): 40% of data gets +1 trending")
    print(f"   Ranging (≤60th percentile): 60% of data gets +1 ranging")
    print(f"   ✅ Gold ranges more than trends - this bias makes sense!")

def test_bb_width_narrow_neutral():
    """Test Issue #3: Narrower BB Width Neutral Zone"""
    print("\n\n🧪 TESTING BB WIDTH NARROWER NEUTRAL ZONE")
    print("=" * 60)
    
    bb_percentiles = [0.05, 0.15, 0.25, 0.35, 0.42, 0.48, 0.55, 0.65, 0.75, 0.85, 0.95]
    
    print("BB Width Percentile → Points Awarded:")
    for bb_pct in bb_percentiles:
        bb_squeeze = bb_pct < 0.20
        
        if bb_squeeze:
            points = "+2 ranging (squeeze)"
        elif bb_pct > 0.70:
            points = "+2 trending (wide)"
        elif bb_pct > 0.50:
            points = "+1 trending (medium-wide)"
        elif bb_pct > 0.40:
            points = "+0.5 trending (mild bias)"
        elif bb_pct < 0.35:
            points = "+1 ranging (narrow)"
        else:
            points = "0 points (neutral)"
        
        print(f"   {bb_pct:.2f} → {points}")
    
    print(f"\n📊 Distribution:")
    print(f"   0-20th: +2 ranging (20% of data)")
    print(f"   20-35th: +1 ranging (15% of data)")
    print(f"   35-40th: 0 points (5% of data)")
    print(f"   40-50th: +0.5 trending (10% of data) ← FIXED")
    print(f"   50-70th: +1 trending (20% of data)")
    print(f"   70-100th: +2 trending (30% of data)")
    print(f"   ✅ Only 5% neutral zone (was 15%)")

def test_complete_scoring_scenarios():
    """Test complete scoring scenarios with all fixes"""
    print("\n\n🧪 TESTING COMPLETE SCORING SCENARIOS")
    print("=" * 60)
    
    scenarios = [
        {
            'name': 'Strong Trending Market',
            'atr_pct': 0.85,
            'slope_abs': 0.0020,
            'bb_width_pct': 0.80,
            'vol_pct': 0.85,
            'expected_trending': 9,  # 3+3+2+1
            'expected_ranging': 0,
            'expected_regime': 'TRENDING'
        },
        {
            'name': 'Strong Ranging Market',
            'atr_pct': 0.15,
            'slope_abs': 0.0003,
            'bb_width_pct': 0.15,  # BB squeeze
            'vol_pct': 0.15,
            'expected_trending': 0,
            'expected_ranging': 9,  # 3+3+2+1
            'expected_regime': 'RANGING'
        },
        {
            'name': 'Mild Trending Market (Fixed)',
            'atr_pct': 0.75,
            'slope_abs': 0.0010,  # Middle zone
            'bb_width_pct': 0.55,  # Medium-wide
            'vol_pct': 0.65,  # Above 0.60 threshold
            'expected_trending': 5,  # 3+1+1+1 (slope middle goes to trending)
            'expected_ranging': 0,
            'expected_regime': 'TRENDING'
        },
        {
            'name': 'Transitional Market (Edge Case)',
            'atr_pct': 0.55,  # Middle zone
            'slope_abs': 0.0008,  # Middle zone, closer to ranging
            'bb_width_pct': 0.42,  # Mild trending bias
            'vol_pct': 0.45,  # Ranging bias
            'expected_trending': 1.5,  # 1+0+0.5+0
            'expected_ranging': 2,  # 0+1+0+1
            'expected_regime': 'TRANSITIONAL'
        },
        {
            'name': 'Borderline Ranging Market',
            'atr_pct': 0.25,  # Ranging
            'slope_abs': 0.0012,  # Middle, closer to trending
            'bb_width_pct': 0.30,  # Narrow
            'vol_pct': 0.35,  # Ranging bias
            'expected_trending': 1,  # 0+1+0+0
            'expected_ranging': 5,  # 3+0+1+1
            'expected_regime': 'RANGING'
        }
    ]
    
    for scenario in scenarios:
        print(f"\n🔍 {scenario['name']}:")
        
        # Simulate the FINAL FIXED scoring logic
        trending_score = 0
        ranging_score = 0
        
        # ATR Analysis - ALWAYS AWARDS POINTS
        atr_pct = scenario['atr_pct']
        if atr_pct > 0.70:
            trending_score += 3
        elif atr_pct < 0.30:
            ranging_score += 3
        else:
            if atr_pct > 0.5:
                trending_score += 1
            else:
                ranging_score += 1
        
        # EMA Slope Analysis - ALWAYS AWARDS POINTS
        slope_abs = scenario['slope_abs']
        if slope_abs > 0.0015:
            trending_score += 3
        elif slope_abs < 0.0005:
            ranging_score += 3
        else:
            mid_threshold = (0.0015 + 0.0005) / 2  # 0.001
            if slope_abs > mid_threshold:
                trending_score += 1
            else:
                ranging_score += 1
        
        # FIXED: BB Width Analysis - NARROWER NEUTRAL ZONE
        bb_width_pct = scenario['bb_width_pct']
        bb_squeeze = bb_width_pct < 0.20
        if bb_squeeze:
            ranging_score += 2
        elif bb_width_pct > 0.70:
            trending_score += 2
        elif bb_width_pct > 0.50:
            trending_score += 1
        elif bb_width_pct > 0.40:  # FIXED: Narrower neutral zone
            trending_score += 0.5
        elif bb_width_pct < 0.35:
            ranging_score += 1
        
        # FIXED: Volatility Analysis - FULL WEIGHT ALWAYS
        vol_pct = scenario['vol_pct']
        if vol_pct > 0.60:  # FIXED: Trending bias
            trending_score += 1
        else:  # FIXED: Ranging bias
            ranging_score += 1
        
        # Calculate regime
        score_diff = abs(trending_score - ranging_score)
        confidence = max(trending_score, ranging_score) / 9
        
        if trending_score >= 3.5 and score_diff >= 1.5 and trending_score > ranging_score:
            regime = "TRENDING"
        elif ranging_score >= 3.5 and score_diff >= 1.5 and ranging_score > trending_score:
            regime = "RANGING"
        else:
            regime = "TRANSITIONAL"
        
        print(f"   ATR: {atr_pct:.2f}, Slope: {slope_abs:.4f}, BB: {bb_width_pct:.2f}, Vol: {vol_pct:.2f}")
        print(f"   Trending Score: {trending_score} (Expected: {scenario['expected_trending']})")
        print(f"   Ranging Score: {ranging_score} (Expected: {scenario['expected_ranging']})")
        print(f"   Score Diff: {score_diff:.1f}")
        print(f"   Confidence: {confidence:.3f}")
        print(f"   Regime: {regime} (Expected: {scenario['expected_regime']})")
        
        # Verify expectations
        regime_match = regime == scenario['expected_regime']
        print(f"   ✅ {'PASS' if regime_match else 'FAIL'}")

def show_final_improvements():
    """Show summary of final improvements"""
    print("\n\n✅ FINAL IMPROVEMENTS SUMMARY")
    print("=" * 60)
    print("🔧 VOLATILITY FIX:")
    print("   ✅ Always awards full 1 point (no more 0.5 points)")
    print("   ✅ 60th percentile threshold (trending bias for high vol)")
    print("   ✅ Reflects gold's ranging nature (60% ranging, 40% trending)")
    print()
    print("🔧 BB WIDTH NEUTRAL ZONE FIX:")
    print("   ✅ Narrowed neutral zone: 15% → 5% of data")
    print("   ✅ Added mild trending bias for 40-50th percentile (+0.5)")
    print("   ✅ Better point distribution across all percentiles")
    print()
    print("🔧 SCORING CONSISTENCY:")
    print("   ✅ Max possible score: 9 points (3+3+2+1)")
    print("   ✅ All categories always award points")
    print("   ✅ Consistent confidence calculation")
    print()
    print("🎯 EXPECTED RESULTS:")
    print("   • More decisive regime classification")
    print("   • Better balance between trending/ranging detection")
    print("   • Fewer edge cases falling into TRANSITIONAL")
    print("   • More accurate confidence scores")
    print("   • Optimized for XAUUSD characteristics")

def main():
    """Main function"""
    test_volatility_fix()
    test_bb_width_narrow_neutral()
    test_complete_scoring_scenarios()
    show_final_improvements()

if __name__ == "__main__":
    main()
