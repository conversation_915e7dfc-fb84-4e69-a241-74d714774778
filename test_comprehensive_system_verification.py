#!/usr/bin/env python3
"""
Comprehensive System Verification Test

Tests all reported issues:
1. Same signal SL update
2. Position sizing based on SL distance 
3. Acceleration-based trailing in loss
4. Background trailing monitor
5. Partial close system
"""

import pandas as pd
import sys
import os
import threading
import time
from datetime import datetime, timedel<PERSON>

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader

def test_comprehensive_system():
    """Test all trading system components"""
    
    print("🧪 Comprehensive System Verification Test")
    print("=" * 60)
    
    # Create test trader
    trader = FixedLiveTrader("XAUUSD!")
    
    test_results = []
    
    # Test 1: Same Signal SL Update
    print(f"\n📊 Test 1: Same Signal SL Update")
    print("-" * 40)
    
    # Check if same signal logic updates SL
    print("🔍 Checking same signal handling logic...")
    
    # Look for the problematic same signal handlers
    import inspect
    source_lines = inspect.getsource(trader.execute_trade).split('\n')
    
    same_signal_handlers = []
    for i, line in enumerate(source_lines):
        if "Same signal" in line and "Already have" in line:
            same_signal_handlers.append((i, line.strip()))
    
    print(f"Found {len(same_signal_handlers)} same signal handlers:")
    for line_num, line in same_signal_handlers:
        print(f"  Line {line_num}: {line}")
    
    # Check if they all update SL or just return False
    sl_update_handlers = 0
    for line_num, line in same_signal_handlers:
        # Look for SL update logic after this line
        remaining_lines = source_lines[line_num:line_num+20]
        has_sl_update = any("modify_position" in l and "stop_loss" in l for l in remaining_lines)
        if has_sl_update:
            sl_update_handlers += 1
            print(f"  ✅ Handler at line {line_num} has SL update logic")
        else:
            print(f"  ❌ Handler at line {line_num} missing SL update logic")
    
    test1_passed = sl_update_handlers == len(same_signal_handlers)
    test_results.append(("Same Signal SL Update", test1_passed))
    
    # Test 2: Position Sizing Calculation
    print(f"\n📊 Test 2: Position Sizing Based on SL Distance")
    print("-" * 40)
    
    # Test position sizing with different SL distances
    balance = 1000.0
    current_price = 4300.0
    atr_value = 15.0
    
    test_scenarios = [
        {"signal": "BUY", "signal_candle_low": 4290.0, "expected_sl": 4288.5, "expected_risk": 40.0},
        {"signal": "SELL", "signal_candle_high": 4310.0, "expected_sl": 4311.5, "expected_risk": 40.0},
    ]
    
    position_sizing_correct = True
    
    for scenario in test_scenarios:
        signal = scenario["signal"]
        expected_sl = scenario["expected_sl"]
        expected_risk = scenario["expected_risk"]
        
        # Mock signal candle data
        if signal == "BUY":
            sl_distance = abs(current_price - expected_sl)
        else:
            sl_distance = abs(expected_sl - current_price)
        
        print(f"🔍 Testing {signal} signal:")
        print(f"  Current Price: {current_price:.2f}")
        print(f"  Expected SL: {expected_sl:.2f}")
        print(f"  SL Distance: {sl_distance:.2f} points")
        print(f"  Expected Risk: ${expected_risk:.2f} (4% of ${balance:.0f})")
        
        # Calculate position size using trader's method
        try:
            # This should use actual SL distance, not fixed 1.50
            lot_size = trader.calculate_position_size(
                balance=balance,
                current_price=current_price,
                atr_value=atr_value,
                signal=signal
            )
            
            # Calculate actual risk
            contract_size = trader.SYMBOL_SPECS[trader.symbol]['contract_size']
            actual_risk = sl_distance * lot_size * contract_size
            
            print(f"  Calculated Lot Size: {lot_size:.2f}")
            print(f"  Actual Risk: ${actual_risk:.2f}")
            
            # Check if risk is approximately 4% (within 10% tolerance)
            risk_tolerance = expected_risk * 0.1  # 10% tolerance
            risk_correct = abs(actual_risk - expected_risk) <= risk_tolerance
            
            if risk_correct:
                print(f"  ✅ Position sizing correct (within tolerance)")
            else:
                print(f"  ❌ Position sizing incorrect: ${actual_risk:.2f} vs expected ${expected_risk:.2f}")
                position_sizing_correct = False
                
        except Exception as e:
            print(f"  ❌ Position sizing calculation failed: {e}")
            position_sizing_correct = False
    
    test_results.append(("Position Sizing", position_sizing_correct))
    
    # Test 3: Background Trailing Monitor
    print(f"\n📊 Test 3: Background Trailing Monitor")
    print("-" * 40)
    
    # Check if trailing monitor can start
    print("🔍 Testing background trailing monitor...")
    
    # Mock a position
    trader.current_position = {
        'type': 'BUY',
        'ticket': 12345,
        'time': datetime.now(),
        'volume': 0.10,
        'remaining_volume': 0.10,
        'price': 4300.0,
        'sl': 4285.0
    }
    
    trader.trailing_stop_data = {
        'initial_sl': 4285.0,
        'current_sl': 4285.0,
        'atr_value': 15.0,
        'profit_atr_count': 0,
        'original_sl_distance': 15.0
    }
    
    # Start trailing monitor
    try:
        trader.start_real_time_trailing_monitor()
        time.sleep(1)  # Give it time to start
        
        monitor_started = (trader.trailing_monitor_thread is not None and 
                          trader.trailing_monitor_thread.is_alive() and
                          trader.trailing_monitor_active)
        
        if monitor_started:
            print("  ✅ Background trailing monitor started successfully")
            
            # Stop the monitor
            trader.stop_real_time_trailing_monitor()
            time.sleep(1)
            
            monitor_stopped = not trader.trailing_monitor_active
            if monitor_stopped:
                print("  ✅ Background trailing monitor stopped successfully")
            else:
                print("  ⚠️ Background trailing monitor didn't stop cleanly")
                
        else:
            print("  ❌ Background trailing monitor failed to start")
            
    except Exception as e:
        print(f"  ❌ Background trailing monitor error: {e}")
        monitor_started = False
    
    test_results.append(("Background Trailing Monitor", monitor_started))
    
    # Test 4: Acceleration-Based Trailing Conditions
    print(f"\n📊 Test 4: Acceleration-Based Trailing Logic")
    print("-" * 40)
    
    # Check if acceleration trailing has proper conditions
    print("🔍 Checking acceleration-based trailing conditions...")
    
    # Look for acceleration trailing logic
    source_code = inspect.getsource(trader.execute_trade)
    
    # Check for acceleration conditions
    has_accel_logic = "accel" in source_code.lower() and "trailing" in source_code.lower()
    has_loss_condition = "loss" in source_code.lower() or "profit" in source_code.lower()
    
    print(f"  Has acceleration logic: {'✅' if has_accel_logic else '❌'}")
    print(f"  Has profit/loss conditions: {'✅' if has_loss_condition else '❌'}")
    
    # Check for specific acceleration thresholds
    has_accel_threshold = "-10" in source_code or "10%" in source_code
    print(f"  Has acceleration threshold: {'✅' if has_accel_threshold else '❌'}")
    
    accel_logic_correct = has_accel_logic and has_loss_condition and has_accel_threshold
    test_results.append(("Acceleration Trailing Logic", accel_logic_correct))
    
    # Test 5: Partial Close System
    print(f"\n📊 Test 5: Partial Close System")
    print("-" * 40)
    
    # Check partial close method
    print("🔍 Testing partial close system...")
    
    try:
        # Test partial close calculation
        test_volume = 0.30
        close_fraction = 1/3
        expected_close_volume = test_volume * close_fraction
        
        print(f"  Test volume: {test_volume:.2f}")
        print(f"  Close fraction: {close_fraction:.1%}")
        print(f"  Expected close volume: {expected_close_volume:.2f}")
        
        # Check if method exists and has proper logic
        has_partial_close = hasattr(trader, 'close_partial_position')
        print(f"  Has partial close method: {'✅' if has_partial_close else '❌'}")
        
        if has_partial_close:
            # Check method signature
            import inspect
            sig = inspect.signature(trader.close_partial_position)
            has_fraction_param = 'close_fraction' in sig.parameters
            print(f"  Has close_fraction parameter: {'✅' if has_fraction_param else '❌'}")
            
            partial_close_correct = has_fraction_param
        else:
            partial_close_correct = False
            
    except Exception as e:
        print(f"  ❌ Partial close system error: {e}")
        partial_close_correct = False
    
    test_results.append(("Partial Close System", partial_close_correct))
    
    # Clean up
    trader.current_position = None
    trader.trailing_stop_data = None
    
    # Summary
    print(f"\n🎯 Test Summary")
    print("=" * 30)
    
    passed_tests = sum(1 for _, passed in test_results if passed)
    total_tests = len(test_results)
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed Tests: {passed_tests}")
    print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
    
    for test_name, passed in test_results:
        print(f"  {test_name}: {'✅ PASSED' if passed else '❌ FAILED'}")
    
    if passed_tests < total_tests:
        print(f"\n❌ ISSUES DETECTED:")
        for test_name, passed in test_results:
            if not passed:
                print(f"  • {test_name} needs fixing")
        
        print(f"\n🔧 RECOMMENDED ACTIONS:")
        if not test_results[0][1]:  # Same Signal SL Update
            print("  1. Fix same signal handlers to update SL instead of just returning False")
        if not test_results[1][1]:  # Position Sizing
            print("  2. Fix position sizing to use actual SL distance, not fixed 1.50")
        if not test_results[2][1]:  # Background Trailing
            print("  3. Fix background trailing monitor startup/shutdown")
        if not test_results[3][1]:  # Acceleration Trailing
            print("  4. Review acceleration-based trailing conditions")
        if not test_results[4][1]:  # Partial Close
            print("  5. Fix partial close system implementation")
    else:
        print(f"\n🎉 ALL SYSTEMS WORKING CORRECTLY!")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    try:
        success = test_comprehensive_system()
        exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
