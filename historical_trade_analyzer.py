#!/usr/bin/env python3
"""
Historical Trade Analyzer
Analyzes trades from temp_logs.txt and creates comprehensive trade log with MT5 data
"""

import pandas as pd
import numpy as np
import re
from datetime import datetime, timedelta
import MetaTrader5 as mt5
import logging
from typing import Dict, List, Optional, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HistoricalTradeAnalyzer:
    """
    Analyzes historical trades from log files and creates comprehensive trade records
    """
    
    def __init__(self, log_file: str = "temp_logs.txt"):
        self.log_file = log_file
        self.trades = []
        
    def parse_log_file(self) -> List[Dict]:
        """Parse the log file and extract trade information"""
        try:
            with open(self.log_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            trades = []
            current_trade = None
            
            for line in lines:
                line = line.strip()
                
                # Extract timestamp
                timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                if not timestamp_match:
                    continue
                
                timestamp = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                
                # Look for trade execution (avoid duplicates by using a set to track processed tickets)
                if "Order placed successfully:" in line:
                    ticket_match = re.search(r'Order placed successfully: (\d+)', line)
                    if ticket_match:
                        ticket = int(ticket_match.group(1))

                        # Check if we already processed this ticket at this timestamp
                        trade_key = f"{ticket}_{timestamp}"
                        if not hasattr(self, '_processed_trades'):
                            self._processed_trades = set()

                        if trade_key not in self._processed_trades:
                            # Find the trade details in previous lines
                            trade_details = self._extract_trade_details(lines, line, timestamp)
                            if trade_details:
                                trade_details['ticket'] = ticket
                                trade_details['entry_timestamp'] = timestamp
                                trades.append(trade_details)
                                self._processed_trades.add(trade_key)
                                logger.info(f"📝 Parsed trade: Ticket {ticket} at {timestamp}")

                # Also look for the alternative format (but avoid duplicates)
                elif "✅ Order placed successfully!" in line:
                    # Look for ticket in surrounding lines
                    ticket = self._find_ticket_in_context(lines, line)
                    if ticket:
                        trade_key = f"{ticket}_{timestamp}"
                        if not hasattr(self, '_processed_trades'):
                            self._processed_trades = set()

                        if trade_key not in self._processed_trades:
                            trade_details = self._extract_trade_details(lines, line, timestamp)
                            if trade_details:
                                trade_details['ticket'] = ticket
                                trade_details['entry_timestamp'] = timestamp
                                trades.append(trade_details)
                                self._processed_trades.add(trade_key)
                                logger.info(f"📝 Parsed trade (alt format): Ticket {ticket} at {timestamp}")
                
                # Look for position closures
                elif "Position" in line and "closed successfully" in line:
                    ticket_match = re.search(r'Position (\d+) closed successfully', line)
                    if ticket_match:
                        ticket = int(ticket_match.group(1))
                        
                        # Find the corresponding trade and update it
                        for trade in trades:
                            if trade['ticket'] == ticket and 'exit_timestamp' not in trade:
                                trade['exit_timestamp'] = timestamp
                                
                                # Extract exit reason from surrounding lines
                                exit_reason = self._extract_exit_reason(lines, line)
                                trade['exit_reason'] = exit_reason
                                break
            
            logger.info(f"📊 Parsed {len(trades)} trades from log file")
            return trades
            
        except Exception as e:
            logger.error(f"❌ Error parsing log file: {e}")
            return []
    
    def _extract_trade_details(self, lines: List[str], current_line: str, timestamp: datetime) -> Optional[Dict]:
        """Extract trade details from surrounding log lines"""
        try:
            # Find the index of current line
            current_index = -1
            for i, line in enumerate(lines):
                if current_line.strip() in line:
                    current_index = i
                    break
            
            if current_index == -1:
                return None
            
            # Look backwards for trade details (usually within 20 lines)
            trade_details = {}
            
            for i in range(max(0, current_index - 20), current_index):
                line = lines[i].strip()
                
                # Extract regime analysis
                regime_match = re.search(r'REGIME ANALYSIS: ML:(\w+)\(([\d.]+)\) \| Regime:(\w+)\(([\d.]+)\) \| Trend:(\w+) \| Logic:([^|]+)', line)
                if regime_match:
                    trade_details.update({
                        'ml_signal': regime_match.group(1),
                        'ml_confidence': float(regime_match.group(2)),
                        'regime': regime_match.group(3),
                        'regime_confidence': float(regime_match.group(4)),
                        'trend_direction': regime_match.group(5),
                        'logic': regime_match.group(6).strip()
                    })
                
                # Extract trade execution details
                if "Signal:" in line and "REGIME-BASED TRADE" in lines[i-1] if i > 0 else False:
                    signal_match = re.search(r'Signal: (\w+) \(([^)]+)\)', line)
                    if signal_match:
                        trade_details['final_decision'] = signal_match.group(1)
                        trade_details['logic'] = signal_match.group(2)
                
                # Extract price and volume
                if "Price:" in line:
                    price_match = re.search(r'Price: ([\d.]+)', line)
                    if price_match:
                        trade_details['entry_price'] = float(price_match.group(1))
                
                if "Lot Size:" in line:
                    volume_match = re.search(r'Lot Size: ([\d.]+)', line)
                    if volume_match:
                        trade_details['volume'] = float(volume_match.group(1))
                
                if "Stop Loss:" in line:
                    sl_match = re.search(r'Stop Loss: ([\d.]+)', line)
                    if sl_match:
                        trade_details['stop_loss'] = float(sl_match.group(1))
                
                if "ATR:" in line:
                    atr_match = re.search(r'ATR: ([\d.]+)', line)
                    if atr_match:
                        trade_details['atr_value'] = float(atr_match.group(1))
            
            # Determine direction from final decision or ML signal
            if 'final_decision' in trade_details:
                trade_details['direction'] = trade_details['final_decision']
            elif 'ml_signal' in trade_details:
                trade_details['direction'] = trade_details['ml_signal']
            
            return trade_details if len(trade_details) > 5 else None  # Ensure we have enough data
            
        except Exception as e:
            logger.error(f"❌ Error extracting trade details: {e}")
            return None
    
    def _extract_exit_reason(self, lines: List[str], current_line: str) -> str:
        """Extract exit reason from surrounding lines"""
        try:
            # Find the index of current line
            current_index = -1
            for i, line in enumerate(lines):
                if current_line.strip() in line:
                    current_index = i
                    break
            
            # Look backwards for exit reason
            for i in range(max(0, current_index - 5), current_index):
                line = lines[i].strip()
                
                if "Opposite signal detected" in line:
                    return "Opposite Signal"
                elif "regime change" in line.lower():
                    return "Regime Change"
                elif "stop loss" in line.lower():
                    return "Stop Loss"
                elif "take profit" in line.lower():
                    return "Take Profit"
            
            return "Manual/Other"
            
        except Exception as e:
            logger.error(f"❌ Error extracting exit reason: {e}")
            return "Unknown"

    def _find_ticket_in_context(self, lines: List[str], current_line: str) -> Optional[int]:
        """Find ticket number in surrounding lines"""
        try:
            # Find the index of current line
            current_index = -1
            for i, line in enumerate(lines):
                if current_line.strip() in line:
                    current_index = i
                    break

            if current_index == -1:
                return None

            # Look backwards and forwards for ticket
            for i in range(max(0, current_index - 10), min(len(lines), current_index + 5)):
                line = lines[i].strip()

                # Look for ticket in various formats
                ticket_patterns = [
                    r'Order placed successfully: (\d+)',
                    r'Position (\d+) modified',
                    r'Position (\d+) closed',
                    r'ticket.*?(\d{8,})',  # 8+ digit numbers
                    r'(\d{8,})'  # Any 8+ digit number
                ]

                for pattern in ticket_patterns:
                    match = re.search(pattern, line)
                    if match:
                        ticket = int(match.group(1))
                        if ticket > 10000000:  # Reasonable ticket number
                            return ticket

            return None

        except Exception as e:
            logger.error(f"❌ Error finding ticket: {e}")
            return None
    
    def get_mt5_trade_data(self, trades: List[Dict]) -> List[Dict]:
        """Get actual trade data from MT5 for accurate results"""
        try:
            if not mt5.initialize():
                logger.error("❌ Failed to initialize MT5")
                return trades

            logger.info("🔄 Fetching MT5 trade data...")

            # Get deals from last 72 hours to ensure we capture all trades including 10/01
            now = datetime.now()
            from_date = now - timedelta(hours=72)

            # Get all deals in the period
            deals = mt5.history_deals_get(from_date, now)
            if deals is None:
                logger.warning("⚠️ No deals found in MT5 history")
                return trades

            deals_df = pd.DataFrame(list(deals), columns=deals[0]._asdict().keys())
            deals_df['time'] = pd.to_datetime(deals_df['time'], unit='s')

            logger.info(f"📊 Found {len(deals_df)} deals in MT5 history")

            # Also get positions history
            positions = mt5.history_orders_get(from_date, now)
            if positions is not None:
                positions_df = pd.DataFrame(list(positions), columns=positions[0]._asdict().keys())
                positions_df['time_setup'] = pd.to_datetime(positions_df['time_setup'], unit='s')
                positions_df['time_done'] = pd.to_datetime(positions_df['time_done'], unit='s')
                logger.info(f"📊 Found {len(positions_df)} positions in MT5 history")
            else:
                positions_df = pd.DataFrame()

            # Match trades with MT5 data using multiple approaches
            for i, trade in enumerate(trades):
                logger.info(f"🔍 Processing trade {i+1}/{len(trades)}: Ticket {trade.get('ticket', 'N/A')}")

                ticket = trade.get('ticket')
                entry_time = trade.get('entry_timestamp')

                if not ticket or not entry_time:
                    logger.warning(f"⚠️ Missing ticket or entry_time for trade {i+1}")
                    continue

                # Convert entry_time to datetime if it's a string
                if isinstance(entry_time, str):
                    try:
                        entry_time = datetime.strptime(entry_time, '%Y-%m-%d %H:%M:%S')
                    except:
                        logger.warning(f"⚠️ Could not parse entry_time: {entry_time}")
                        continue

                # Method 1: Try to find by exact ticket match
                entry_deals = deals_df[deals_df['position_id'] == ticket]

                if len(entry_deals) == 0:
                    # Method 2: Try to find by time proximity (within 5 minutes)
                    time_window = timedelta(minutes=5)
                    nearby_deals = deals_df[
                        (deals_df['time'] >= entry_time - time_window) &
                        (deals_df['time'] <= entry_time + time_window) &
                        (deals_df['symbol'] == 'XAUUSD!')
                    ]

                    if len(nearby_deals) > 0:
                        logger.info(f"📍 Found {len(nearby_deals)} deals near entry time for trade {i+1}")
                        # Take the closest deal by time
                        nearby_deals['time_diff'] = abs((nearby_deals['time'] - entry_time).dt.total_seconds())
                        closest_deal = nearby_deals.loc[nearby_deals['time_diff'].idxmin()]

                        # Update the ticket to match MT5
                        trade['mt5_ticket'] = closest_deal['position_id']
                        ticket = closest_deal['position_id']
                        entry_deals = deals_df[deals_df['position_id'] == ticket]

                if len(entry_deals) > 0:
                    # Find entry and exit deals
                    entry_deal = entry_deals[entry_deals['entry'] == 0].iloc[0] if len(entry_deals[entry_deals['entry'] == 0]) > 0 else None
                    exit_deal = entry_deals[entry_deals['entry'] == 1].iloc[0] if len(entry_deals[entry_deals['entry'] == 1]) > 0 else None

                    if entry_deal is not None:
                        trade['mt5_entry_price'] = entry_deal['price']
                        trade['mt5_entry_time'] = entry_deal['time']
                        trade['mt5_volume'] = entry_deal['volume']
                        trade['mt5_symbol'] = entry_deal['symbol']
                        logger.info(f"✅ Found entry deal for trade {i+1}: {entry_deal['price']} at {entry_deal['time']}")

                    if exit_deal is not None:
                        trade['mt5_exit_price'] = exit_deal['price']
                        trade['mt5_exit_time'] = exit_deal['time']
                        trade['mt5_profit'] = exit_deal['profit']
                        logger.info(f"✅ Found exit deal for trade {i+1}: {exit_deal['price']} at {exit_deal['time']}, P&L: ${exit_deal['profit']}")

                        # Calculate accurate metrics
                        self._calculate_trade_metrics(trade)
                    else:
                        logger.warning(f"⚠️ No exit deal found for trade {i+1} (ticket: {ticket})")
                else:
                    logger.warning(f"⚠️ No deals found for trade {i+1} (ticket: {ticket})")

            # Look for the specific trade the user mentioned (-4.76 loss)
            target_loss = -4.76
            for trade in trades:
                if 'mt5_profit' in trade and trade['mt5_profit'] is not None:
                    if abs(trade['mt5_profit'] - target_loss) < 0.01:  # Within 1 cent
                        logger.info(f"🎯 FOUND TARGET TRADE: Ticket {trade['ticket']} with P&L ${trade['mt5_profit']}")
                        logger.info(f"   Entry Time: {trade.get('mt5_entry_time', 'N/A')}")
                        logger.info(f"   Exit Time: {trade.get('mt5_exit_time', 'N/A')}")

            logger.info("✅ MT5 trade data fetched successfully")
            return trades

        except Exception as e:
            logger.error(f"❌ Error getting MT5 trade data: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return trades
        finally:
            mt5.shutdown()
    
    def _calculate_trade_metrics(self, trade: Dict):
        """Calculate comprehensive trade metrics"""
        try:
            if 'mt5_entry_price' not in trade or 'mt5_exit_price' not in trade:
                return
            
            entry_price = trade['mt5_entry_price']
            exit_price = trade['mt5_exit_price']
            direction = trade.get('direction', 'BUY')
            volume = trade.get('mt5_volume', trade.get('volume', 0.01))
            
            # Calculate profit/loss in pips
            if direction == 'BUY':
                profit_loss_pips = (exit_price - entry_price) * 10  # XAUUSD: 1 pip = 0.1
            else:  # SELL
                profit_loss_pips = (entry_price - exit_price) * 10
            
            # Calculate profit/loss in USD
            profit_loss_usd = trade.get('mt5_profit', profit_loss_pips * volume * 10)
            
            # Calculate duration
            if 'mt5_entry_time' in trade and 'mt5_exit_time' in trade:
                duration = (trade['mt5_exit_time'] - trade['mt5_entry_time']).total_seconds() / 60
                trade['duration_minutes'] = round(duration, 1)
            
            # Calculate max excursion
            max_excursion = self._calculate_max_excursion(trade)
            
            # Update trade with calculated metrics
            trade.update({
                'profit_loss_pips': round(profit_loss_pips, 1),
                'profit_loss_usd': round(profit_loss_usd, 2),
                'win_loss': 'WIN' if profit_loss_pips > 0 else 'LOSS' if profit_loss_pips < 0 else 'BREAKEVEN',
                'max_favorable_pips': max_excursion['max_favorable_pips'],
                'max_adverse_pips': max_excursion['max_adverse_pips']
            })
            
            # Calculate profit factor
            if max_excursion['max_adverse_pips'] > 0:
                trade['profit_factor'] = abs(profit_loss_pips) / max_excursion['max_adverse_pips']
            else:
                trade['profit_factor'] = abs(profit_loss_pips) if profit_loss_pips > 0 else 0
            
        except Exception as e:
            logger.error(f"❌ Error calculating trade metrics: {e}")
    
    def _calculate_max_excursion(self, trade: Dict) -> Dict:
        """Calculate maximum favorable and adverse excursion"""
        try:
            if 'mt5_entry_time' not in trade or 'mt5_exit_time' not in trade:
                return {'max_favorable_pips': 0, 'max_adverse_pips': 0}
            
            # Get M1 data for the trade period
            rates = mt5.copy_rates_range(
                "XAUUSD!", 
                mt5.TIMEFRAME_M1, 
                trade['mt5_entry_time'], 
                trade['mt5_exit_time']
            )
            
            if rates is None or len(rates) == 0:
                return {'max_favorable_pips': 0, 'max_adverse_pips': 0}
            
            entry_price = trade['mt5_entry_price']
            direction = trade.get('direction', 'BUY')
            
            max_favorable = 0
            max_adverse = 0
            
            for rate in rates:
                high = rate[2]  # High price
                low = rate[3]   # Low price
                
                if direction == 'BUY':
                    favorable = (high - entry_price) * 10
                    adverse = (entry_price - low) * 10
                else:  # SELL
                    favorable = (entry_price - low) * 10
                    adverse = (high - entry_price) * 10
                
                max_favorable = max(max_favorable, favorable)
                max_adverse = max(max_adverse, adverse)
            
            return {
                'max_favorable_pips': round(max_favorable, 1),
                'max_adverse_pips': round(max_adverse, 1)
            }
            
        except Exception as e:
            logger.error(f"❌ Error calculating max excursion: {e}")
            return {'max_favorable_pips': 0, 'max_adverse_pips': 0}
    
    def create_comprehensive_csv(self, output_file: str = "historical_trades.csv"):
        """Create comprehensive CSV file with all trade data"""
        try:
            # Parse log file
            trades = self.parse_log_file()
            if not trades:
                logger.error("❌ No trades found in log file")
                return
            
            # Get MT5 data
            trades = self.get_mt5_trade_data(trades)
            
            # Create DataFrame
            df = pd.DataFrame(trades)
            
            # Ensure all required columns exist
            required_columns = [
                'ticket', 'entry_timestamp', 'symbol', 'direction', 'volume', 'entry_price', 'stop_loss',
                'regime', 'regime_confidence', 'trend_direction', 'ml_signal', 'ml_confidence', 
                'final_decision', 'logic', 'atr_value', 'exit_timestamp', 'exit_price', 'exit_reason',
                'profit_loss_pips', 'profit_loss_usd', 'max_favorable_pips', 'max_adverse_pips',
                'duration_minutes', 'win_loss', 'profit_factor'
            ]
            
            for col in required_columns:
                if col not in df.columns:
                    df[col] = None
            
            # Add symbol if missing
            df['symbol'] = df['symbol'].fillna('XAUUSD!')
            
            # Reorder columns
            df = df[required_columns]
            
            # Save to CSV
            df.to_csv(output_file, index=False)
            
            logger.info(f"✅ Created comprehensive trade log: {output_file}")
            logger.info(f"📊 Total trades: {len(df)}")
            
            # Print summary
            self._print_summary(df)
            
        except Exception as e:
            logger.error(f"❌ Error creating comprehensive CSV: {e}")
    
    def _print_summary(self, df: pd.DataFrame):
        """Print trade summary"""
        try:
            print("\n📊 HISTORICAL TRADE ANALYSIS")
            print("=" * 60)
            print(f"Total Trades Parsed: {len(df)}")

            # Show all trades with their status
            print("\n📋 ALL TRADES:")
            for i, row in df.iterrows():
                ticket = row['ticket']
                entry_time = row['entry_timestamp']
                direction = row.get('direction', 'N/A')
                entry_price = row.get('mt5_entry_price', row.get('entry_price', 'N/A'))
                exit_price = row.get('mt5_exit_price', 'N/A')
                profit = row.get('mt5_profit', row.get('profit_loss_usd', 'N/A'))

                status = "CLOSED" if pd.notna(row.get('mt5_exit_time')) else "OPEN/MISSING"

                print(f"  {i+1:2d}. Ticket: {ticket} | {entry_time} | {direction} | Entry: {entry_price} | Exit: {exit_price} | P&L: ${profit} | {status}")

            # Analyze completed trades (use profit data which we have)
            completed_trades = df[pd.notna(df['profit_loss_usd']) | pd.notna(df.get('mt5_profit', pd.Series()))]

            if len(completed_trades) == 0:
                print("\n⚠️ No completed trades found with MT5 data")
                print("=" * 60)
                return

            # Use profit_loss_usd column which we have
            profit_col = 'profit_loss_usd' if 'profit_loss_usd' in completed_trades.columns else 'mt5_profit'
            wins = completed_trades[completed_trades[profit_col] > 0]
            losses = completed_trades[completed_trades[profit_col] < 0]
            breakeven = completed_trades[completed_trades[profit_col] == 0]

            print(f"\n📊 COMPLETED TRADES ANALYSIS:")
            print(f"Completed Trades: {len(completed_trades)}")
            print(f"Wins: {len(wins)} | Losses: {len(losses)} | Breakeven: {len(breakeven)}")
            print(f"Win Rate: {len(wins) / len(completed_trades) * 100:.1f}%")
            print(f"Total P&L: ${completed_trades[profit_col].sum():.2f}")
            print(f"Avg Win: ${wins[profit_col].mean():.2f}" if len(wins) > 0 else "Avg Win: N/A")
            print(f"Avg Loss: ${losses[profit_col].mean():.2f}" if len(losses) > 0 else "Avg Loss: N/A")

            if 'duration_minutes' in completed_trades.columns:
                avg_duration = completed_trades['duration_minutes'].mean()
                if pd.notna(avg_duration):
                    print(f"Avg Duration: {avg_duration:.1f} minutes")

            # Show last few trades
            print(f"\n🔍 LAST 5 TRADES:")
            last_trades = completed_trades.tail(5)
            for i, row in last_trades.iterrows():
                entry_time = row.get('mt5_entry_time', row.get('entry_timestamp', 'N/A'))
                exit_time = row.get('mt5_exit_time', 'N/A')
                profit = row.get(profit_col, 'N/A')
                direction = row.get('direction', 'N/A')

                print(f"  Ticket: {row['ticket']} | {direction} | Entry: {entry_time} | Exit: {exit_time} | P&L: ${profit}")

            print("=" * 60)

        except Exception as e:
            logger.error(f"❌ Error printing summary: {e}")
            import traceback
            logger.error(traceback.format_exc())

def main():
    """Main function"""
    analyzer = HistoricalTradeAnalyzer("temp_logs.txt")
    analyzer.create_comprehensive_csv("historical_trades.csv")

if __name__ == "__main__":
    main()
