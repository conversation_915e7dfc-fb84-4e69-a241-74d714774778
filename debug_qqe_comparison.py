#!/usr/bin/env python3
"""
Debug QQE Calculation to Match TradingView
"""

import sys
import os
sys.path.append('src')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Import MT5 and QQE
from mt5_integration import MT5Manager
from qqe_indicator import QQEIndicator

def debug_qqe_calculation():
    """Debug QQE calculation with real market data"""
    print("🔍 QQE Debug - Comparing with TradingView")
    print("=" * 60)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Connect to MT5
    mt5_manager = MT5Manager()
    if not mt5_manager.connect():
        print("❌ Failed to connect to MT5")
        return
    
    # Get recent data (more periods to catch any recent crossovers)
    df = mt5_manager.get_latest_data("XAUUSD!", "M5", 200)
    if df is None or len(df) < 50:
        print("❌ Failed to get market data")
        return
    
    print(f"📊 Got {len(df)} periods of XAUUSD M5 data")
    print(f"   Latest close: {df['close'].iloc[-1]:.2f}")
    print(f"   Time range: {df.index[0]} to {df.index[-1]}")

    # Check if we have very recent data
    import datetime
    now = datetime.datetime.now()
    last_candle_time = df.index[-1].to_pydatetime()
    time_diff = now - last_candle_time
    print(f"   Time since last candle: {time_diff}")

    if time_diff.total_seconds() > 600:  # More than 10 minutes
        print(f"   ⚠️  Data might be stale - last candle is {time_diff} old")
    
    # Create QQE indicator - FIXED: Match user's TradingView settings
    qqe = QQEIndicator(
        rsi_period=7,  # USER'S TRADINGVIEW SETTING: 7
        rsi_smoothing=5,
        qqe_factor=1.0,  # USER'S TRADINGVIEW SETTING: 1
        threshold=10
    )
    
    # Calculate QQE step by step
    print(f"\n🔄 Calculating QQE indicators...")
    
    # Step 1: RSI
    df['rsi'] = qqe.calculate_rsi(df['close'])
    print(f"   RSI calculated: {df['rsi'].iloc[-1]:.1f}")
    
    # Step 2: RSI MA (smoothed)
    df['rsi_ma'] = qqe.calculate_ema(df['rsi'], 5)
    print(f"   RSI MA calculated: {df['rsi_ma'].iloc[-1]:.1f}")
    
    # Step 3: ATR of RSI
    df['atr_rsi'] = abs(df['rsi_ma'] - df['rsi_ma'].shift(1))
    df['ma_atr_rsi'] = qqe.calculate_ema(df['atr_rsi'], 27)  # Wilders period = 14*2-1 = 27
    df['dar'] = qqe.calculate_ema(df['ma_atr_rsi'], 27) * 4.238
    print(f"   DAR calculated: {df['dar'].iloc[-1]:.3f}")
    
    # Step 4: Calculate bands
    df = qqe.calculate_qqe_bands(df)
    df = qqe.generate_qqe_signals(df)
    
    # Show recent QQE data with Pine Script exact logic
    print(f"\n📈 Recent QQE Values (last 15 periods) - Pine Script Logic:")
    print("Time                 | RSI   | RSI_MA | Long_B | Short_B | TL    | Trend | L_Cnt | S_Cnt | Signal")
    print("-" * 110)

    recent = df.tail(15)
    for idx, row in recent.iterrows():
        time_str = idx.strftime('%m-%d %H:%M')
        rsi = row.get('rsi', 0)
        rsi_ma = row.get('rsi_ma', 0)
        longband = row.get('longband', 0)
        shortband = row.get('shortband', 0)
        tl = row.get('fast_atr_rsi_tl', 0)
        trend = int(row.get('trend', 0))
        long_count = int(row.get('qqe_long_count', 0))
        short_count = int(row.get('qqe_short_count', 0))

        # Pine Script signal logic: only when count == 1
        signal = ""
        if long_count == 1:
            signal = "LONG"
        elif short_count == 1:
            signal = "SHORT"
        else:
            signal = "----"

        # Show condition: FastAtrRsiTL vs RSIndex
        condition = "L" if tl < rsi_ma else "S"

        print(f"{time_str} | {rsi:5.1f} | {rsi_ma:6.1f} | {longband:6.1f} | {shortband:7.1f} | {tl:5.1f} | {trend:5d} | {long_count:5d} | {short_count:5d} | {signal:5s} ({condition})")
    
    # Get current analysis
    qqe_analysis = qqe.get_qqe_analysis(df)
    
    print(f"\n🎯 Current QQE Status:")
    print(f"   RSI: {qqe_analysis.get('rsi', 0):.1f}")
    print(f"   RSI MA: {qqe_analysis.get('rsi_ma', 0):.1f}")
    print(f"   Fast ATR RSI TL: {qqe_analysis.get('fast_atr_rsi_tl', 0):.1f}")
    print(f"   QQE Trend: {int(qqe_analysis.get('trend', 0)):+d}")
    print(f"   QQE Signal: {int(qqe_analysis.get('qqe_signal', 0)):+d}")
    print(f"   QQE Long Count: {qqe_analysis.get('qqe_long_count', 0)}")
    print(f"   QQE Short Count: {qqe_analysis.get('qqe_short_count', 0)}")
    print(f"   Last Signal Type: {qqe_analysis.get('last_signal_type', 'NONE')}")
    
    # Find recent crossovers with Pine Script exact values
    print(f"\n🔄 Recent QQE Crossovers (Pine Script Logic):")
    crossovers = []

    for i in range(1, len(df)):
        long_count = df.iloc[i]['qqe_long_count']
        short_count = df.iloc[i]['qqe_short_count']

        if long_count == 1:
            # Pine Script: qqeLong = QQExlong == 1 ? FastAtrRsiTL[1] - 50 : na
            tl_prev = df.iloc[i-1]['fast_atr_rsi_tl'] if i > 0 else df.iloc[i]['fast_atr_rsi_tl']
            signal_value = tl_prev - 50
            crossovers.append((df.index[i], 'LONG', tl_prev, signal_value))
        elif short_count == 1:
            # Pine Script: qqeShort = QQExshort == 1 ? FastAtrRsiTL[1] - 50 : na
            tl_prev = df.iloc[i-1]['fast_atr_rsi_tl'] if i > 0 else df.iloc[i]['fast_atr_rsi_tl']
            signal_value = tl_prev - 50
            crossovers.append((df.index[i], 'SHORT', tl_prev, signal_value))

    # Show last 5 crossovers
    recent_crossovers = crossovers[-5:] if len(crossovers) >= 5 else crossovers

    if recent_crossovers:
        for time, signal_type, tl_value, signal_value in recent_crossovers:
            print(f"   {time.strftime('%m-%d %H:%M')}: {signal_type} signal (TL[1]: {tl_value:.1f}, Signal: {signal_value:.1f})")
    else:
        print("   No recent crossovers found")

    # Check the very last candle for signals
    print(f"\n🎯 Last Closed Candle Analysis:")
    if len(df) >= 2:
        last_idx = len(df) - 1
        last_row = df.iloc[last_idx]
        prev_row = df.iloc[last_idx - 1]

        print(f"   Time: {df.index[last_idx].strftime('%m-%d %H:%M')}")
        print(f"   RSI MA: {last_row['rsi_ma']:.1f}")
        print(f"   Fast ATR RSI TL: {last_row['fast_atr_rsi_tl']:.1f}")
        print(f"   Long Count: {int(last_row['qqe_long_count'])}")
        print(f"   Short Count: {int(last_row['qqe_short_count'])}")

        # Check Pine Script signal conditions
        if int(last_row['qqe_long_count']) == 1:
            signal_value = prev_row['fast_atr_rsi_tl'] - 50
            print(f"   ✅ PINE SCRIPT LONG SIGNAL: {signal_value:.1f}")
        elif int(last_row['qqe_short_count']) == 1:
            signal_value = prev_row['fast_atr_rsi_tl'] - 50
            print(f"   ✅ PINE SCRIPT SHORT SIGNAL: {signal_value:.1f}")
        else:
            print(f"   ❌ NO PINE SCRIPT SIGNAL (not a fresh crossover)")

        # Show the condition
        condition = "LONG" if last_row['fast_atr_rsi_tl'] < last_row['rsi_ma'] else "SHORT"
        print(f"   Condition: TL ({last_row['fast_atr_rsi_tl']:.1f}) {'<' if condition == 'LONG' else '>'} RSI_MA ({last_row['rsi_ma']:.1f}) = {condition}")
    
    # Show comparison with TradingView expectations
    print(f"\n📊 TradingView Comparison:")
    print(f"   You reported: Last signal was SHORT")
    print(f"   Our calculation: Last signal was {qqe_analysis.get('last_signal_type', 'NONE')}")
    print(f"   Current trend: {'BULLISH' if qqe_analysis.get('qqe_long_count', 0) > 0 else 'BEARISH' if qqe_analysis.get('qqe_short_count', 0) > 0 else 'NEUTRAL'}")
    
    # Check if RSI MA is above or below TL
    rsi_ma = qqe_analysis.get('rsi_ma', 0)
    tl = qqe_analysis.get('fast_atr_rsi_tl', 0)
    print(f"   RSI MA vs TL: {rsi_ma:.1f} {'>' if rsi_ma > tl else '<'} {tl:.1f} = {'LONG' if rsi_ma > tl else 'SHORT'} condition")
    
    mt5_manager.disconnect()
    print(f"\n✅ QQE Debug Complete!")

if __name__ == "__main__":
    try:
        debug_qqe_calculation()
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
