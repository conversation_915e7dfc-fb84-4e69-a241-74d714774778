#!/usr/bin/env python3
"""
Test script to verify pending order functionality
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager
from fixed_live_trader import FixedLiveTrader

def test_pending_order_logic():
    """Test the pending order logic"""
    print("📋 Testing Pending Order Logic...")
    
    # Create trader instance
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test pending order methods
    confirmation_candle_data = {
        'high': 4200.50,
        'low': 4198.20,
        'close': 4199.10
    }
    
    print("📊 Testing pending order methods:")
    print(f"   Confirmation candle: High={confirmation_candle_data['high']}, Low={confirmation_candle_data['low']}, Close={confirmation_candle_data['close']}")
    
    # Test BUY STOP order
    print("\n🟢 Testing BUY STOP order placement:")
    try:
        result = trader.place_pending_order_from_confirmation_candle('BUY', confirmation_candle_data, 0.01, 1.0)
        if result is None:
            print("   ✅ Method handled no MT5 connection gracefully")
        else:
            print(f"   ✅ Method returned: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test SELL STOP order
    print("\n🔴 Testing SELL STOP order placement:")
    try:
        result = trader.place_pending_order_from_confirmation_candle('SELL', confirmation_candle_data, 0.01, 1.0)
        if result is None:
            print("   ✅ Method handled no MT5 connection gracefully")
        else:
            print(f"   ✅ Method returned: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test order management methods
    print("\n🔧 Testing order management methods:")
    try:
        trader.check_and_remove_expired_pending_orders()
        print("   ✅ Expired order checking method executed")
        
        trader.check_pending_orders_filled()
        print("   ✅ Filled order checking method executed")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n✅ Pending order logic test completed!")

def test_mt5_manager_improvements():
    """Test the MT5Manager improvements"""
    print("\n🔧 Testing MT5Manager Improvements...")
    
    # Create MT5Manager instance
    mt5_manager = MT5Manager("XAUUSD!")
    
    # Test parameter validation
    print("📊 Testing parameter validation:")
    
    # Test invalid volume
    result = mt5_manager.place_order("XAUUSD!", "BUY_STOP", 0, 4200.0, sl=4190.0)
    if result is None:
        print("   ✅ Invalid volume (0) rejected correctly")
    
    # Test invalid price
    result = mt5_manager.place_order("XAUUSD!", "BUY_STOP", 0.01, 0, sl=4190.0)
    if result is None:
        print("   ✅ Invalid price (0) rejected correctly")
    
    print("\n✅ MT5Manager improvements test completed!")

def main():
    """Run all pending order tests"""
    print("🧪 PENDING ORDER FUNCTIONALITY TEST")
    print("=" * 50)
    
    # Test 1: Pending order logic
    test_pending_order_logic()
    
    # Test 2: MT5Manager improvements
    test_mt5_manager_improvements()
    
    print("\n📊 SUMMARY")
    print("=" * 30)
    print("✅ All pending order tests completed!")
    print("🎯 The system should now handle pending orders correctly")
    print("📝 Improvements made:")
    print("   • Fixed data type issues (numpy float -> Python float)")
    print("   • Added parameter validation")
    print("   • Added symbol validation and selection")
    print("   • Added retry logic with different filling types")
    print("   • Added better error reporting with MT5 last_error()")

if __name__ == "__main__":
    main()
