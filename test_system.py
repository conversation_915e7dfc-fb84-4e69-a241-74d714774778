"""
Comprehensive system testing script
"""
import sys
import unittest
import numpy as np
import pandas as pd
from pathlib import Path
import logging

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from config.config import *
from src.data_manager import DataManager
from src.feature_engineering import FeatureEngineer
from src.data_preprocessing import DataPreprocessor
from src.lstm_model import LSTMTradingModel
from src.risk_management import RiskManager
from src.trading_logic import TradingEngine, SignalType

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestDataManager(unittest.TestCase):
    """Test data management functionality"""
    
    def setUp(self):
        self.data_manager = DataManager()
    
    def test_load_historical_data(self):
        """Test loading historical data"""
        data = self.data_manager.load_historical_data()
        
        self.assertIsNotNone(data, "Historical data should not be None")
        self.assertGreater(len(data), 0, "Historical data should not be empty")
        
        # Check required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            self.assertIn(col, data.columns, f"Column {col} should be present")
        
        # Check data types
        self.assertTrue(pd.api.types.is_datetime64_any_dtype(data.index), "Index should be datetime")
        
        logger.info(f"✓ Data loading test passed. Shape: {data.shape}")
    
    def test_data_validation(self):
        """Test data validation"""
        data = self.data_manager.load_historical_data()
        
        if data is not None:
            # Check for NaN values
            nan_count = data.isnull().sum().sum()
            self.assertEqual(nan_count, 0, "Data should not contain NaN values after validation")
            
            # Check OHLC relationships
            invalid_ohlc = (
                (data['high'] < data['low']) |
                (data['high'] < data['open']) |
                (data['high'] < data['close']) |
                (data['low'] > data['open']) |
                (data['low'] > data['close'])
            ).sum()
            
            self.assertEqual(invalid_ohlc, 0, "OHLC relationships should be valid")
            
            logger.info("✓ Data validation test passed")

class TestFeatureEngineering(unittest.TestCase):
    """Test feature engineering functionality"""
    
    def setUp(self):
        self.feature_engineer = FeatureEngineer()
        # Create sample data
        dates = pd.date_range('2023-01-01', periods=1000, freq='5T')
        np.random.seed(42)
        
        # Generate realistic OHLCV data
        close_prices = 2000 + np.cumsum(np.random.randn(1000) * 0.1)
        
        self.sample_data = pd.DataFrame({
            'open': close_prices + np.random.randn(1000) * 0.05,
            'high': close_prices + np.abs(np.random.randn(1000) * 0.1),
            'low': close_prices - np.abs(np.random.randn(1000) * 0.1),
            'close': close_prices,
            'volume': np.random.randint(100, 1000, 1000)
        }, index=dates)
        
        # Ensure OHLC relationships are valid
        self.sample_data['high'] = np.maximum.reduce([
            self.sample_data['open'], self.sample_data['high'], 
            self.sample_data['low'], self.sample_data['close']
        ])
        self.sample_data['low'] = np.minimum.reduce([
            self.sample_data['open'], self.sample_data['high'], 
            self.sample_data['low'], self.sample_data['close']
        ])
    
    def test_create_technical_indicators(self):
        """Test technical indicator creation"""
        features_data = self.feature_engineer.create_technical_indicators(self.sample_data)
        
        self.assertIsNotNone(features_data, "Features data should not be None")
        self.assertGreater(len(features_data.columns), len(self.sample_data.columns), 
                          "Should create additional feature columns")
        
        # Check for specific indicators
        expected_indicators = ['atr', 'rsi', 'ema_fast', 'ema_slow', 'macd']
        for indicator in expected_indicators:
            self.assertTrue(any(indicator in col for col in features_data.columns),
                          f"Should contain {indicator} indicator")
        
        logger.info(f"✓ Feature engineering test passed. Created {len(features_data.columns) - len(self.sample_data.columns)} features")
    
    def test_create_target_variable(self):
        """Test target variable creation"""
        features_data = self.feature_engineer.create_technical_indicators(self.sample_data)
        target_data = self.feature_engineer.create_target_variable(features_data)
        
        self.assertIn('target', target_data.columns, "Should create target column")
        self.assertIn('future_return', target_data.columns, "Should create future_return column")
        
        # Check target distribution
        target_values = target_data['target'].unique()
        self.assertTrue(len(target_values) >= 2, "Should have multiple target classes")
        
        logger.info("✓ Target variable creation test passed")

class TestDataPreprocessing(unittest.TestCase):
    """Test data preprocessing functionality"""
    
    def setUp(self):
        self.preprocessor = DataPreprocessor()
        
        # Create sample data with features
        dates = pd.date_range('2023-01-01', periods=2000, freq='5T')
        np.random.seed(42)
        
        # Generate sample data with features
        n_features = 20
        feature_data = np.random.randn(2000, n_features)
        
        feature_columns = [f'feature_{i}' for i in range(n_features)]
        
        self.sample_data = pd.DataFrame(feature_data, columns=feature_columns, index=dates)
        self.sample_data['target'] = np.random.randint(0, 3, 2000)  # 3 classes
        
        # Add OHLCV columns
        close_prices = 2000 + np.cumsum(np.random.randn(2000) * 0.1)
        self.sample_data['open'] = close_prices + np.random.randn(2000) * 0.05
        self.sample_data['high'] = close_prices + np.abs(np.random.randn(2000) * 0.1)
        self.sample_data['low'] = close_prices - np.abs(np.random.randn(2000) * 0.1)
        self.sample_data['close'] = close_prices
        self.sample_data['volume'] = np.random.randint(100, 1000, 2000)
    
    def test_prepare_dataset(self):
        """Test dataset preparation"""
        dataset = self.preprocessor.prepare_dataset(self.sample_data)
        
        self.assertIsNotNone(dataset, "Dataset should not be None")
        self.assertIn('train', dataset, "Should contain training data")
        self.assertIn('validation', dataset, "Should contain validation data")
        self.assertIn('test', dataset, "Should contain test data")
        
        X_train, y_train = dataset['train']
        X_val, y_val = dataset['validation']
        X_test, y_test = dataset['test']
        
        # Check shapes
        self.assertEqual(len(X_train.shape), 3, "Training X should be 3D")
        self.assertEqual(len(y_train.shape), 1, "Training y should be 1D")
        
        self.assertGreater(len(X_train), 0, "Training set should not be empty")
        self.assertGreater(len(X_val), 0, "Validation set should not be empty")
        self.assertGreater(len(X_test), 0, "Test set should not be empty")
        
        logger.info(f"✓ Dataset preparation test passed. Train: {X_train.shape}, Val: {X_val.shape}, Test: {X_test.shape}")

class TestLSTMModel(unittest.TestCase):
    """Test LSTM model functionality"""
    
    def setUp(self):
        self.input_shape = (60, 20)  # sequence_length, num_features
        self.num_classes = 3
        self.model = LSTMTradingModel(self.input_shape, self.num_classes)
    
    def test_build_model(self):
        """Test model building"""
        keras_model = self.model.build_model()
        
        self.assertIsNotNone(keras_model, "Model should not be None")
        self.assertEqual(keras_model.input_shape[1:], self.input_shape, "Input shape should match")
        self.assertEqual(keras_model.output_shape[1], self.num_classes, "Output shape should match")
        
        logger.info("✓ Model building test passed")
    
    def test_model_prediction(self):
        """Test model prediction"""
        keras_model = self.model.build_model()
        
        # Create dummy input
        X_dummy = np.random.randn(10, *self.input_shape)
        
        # Test prediction
        predictions = self.model.predict(X_dummy)
        probabilities = self.model.predict_proba(X_dummy)
        
        self.assertEqual(len(predictions), 10, "Should predict for all samples")
        self.assertEqual(probabilities.shape, (10, self.num_classes), "Probabilities shape should match")
        
        logger.info("✓ Model prediction test passed")

class TestRiskManagement(unittest.TestCase):
    """Test risk management functionality"""
    
    def setUp(self):
        self.risk_manager = RiskManager()
    
    def test_position_size_calculation(self):
        """Test position size calculation"""
        position_size = self.risk_manager.calculate_optimal_position_size(
            account_balance=10000,
            entry_price=2000.0,
            stop_loss=1990.0,
            confidence=0.8
        )
        
        self.assertGreater(position_size, 0, "Position size should be positive")
        self.assertLess(position_size, 10, "Position size should be reasonable")
        
        logger.info(f"✓ Position size calculation test passed. Size: {position_size}")
    
    def test_trade_risk_validation(self):
        """Test trade risk validation"""
        account_info = {
            'balance': 10000,
            'equity': 10000,
            'free_margin': 5000,
            'margin_level': 1000
        }
        
        is_valid, reason = self.risk_manager.validate_trade_risk(
            account_info=account_info,
            position_size=0.1,
            entry_price=2000.0,
            stop_loss=1990.0
        )
        
        self.assertIsInstance(is_valid, bool, "Should return boolean")
        self.assertIsInstance(reason, str, "Should return reason string")
        
        logger.info(f"✓ Risk validation test passed. Valid: {is_valid}, Reason: {reason}")

class TestTradingLogic(unittest.TestCase):
    """Test trading logic functionality"""
    
    def setUp(self):
        # Mock MT5Manager for testing
        class MockMT5Manager:
            def __init__(self):
                self.connected = True
            
            def get_account_info(self):
                return {
                    'balance': 10000,
                    'equity': 10000,
                    'free_margin': 5000,
                    'margin_level': 1000
                }
            
            def calculate_position_size(self, risk_amount, entry_price, stop_loss):
                return 0.1
            
            def send_order(self, **kwargs):
                return {'order': 12345, 'price': kwargs.get('price', 2000.0)}
            
            def get_positions(self):
                return []
        
        self.trading_engine = TradingEngine(MockMT5Manager())
    
    def test_process_prediction(self):
        """Test prediction processing"""
        prediction_result = {
            'prediction_class': 1,  # BUY
            'confidence': 0.8,
            'prediction_probabilities': [0.1, 0.8, 0.1],
            'signal_strength': 'STRONG',
            'market_context': {
                'market_regime': 'TRENDING',
                'trend_direction': 'UP',
                'rsi_level': 45
            }
        }
        
        market_data = {
            'close': 2000.0,
            'atr': 10.0
        }
        
        decision = self.trading_engine.process_prediction(prediction_result, market_data)
        
        if decision:  # Decision might be None due to filtering
            self.assertEqual(decision.signal, SignalType.BUY, "Should generate BUY signal")
            self.assertGreater(decision.position_size, 0, "Position size should be positive")
            self.assertNotEqual(decision.stop_loss, decision.entry_price, "Stop loss should differ from entry")
        
        logger.info("✓ Prediction processing test passed")

def run_comprehensive_test():
    """Run comprehensive system test"""
    logger.info("Starting comprehensive system test...")

    # Create test suite
    test_suite = unittest.TestSuite()

    # Add test cases
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestDataManager))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestFeatureEngineering))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestDataPreprocessing))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestLSTMModel))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestRiskManagement))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestTradingLogic))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # Print summary
    if result.wasSuccessful():
        logger.info("🎉 All tests passed successfully!")
        return True
    else:
        logger.error(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
