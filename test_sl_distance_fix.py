#!/usr/bin/env python3
"""
Test script to verify the sl_distance variable fix
Tests the case where position has no stop_loss set
"""

import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader

def test_no_stop_loss_case():
    """Test the case where position has no stop loss (the error case)"""
    print("🧪 TESTING SL_DISTANCE FIX - No Stop Loss Case")
    print("=" * 60)
    
    trader = FixedLiveTrader("XAUUSD!")
    
    # Mock position WITHOUT stop_loss (this was causing the error)
    trader.current_position = {
        'ticket': 12345,
        'type': 'BUY',
        'price': 2000.00,  # Entry price
        'volume': 0.10,
        # 'stop_loss': None,  # NO STOP LOSS - this was causing the error
        'take_profit': 2030.00
    }
    
    # Mock MT5 manager
    class MockMT5Manager:
        def get_symbol_info_tick(self, symbol):
            return {
                'bid': 1990.0,  # 10 points loss
                'ask': 1990.5
            }
        
        def get_contract_size(self, symbol):
            return 100
    
    trader.mt5_manager = MockMT5Manager()
    
    print("📊 POSITION SETUP:")
    print(f"   Entry Price: {trader.current_position['price']:.2f}")
    print(f"   Stop Loss: {trader.current_position.get('stop_loss', 'NOT SET')}")
    print(f"   Current Price: 1990.00 (10 points loss)")
    print()
    
    try:
        # This was causing the error before the fix
        is_profitable, profit_points, profit_percentage, position_risk_value = trader.get_position_profit_loss_info()
        
        print("✅ SUCCESS - No error occurred!")
        print(f"   Is Profitable: {is_profitable}")
        print(f"   Profit Points: {profit_points:+.5f}")
        print(f"   Profit Percentage: {profit_percentage:+.1f}%")
        print(f"   Position Risk Value: {position_risk_value:.2f}")
        
        # Test trailing allowance
        should_allow, reason = trader.should_allow_trailing("CANDLE")
        print(f"\n📋 CANDLE TRAILING:")
        print(f"   Should Allow: {should_allow}")
        print(f"   Reason: {reason}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR STILL EXISTS: {e}")
        return False

def test_with_stop_loss_case():
    """Test the normal case where position has stop loss"""
    print("\n🧪 TESTING SL_DISTANCE FIX - With Stop Loss Case")
    print("=" * 60)
    
    trader = FixedLiveTrader("XAUUSD!")
    
    # Mock position WITH stop_loss (normal case)
    trader.current_position = {
        'ticket': 12345,
        'type': 'BUY',
        'price': 2000.00,  # Entry price
        'volume': 0.10,
        'stop_loss': 1970.00,  # 30 points SL
        'take_profit': 2030.00
    }
    
    # Mock MT5 manager
    class MockMT5Manager:
        def get_symbol_info_tick(self, symbol):
            return {
                'bid': 1990.0,  # 10 points loss
                'ask': 1990.5
            }
        
        def get_contract_size(self, symbol):
            return 100
    
    trader.mt5_manager = MockMT5Manager()
    
    print("📊 POSITION SETUP:")
    print(f"   Entry Price: {trader.current_position['price']:.2f}")
    print(f"   Stop Loss: {trader.current_position['stop_loss']:.2f}")
    print(f"   Current Price: 1990.00 (10 points loss)")
    print()
    
    try:
        is_profitable, profit_points, profit_percentage, position_risk_value = trader.get_position_profit_loss_info()
        
        print("✅ SUCCESS - Normal case working!")
        print(f"   Is Profitable: {is_profitable}")
        print(f"   Profit Points: {profit_points:+.5f}")
        print(f"   Profit Percentage: {profit_percentage:+.1f}%")
        print(f"   Position Risk Value: {position_risk_value:.2f}")
        
        # Test trailing allowance
        should_allow, reason = trader.should_allow_trailing("CANDLE")
        print(f"\n📋 CANDLE TRAILING:")
        print(f"   Should Allow: {should_allow}")
        print(f"   Reason: {reason}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    print("🚀 TESTING SL_DISTANCE VARIABLE FIX")
    print("=" * 60)
    
    # Test the error case (no stop loss)
    no_sl_test = test_no_stop_loss_case()
    
    # Test the normal case (with stop loss)
    with_sl_test = test_with_stop_loss_case()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS:")
    print(f"   No Stop Loss Test: {'✅ PASSED' if no_sl_test else '❌ FAILED'}")
    print(f"   With Stop Loss Test: {'✅ PASSED' if with_sl_test else '❌ FAILED'}")
    
    if no_sl_test and with_sl_test:
        print("\n🎯 SL_DISTANCE FIX SUCCESSFUL! No more variable errors! 🚀")
    else:
        print("\n⚠️ Some tests failed - please review the fix.")
