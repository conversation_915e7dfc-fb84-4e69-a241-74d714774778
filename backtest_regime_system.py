#!/usr/bin/env python3
"""
Backtest Regime-Based Trading System
Test the regime detection and signal reversal logic
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager
from regime_based_trader import RegimeDetector, RegimeBasedTrader

class RegimeBacktester:
    """Backtest the regime-based system"""
    
    def __init__(self, initial_balance=10000):
        self.mt5_manager = MT5Manager()
        self.regime_detector = RegimeDetector()
        self.initial_balance = initial_balance
        
        # Load model components
        import joblib
        try:
            self.model = joblib.load('models_fixed/xgboost_model.pkl')
            self.scaler = joblib.load('models_fixed/feature_scaler.pkl')
            self.selected_features = joblib.load('models_fixed/selected_features.pkl')
            print("✅ Model loaded successfully")
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            self.model = None
    
    def create_technical_indicators(self, df):
        """Create technical indicators (same as regime trader)"""
        df = df.copy()
        
        # Basic price features
        df['return_1'] = df['close'].pct_change(1)
        df['return_3'] = df['close'].pct_change(3)
        df['return_5'] = df['close'].pct_change(5)
        
        # Volatility
        df['high_low_pct'] = (df['high'] - df['low']) / df['close']
        df['close_open_pct'] = (df['close'] - df['open']) / df['open']
        
        # Moving averages
        df['sma_5'] = df['close'].rolling(5).mean()
        df['sma_10'] = df['close'].rolling(10).mean()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['sma_50'] = df['close'].rolling(50).mean()
        
        # Price relative to moving averages
        df['price_sma5_ratio'] = df['close'] / df['sma_5']
        df['price_sma20_ratio'] = df['close'] / df['sma_20']
        df['price_sma50_ratio'] = df['close'] / df['sma_50']
        
        # Bollinger Bands
        bb_period = 20
        bb_std = 2
        df['bb_middle'] = df['close'].rolling(bb_period).mean()
        bb_rolling_std = df['close'].rolling(bb_period).std()
        df['bb_upper'] = df['bb_middle'] + (bb_rolling_std * bb_std)
        df['bb_lower'] = df['bb_middle'] - (bb_rolling_std * bb_std)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # RSI
        def calculate_rsi(prices, period=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        df['rsi'] = calculate_rsi(df['close'])
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Rolling min/max
        df['highest_high_5'] = df['high'].rolling(5).max()
        df['lowest_low_5'] = df['low'].rolling(5).min()
        df['highest_high_10'] = df['high'].rolling(10).max()
        df['lowest_low_10'] = df['low'].rolling(10).min()
        df['highest_high_20'] = df['high'].rolling(20).max()
        df['lowest_low_20'] = df['low'].rolling(20).min()
        
        # Price position in recent range
        df['price_position_5'] = (df['close'] - df['lowest_low_5']) / (df['highest_high_5'] - df['lowest_low_5'])
        df['price_position_20'] = (df['close'] - df['lowest_low_20']) / (df['highest_high_20'] - df['lowest_low_20'])
        
        # Momentum indicators
        df['momentum_3'] = df['close'] / df['close'].shift(3)
        df['momentum_5'] = df['close'] / df['close'].shift(5)
        df['momentum_10'] = df['close'] / df['close'].shift(10)
        
        # Stochastic %K
        def calculate_stochastic(high, low, close, k_period=14):
            lowest_low = low.rolling(k_period).min()
            highest_high = high.rolling(k_period).max()
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            return k_percent
        
        df['stoch_k'] = calculate_stochastic(df['high'], df['low'], df['close'])
        df['stoch_d'] = df['stoch_k'].rolling(3).mean()
        
        # Williams %R
        df['williams_r'] = -100 * ((df['highest_high_20'] - df['close']) / (df['highest_high_20'] - df['lowest_low_20']))
        
        # Average True Range (ATR)
        df['tr1'] = df['high'] - df['low']
        df['tr2'] = abs(df['high'] - df['close'].shift(1))
        df['tr3'] = abs(df['low'] - df['close'].shift(1))
        df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
        df['atr'] = df['true_range'].rolling(14).mean()
        df['atr_ratio'] = df['atr'] / df['close']
        
        # Clean up
        df = df.drop(['tr1', 'tr2', 'tr3'], axis=1, errors='ignore')
        
        return df
    
    def get_model_prediction(self, df, index):
        """Get model prediction for specific index"""
        try:
            if self.model is None:
                return None, 0, 0.5
            
            # Get features for this index
            features = df[self.selected_features].iloc[index]
            features = features.fillna(features.median())
            
            # Scale features
            features_scaled = self.scaler.transform(features.values.reshape(1, -1))
            
            # Make prediction
            pred_proba = self.model.predict_proba(features_scaled)[0, 1]
            confidence = abs(pred_proba - 0.5) * 2
            
            # Determine raw signal
            raw_signal = "BUY" if pred_proba > 0.5 else "SELL"
            
            return raw_signal, confidence, pred_proba
            
        except Exception as e:
            return None, 0, 0.5
    
    def apply_regime_logic(self, raw_signal, regime):
        """Apply regime-based signal modification"""
        if regime == "RANGING":
            return raw_signal, "FOLLOW"
        elif regime == "TRENDING":
            return "SELL" if raw_signal == "BUY" else "BUY", "REVERSE"
        else:  # TRANSITIONAL
            return None, "NO_TRADE"
    
    def run_regime_backtest(self, bars=3000):
        """Run comprehensive regime-based backtest"""
        print("🚀 REGIME-BASED SYSTEM BACKTEST")
        print("=" * 60)
        
        # Get data
        if not self.mt5_manager.connect():
            print("❌ Failed to connect to MT5")
            return
        
        df = self.mt5_manager.get_latest_data("XAUUSD!", "M5", bars)
        if df is None or len(df) < 1000:
            print("❌ Insufficient data")
            return
        
        print(f"📊 Data: {len(df)} bars from {df.index[0]} to {df.index[-1]}")
        
        # Create indicators
        df = self.create_technical_indicators(df)
        df = self.regime_detector.calculate_regime_indicators(df)
        
        # Initialize tracking
        balance = self.initial_balance
        trades = []
        current_position = None
        current_regime = None
        
        # Performance tracking
        equity_curve = []
        regime_changes = 0
        regime_stats = {"TRENDING": 0, "RANGING": 0, "TRANSITIONAL": 0}
        
        # Start backtest from index 150 (after indicators are calculated)
        for i in range(150, len(df)):
            current_bar = df.iloc[i]
            current_time = df.index[i]
            
            # Update equity curve
            equity_curve.append(balance)
            
            # Detect regime
            regime, regime_conf, regime_details = self.regime_detector.detect_regime(df.iloc[:i+1])
            regime_stats[regime] += 1
            
            # Check for regime change
            if current_regime != regime:
                if current_regime is not None:
                    regime_changes += 1
                    # Close position on regime change
                    if current_position:
                        exit_price = current_bar['close']
                        if current_position['direction'] == 'BUY':
                            pnl = (exit_price - current_position['entry_price']) * current_position['position_size'] * 100
                        else:
                            pnl = (current_position['entry_price'] - exit_price) * current_position['position_size'] * 100
                        
                        balance += pnl
                        
                        trades.append({
                            'entry_time': current_position['entry_time'],
                            'exit_time': current_time,
                            'direction': current_position['direction'],
                            'entry_price': current_position['entry_price'],
                            'exit_price': exit_price,
                            'pnl': pnl,
                            'exit_reason': 'Regime Change',
                            'regime': current_regime,
                            'new_regime': regime
                        })
                        
                        current_position = None
                
                current_regime = regime
            
            # Check for exit conditions (stop loss)
            if current_position:
                exit_trade = False
                exit_reason = ""
                exit_price = current_bar['close']
                
                if current_position['direction'] == 'BUY':
                    if current_bar['low'] <= current_position['stop_loss']:
                        exit_trade = True
                        exit_reason = "Stop Loss"
                        exit_price = current_position['stop_loss']
                else:  # SELL
                    if current_bar['high'] >= current_position['stop_loss']:
                        exit_trade = True
                        exit_reason = "Stop Loss"
                        exit_price = current_position['stop_loss']
                
                if exit_trade:
                    if current_position['direction'] == 'BUY':
                        pnl = (exit_price - current_position['entry_price']) * current_position['position_size'] * 100
                    else:
                        pnl = (current_position['entry_price'] - exit_price) * current_position['position_size'] * 100
                    
                    balance += pnl
                    
                    trades.append({
                        'entry_time': current_position['entry_time'],
                        'exit_time': current_time,
                        'direction': current_position['direction'],
                        'entry_price': current_position['entry_price'],
                        'exit_price': exit_price,
                        'pnl': pnl,
                        'exit_reason': exit_reason,
                        'regime': regime
                    })
                    
                    current_position = None
            
            # Check for new entry signals (only if no current position)
            if current_position is None:
                raw_signal, confidence, pred_proba = self.get_model_prediction(df, i)
                
                if raw_signal and confidence >= 0.30:  # Min confidence threshold
                    final_signal, logic = self.apply_regime_logic(raw_signal, regime)
                    
                    if final_signal:  # Valid signal after regime logic
                        entry_price = current_bar['close']
                        atr_value = current_bar['atr']
                        
                        if pd.notna(atr_value) and atr_value > 0:
                            # Calculate position size (4% risk, 1 ATR stop)
                            risk_amount = balance * 0.04
                            stop_distance = atr_value * 1.0
                            # For XAUUSD: 1 lot = 100 ounces, so risk per lot = stop_distance * 100
                            risk_per_lot = stop_distance * 100
                            position_size = risk_amount / risk_per_lot  # This gives lot size
                            position_size = max(0.01, min(position_size, 10.0))  # Limit position size
                            
                            # Set stop loss
                            if final_signal == 'BUY':
                                stop_loss = entry_price - atr_value
                            else:
                                stop_loss = entry_price + atr_value
                            
                            current_position = {
                                'entry_time': current_time,
                                'entry_price': entry_price,
                                'direction': final_signal,
                                'position_size': position_size,
                                'stop_loss': stop_loss,
                                'raw_signal': raw_signal,
                                'logic': logic,
                                'confidence': confidence,
                                'regime': regime
                            }
        
        # Close final position if exists
        if current_position:
            exit_price = df.iloc[-1]['close']
            if current_position['direction'] == 'BUY':
                pnl = (exit_price - current_position['entry_price']) * current_position['position_size'] * 100
            else:
                pnl = (current_position['entry_price'] - exit_price) * current_position['position_size'] * 100
            
            balance += pnl
            
            trades.append({
                'entry_time': current_position['entry_time'],
                'exit_time': df.index[-1],
                'direction': current_position['direction'],
                'entry_price': current_position['entry_price'],
                'exit_price': exit_price,
                'pnl': pnl,
                'exit_reason': 'End of Data',
                'regime': current_position['regime']
            })
        
        # Analyze results
        self.analyze_regime_results(trades, equity_curve, balance, regime_stats, regime_changes)
        
        self.mt5_manager.disconnect()
        return trades
    
    def analyze_regime_results(self, trades, equity_curve, final_balance, regime_stats, regime_changes):
        """Analyze regime-based backtest results"""
        print("\n📊 REGIME-BASED BACKTEST RESULTS")
        print("=" * 60)
        
        if not trades:
            print("❌ No trades generated")
            return
        
        trades_df = pd.DataFrame(trades)
        
        # Basic performance
        total_return = (final_balance - self.initial_balance) / self.initial_balance * 100
        total_trades = len(trades)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        win_rate = winning_trades / total_trades * 100
        
        print(f"💰 Initial Balance: ${self.initial_balance:,.2f}")
        print(f"💰 Final Balance: ${final_balance:,.2f}")
        print(f"📈 Total Return: {total_return:.2f}%")
        print(f"📊 Total Trades: {total_trades}")
        print(f"🎯 Win Rate: {win_rate:.1f}%")
        print(f"🔄 Regime Changes: {regime_changes}")
        
        # Regime distribution
        total_bars = sum(regime_stats.values())
        print(f"\n📊 REGIME DISTRIBUTION:")
        for regime, count in regime_stats.items():
            pct = count / total_bars * 100
            print(f"   {regime}: {pct:.1f}% ({count:,} bars)")
        
        # Performance by regime
        print(f"\n📊 PERFORMANCE BY REGIME:")
        for regime in ['TRENDING', 'RANGING', 'TRANSITIONAL']:
            regime_trades = trades_df[trades_df['regime'] == regime]
            if len(regime_trades) > 0:
                regime_pnl = regime_trades['pnl'].sum()
                regime_win_rate = len(regime_trades[regime_trades['pnl'] > 0]) / len(regime_trades) * 100
                print(f"   {regime}: {len(regime_trades)} trades, ${regime_pnl:.2f} P&L, {regime_win_rate:.1f}% win rate")
        
        # Signal logic analysis
        print(f"\n📊 SIGNAL LOGIC ANALYSIS:")
        if 'logic' in trades_df.columns:
            for logic in ['FOLLOW', 'REVERSE']:
                logic_trades = trades_df[trades_df.get('logic', '') == logic]
                if len(logic_trades) > 0:
                    logic_pnl = logic_trades['pnl'].sum()
                    logic_win_rate = len(logic_trades[logic_trades['pnl'] > 0]) / len(logic_trades) * 100
                    print(f"   {logic}: {len(logic_trades)} trades, ${logic_pnl:.2f} P&L, {logic_win_rate:.1f}% win rate")
        
        # Exit reasons
        print(f"\n📊 EXIT REASONS:")
        exit_reasons = trades_df['exit_reason'].value_counts()
        for reason, count in exit_reasons.items():
            print(f"   {reason}: {count} ({count/total_trades*100:.1f}%)")
        
        # Performance assessment
        print(f"\n🎯 REGIME SYSTEM ASSESSMENT:")
        if total_return > 15 and win_rate > 55:
            print("🟢 EXCELLENT: Regime detection working well!")
        elif total_return > 5 and win_rate > 50:
            print("🟡 GOOD: Decent regime-based performance")
        elif total_return > 0:
            print("🟠 FAIR: Profitable but needs optimization")
        else:
            print("🔴 POOR: Regime logic needs adjustment")

def main():
    """Main function"""
    backtester = RegimeBacktester(initial_balance=10000)
    results = backtester.run_regime_backtest(3000)

if __name__ == "__main__":
    main()
