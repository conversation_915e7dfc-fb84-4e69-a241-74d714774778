#!/usr/bin/env python3
"""
Test timing logic to debug the candle close detection
"""

from datetime import datetime
import time

def test_timing_logic():
    """Test the timing logic"""
    print("🕐 Testing Timing Logic")
    print("=" * 40)
    
    for i in range(10):
        current_time = datetime.now()
        
        # Check if we're at a 5-minute boundary (within 10 seconds tolerance)
        seconds_since_hour = current_time.minute * 60 + current_time.second
        is_at_5min_boundary = (seconds_since_hour % 300) <= 10  # Within 10 seconds of 5-min mark
        
        print(f"Time: {current_time.strftime('%H:%M:%S')}")
        print(f"  Seconds since hour: {seconds_since_hour}")
        print(f"  Seconds % 300: {seconds_since_hour % 300}")
        print(f"  Is at 5-min boundary: {is_at_5min_boundary}")
        print(f"  Minute: {current_time.minute} (should be 0, 5, 10, 15, etc. for boundary)")
        print()
        
        time.sleep(2)

if __name__ == "__main__":
    test_timing_logic()
