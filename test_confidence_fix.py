#!/usr/bin/env python3
"""
Test the confidence threshold fix
"""

import sys
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_confidence_fix():
    """Test that confidence threshold matches new signal thresholds"""
    print("🧪 TESTING CONFIDENCE THRESHOLD FIX")
    print("=" * 50)
    
    try:
        # Initialize trader
        trader = FixedLiveTrader()
        
        # Connect to MT5
        if not trader.mt5_manager.connect():
            print("❌ Cannot connect to MT5")
            return
        
        print("✅ Connected to MT5")
        
        # Test 1: Verify new confidence threshold
        print("\n1️⃣ CONFIDENCE THRESHOLD VERIFICATION")
        print("-" * 40)
        
        print(f"New Min Confidence: {trader.min_confidence}")
        print("Expected: 0.15 (to match ±15% thresholds)")
        
        if trader.min_confidence == 0.15:
            print("✅ CORRECT: Confidence threshold matches signal thresholds")
        else:
            print("❌ INCORRECT: Confidence threshold needs adjustment")
        
        # Test 2: Current system status
        print(f"\n2️⃣ CURRENT SYSTEM STATUS")
        print("-" * 40)
        
        result = trader.get_live_prediction()
        if result:
            signal, confidence, details, atr_value, regime, logic, regime_details, candle_strength = result
            candle_net_strength = candle_strength['net_strength'] * 100
            
            print(f"Candle Strength: {candle_net_strength:+.1f}%")
            print(f"Signal: {signal}")
            print(f"Confidence: {confidence:.3f}")
            print(f"Min Required: {trader.min_confidence:.3f}")
            
            # Test confidence logic
            print(f"\n3️⃣ CONFIDENCE LOGIC TEST")
            print("-" * 40)
            
            if signal:
                if confidence >= trader.min_confidence:
                    print(f"✅ PASS: Confidence {confidence:.3f} >= {trader.min_confidence:.3f}")
                    print("   Signal will be executed")
                else:
                    print(f"❌ FAIL: Confidence {confidence:.3f} < {trader.min_confidence:.3f}")
                    print("   Signal will be rejected")
            else:
                print("⚠️  No signal generated - cannot test confidence")
        
        # Test 4: Confidence calculation examples
        print(f"\n4️⃣ CONFIDENCE CALCULATION EXAMPLES")
        print("-" * 45)
        
        examples = [
            (15.0, "Minimum BUY threshold"),
            (16.4, "Current example strength"),
            (20.0, "Moderate strength"),
            (25.0, "Strong strength"),
            (30.0, "Very strong strength"),
            (-15.0, "Minimum SELL threshold"),
            (-20.0, "Moderate SELL strength"),
            (-25.0, "Strong SELL strength")
        ]
        
        print("Strength | Confidence | Pass/Fail | Signal")
        print("-" * 45)
        
        for strength, description in examples:
            confidence = min(abs(strength) / 100, 1.0)
            will_pass = confidence >= trader.min_confidence
            status = "PASS" if will_pass else "FAIL"
            signal = "BUY" if strength > 0 else "SELL"
            
            print(f"{strength:+6.1f}% | {confidence:8.3f} | {status:8s} | {signal}")
        
        # Test 5: Before vs After comparison
        print(f"\n5️⃣ BEFORE VS AFTER COMPARISON")
        print("-" * 40)
        
        print("BEFORE (Min Confidence: 0.30):")
        print("• Required candle strength: ±30% for confidence ≥ 0.30")
        print("• Signal thresholds: ±30%")
        print("• Result: Thresholds matched")
        print("")
        print("PROBLEM (Lowered thresholds but kept old confidence):")
        print("• Signal thresholds: ±15%")
        print("• Min confidence: 0.30")
        print("• Result: +16% signal → 0.16 confidence → REJECTED")
        print("")
        print("AFTER (Fixed confidence threshold):")
        print("• Signal thresholds: ±15%")
        print("• Min confidence: 0.15")
        print("• Result: +16% signal → 0.16 confidence → ACCEPTED")
        
        # Test 6: Expected impact
        print(f"\n6️⃣ EXPECTED IMPACT")
        print("-" * 25)
        
        print("✅ POSITIVE IMPACTS:")
        print("• Signals at ±15% will now execute")
        print("• No more confidence rejections for valid signals")
        print("• Consistent threshold logic")
        print("• More trading opportunities realized")
        print("")
        print("⚠️  QUALITY CONTROL:")
        print("• Still filters out very weak signals (<15%)")
        print("• Maintains minimum quality threshold")
        print("• Confidence scales with signal strength")
        
        print(f"\n✅ CONFIDENCE THRESHOLD FIX COMPLETE")
        print("=" * 50)
        print("🎯 Min confidence lowered from 0.30 to 0.15")
        print("📈 Matches new ±15% signal thresholds")
        print("⚡ Valid signals will no longer be rejected")
        print("🚀 System ready for increased trading activity")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            trader.mt5_manager.disconnect()
        except:
            pass

if __name__ == "__main__":
    test_confidence_fix()
