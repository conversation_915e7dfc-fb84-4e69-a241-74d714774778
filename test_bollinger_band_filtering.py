#!/usr/bin/env python3
"""
Test Bollinger Band filtering for RANGING markets
"""

import sys
import warnings
import pandas as pd
import numpy as np
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_bollinger_band_filtering():
    """Test the Bollinger Band filtering logic for RANGING markets"""
    print("🎯 TESTING BOLLINGER BAND FILTERING FOR RANGING MARKETS")
    print("=" * 65)
    
    try:
        # Initialize trader
        trader = FixedLiveTrader()
        
        # Connect to MT5
        if not trader.mt5_manager.connect():
            print("❌ Cannot connect to MT5")
            return
        
        print("✅ Connected to MT5")
        
        # Test 1: Explain the new logic
        print("\n1️⃣ NEW BOLLINGER BAND FILTERING LOGIC")
        print("-" * 45)
        
        print("ENHANCED RANGING MARKET LOGIC:")
        print("• When price near UPPER band (≥80%) → Only SELL signals allowed")
        print("• When price near LOWER band (≤20%) → Only BUY signals allowed")
        print("• When price in MIDDLE area (20%-80%) → Both signals allowed")
        print("")
        print("RATIONALE:")
        print("✅ Upper band = Resistance → Good place to SELL")
        print("✅ Lower band = Support → Good place to BUY")
        print("✅ Middle area = Neutral → Follow candle strength")
        print("")
        print("SIGNAL FILTERING:")
        print("❌ BUY signals VETOED near upper band")
        print("❌ SELL signals VETOED near lower band")
        print("✅ Appropriate signals ALLOWED in correct zones")
        
        # Test 2: Get current live data
        print(f"\n2️⃣ CURRENT LIVE MARKET ANALYSIS")
        print("-" * 40)
        
        result = trader.get_live_prediction()
        if result:
            signal, confidence, details, atr_value, regime, logic, regime_details, candle_strength = result
            candle_net_strength = candle_strength['net_strength'] * 100
            
            print(f"Current Status:")
            print(f"• Regime: {regime}")
            print(f"• Candle Strength: {candle_net_strength:+.1f}%")
            print(f"• Raw Signal: {signal}")
            print(f"• Final Logic: {logic}")
            
            # Get BB data if available
            if "BB_" in logic:
                print(f"• BB Filtering: ACTIVE")
                if "pos:" in logic:
                    pos_start = logic.find("pos:") + 4
                    pos_end = logic.find(")", pos_start)
                    bb_pos = float(logic[pos_start:pos_end])
                    print(f"• BB Position: {bb_pos:.2f} (0=lower, 0.5=middle, 1=upper)")
                    
                    # Interpret position
                    if bb_pos >= 0.8:
                        zone = "UPPER BAND (Resistance)"
                        allowed = "SELL only"
                    elif bb_pos <= 0.2:
                        zone = "LOWER BAND (Support)"
                        allowed = "BUY only"
                    else:
                        zone = "MIDDLE AREA"
                        allowed = "Both BUY/SELL"
                    
                    print(f"• Price Zone: {zone}")
                    print(f"• Signals Allowed: {allowed}")
            else:
                print(f"• BB Filtering: Not active (not in RANGING or no BB data)")
        
        # Test 3: Simulate different BB positions
        print(f"\n3️⃣ BOLLINGER BAND POSITION SIMULATION")
        print("-" * 45)
        
        # Create mock features_df for testing
        mock_data = {
            'close': [3850.0],
            'bb_upper': [3860.0],
            'bb_middle': [3850.0],
            'bb_lower': [3840.0]
        }
        
        test_scenarios = [
            # (price, expected_position, description)
            (3858.0, 0.9, "Near Upper Band"),
            (3856.0, 0.8, "At Upper Threshold"),
            (3854.0, 0.7, "Upper Middle"),
            (3850.0, 0.5, "Exact Middle"),
            (3846.0, 0.3, "Lower Middle"),
            (3844.0, 0.2, "At Lower Threshold"),
            (3842.0, 0.1, "Near Lower Band")
        ]
        
        print("Price Position Tests:")
        print("Price  | BB Pos | Zone        | BUY Signal | SELL Signal")
        print("-" * 60)
        
        for price, expected_pos, description in test_scenarios:
            # Calculate actual position
            bb_upper = 3860.0
            bb_lower = 3840.0
            actual_pos = (price - bb_lower) / (bb_upper - bb_lower)
            
            # Test signal filtering
            mock_df = pd.DataFrame({
                'close': [price],
                'bb_upper': [bb_upper],
                'bb_middle': [3850.0],
                'bb_lower': [bb_lower]
            })
            
            # Test BUY signal
            buy_signal, buy_logic = trader.apply_regime_logic("BUY", "RANGING", "UP", "UP", mock_df)
            buy_result = "ALLOWED" if buy_signal == "BUY" else "VETOED"
            
            # Test SELL signal
            sell_signal, sell_logic = trader.apply_regime_logic("SELL", "RANGING", "DOWN", "DOWN", mock_df)
            sell_result = "ALLOWED" if sell_signal == "SELL" else "VETOED"
            
            print(f"{price:6.0f} | {actual_pos:6.2f} | {description:11s} | {buy_result:10s} | {sell_result:11s}")
        
        # Test 4: Logic verification
        print(f"\n4️⃣ LOGIC VERIFICATION")
        print("-" * 30)
        
        print("Expected Behavior:")
        print("✅ Upper Band (≥0.8): BUY=VETOED, SELL=ALLOWED")
        print("✅ Lower Band (≤0.2): BUY=ALLOWED, SELL=VETOED")
        print("✅ Middle (0.2-0.8): BUY=ALLOWED, SELL=ALLOWED")
        print("")
        print("This makes sense because:")
        print("• Upper band acts as RESISTANCE → Sell pressure")
        print("• Lower band acts as SUPPORT → Buy pressure")
        print("• Middle area is neutral → Follow momentum")
        
        # Test 5: Integration with other regimes
        print(f"\n5️⃣ INTEGRATION WITH OTHER REGIMES")
        print("-" * 40)
        
        print("BB Filtering applies ONLY to RANGING markets:")
        print("• TRENDING markets: Follow candle strength + trend alignment")
        print("• TRANSITIONAL markets: Follow candle strength with caution")
        print("• RANGING markets: Follow candle strength + BB position")
        print("")
        print("This is logical because:")
        print("✅ Ranging markets: BB bands are reliable support/resistance")
        print("✅ Trending markets: Price breaks through BB bands")
        print("✅ Transitional markets: BB bands may be unreliable")
        
        # Test 6: Expected performance impact
        print(f"\n6️⃣ EXPECTED PERFORMANCE IMPACT")
        print("-" * 40)
        
        print("✅ BENEFITS:")
        print("• Better entries in ranging markets")
        print("• Reduced false signals near support/resistance")
        print("• Higher probability trades")
        print("• Better risk/reward in ranging conditions")
        print("")
        print("📊 STATISTICS EXPECTED:")
        print("• Fewer total signals in ranging markets")
        print("• Higher win rate on ranging market trades")
        print("• Better entry timing near BB extremes")
        print("• Reduced whipsaws in choppy conditions")
        
        print(f"\n✅ BOLLINGER BAND FILTERING TEST COMPLETE")
        print("=" * 65)
        print("🎯 BB filtering active for RANGING markets only")
        print("📊 Upper band (≥80%) → SELL signals only")
        print("📊 Lower band (≤20%) → BUY signals only")
        print("📊 Middle area (20%-80%) → Both signals allowed")
        print("🚀 Enhanced ranging market performance expected")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            trader.mt5_manager.disconnect()
        except:
            pass

if __name__ == "__main__":
    test_bollinger_band_filtering()
