#!/usr/bin/env python3
"""
Test script for enhanced candle confirmation trailing stop logic
Tests the new profit/loss based trailing allowance system
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader

def create_mock_trader():
    """Create a mock trader for testing"""
    trader = FixedLiveTrader("XAUUSD!")
    
    # Mock current position for testing
    trader.current_position = {
        'ticket': 12345,
        'type': 'BUY',
        'price': 2000.00,  # Entry price
        'volume': 0.10,
        'stop_loss': 1970.00,  # 30 points SL = $30 risk (for 0.10 lot)
        'take_profit': 2030.00
    }
    
    return trader

def test_candle_trailing_logic():
    """Test the enhanced candle confirmation trailing logic"""
    print("🧪 TESTING ENHANCED CANDLE CONFIRMATION TRAILING LOGIC")
    print("=" * 70)
    
    trader = create_mock_trader()
    
    # Test scenarios with different profit/loss levels
    test_scenarios = [
        {
            'name': 'Profitable Position (+20 points)',
            'current_price': 2020.00,
            'expected_allow': True,
            'expected_reason_contains': 'profitable'
        },
        {
            'name': 'Small Loss (-10 points, 33% of risk)',
            'current_price': 1990.00,
            'expected_allow': False,
            'expected_reason_contains': 'loss < 50%'
        },
        {
            'name': 'Medium Loss (-20 points, 67% of risk)',
            'current_price': 1980.00,
            'expected_allow': True,  # Fixed: 67% loss > 50%, should allow trailing
            'expected_reason_contains': '50%+ in loss'
        },
        {
            'name': 'Around 50% Loss (-15 points)',
            'current_price': 1985.00,
            'expected_allow': False,  # Due to bid/ask spread, this becomes ~48%, so blocked
            'expected_reason_contains': 'loss < 50%'
        },
        {
            'name': 'Large Loss (-25 points, 83% of risk)',
            'current_price': 1975.00,
            'expected_allow': True,
            'expected_reason_contains': '50%+ in loss'
        },
        {
            'name': 'Very Large Loss (-40 points, 133% of risk)',
            'current_price': 1960.00,
            'expected_allow': True,
            'expected_reason_contains': '50%+ in loss'
        }
    ]
    
    print(f"📊 POSITION SETUP:")
    print(f"   Entry Price: {trader.current_position['price']:.2f}")
    print(f"   Stop Loss: {trader.current_position['stop_loss']:.2f}")
    print(f"   Position Risk: 30 points ($30 for 0.10 lot)")
    print(f"   50% Risk Threshold: 15 points loss")
    print()
    
    # Mock the MT5 manager methods
    class MockMT5Manager:
        def get_symbol_info_tick(self, symbol):
            return {
                'bid': test_price - 0.5,
                'ask': test_price + 0.5
            }

        def get_pip_size(self, symbol):
            return 0.01

        def get_contract_size(self, symbol):
            return 100  # XAUUSD contract size
    
    all_passed = True
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"🧪 TEST {i}: {scenario['name']}")
        print("-" * 50)
        
        # Set the test price globally for the mock
        global test_price
        test_price = scenario['current_price']
        
        # Mock the MT5 manager
        trader.mt5_manager = MockMT5Manager()
        
        # Test the trailing allowance logic
        should_allow, reason = trader.should_allow_trailing("CANDLE")
        
        # Calculate expected values for verification
        entry_price = trader.current_position['price']
        profit_points = test_price - entry_price
        position_risk = entry_price - trader.current_position['stop_loss']  # 30 points
        profit_percentage = (profit_points / position_risk) * 100
        
        print(f"   Current Price: {test_price:.2f}")
        print(f"   Profit/Loss: {profit_points:+.2f} points")
        print(f"   Profit %: {profit_percentage:+.1f}% of position risk")
        print(f"   Should Allow: {should_allow}")
        print(f"   Reason: {reason}")
        
        # Verify results
        if should_allow == scenario['expected_allow']:
            if scenario['expected_reason_contains'].lower() in reason.lower():
                print(f"   ✅ PASSED")
            else:
                print(f"   ❌ FAILED: Reason doesn't contain '{scenario['expected_reason_contains']}'")
                all_passed = False
        else:
            print(f"   ❌ FAILED: Expected {scenario['expected_allow']}, got {should_allow}")
            all_passed = False
        
        print()
    
    print("=" * 70)
    if all_passed:
        print("🎯 ALL TESTS PASSED! ✅")
        print("\n📋 SUMMARY OF NEW LOGIC:")
        print("• ✅ Profitable positions → Allow candle trailing (protect profits)")
        print("• ❌ Small losses (< 50% risk) → Block candle trailing (let recover)")
        print("• ✅ Large losses (≥ 50% risk) → Allow candle trailing (cut losses)")
    else:
        print("❌ SOME TESTS FAILED!")
    
    return all_passed

def test_atr_trailing_logic():
    """Test that ATR trailing logic remains unchanged"""
    print("\n🧪 TESTING ATR TRAILING LOGIC (Should be unchanged)")
    print("=" * 70)
    
    trader = create_mock_trader()
    
    # Mock the MT5 manager
    class MockMT5Manager:
        def get_symbol_info_tick(self, symbol):
            return {
                'bid': 2010.0,
                'ask': 2010.5
            }

        def get_contract_size(self, symbol):
            return 100

    trader.mt5_manager = MockMT5Manager()
    
    # Test ATR trailing (should only allow if profitable)
    should_allow_profit, reason_profit = trader.should_allow_trailing("ATR")
    print(f"📊 ATR Trailing with Profit (+10 points):")
    print(f"   Should Allow: {should_allow_profit}")
    print(f"   Reason: {reason_profit}")
    
    # Change to loss scenario
    class MockMT5ManagerLoss:
        def get_symbol_info_tick(self, symbol):
            return {
                'bid': 1990.0,
                'ask': 1990.5
            }

        def get_contract_size(self, symbol):
            return 100

    trader.mt5_manager = MockMT5ManagerLoss()
    
    should_allow_loss, reason_loss = trader.should_allow_trailing("ATR")
    print(f"\n📊 ATR Trailing with Loss (-10 points):")
    print(f"   Should Allow: {should_allow_loss}")
    print(f"   Reason: {reason_loss}")
    
    # Verify ATR logic is unchanged
    if should_allow_profit and not should_allow_loss:
        print(f"\n✅ ATR TRAILING LOGIC UNCHANGED (Only allows when profitable)")
        return True
    else:
        print(f"\n❌ ATR TRAILING LOGIC CHANGED!")
        return False

if __name__ == "__main__":
    print("🚀 STARTING CANDLE CONFIRMATION TRAILING TESTS")
    print("=" * 70)
    
    # Test the new candle trailing logic
    candle_tests_passed = test_candle_trailing_logic()
    
    # Test that ATR trailing logic is unchanged
    atr_tests_passed = test_atr_trailing_logic()
    
    print("\n" + "=" * 70)
    print("🏁 FINAL RESULTS:")
    print(f"   Candle Trailing Tests: {'✅ PASSED' if candle_tests_passed else '❌ FAILED'}")
    print(f"   ATR Trailing Tests: {'✅ PASSED' if atr_tests_passed else '❌ FAILED'}")
    
    if candle_tests_passed and atr_tests_passed:
        print("\n🎯 ALL SYSTEMS GO! Enhanced trailing logic implemented successfully! 🚀")
    else:
        print("\n⚠️ Some tests failed - please review the implementation.")
