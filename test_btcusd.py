#!/usr/bin/env python3
"""
Test script to run the trading system with BTCUSD
This will verify that the system works with BTCUSD and doesn't use old XAUUSD data
"""

import sys
import os

# Add src to path
sys.path.append('src')

from fixed_live_trader import FixedLiveTrader

def test_btcusd():
    """Test the trading system with BTCUSD"""
    print("🧪 TESTING BTCUSD TRADING SYSTEM")
    print("=" * 50)
    print("🎯 Purpose: Verify BTCUSD support and no XAUUSD data contamination")
    print("📊 Symbol: BTCUSD")
    print("💰 Contract Size: 1 BTC per lot")
    print("🔧 Risk Management: 4% per trade")
    print("=" * 50)
    
    try:
        # Create trader with BTCUSD
        trader = FixedLiveTrader(symbol="BTCUSD")
        
        print(f"✅ Trader initialized successfully")
        print(f"📈 Trading Symbol: {trader.symbol}")
        print(f"📊 Contract Size: {trader.SYMBOL_SPECS[trader.symbol]['contract_size']}")
        print(f"🏷️  Symbol Name: {trader.SYMBOL_SPECS[trader.symbol]['name']}")
        
        # Test position sizing calculation
        print("\n🧮 Testing Position Size Calculation:")
        test_balance = 10000.0  # $10,000 account
        test_price = 45000.0    # $45,000 per BTC
        test_atr = 500.0        # $500 ATR
        
        lot_size = trader.calculate_position_size(test_balance, test_price, test_atr)
        print(f"   Test Balance: ${test_balance:,.2f}")
        print(f"   Test BTC Price: ${test_price:,.2f}")
        print(f"   Test ATR: ${test_atr:.2f}")
        print(f"   Calculated Lot Size: {lot_size} lots")
        
        # Calculate expected values for verification
        risk_amount = test_balance * 0.04  # 4% risk
        stop_distance = test_atr * 1.0     # 1 ATR stop
        risk_per_lot = stop_distance * 1   # 1 BTC per lot
        expected_lot_size = risk_amount / risk_per_lot
        
        print(f"\n🔍 Verification:")
        print(f"   Expected Risk Amount: ${risk_amount:.2f}")
        print(f"   Expected Stop Distance: ${stop_distance:.2f}")
        print(f"   Expected Risk per Lot: ${risk_per_lot:.2f}")
        print(f"   Expected Lot Size: {expected_lot_size:.4f}")
        print(f"   Actual Lot Size: {lot_size}")
        
        if abs(lot_size - round(expected_lot_size, 2)) < 0.01:
            print("✅ Position sizing calculation CORRECT for BTCUSD")
        else:
            print("❌ Position sizing calculation INCORRECT")
            
        print("\n🚀 Starting BTCUSD trading system...")
        print("⚠️  Press Ctrl+C to stop the system")
        print("📊 Monitor the logs to ensure BTCUSD data is being used (not XAUUSD)")
        
        # Start the trading system
        trader.start_trading()
        
    except KeyboardInterrupt:
        print("\n🛑 Test stopped by user")
    except Exception as e:
        print(f"❌ Error during BTCUSD test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_btcusd()
