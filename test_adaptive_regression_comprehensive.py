#!/usr/bin/env python3
"""
Comprehensive Test for Adaptive Regression Channel System
Tests with real XAUUSD data and visualizes channels
"""

import sys
sys.path.append('src')

import pandas as pd
import numpy as np
from datetime import datetime
import logging

from mt5_integration import MT5<PERSON>anager
from simple_regression_channel import SimpleRegressionChannel

# Try to import matplotlib, skip visualization if not available
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ Matplotlib not available, skipping visualization")

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_simple_regression_channel():
    """Comprehensive test of adaptive regression channel with real data"""
    
    print("🔍 COMPREHENSIVE ADAPTIVE REGRESSION CHANNEL TEST")
    print("=" * 60)
    
    # Initialize MT5 and get data
    mt5_manager = MT5Manager()
    
    try:
        if not mt5_manager.connect():
            print("❌ Failed to connect to MT5")
            return
            
        print("✅ Connected to MT5")
        
        # Get substantial amount of data for testing
        df = mt5_manager.get_latest_data("EURUSD!", "M5", 200)
        if df is None or len(df) < 50:
            print("❌ Failed to get sufficient data")
            return
            
        print(f"📊 Got {len(df)} candles of XAUUSD! M5 data")
        print(f"📅 Data range: {df.index[0]} to {df.index[-1]}")
        print(f"💰 Price range: {df['low'].min():.2f} - {df['high'].max():.2f}")
        
        # Initialize simple regression channel
        simple_channel = SimpleRegressionChannel(
            periods=20,                       # Fixed 20-period lookback
            std_multiplier=2.0               # 2 standard deviations for channel bounds
        )
        
        print("\n🔧 SIMPLE REGRESSION CHANNEL PARAMETERS:")
        print(f"   Lookback Period: {simple_channel.periods} candles")
        print(f"   Standard Deviation Multiplier: {simple_channel.std_multiplier}")
        print(f"   Channel Type: Fixed linear regression with standard deviation bounds")
        
        # Test channel building step by step
        print("\n🧪 STEP-BY-STEP CHANNEL TESTING:")
        
        # Start with minimum data and gradually increase - EXTREME TEST POINTS (every 3-4 candles)
        test_points = [8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 68, 72, 76, 80, 84, 88, 92, 96, 100, 104, 108, 112, 116, 120, 124, 128, 132, 136, 140, 144, 148, 152, 156, 160, 164, 168, 172, 176, 180, 184, 188, 192, 196, len(df)]
        channel_history = []
        
        for i, end_point in enumerate(test_points):
            if end_point > len(df):
                end_point = len(df)
                
            test_df = df.iloc[:end_point].copy()
            print(f"\n📊 TEST {i+1}: Using {len(test_df)} candles")
            
            # Update channel state
            channel_info = simple_channel.update_channel_state(test_df)
            
            print(f"   State: {channel_info['state']}")
            print(f"   Regime Contribution: {channel_info['regime_contribution']}")
            
            if channel_info['channel_data']:
                channel_data = channel_info['channel_data']
                print(f"   ✅ CHANNEL ACTIVE:")
                print(f"      Candles: {channel_data['candle_count']}")
                print(f"      R²: {channel_data['r_squared']:.3f}")
                print(f"      Slope: {channel_data['slope']:+.6f}")
                print(f"      Width: {channel_data['width']:.5f}")
                print(f"      Position: {channel_data['position']:.3f}")
                
                # Store for plotting
                channel_history.append({
                    'end_point': end_point,
                    'state': channel_info['state'],
                    'channel_data': channel_data.copy()
                })
            else:
                print(f"   ❌ NO CHANNEL DATA")
                channel_history.append({
                    'end_point': end_point,
                    'state': channel_info['state'],
                    'channel_data': None
                })
        
        # Test simple channel calculation
        print("\n🔍 SIMPLE CHANNEL CALCULATION TEST:")
        if len(df) >= simple_channel.periods:
            channel_data = simple_channel.calculate_regression_channel(df['close'])

            if channel_data:
                print(f"   ✅ CHANNEL CALCULATED SUCCESSFULLY:")
                print(f"      Upper: {channel_data['upper_bound']:.5f}")
                print(f"      Center: {channel_data['center_line']:.5f}")
                print(f"      Lower: {channel_data['lower_bound']:.5f}")
                print(f"      Width: {channel_data['width']:.5f}")
                print(f"      R²: {channel_data['r_squared']:.3f}")
                print(f"      Position: {channel_data['position']:.3f}")
                print(f"      Slope: {channel_data['slope']:+.6f}")
            else:
                print("   ❌ Failed to calculate channel")
        else:
            print(f"   ❌ Insufficient data (need {simple_channel.periods} candles, have {len(df)})")
        
        # Create visualization if matplotlib is available
        if MATPLOTLIB_AVAILABLE:
            print("\n📈 CREATING VISUALIZATION...")
            create_channel_visualization(df, channel_history, simple_channel)
        else:
            print("\n⚠️ SKIPPING VISUALIZATION (matplotlib not available)")
        
        # Test position calculation
        print("\n📍 POSITION CALCULATION TEST:")
        if channel_history and channel_history[-1]['channel_data']:
            last_channel = channel_history[-1]['channel_data']
            current_price = df.iloc[-1]['close']
            position = simple_channel.get_channel_position(current_price)
            if position is not None:
                print(f"   Current Price: {current_price:.5f}")
                print(f"   Channel Position: {position:.3f} (0=bottom, 1=top)")
            else:
                print("   ❌ Could not calculate position")
        
        print("\n✅ COMPREHENSIVE TEST COMPLETED")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    finally:
        mt5_manager.disconnect()

def create_channel_visualization(df, channel_history, simple_channel):
    """Create comprehensive visualization of adaptive channels"""

    if not MATPLOTLIB_AVAILABLE:
        print("❌ Cannot create visualization - matplotlib not available")
        return

    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
    
    # Plot 1: Price with channels
    ax1.plot(df.index, df['close'], 'b-', linewidth=1, label='Close Price', alpha=0.7)
    ax1.plot(df.index, df['high'], 'g-', linewidth=0.5, alpha=0.3, label='High')
    ax1.plot(df.index, df['low'], 'r-', linewidth=0.5, alpha=0.3, label='Low')
    
    # Plot channels from history - MORE COLORS for 13+ channels
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink',
              'cyan', 'magenta', 'yellow', 'lime', 'indigo', 'teal', 'coral',
              'navy', 'maroon', 'olive', 'silver', 'gold', 'crimson']
    for i, history in enumerate(channel_history):
        if history['channel_data']:
            channel_data = history['channel_data']
            end_point = history['end_point']
            
            # Get the homogeneous candles used for this channel
            homogeneous_indices = history['homogeneous_indices']
            if homogeneous_indices:
                # Convert relative indices to absolute
                abs_indices = [end_point + idx if idx < 0 else idx for idx in homogeneous_indices]
                abs_indices = [idx for idx in abs_indices if 0 <= idx < len(df)]
                
                if abs_indices:
                    color = colors[i % len(colors)]

                    # Calculate actual sloped regression lines
                    channel_times = df.index[abs_indices]
                    slope = channel_data['slope']

                    # Get the regression data for proper slope visualization
                    X_indices = list(range(len(abs_indices)))

                    # Calculate sloped lines based on regression
                    upper_values = []
                    lower_values = []
                    center_values = []

                    for j, x_val in enumerate(X_indices):
                        # Calculate center line value at this X position
                        center_val = channel_data['center_line'] + slope * (x_val - (len(X_indices) - 1))

                        # Calculate channel width at this position (assuming constant width)
                        width = channel_data['width']

                        upper_val = center_val + width
                        lower_val = center_val - width

                        center_values.append(center_val)
                        upper_values.append(upper_val)
                        lower_values.append(lower_val)

                    # Plot sloped channel bounds
                    channel_type = channel_data.get('channel_type', 'UNKNOWN')
                    quality = channel_data.get('quality_score', channel_data['r_squared'])

                    ax1.plot(channel_times, upper_values, color=color, linewidth=2, alpha=0.8,
                            label=f'Ch{i+1}: {channel_type} (slope:{slope:+.3f}, Q:{quality:.2f})')
                    ax1.plot(channel_times, lower_values, color=color, linewidth=2, alpha=0.8)
                    ax1.plot(channel_times, center_values, color=color, linewidth=1, alpha=0.6, linestyle='--')

                    # Fill channel
                    ax1.fill_between(channel_times, upper_values, lower_values,
                                   color=color, alpha=0.1)
    
    ax1.set_title(f'XAUUSD! M5 - Adaptive Regression Channels ({len([h for h in channel_history if h["channel_data"]])} Channels Generated)')
    ax1.set_ylabel('Price')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Channel states over time
    states = [h['state'] for h in channel_history]
    test_points = [h['end_point'] for h in channel_history]
    
    state_mapping = {'ACTIVE_CHANNEL': 2, 'TRANSITION': 1, 'BUILDING': 0}
    state_values = [state_mapping.get(state, -1) for state in states]
    
    ax2.plot(test_points, state_values, 'o-', linewidth=2, markersize=8)
    ax2.set_ylabel('Channel State')
    ax2.set_yticks([0, 1, 2])
    ax2.set_yticklabels(['BUILDING', 'TRANSITION', 'ACTIVE'])
    ax2.grid(True, alpha=0.3)
    ax2.set_title('Channel State Evolution')
    
    # Plot 3: Channel quality metrics
    r_squared_values = []
    widths = []
    slopes = []
    
    for h in channel_history:
        if h['channel_data']:
            r_squared_values.append(h['channel_data']['r_squared'])
            widths.append(h['channel_data']['width'])
            slopes.append(abs(h['channel_data']['slope']))
        else:
            r_squared_values.append(0)
            widths.append(0)
            slopes.append(0)
    
    ax3_twin = ax3.twinx()
    
    line1 = ax3.plot(test_points, r_squared_values, 'g-o', label='R² Quality', linewidth=2)
    line2 = ax3_twin.plot(test_points, widths, 'r-s', label='Channel Width', linewidth=2)
    
    ax3.set_ylabel('R² Quality', color='g')
    ax3_twin.set_ylabel('Channel Width', color='r')
    ax3.set_xlabel('Data Points Used')
    ax3.grid(True, alpha=0.3)
    ax3.set_title('Channel Quality Metrics')
    
    # Combine legends
    lines1, labels1 = ax3.get_legend_handles_labels()
    lines2, labels2 = ax3_twin.get_legend_handles_labels()
    ax3.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    plt.tight_layout()
    plt.savefig('adaptive_regression_channel_test.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("📊 Visualization saved as 'adaptive_regression_channel_test.png'")

def test_position_tracking(df, simple_channel, channel_data):
    """Test position tracking functionality"""

    print("📍 Testing position tracking with current channel...")

    # Test with last few candles
    for i in range(5, 0, -1):
        test_df = df.iloc[:-i] if i > 0 else df
        if len(test_df) > 0:
            current_price = test_df.iloc[-1]['close']
            position = simple_channel.get_channel_position(current_price)

            if position is not None:
                print(f"   Test {6-i}: Price {current_price:.5f} → Position {position:.3f}")
            else:
                print(f"   Test {6-i}: Could not calculate position for price {current_price:.5f}")

if __name__ == "__main__":
    test_simple_regression_channel()
