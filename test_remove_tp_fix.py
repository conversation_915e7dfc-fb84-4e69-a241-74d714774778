#!/usr/bin/env python3
"""
Remove Take Profit Fix Test

Tests that the remove_take_profit function correctly handles cases where TP is already 0.0
"""

import pandas as pd
import sys
import os
from datetime import datetime
from unittest.mock import Mock, patch

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader

def test_remove_tp_fix():
    """Test that remove_take_profit handles TP=0.0 correctly"""
    
    print("🧪 Remove Take Profit Fix Test")
    print("=" * 50)
    
    # Create test trader
    trader = FixedLiveTrader("XAUUSD!")
    
    test_results = []
    
    # Test 1: Position with TP = 0.0 (should skip modification)
    print(f"\n📊 Test 1: Position with TP already 0.0")
    print("-" * 40)
    
    # Mock position data
    trader.current_position = {
        'type': 'BUY',
        'ticket': 12345,
        'time': datetime.now(),
        'volume': 0.10,
        'remaining_volume': 0.10,
        'price': 4300.0,
        'sl': 4298.5,
        'take_profit': 0.0
    }
    
    # Mock MT5 manager to return position with TP = 0.0
    mock_positions = [{
        'ticket': 12345,
        'symbol': 'XAUUSD!',
        'type': 0,  # BUY
        'volume': 0.10,
        'price_open': 4300.0,
        'sl': 4298.5,
        'tp': 0.0,  # Already no TP
        'profit': 5.0,
        'time': 1234567890
    }]
    
    with patch.object(trader.mt5_manager, 'get_positions', return_value=mock_positions):
        result = trader.remove_take_profit("Test - Already 0.0")
    
    print(f"  Result: {'✅ SUCCESS' if result else '❌ FAILED'}")
    print(f"  Expected: Should return True without calling modify_position")
    
    test1_passed = result == True
    test_results.append(("TP Already 0.0", test1_passed))
    
    # Test 2: Position with TP > 0.0 (should modify)
    print(f"\n📊 Test 2: Position with TP > 0.0")
    print("-" * 40)
    
    # Mock position data with TP
    mock_positions_with_tp = [{
        'ticket': 12345,
        'symbol': 'XAUUSD!',
        'type': 0,  # BUY
        'volume': 0.10,
        'price_open': 4300.0,
        'sl': 4298.5,
        'tp': 4301.5,  # Has TP
        'profit': 5.0,
        'time': 1234567890
    }]
    
    with patch.object(trader.mt5_manager, 'get_positions', return_value=mock_positions_with_tp), \
         patch.object(trader.mt5_manager, 'modify_position', return_value=True) as mock_modify:
        
        result = trader.remove_take_profit("Test - Has TP")
    
    print(f"  Result: {'✅ SUCCESS' if result else '❌ FAILED'}")
    print(f"  Expected: Should call modify_position and return True")
    print(f"  modify_position called: {'✅ YES' if mock_modify.called else '❌ NO'}")
    
    if mock_modify.called:
        call_args = mock_modify.call_args
        print(f"  Called with: ticket={call_args[1].get('ticket')}, take_profit={call_args[1].get('take_profit')}")
    
    test2_passed = result == True and mock_modify.called
    test_results.append(("TP > 0.0 Modification", test2_passed))
    
    # Test 3: Position not found in MT5
    print(f"\n📊 Test 3: Position not found in MT5")
    print("-" * 40)
    
    with patch.object(trader.mt5_manager, 'get_positions', return_value=[]):
        result = trader.remove_take_profit("Test - Not Found")
    
    print(f"  Result: {'✅ HANDLED' if result == False else '❌ UNEXPECTED'}")
    print(f"  Expected: Should return False when position not found")
    
    test3_passed = result == False
    test_results.append(("Position Not Found", test3_passed))
    
    # Test 4: No current position
    print(f"\n📊 Test 4: No current position")
    print("-" * 40)
    
    trader.current_position = None
    result = trader.remove_take_profit("Test - No Position")
    
    print(f"  Result: {'✅ SUCCESS' if result else '❌ FAILED'}")
    print(f"  Expected: Should return True when no position")
    
    test4_passed = result == True
    test_results.append(("No Current Position", test4_passed))
    
    # Test 5: MT5 modify_position fails
    print(f"\n📊 Test 5: MT5 modify_position fails")
    print("-" * 40)
    
    # Reset position
    trader.current_position = {
        'type': 'BUY',
        'ticket': 12345,
        'time': datetime.now(),
        'volume': 0.10,
        'remaining_volume': 0.10,
        'price': 4300.0,
        'sl': 4298.5,
        'take_profit': 4301.5
    }
    
    with patch.object(trader.mt5_manager, 'get_positions', return_value=mock_positions_with_tp), \
         patch.object(trader.mt5_manager, 'modify_position', return_value=False) as mock_modify_fail:
        
        result = trader.remove_take_profit("Test - Modify Fails")
    
    print(f"  Result: {'✅ HANDLED' if result == False else '❌ UNEXPECTED'}")
    print(f"  Expected: Should return False when modify fails")
    print(f"  modify_position called: {'✅ YES' if mock_modify_fail.called else '❌ NO'}")
    
    test5_passed = result == False and mock_modify_fail.called
    test_results.append(("Modify Position Fails", test5_passed))
    
    # Summary
    print(f"\n🎯 Test Summary")
    print("=" * 30)
    
    passed_tests = sum(1 for _, passed in test_results if passed)
    total_tests = len(test_results)
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed Tests: {passed_tests}")
    print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
    
    for test_name, passed in test_results:
        print(f"  {test_name}: {'✅ PASSED' if passed else '❌ FAILED'}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 REMOVE TAKE PROFIT FIX SUCCESSFUL!")
        print("✅ Correctly skips modification when TP is already 0.0")
        print("✅ Properly modifies position when TP > 0.0")
        print("✅ Handles edge cases (no position, position not found, modify fails)")
        print("✅ Gets current TP from MT5 instead of relying on cached data")
        
        print(f"\n📊 FIXES APPLIED:")
        print("  • Check current TP from MT5 before attempting modification")
        print("  • Skip modification if TP is already 0.0")
        print("  • Log current and target TP values for clarity")
        print("  • Handle all error cases gracefully")
        print("  • Include TP data in position structure")
        
    else:
        print(f"\n⚠️ SOME ISSUES REMAIN:")
        for test_name, passed in test_results:
            if not passed:
                print(f"  • {test_name} needs attention")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    try:
        success = test_remove_tp_fix()
        exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
