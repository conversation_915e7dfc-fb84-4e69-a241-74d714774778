#!/usr/bin/env python3
"""
Test trend direction filtering for TRENDING markets
"""

import sys
import warnings
import pandas as pd
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_trend_direction_filtering():
    """Test the trend direction filtering implementation"""
    print("🎯 TESTING TREND DIRECTION FILTERING")
    print("=" * 50)
    
    try:
        # Initialize trader
        trader = FixedLiveTrader()
        
        print("1️⃣ PROBLEM IDENTIFIED")
        print("-" * 30)
        
        print("❌ PREVIOUS ISSUE:")
        print("• TRENDING DOWN market")
        print("• System generated BUY signal")
        print("• BUY against downtrend = counterproductive")
        print("")
        print("📝 LOG EVIDENCE:")
        print("• Regime:TRENDING(0.52) | Trend:DOWN")
        print("• CandleStrength:BUY | Signal:+23.0% increase")
        print("• Result: BUY signal in downtrend!")
        
        print("\n2️⃣ SOLUTION IMPLEMENTED")
        print("-" * 35)
        
        print("✅ NEW TREND DIRECTION FILTERING:")
        print("• TRENDING + UP trend → Only BUY signals allowed")
        print("• TRENDING + DOWN trend → Only SELL signals allowed")
        print("• Opposite signals → VETOED with TREND_VETO")
        print("")
        print("🎯 LOGIC ENHANCEMENT:")
        print("• RANGING: BB filtering (upper/lower thirds)")
        print("• TRENDING: Trend direction filtering")
        print("• TRANSITIONAL: Follow candle strength")
        
        print("\n3️⃣ TESTING SCENARIOS")
        print("-" * 30)
        
        test_scenarios = [
            # (regime, trend_direction, raw_signal, expected_result, expected_logic)
            ("TRENDING", "UP", "BUY", "ALLOWED", "TREND_ALIGNED(UP)"),
            ("TRENDING", "UP", "SELL", "VETOED", "TREND_VETO(UP_blocks_SELL)"),
            ("TRENDING", "DOWN", "SELL", "ALLOWED", "TREND_ALIGNED(DOWN)"),
            ("TRENDING", "DOWN", "BUY", "VETOED", "TREND_VETO(DOWN_blocks_BUY)"),
            ("RANGING", "UP", "BUY", "DEPENDS", "BB position dependent"),
            ("RANGING", "DOWN", "SELL", "DEPENDS", "BB position dependent"),
            ("TRANSITIONAL", "UP", "BUY", "ALLOWED", "CANDLE_STRENGTH_TRANSITIONAL"),
            ("TRANSITIONAL", "DOWN", "SELL", "ALLOWED", "CANDLE_STRENGTH_TRANSITIONAL"),
        ]
        
        print("Regime      | Trend | Signal | Result  | Logic")
        print("-" * 55)
        
        for regime, trend, signal, expected, logic in test_scenarios:
            # Test the logic
            result_signal, result_logic = trader.apply_regime_logic(
                raw_signal=signal,
                regime=regime,
                trend_direction=trend,
                accurate_trend_direction=trend,
                features_df=None
            )
            
            actual_result = "ALLOWED" if result_signal == signal else "VETOED"
            status = "✅" if actual_result == expected or expected == "DEPENDS" else "❌"
            
            print(f"{regime:11s} | {trend:5s} | {signal:6s} | {actual_result:7s} {status} | {result_logic}")
        
        print("\n4️⃣ SPECIFIC PROBLEM CASE")
        print("-" * 35)
        
        print("🚨 THE EXACT ISSUE FROM LOG:")
        print("• Regime: TRENDING")
        print("• Trend: DOWN")
        print("• Raw Signal: BUY")
        print("• Previous Result: BUY allowed (wrong!)")
        print("")
        
        # Test the exact problematic case
        result_signal, result_logic = trader.apply_regime_logic(
            raw_signal="BUY",
            regime="TRENDING", 
            trend_direction="DOWN",
            accurate_trend_direction="DOWN",
            features_df=None
        )
        
        print("✅ NEW RESULT:")
        print(f"• Signal: {result_signal}")
        print(f"• Logic: {result_logic}")
        
        if result_signal is None:
            print("🎉 SUCCESS: BUY signal correctly VETOED in DOWN trend!")
        else:
            print("❌ FAILURE: BUY signal still allowed in DOWN trend!")
        
        print("\n5️⃣ EXPECTED BEHAVIOR CHANGES")
        print("-" * 40)
        
        print("📈 TRENDING UP MARKETS:")
        print("• BUY signals: ✅ ALLOWED (TREND_ALIGNED)")
        print("• SELL signals: ❌ VETOED (TREND_VETO)")
        print("• Result: Only trend-following trades")
        print("")
        print("📉 TRENDING DOWN MARKETS:")
        print("• SELL signals: ✅ ALLOWED (TREND_ALIGNED)")
        print("• BUY signals: ❌ VETOED (TREND_VETO)")
        print("• Result: Only trend-following trades")
        print("")
        print("🔄 RANGING MARKETS:")
        print("• Both signals allowed (BB filtering applies)")
        print("• Upper third: SELL only")
        print("• Lower third: BUY only")
        print("")
        print("⚖️ TRANSITIONAL MARKETS:")
        print("• Both signals allowed (follow momentum)")
        print("• No additional filtering")
        
        print("\n6️⃣ LOG MESSAGE IMPROVEMENTS")
        print("-" * 40)
        
        print("NEW VETO MESSAGES:")
        print("• TREND_VETO(UP_blocks_SELL)")
        print("• TREND_VETO(DOWN_blocks_BUY)")
        print("")
        print("NEW ALIGNMENT MESSAGES:")
        print("• TREND_ALIGNED(UP)")
        print("• TREND_ALIGNED(DOWN)")
        print("")
        print("EXAMPLE LOG OUTPUT:")
        print("🎯 REGIME ANALYSIS: CandleStrength:BUY | Regime:TRENDING | Trend:DOWN")
        print("⚠️ No trade - TREND_VETO(DOWN_blocks_BUY)")
        
        print("\n7️⃣ SYSTEM INTEGRATION")
        print("-" * 30)
        
        print("✅ FILTERING HIERARCHY:")
        print("1. Generate signal from candle strength change")
        print("2. Apply acceleration confidence weighting")
        print("3. Apply regime-specific filtering:")
        print("   • RANGING: BB position filtering")
        print("   • TRENDING: Trend direction filtering ← NEW")
        print("   • TRANSITIONAL: No additional filtering")
        print("4. Apply candle approval filter")
        print("5. Execute trade if all filters pass")
        
        print("\n8️⃣ BENEFITS")
        print("-" * 15)
        
        print("🎯 IMPROVED TRADE QUALITY:")
        print("• No more counter-trend trades in TRENDING markets")
        print("• Better alignment with market direction")
        print("• Reduced drawdowns from fighting the trend")
        print("")
        print("📊 BETTER RISK MANAGEMENT:")
        print("• Trend-following approach in trending markets")
        print("• Mean-reversion approach in ranging markets")
        print("• Adaptive strategy based on market regime")
        print("")
        print("⚡ CLEARER LOGIC:")
        print("• Transparent veto reasons in logs")
        print("• Easy to understand decision process")
        print("• Better debugging capabilities")
        
        print(f"\n✅ TREND DIRECTION FILTERING TEST COMPLETE")
        print("=" * 50)
        print("🎯 TRENDING markets now enforce trend direction")
        print("❌ Counter-trend signals are VETOED")
        print("✅ Only trend-aligned signals allowed")
        print("🚀 Better trade quality and risk management!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_trend_direction_filtering()
