#!/usr/bin/env python3
"""
Debug Swing Point Detection
"""

import sys
sys.path.append('src')

from mt5_integration import MT5Manager
from fixed_live_trader import FixedLiveTrader
import pandas as pd

def debug_swing_points():
    """Debug why swing points aren't being detected"""
    
    # Initialize components
    mt5_manager = MT5Manager()
    trader = FixedLiveTrader()
    
    print("🔍 SWING POINT DEBUG ANALYSIS")
    print("=" * 50)
    
    try:
        # Connect to MT5
        if not mt5_manager.connect():
            print("❌ Failed to connect to MT5")
            return
            
        # Get recent data
        df = mt5_manager.get_latest_data("XAUUSD", "M5", 50)
        if df is None or len(df) < 10:
            print("❌ Failed to get data")
            return
            
        print(f"📊 Got {len(df)} candles")
        
        # Show last 5 candles for context
        print("\n📈 LAST 5 CANDLES:")
        for i in range(5):
            idx = -(5-i)
            candle = df.iloc[idx]
            print(f"  {i+1}: H={candle['high']:.5f} L={candle['low']:.5f} C={candle['close']:.5f} | {candle.name}")
        
        # Check if we have enough data
        if len(df) < 4:
            print("❌ Not enough data for swing point detection (need at least 4 candles)")
            return
            
        # Get the 3 most recent CLOSED candles (excluding current forming)
        c3 = df.iloc[-4]  # 3rd candle back (earliest)
        c2 = df.iloc[-3]  # 2nd candle back (middle) 
        c1 = df.iloc[-2]  # 1st candle back (latest closed)
        current = df.iloc[-1]  # Current forming candle
        
        print(f"\n🎯 SWING POINT ANALYSIS:")
        print(f"Current forming: H={current['high']:.5f} L={current['low']:.5f} C={current['close']:.5f}")
        print(f"C1 (latest closed): H={c1['high']:.5f} L={c1['low']:.5f} C={c1['close']:.5f}")
        print(f"C2 (middle): H={c2['high']:.5f} L={c2['low']:.5f} C={c2['close']:.5f}")
        print(f"C3 (earliest): H={c3['high']:.5f} L={c3['low']:.5f} C={c3['close']:.5f}")
        
        # Extract values
        l1, l2, l3 = c1['low'], c2['low'], c3['low']
        h1, h2, h3 = c1['high'], c2['high'], c3['high']
        
        print(f"\n🔍 RECENT HIGH CHECK:")
        print(f"Condition: H2 > H3 AND H2 > H1 (middle candle's HIGH is higher than both sides)")
        print(f"H2 ({h2:.5f}) > H3 ({h3:.5f}) = {h2 > h3}")
        print(f"H2 ({h2:.5f}) > H1 ({h1:.5f}) = {h2 > h1}")
        print(f"Both conditions met: {h2 > h3 and h2 > h1}")
        if h2 > h3 and h2 > h1:
            print(f"✅ RECENT HIGH FOUND: H2 = {h2:.5f}")
        else:
            print(f"❌ No recent high found")

        print(f"\n🔍 RECENT LOW CHECK:")
        print(f"Condition: L2 < L3 AND L2 < L1 (middle candle's LOW is lower than both sides)")
        print(f"L2 ({l2:.5f}) < L3 ({l3:.5f}) = {l2 < l3}")
        print(f"L2 ({l2:.5f}) < L1 ({l1:.5f}) = {l2 < l1}")
        print(f"Both conditions met: {l2 < l3 and l2 < l1}")
        if l2 < l3 and l2 < l1:
            print(f"✅ RECENT LOW FOUND: L2 = {l2:.5f}")
        else:
            print(f"❌ No recent low found")
            
        # Test the actual function
        print(f"\n🧪 TESTING ACTUAL FUNCTION:")
        features_df = trader.feature_engineer.create_technical_indicators(df)
        swing_points = trader.find_recent_swing_points(features_df)
        
        print(f"Function result: {swing_points}")
        
        # Show more candles to see if pattern exists elsewhere
        print(f"\n📊 EXTENDED CANDLE ANALYSIS (last 8 candles):")
        for i in range(8):
            idx = -(8-i)
            candle = df.iloc[idx]
            print(f"  {i+1}: H={candle['high']:.5f} L={candle['low']:.5f} | {candle.name}")
            
        # Check multiple 3-candle combinations
        print(f"\n🔍 CHECKING MULTIPLE 3-CANDLE COMBINATIONS:")
        for start_idx in range(-8, -2):  # Check different starting positions
            try:
                c3_test = df.iloc[start_idx]
                c2_test = df.iloc[start_idx + 1] 
                c1_test = df.iloc[start_idx + 2]
                
                l1_test, l2_test, l3_test = c1_test['low'], c2_test['low'], c3_test['low']
                h1_test, h2_test, h3_test = c1_test['high'], c2_test['high'], c3_test['high']
                
                high_found = h2_test > h3_test and h2_test > h1_test
                low_found = l2_test < l3_test and l2_test < l1_test
                
                if high_found or low_found:
                    print(f"  Position {start_idx}: High={high_found} Low={low_found}")
                    if high_found:
                        print(f"    Swing High: H2={h2_test:.5f}")
                    if low_found:
                        print(f"    Swing Low: L2={l2_test:.5f}")
            except:
                continue
                
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        mt5_manager.disconnect()

if __name__ == "__main__":
    debug_swing_points()
