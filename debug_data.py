#!/usr/bin/env python3
"""
Debug script to analyze data and target distribution
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import sys
import os

# Add src to path
sys.path.append('src')

from data_manager import DataManager
from feature_engineering import FeatureEngineer
from data_preprocessing import DataPreprocessor

def analyze_data():
    """Analyze the data and target distribution"""
    
    print("🔍 Loading and analyzing data...")
    
    # Load data
    data_manager = DataManager()
    df = data_manager.load_historical_data()
    
    print(f"📊 Raw data shape: {df.shape}")
    print(f"📅 Date range: {df.index.min()} to {df.index.max()}")
    
    # Create features
    feature_engineer = FeatureEngineer()
    df_features = feature_engineer.create_technical_indicators(df)
    
    print(f"📊 Features data shape: {df_features.shape}")
    print(f"📈 Number of features: {len([col for col in df_features.columns if col not in ['open', 'high', 'low', 'close', 'volume']])}")
    
    # Create targets
    df_with_targets = feature_engineer.create_target_variable(df_features)
    
    print(f"📊 Data with targets shape: {df_with_targets.shape}")
    
    # Analyze target distribution
    print("\n🎯 Target Distribution:")
    target_counts = df_with_targets['target'].value_counts().sort_index()
    print(target_counts)
    print(f"Hold: {target_counts[0]} ({target_counts[0]/len(df_with_targets)*100:.1f}%)")
    print(f"Buy: {target_counts[1]} ({target_counts[1]/len(df_with_targets)*100:.1f}%)")
    print(f"Sell: {target_counts[2]} ({target_counts[2]/len(df_with_targets)*100:.1f}%)")
    
    # Analyze future returns
    print("\n📈 Future Returns Analysis:")
    print(f"Mean future return: {df_with_targets['future_return'].mean():.6f}")
    print(f"Std future return: {df_with_targets['future_return'].std():.6f}")
    print(f"Min future return: {df_with_targets['future_return'].min():.6f}")
    print(f"Max future return: {df_with_targets['future_return'].max():.6f}")
    
    # Analyze ATR threshold
    atr_threshold = df_with_targets['atr'] / df_with_targets['close'] * 0.5
    print(f"\n🎯 ATR Threshold Analysis:")
    print(f"Mean ATR threshold: {atr_threshold.mean():.6f}")
    print(f"Std ATR threshold: {atr_threshold.std():.6f}")
    print(f"Min ATR threshold: {atr_threshold.min():.6f}")
    print(f"Max ATR threshold: {atr_threshold.max():.6f}")
    
    # Check how many returns exceed threshold
    positive_above_threshold = (df_with_targets['future_return'] > atr_threshold).sum()
    negative_below_threshold = (df_with_targets['future_return'] < -atr_threshold).sum()
    
    print(f"\n📊 Threshold Analysis:")
    print(f"Positive returns above threshold: {positive_above_threshold} ({positive_above_threshold/len(df_with_targets)*100:.1f}%)")
    print(f"Negative returns below threshold: {negative_below_threshold} ({negative_below_threshold/len(df_with_targets)*100:.1f}%)")
    
    # Sample some data
    print("\n🔍 Sample Data (first 10 rows):")
    sample_cols = ['close', 'atr', 'future_return', 'target']
    print(df_with_targets[sample_cols].head(10))
    
    # Check for NaN values
    print("\n❌ NaN Values:")
    nan_counts = df_with_targets.isnull().sum()
    print(f"Total NaN values: {nan_counts.sum()}")
    if nan_counts.sum() > 0:
        print("Columns with NaN:")
        print(nan_counts[nan_counts > 0])
    
    # Check feature statistics
    print("\n📊 Feature Statistics (sample):")
    feature_cols = [col for col in df_with_targets.columns if col not in ['open', 'high', 'low', 'close', 'volume', 'target', 'future_return', 'trend_target']]
    sample_features = feature_cols[:5]  # First 5 features
    
    for col in sample_features:
        values = df_with_targets[col].dropna()
        print(f"{col}: mean={values.mean():.4f}, std={values.std():.4f}, min={values.min():.4f}, max={values.max():.4f}")
    
    return df_with_targets

if __name__ == "__main__":
    try:
        df = analyze_data()
        print("\n✅ Data analysis completed successfully!")
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
