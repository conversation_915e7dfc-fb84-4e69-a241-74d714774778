#!/usr/bin/env python3
"""
Test Enhanced EMA Test-and-Bounce Logic with Approach Direction

This test verifies the improved EMA logic that checks:
1. Where price came from (approach direction)
2. Where it tested (candle wicks)
3. Where it closed (bounce/rejection confirmation)
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from fixed_live_trader import FixedLiveTrader

def create_test_data_with_previous(prev_price, current_price, ema_10, ema_20, atr):
    """Create test DataFrame with previous and current candle data"""
    data = {
        'close': [prev_price, current_price],  # Previous and current close
        'high': [prev_price + 2, current_price + 5],
        'low': [prev_price - 2, current_price - 5],
        'ema_10': [ema_10, ema_10],
        'ema_20': [ema_20, ema_20],
        'regression_lower': [4300, 4300],
        'regression_upper': [4400, 4400],
        'regression_lower_short': [4320, 4320],
        'regression_upper_short': [4380, 4380],
        'atr': [atr, atr]
    }
    return pd.DataFrame(data)

def test_enhanced_ema_logic():
    """Test the enhanced EMA test-and-bounce logic with approach direction"""
    print("🧪 ENHANCED EMA TEST-AND-BOUNCE LOGIC")
    print("=" * 70)
    
    trader = FixedLiveTrader("XAUUSD!")
    
    test_scenarios = [
        {
            'name': 'Valid BUY Test-and-Bounce',
            'description': 'Price from above EMA → tested below → bounced back',
            'prev_price': 4370.00,  # Was above EMA
            'current_price': 4363.06,  # Closed near EMA
            'candle_low': 4350.25,  # Tested below EMA
            'candle_high': 4369.40,
            'ema_level': 4363.06,
            'expected_buy': True,
            'expected_sell': False
        },
        {
            'name': 'Invalid BUY Test (came from below)',
            'description': 'Price from below EMA → tested below → bounced back',
            'prev_price': 4355.00,  # Was below EMA (INVALID for BUY)
            'current_price': 4363.06,  # Closed near EMA
            'candle_low': 4350.25,  # Tested below EMA
            'candle_high': 4369.40,
            'ema_level': 4363.06,
            'expected_buy': False,  # Should be FALSE - didn't come from above
            'expected_sell': False
        },
        {
            'name': 'Valid SELL Test-and-Rejection',
            'description': 'Price from below EMA → tested above → rejected back',
            'prev_price': 4355.00,  # Was below EMA
            'current_price': 4363.06,  # Closed near EMA
            'candle_low': 4350.25,
            'candle_high': 4375.00,  # Tested above EMA
            'ema_level': 4363.06,
            'expected_buy': False,
            'expected_sell': True
        },
        {
            'name': 'Invalid SELL Test (came from above)',
            'description': 'Price from above EMA → tested above → rejected back',
            'prev_price': 4370.00,  # Was above EMA (INVALID for SELL)
            'current_price': 4363.06,  # Closed near EMA
            'candle_low': 4350.25,
            'candle_high': 4375.00,  # Tested above EMA
            'ema_level': 4363.06,
            'expected_buy': False,
            'expected_sell': False  # Should be FALSE - didn't come from below
        },
        {
            'name': 'No Test Pattern',
            'description': 'Price near EMA but no significant wick testing',
            'prev_price': 4370.00,  # Was above EMA
            'current_price': 4363.06,  # Closed near EMA
            'candle_low': 4362.00,  # Didn't test far enough below
            'candle_high': 4364.00,  # Didn't test far enough above
            'ema_level': 4363.06,
            'expected_buy': False,
            'expected_sell': False
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🔍 TEST {i}: {scenario['name']}")
        print(f"   {scenario['description']}")
        print("-" * 50)
        
        # Create test data
        df = create_test_data_with_previous(
            prev_price=scenario['prev_price'],
            current_price=scenario['current_price'],
            ema_10=scenario['ema_level'],
            ema_20=scenario['ema_level'],
            atr=10.0
        )
        
        # Create current candle data
        current_candle = {
            'high': scenario['candle_high'],
            'low': scenario['candle_low'],
            'close': scenario['current_price']
        }
        
        print(f"📊 SETUP:")
        print(f"   Previous Close: {scenario['prev_price']:.2f}")
        print(f"   EMA Level: {scenario['ema_level']:.2f}")
        print(f"   Current Close: {scenario['current_price']:.2f}")
        print(f"   Candle Low: {scenario['candle_low']:.2f}")
        print(f"   Candle High: {scenario['candle_high']:.2f}")
        print(f"   Tolerance: {10.0 * 0.05:.2f}")
        
        # Test the confluence calculation
        confluence = trader._get_support_resistance_confluence(
            df, scenario['current_price'], "UP", current_candle
        )
        
        # Analyze results
        buy_detected = confluence['buy_confluence'] > 0.5
        sell_detected = confluence['sell_confluence'] > 0.5
        
        print(f"\n🎯 RESULTS:")
        print(f"   BUY Pattern Detected: {buy_detected} (Expected: {scenario['expected_buy']})")
        print(f"   SELL Pattern Detected: {sell_detected} (Expected: {scenario['expected_sell']})")
        print(f"   BuyConf: {confluence['buy_confluence']:.3f}")
        print(f"   SellConf: {confluence['sell_confluence']:.3f}")
        
        # Validation
        buy_correct = buy_detected == scenario['expected_buy']
        sell_correct = sell_detected == scenario['expected_sell']
        
        if buy_correct and sell_correct:
            print(f"   ✅ TEST PASSED - Logic working correctly!")
        else:
            print(f"   ❌ TEST FAILED - Logic needs adjustment")
            if not buy_correct:
                print(f"      BUY detection wrong: got {buy_detected}, expected {scenario['expected_buy']}")
            if not sell_correct:
                print(f"      SELL detection wrong: got {sell_detected}, expected {scenario['expected_sell']}")

def test_approach_direction_logic():
    """Test the specific approach direction logic"""
    print(f"\n🧪 APPROACH DIRECTION LOGIC TEST")
    print("=" * 70)
    
    # Test parameters
    ema_level = 4363.06
    tolerance = 10.0 * 0.05  # 0.5
    current_price = 4363.06  # At EMA
    
    test_cases = [
        # (prev_price, candle_low, candle_high, expected_buy, expected_sell, description)
        (4370.00, 4350.25, 4369.40, True, False, "From above → tested below → bounced (BUY)"),
        (4355.00, 4350.25, 4375.00, False, True, "From below → tested above → rejected (SELL)"),
        (4355.00, 4350.25, 4369.40, False, False, "From below → tested below (INVALID)"),
        (4370.00, 4358.00, 4375.00, False, False, "From above → tested above (INVALID)"),
        (4363.06, 4350.25, 4375.00, False, False, "From EMA → tested both ways (INVALID)"),
    ]
    
    for i, (prev_price, candle_low, candle_high, expected_buy, expected_sell, description) in enumerate(test_cases, 1):
        print(f"\n🔍 CASE {i}: {description}")
        
        # Manual logic check
        print(f"   Previous: {prev_price:.2f}, EMA: {ema_level:.2f}, Current: {current_price:.2f}")
        print(f"   Candle: Low {candle_low:.2f}, High {candle_high:.2f}")
        
        # Check conditions manually
        came_from_above = prev_price > ema_level + tolerance
        came_from_below = prev_price < ema_level - tolerance
        tested_below = candle_low < ema_level - tolerance
        tested_above = candle_high > ema_level + tolerance
        closed_near_ema = abs(current_price - ema_level) <= tolerance
        
        print(f"   Came from above EMA? {came_from_above}")
        print(f"   Came from below EMA? {came_from_below}")
        print(f"   Tested below EMA? {tested_below}")
        print(f"   Tested above EMA? {tested_above}")
        print(f"   Closed near EMA? {closed_near_ema}")
        
        # Apply enhanced logic
        buy_pattern = came_from_above and tested_below and closed_near_ema
        sell_pattern = came_from_below and tested_above and closed_near_ema
        
        print(f"   BUY Pattern: {buy_pattern} (Expected: {expected_buy})")
        print(f"   SELL Pattern: {sell_pattern} (Expected: {expected_sell})")
        
        if buy_pattern == expected_buy and sell_pattern == expected_sell:
            print(f"   ✅ LOGIC CORRECT")
        else:
            print(f"   ❌ LOGIC ERROR")

def main():
    """Run all enhanced EMA logic tests"""
    print("🚀 ENHANCED EMA TEST-AND-BOUNCE LOGIC TESTS")
    print("=" * 70)
    print("Testing improved logic that checks approach direction:")
    print("• BUY: Price from ABOVE → tested BELOW → bounced back")
    print("• SELL: Price from BELOW → tested ABOVE → rejected back")
    
    test_enhanced_ema_logic()
    test_approach_direction_logic()
    
    print(f"\n💡 ENHANCEMENT SUMMARY:")
    print(f"   ✅ Added approach direction validation")
    print(f"   ✅ BUY signals require coming from above EMA")
    print(f"   ✅ SELL signals require coming from below EMA")
    print(f"   ✅ Eliminates false test-and-bounce patterns")
    print(f"   ✅ More accurate support/resistance classification")

if __name__ == "__main__":
    main()
