#!/usr/bin/env python3
"""
Test detailed regime reasoning and logging
"""

import sys
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_detailed_regime_reasoning():
    """Test the detailed regime reasoning and logging"""
    print("🔍 TESTING DETAILED REGIME REASONING")
    print("=" * 50)
    
    try:
        # Initialize trader
        trader = FixedLiveTrader()
        
        # Connect to MT5
        if not trader.mt5_manager.connect():
            print("❌ Cannot connect to MT5")
            return
        
        print("✅ Connected to MT5")
        
        print("\n1️⃣ REGIME REASONING FEATURES")
        print("-" * 35)
        
        print("NEW DETAILED LOGGING INCLUDES:")
        print("✅ Specific reason for each scoring factor")
        print("✅ Threshold comparisons with actual values")
        print("✅ Point allocation breakdown")
        print("✅ Final regime decision reasoning")
        print("✅ Brief reason in main log")
        print("✅ Full breakdown every 10 iterations")
        
        print("\n2️⃣ SCORING FACTORS TRACKED")
        print("-" * 35)
        
        print("Each factor shows:")
        print("• ATR Analysis: Volatility level vs thresholds")
        print("• Fast EMA Slope: Trend strength vs thresholds")
        print("• Slow EMA Slope: Trend confirmation vs thresholds")
        print("• RSI Momentum: Momentum strength vs neutral zone")
        print("• Multi-timeframe Momentum: Directional consistency")
        print("• Bollinger Bands: Width and squeeze analysis")
        print("• Volatility: Price volatility percentile")
        
        print("\n3️⃣ CURRENT MARKET ANALYSIS WITH REASONING")
        print("-" * 50)
        
        result = trader.get_live_prediction()
        if result:
            signal, confidence, details, atr_value, regime, logic, regime_details, candle_strength = result
            
            print(f"Current Market Status:")
            print(f"• {details}")
            
            # Show detailed reasoning
            reasoning = regime_details.get('reasoning', [])
            regime_reason = regime_details.get('regime_reason', 'No reason available')
            
            print(f"\n🎯 REGIME DECISION BREAKDOWN:")
            print(f"Final Decision: {regime_reason}")
            
            print(f"\n🔍 DETAILED SCORING:")
            if reasoning:
                for i, reason in enumerate(reasoning, 1):
                    print(f"{i:2d}. {reason}")
            else:
                print("No detailed reasoning available")
            
            print(f"\n📊 SCORE SUMMARY:")
            trending_score = regime_details.get('trending_score', 0)
            ranging_score = regime_details.get('ranging_score', 0)
            score_diff = regime_details.get('score_diff', 0)
            confidence_val = regime_details.get('confidence', 0)
            
            print(f"• Trending Score: {trending_score:.1f}/13.5")
            print(f"• Ranging Score: {ranging_score:.1f}/13.5")
            print(f"• Score Difference: {score_diff:.1f}")
            print(f"• Confidence: {confidence_val:.1%}")
            
            # Explain the decision logic
            print(f"\n🧮 DECISION LOGIC:")
            print(f"• Minimum Score Required: 3.0")
            print(f"• Minimum Score Difference: 1.0")
            
            if regime == "TRENDING":
                print(f"✅ TRENDING: {trending_score:.1f} ≥ 3.0 AND {score_diff:.1f} ≥ 1.0 AND {trending_score:.1f} > {ranging_score:.1f}")
            elif regime == "RANGING":
                print(f"✅ RANGING: {ranging_score:.1f} ≥ 3.0 AND {score_diff:.1f} ≥ 1.0 AND {ranging_score:.1f} > {trending_score:.1f}")
            else:
                print(f"✅ TRANSITIONAL: Either insufficient score or scores too close")
                if score_diff < 1.0:
                    print(f"   Reason: Scores too close ({score_diff:.1f} < 1.0)")
                else:
                    print(f"   Reason: Insufficient evidence (max {max(trending_score, ranging_score):.1f} < 3.0)")
        
        print(f"\n4️⃣ LOGGING FREQUENCY")
        print("-" * 25)
        
        print("MAIN LOG (Every Iteration):")
        print("• Brief regime reason in main analysis line")
        print("• Shows: TRENDING/RANGING/TRANSITIONAL + brief reason")
        print("")
        print("DETAILED LOG (Every 10 Iterations):")
        print("• Complete scoring breakdown")
        print("• All factor reasoning")
        print("• Full decision explanation")
        print("• Candle strength analysis")
        
        print(f"\n5️⃣ EXAMPLE REASONING PATTERNS")
        print("-" * 40)
        
        print("TRENDING Examples:")
        print("• ATR: HIGH volatility (75% > 70%) → +3 TRENDING")
        print("• Fast EMA: STRONG slope (0.0015 > 0.0012) → +2.5 TRENDING")
        print("• BB: WIDE bands (80% > 65%) → +2 TRENDING")
        print("")
        print("RANGING Examples:")
        print("• ATR: LOW volatility (25% < 30%) → +3 RANGING")
        print("• Fast EMA: FLAT slope (0.0003 < 0.0006) → +2.5 RANGING")
        print("• BB: SQUEEZE detected (15% < 25%) → +2 RANGING")
        print("")
        print("TRANSITIONAL Examples:")
        print("• Scores too close (5.5 vs 5.2, diff 0.3 < 1.0)")
        print("• Insufficient evidence (max score 2.8 < 3.0)")
        
        print(f"\n6️⃣ BENEFITS OF DETAILED REASONING")
        print("-" * 45)
        
        print("✅ TRANSPARENCY:")
        print("• See exactly why regime was classified")
        print("• Understand which factors are most important")
        print("• Track threshold sensitivity")
        
        print(f"\n✅ DEBUGGING:")
        print("• Identify when thresholds need adjustment")
        print("• See which factors are conflicting")
        print("• Understand regime transitions")
        
        print(f"\n✅ OPTIMIZATION:")
        print("• Fine-tune individual factor weights")
        print("• Adjust thresholds based on performance")
        print("• Improve regime detection accuracy")
        
        trader.mt5_manager.disconnect()
        
        print(f"\n✅ DETAILED REGIME REASONING TEST COMPLETE")
        print("=" * 50)
        print("🔍 Every regime decision now shows detailed reasoning")
        print("📊 Main log shows brief reason, detailed log every 10 iterations")
        print("🎯 Complete transparency in regime classification")
        print("🚀 Enhanced debugging and optimization capabilities")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_detailed_regime_reasoning()
