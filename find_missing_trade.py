#!/usr/bin/env python3
"""
Find the missing trade with -4.76 loss at 10/01 8:45:30
"""

import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def find_target_trade():
    """Find the specific trade with -4.76 loss"""
    try:
        if not mt5.initialize():
            logger.error("❌ Failed to initialize MT5")
            return
        
        logger.info("🔍 Searching for trade with -4.76 loss...")
        
        # Get deals from last 7 days to be sure
        now = datetime.now()
        from_date = now - timedelta(days=7)
        
        deals = mt5.history_deals_get(from_date, now)
        if deals is None:
            logger.error("❌ No deals found")
            return
        
        deals_df = pd.DataFrame(list(deals), columns=deals[0]._asdict().keys())
        deals_df['time'] = pd.to_datetime(deals_df['time'], unit='s')
        
        logger.info(f"📊 Found {len(deals_df)} total deals")
        
        # Filter for XAUUSD deals only
        xauusd_deals = deals_df[deals_df['symbol'] == 'XAUUSD!']
        logger.info(f"📊 Found {len(xauusd_deals)} XAUUSD deals")
        
        # Look for deals with profit close to -4.76
        target_profit = -4.76
        tolerance = 0.1  # Within 10 cents
        
        close_deals = xauusd_deals[
            (xauusd_deals['profit'] >= target_profit - tolerance) & 
            (xauusd_deals['profit'] <= target_profit + tolerance)
        ]
        
        logger.info(f"🎯 Found {len(close_deals)} deals with profit close to -4.76:")
        
        for _, deal in close_deals.iterrows():
            logger.info(f"   Ticket: {deal['position_id']} | Time: {deal['time']} | Profit: ${deal['profit']:.2f} | Price: {deal['price']}")
        
        # Also look for deals on 2025-10-01 around 8:45
        oct_1_deals = xauusd_deals[
            (xauusd_deals['time'].dt.date == pd.to_datetime('2025-10-01').date()) &
            (xauusd_deals['time'].dt.hour >= 8) &
            (xauusd_deals['time'].dt.hour <= 9)
        ]
        
        logger.info(f"🗓️ Found {len(oct_1_deals)} deals on 2025-10-01 between 8-9 AM:")
        
        for _, deal in oct_1_deals.iterrows():
            logger.info(f"   Ticket: {deal['position_id']} | Time: {deal['time']} | Profit: ${deal['profit']:.2f} | Price: {deal['price']}")
        
        # Show all deals with negative profit for reference
        loss_deals = xauusd_deals[xauusd_deals['profit'] < 0].sort_values('time')
        logger.info(f"📉 All loss deals (last 20):")
        
        for _, deal in loss_deals.tail(20).iterrows():
            logger.info(f"   Ticket: {deal['position_id']} | Time: {deal['time']} | Profit: ${deal['profit']:.2f}")
        
        # Show the most recent deals
        recent_deals = xauusd_deals.sort_values('time').tail(10)
        logger.info(f"🕐 Most recent 10 deals:")
        
        for _, deal in recent_deals.iterrows():
            logger.info(f"   Ticket: {deal['position_id']} | Time: {deal['time']} | Profit: ${deal['profit']:.2f} | Entry: {deal['entry']}")
        
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    find_target_trade()
