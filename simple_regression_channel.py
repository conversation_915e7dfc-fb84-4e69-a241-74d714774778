#!/usr/bin/env python3
"""
Simple Regression Channel System
Basic linear regression channel implementation
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
import logging

class SimpleRegressionChannel:
    """
    Simple Regression Channel System
    
    Features:
    - Fixed 20-period lookback
    - Basic linear regression with numpy.polyfit
    - Upper/lower bounds using 2 standard deviations
    - Position calculation within channel
    """
    
    def __init__(self, 
                 periods: int = 20,
                 std_multiplier: float = 2.0):
        """
        Initialize Simple Regression Channel
        
        Args:
            periods: Number of periods for regression calculation
            std_multiplier: Standard deviation multiplier for channel bounds
        """
        self.periods = periods
        self.std_multiplier = std_multiplier
        self.logger = logging.getLogger(__name__)
        
        # Simple state tracking
        self.current_channel_data = None
        
    def calculate_regression_channel(self, prices: pd.Series) -> Dict[str, Any]:
        """
        Calculate regression channel with upper and lower bounds
        
        Args:
            prices: Series of price data
            
        Returns:
            Dictionary with channel data or None if insufficient data
        """
        if len(prices) < self.periods:
            return None
            
        # Get the last 'periods' prices
        y_values = prices.iloc[-self.periods:].values
        x_values = np.arange(self.periods)
        
        if len(y_values) != self.periods or np.any(np.isnan(y_values)):
            return None
            
        # Calculate linear regression
        slope, intercept = np.polyfit(x_values, y_values, 1)
        
        # Calculate regression line value at current point
        regression_value = slope * (self.periods - 1) + intercept
        
        # Calculate residuals (deviations from regression line)
        predicted_values = slope * x_values + intercept
        residuals = y_values - predicted_values
        std_residual = np.std(residuals)
        
        # Calculate channel bounds (regression line ± std_multiplier * standard deviations)
        upper_bound = regression_value + (self.std_multiplier * std_residual)
        lower_bound = regression_value - (self.std_multiplier * std_residual)
        
        # Calculate position within channel (0 = lower bound, 1 = upper bound)
        current_price = y_values[-1]
        if upper_bound != lower_bound:
            position = (current_price - lower_bound) / (upper_bound - lower_bound)
        else:
            position = 0.5
            
        # Calculate R-squared for quality assessment
        ss_res = np.sum(residuals ** 2)
        ss_tot = np.sum((y_values - np.mean(y_values)) ** 2)
        r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        channel_data = {
            'center_line': regression_value,
            'upper_bound': upper_bound,
            'lower_bound': lower_bound,
            'width': upper_bound - lower_bound,
            'position': position,
            'slope': slope,
            'r_squared': r_squared,
            'candle_count': self.periods,
            'current_price': current_price
        }
        
        return channel_data
    
    def update_channel_state(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Update channel state with new data
        
        Args:
            df: DataFrame with OHLC data
            
        Returns:
            Dictionary with channel state information
        """
        if len(df) < self.periods:
            self.current_channel_data = None
            return {
                'state': 'BUILDING',
                'has_active_channel': False,
                'regime_contribution': 0.0,
                'channel_data': None
            }
            
        # Calculate new channel
        self.current_channel_data = self.calculate_regression_channel(df['close'])
        
        if self.current_channel_data is None:
            return {
                'state': 'BUILDING',
                'has_active_channel': False,
                'regime_contribution': 0.0,
                'channel_data': None
            }
        
        # Determine regime contribution based on slope
        slope_abs = abs(self.current_channel_data['slope'])
        
        if slope_abs <= 0.05:
            # Flat channel - contribute to RANGING regime
            regime_contribution = -2.0  # Negative to push toward RANGING
        else:
            # Trending channel - no contribution, let other indicators decide
            regime_contribution = 0.0
            
        return {
            'state': 'ACTIVE_CHANNEL',
            'has_active_channel': True,
            'regime_contribution': regime_contribution,
            'channel_data': self.current_channel_data
        }
    
    def get_channel_position(self, current_price: float) -> Optional[float]:
        """
        Get current price position within the channel
        
        Args:
            current_price: Current price to evaluate
            
        Returns:
            Position within channel (0-1) or None if no active channel
        """
        if self.current_channel_data is None:
            return None
            
        upper_bound = self.current_channel_data['upper_bound']
        lower_bound = self.current_channel_data['lower_bound']
        
        if upper_bound == lower_bound:
            return 0.5
            
        position = (current_price - lower_bound) / (upper_bound - lower_bound)
        return max(0.0, min(1.0, position))  # Clamp to 0-1 range
    
    def get_status_summary(self) -> Dict[str, Any]:
        """
        Get summary of current channel status
        
        Returns:
            Dictionary with status information
        """
        if self.current_channel_data is None:
            return {
                'state': 'BUILDING',
                'has_active_channel': False,
                'regime_contribution': 0.0,
                'channel_candles': 0,
                'channel_r_squared': 0.0,
                'channel_width': 0.0,
                'channel_slope': 0.0
            }
            
        slope_abs = abs(self.current_channel_data['slope'])
        regime_contribution = -2.0 if slope_abs <= 0.05 else 0.0
        
        return {
            'state': 'ACTIVE_CHANNEL',
            'has_active_channel': True,
            'regime_contribution': regime_contribution,
            'channel_candles': self.current_channel_data['candle_count'],
            'channel_r_squared': self.current_channel_data['r_squared'],
            'channel_width': self.current_channel_data['width'],
            'channel_slope': self.current_channel_data['slope']
        }
