#!/usr/bin/env python3
"""
Visual example of candle strength calculation with ASCII diagrams
"""

def visual_candle_strength_example():
    """Visual example showing how candle strength is calculated"""
    print("📊 CANDLE STRENGTH VISUAL EXAMPLE")
    print("=" * 60)
    
    print("\n🕯️ EXAMPLE: Last 8 Candles Analysis")
    print("-" * 45)
    
    # Example candle data
    candles = [
        {"type": "Bullish", "body": 2.5, "range": 3.0, "weight": 0.83},
        {"type": "Bearish", "body": 1.8, "range": 2.2, "weight": 0.82},
        {"type": "Bullish", "body": 3.2, "range": 3.5, "weight": 0.91},
        {"type": "Bullish", "body": 1.5, "range": 2.8, "weight": 0.54},
        {"type": "Bearish", "body": 0.8, "range": 2.1, "weight": 0.38},
        {"type": "Bullish", "body": 2.8, "range": 3.1, "weight": 0.90},
        {"type": "Bullish", "body": 2.1, "range": 2.4, "weight": 0.88},
        {"type": "Bullish", "body": 1.9, "range": 2.3, "weight": 0.83}
    ]
    
    print("Candle Analysis (most recent 8 candles):")
    print("Candle | Type     | Body | Range | Body/Range | Weight")
    print("-" * 55)
    
    bullish_total = 0
    bearish_total = 0
    
    for i, candle in enumerate(candles, 1):
        body_range_ratio = candle["body"] / candle["range"]
        print(f"   {i}   | {candle['type']:8s} | {candle['body']:4.1f} | {candle['range']:5.1f} | {body_range_ratio:8.2f} | {candle['weight']:6.2f}")
        
        if candle["type"] == "Bullish":
            bullish_total += candle["weight"]
        else:
            bearish_total += candle["weight"]
    
    print("\n📊 STRENGTH CALCULATION:")
    print("-" * 30)
    
    total_strength = bullish_total + bearish_total
    bullish_norm = bullish_total / total_strength
    bearish_norm = bearish_total / total_strength
    net_strength = bullish_norm - bearish_norm
    percentage = net_strength * 100
    
    print(f"Bullish Total Weight: {bullish_total:.2f}")
    print(f"Bearish Total Weight: {bearish_total:.2f}")
    print(f"Total Strength: {total_strength:.2f}")
    print("")
    print(f"Bullish Normalized: {bullish_norm:.3f} ({bullish_norm*100:.1f}%)")
    print(f"Bearish Normalized: {bearish_norm:.3f} ({bearish_norm*100:.1f}%)")
    print(f"Net Strength: {net_strength:+.3f}")
    print(f"Candle Strength: {percentage:+.1f}%")
    
    # Visual representation
    print(f"\n📈 VISUAL REPRESENTATION:")
    print("-" * 35)
    
    print("Last 8 Candles (left = oldest, right = newest):")
    print("")
    
    # ASCII candle chart
    candle_symbols = []
    for candle in candles:
        if candle["type"] == "Bullish":
            if candle["weight"] > 0.8:
                candle_symbols.append("🟢")  # Strong bullish
            else:
                candle_symbols.append("🔵")  # Weak bullish
        else:
            if candle["weight"] > 0.8:
                candle_symbols.append("🔴")  # Strong bearish
            else:
                candle_symbols.append("🟠")  # Weak bearish
    
    print("Candles: " + " ".join(candle_symbols))
    print("Legend: 🟢=Strong Bull, 🔵=Weak Bull, 🔴=Strong Bear, 🟠=Weak Bear")
    
    # Strength bar
    print(f"\nStrength Bar (-100% to +100%):")
    bar_length = 40
    zero_pos = bar_length // 2
    strength_pos = int((net_strength + 1) * bar_length / 2)
    
    bar = ["-"] * bar_length
    bar[zero_pos] = "|"
    bar[strength_pos] = "●"
    
    print("".join(bar))
    print(f"{'Bearish':<{zero_pos}}{'0%':^{bar_length-zero_pos-7}}{'Bullish':>7}")
    print(f"Current: {percentage:+.1f}%")
    
    print(f"\n🎯 TRADING DECISION:")
    print("-" * 25)
    
    if percentage > 15:
        decision = "BUY SIGNAL"
        reason = f"Bullish strength {percentage:+.1f}% > +15% threshold"
        color = "🟢"
    elif percentage < -15:
        decision = "SELL SIGNAL"
        reason = f"Bearish strength {percentage:+.1f}% < -15% threshold"
        color = "🔴"
    else:
        decision = "NO SIGNAL"
        reason = f"Neutral strength {percentage:+.1f}% between ±15%"
        color = "🟡"
    
    print(f"{color} {decision}")
    print(f"Reason: {reason}")
    
    print(f"\n🔄 SENSITIVE CLOSING LOGIC:")
    print("-" * 35)
    
    print("If you had existing positions:")
    if percentage > 0:
        print(f"• BUY position: KEEP (strength is positive {percentage:+.1f}%)")
        print(f"• SELL position: CLOSE (strength turned positive {percentage:+.1f}%)")
    else:
        print(f"• BUY position: CLOSE (strength turned negative {percentage:+.1f}%)")
        print(f"• SELL position: KEEP (strength is negative {percentage:+.1f}%)")
    
    print(f"\n🧮 KEY INSIGHTS:")
    print("-" * 20)
    
    bullish_count = sum(1 for c in candles if c["type"] == "Bullish")
    bearish_count = len(candles) - bullish_count
    
    print(f"• Bullish Candles: {bullish_count}/8 ({bullish_count/8*100:.0f}%)")
    print(f"• Bearish Candles: {bearish_count}/8 ({bearish_count/8*100:.0f}%)")
    print(f"• Weighted Result: {percentage:+.1f}% (considers candle quality)")
    print(f"• Simple Count: {(bullish_count-bearish_count)/8*100:+.0f}% (ignores quality)")
    print("")
    print("The weighted approach is more accurate because:")
    print("✅ Strong, decisive candles have more influence")
    print("✅ Weak, indecisive candles have less influence")
    print("✅ Body-to-range ratio matters (less wicks = more decisive)")
    print("✅ ATR normalization accounts for volatility")
    
    print(f"\n🎉 CANDLE STRENGTH VISUAL EXAMPLE COMPLETE!")
    print("=" * 60)
    print("This is how your system analyzes market momentum")
    print("using the last 8 candles to make trading decisions!")

if __name__ == "__main__":
    visual_candle_strength_example()
