#!/usr/bin/env python3
"""
Test script to verify 30 pip breathing room for candle confirmation trailing
"""

import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader

def test_30_pip_breathing_room():
    """Test that candle confirmation trailing uses 30 pips instead of 10"""
    print("🧪 TESTING 30 PIP BREATHING ROOM FOR CANDLE CONFIRMATION TRAILING")
    print("=" * 70)
    
    trader = FixedLiveTrader("XAUUSD!")
    
    # Mock profitable position (so trailing is allowed)
    trader.current_position = {
        'ticket': 12345,
        'type': 'BUY',
        'price': 2000.00,  # Entry price
        'volume': 0.10,
        'stop_loss': 1970.00,  # 30 points SL
        'take_profit': 2030.00
    }
    
    # Mock MT5 manager for profitable scenario
    class MockMT5Manager:
        def get_symbol_info_tick(self, symbol):
            return {
                'bid': 2020.0,  # 20 points profit
                'ask': 2020.5
            }
        
        def get_contract_size(self, symbol):
            return 100
        
        def get_pip_size(self, symbol):
            return 0.01  # XAUUSD pip size
        
        def get_positions(self, symbol):
            return [{
                'ticket': 12345,
                'sl': 1970.00
            }]
        
        def modify_position(self, ticket, stop_loss):
            print(f"   📝 MT5 MODIFY POSITION:")
            print(f"      Ticket: {ticket}")
            print(f"      New Stop Loss: {stop_loss:.5f}")
            return True
    
    trader.mt5_manager = MockMT5Manager()
    
    # Test BUY position trailing
    print("🧪 TEST 1: BUY Position Candle Confirmation Trailing")
    print("-" * 50)
    
    confirmation_candle = {
        'high': 2025.00,
        'low': 2015.00,
        'close': 2020.00
    }
    
    print(f"📊 SETUP:")
    print(f"   Position Type: BUY")
    print(f"   Entry Price: {trader.current_position['price']:.2f}")
    print(f"   Current SL: {trader.current_position['stop_loss']:.2f}")
    print(f"   Confirmation Candle Low: {confirmation_candle['low']:.2f}")
    print(f"   Expected New SL: {confirmation_candle['low'] - 0.30:.2f} (30 pips below)")
    print()
    
    result = trader.set_candle_confirmation_trailing_stop(confirmation_candle, 'BUY')
    print(f"   Result: {'✅ SUCCESS' if result else '❌ FAILED'}")
    print()
    
    # Test SELL position trailing
    print("🧪 TEST 2: SELL Position Candle Confirmation Trailing")
    print("-" * 50)
    
    # Change to SELL position
    trader.current_position['type'] = 'SELL'
    trader.current_position['price'] = 2000.00
    trader.current_position['stop_loss'] = 2030.00  # SL above entry for SELL
    
    # Mock MT5 for SELL profit scenario
    class MockMT5ManagerSell:
        def get_symbol_info_tick(self, symbol):
            return {
                'bid': 1980.0,  # 20 points profit for SELL
                'ask': 1980.5
            }
        
        def get_contract_size(self, symbol):
            return 100
        
        def get_pip_size(self, symbol):
            return 0.01
        
        def get_positions(self, symbol):
            return [{
                'ticket': 12345,
                'sl': 2030.00
            }]
        
        def modify_position(self, ticket, stop_loss):
            print(f"   📝 MT5 MODIFY POSITION:")
            print(f"      Ticket: {ticket}")
            print(f"      New Stop Loss: {stop_loss:.5f}")
            return True
    
    trader.mt5_manager = MockMT5ManagerSell()
    
    confirmation_candle_sell = {
        'high': 1985.00,
        'low': 1975.00,
        'close': 1980.00
    }
    
    print(f"📊 SETUP:")
    print(f"   Position Type: SELL")
    print(f"   Entry Price: {trader.current_position['price']:.2f}")
    print(f"   Current SL: {trader.current_position['stop_loss']:.2f}")
    print(f"   Confirmation Candle High: {confirmation_candle_sell['high']:.2f}")
    print(f"   Expected New SL: {confirmation_candle_sell['high'] + 0.30:.2f} (30 pips above)")
    print()
    
    result_sell = trader.set_candle_confirmation_trailing_stop(confirmation_candle_sell, 'SELL')
    print(f"   Result: {'✅ SUCCESS' if result_sell else '❌ FAILED'}")
    
    print("\n" + "=" * 70)
    print("🏁 FINAL RESULTS:")
    print(f"   BUY Trailing Test: {'✅ PASSED' if result else '❌ FAILED'}")
    print(f"   SELL Trailing Test: {'✅ PASSED' if result_sell else '❌ FAILED'}")
    
    if result and result_sell:
        print("\n🎯 30 PIP BREATHING ROOM IMPLEMENTED SUCCESSFULLY! 🚀")
        print("   Candle confirmation trailing now uses 30 pips instead of 10 pips")
        print("   This provides more breathing room for regime changes, velocity, and acceleration exits")
    else:
        print("\n⚠️ Some tests failed - please review the implementation.")
    
    return result and result_sell

if __name__ == "__main__":
    test_30_pip_breathing_room()
