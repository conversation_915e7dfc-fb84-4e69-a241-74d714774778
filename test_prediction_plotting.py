"""
Test script for LSTM Prediction Plotting System
Validates that the prediction and plotting pipeline works correctly
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import sys

# Add src to path
sys.path.append('src')

from prediction_plotter import PredictionPlotter

def load_test_data():
    """Load recent market data for testing"""
    try:
        print("Loading test data...")
        
        # Load the XAUUSD data
        df = pd.read_csv('data/XAU_5m_data.csv')
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        # Use recent data (last 500 candles for context)
        df_recent = df.tail(500).copy()
        
        print(f"✅ Loaded {len(df_recent)} recent candles")
        print(f"Date range: {df_recent.index[0]} to {df_recent.index[-1]}")
        print(f"Current close: ${df_recent.iloc[-1]['close']:.2f}")
        
        return df_recent
        
    except Exception as e:
        print(f"❌ Error loading test data: {e}")
        return None

def test_prediction_plotter():
    """Test the complete prediction plotting pipeline"""
    try:
        print("=== TESTING LSTM PREDICTION PLOTTER ===")
        
        # Load test data
        df = load_test_data()
        if df is None:
            return False
        
        # Initialize plotter
        print("\nInitializing PredictionPlotter...")
        plotter = PredictionPlotter()
        
        # Test prediction pipeline
        print("\nTesting prediction pipeline...")
        price_predictions, plot_path = plotter.predict_and_plot(
            df, 
            save_path='plots/test_prediction.png',
            show_candles=150
        )
        
        if price_predictions is None:
            print("❌ Prediction pipeline failed")
            return False
        
        # Validate predictions
        print("\nValidating predictions...")
        
        # Check that we have predictions for all steps
        expected_steps = ['step_1', 'step_3', 'step_5']
        for step in expected_steps:
            if step not in price_predictions:
                print(f"❌ Missing predictions for {step}")
                return False
            
            # Check OHLC values are reasonable
            pred = price_predictions[step]
            current_close = df.iloc[-1]['close']
            
            for price_type in ['open', 'high', 'low', 'close']:
                if price_type not in pred:
                    print(f"❌ Missing {price_type} prediction for {step}")
                    return False
                
                # Check price is within reasonable range (±20% of current close)
                price_value = pred[price_type]
                if not (current_close * 0.8 <= price_value <= current_close * 1.2):
                    print(f"⚠️  Warning: {step} {price_type} prediction ${price_value:.2f} seems extreme (current: ${current_close:.2f})")
        
        # Check directions and probabilities
        for step in expected_steps:
            step_key = step
            if step_key not in price_predictions['directions']:
                print(f"❌ Missing direction prediction for {step}")
                return False
            
            direction = price_predictions['directions'][step_key]
            prob = price_predictions['direction_probs'][step_key]
            
            if direction not in [0, 1]:
                print(f"❌ Invalid direction value for {step}: {direction}")
                return False
            
            if not (0 <= prob <= 1):
                print(f"❌ Invalid probability for {step}: {prob}")
                return False
        
        print("✅ All predictions validated successfully")
        
        # Check plot was created
        if plot_path and os.path.exists(plot_path):
            print(f"✅ Plot created successfully: {plot_path}")
        else:
            print("⚠️  Plot creation failed or matplotlib not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing prediction plotter: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_components():
    """Test individual components of the prediction system"""
    try:
        print("\n=== TESTING INDIVIDUAL COMPONENTS ===")
        
        # Load test data
        df = load_test_data()
        if df is None:
            return False
        
        # Initialize plotter
        plotter = PredictionPlotter()
        
        # Test 1: Data preparation
        print("\n1. Testing data preparation...")
        sequence_data = plotter.prepare_prediction_data(df)
        
        if sequence_data is None:
            print("❌ Data preparation failed")
            return False
        
        expected_shape = (1, 60, 78)  # (batch, sequence, features)
        if sequence_data.shape != expected_shape:
            print(f"❌ Unexpected sequence shape: {sequence_data.shape}, expected: {expected_shape}")
            return False
        
        print(f"✅ Data preparation successful: {sequence_data.shape}")
        
        # Test 2: Model predictions
        print("\n2. Testing model predictions...")
        predictions = plotter.make_predictions(sequence_data)
        
        if predictions is None:
            print("❌ Model predictions failed")
            return False
        
        required_keys = ['price_percentages', 'direction_probs', 'direction_binary', 'change_percentages']
        for key in required_keys:
            if key not in predictions:
                print(f"❌ Missing prediction key: {key}")
                return False
        
        print("✅ Model predictions successful")
        
        # Test 3: Price conversion
        print("\n3. Testing price conversion...")
        current_close = df.iloc[-1]['close']
        price_predictions = plotter.convert_predictions_to_prices(predictions, current_close)
        
        if price_predictions is None:
            print("❌ Price conversion failed")
            return False
        
        # Check structure
        expected_steps = ['step_1', 'step_3', 'step_5']
        for step in expected_steps:
            if step not in price_predictions:
                print(f"❌ Missing price predictions for {step}")
                return False
        
        print("✅ Price conversion successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing individual components: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 Starting LSTM Prediction Plotter Tests")
    print("=" * 60)
    
    # Create plots directory
    os.makedirs('plots', exist_ok=True)
    
    # Test individual components first
    components_ok = test_individual_components()
    
    if not components_ok:
        print("\n❌ Individual component tests failed")
        return
    
    # Test complete pipeline
    pipeline_ok = test_prediction_plotter()
    
    if pipeline_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ LSTM Prediction Plotter is working correctly")
        print("✅ Ready for integration with live trading system")
    else:
        print("\n❌ PIPELINE TESTS FAILED")
        print("❌ Check errors above and fix issues")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
