#!/usr/bin/env python3
"""
Toggle Volume Divergence Filter On/Off
Quick script to enable or disable volume divergence filtering
"""

import sys
import os

def disable_divergence_filter():
    """Disable volume divergence filtering"""
    print("🔧 Disabling volume divergence filter...")
    
    # Read the file
    with open('qqe_indicator.py', 'r') as f:
        content = f.read()
    
    # Replace active filtering with disabled version
    old_code = """                # Only apply filter if divergence is strong enough
                if not pd.isna(divergence_strength) and divergence_strength >= self.divergence_strength_threshold:
                    if divergence_type == 'BEARISH_DIV' and current_qqe_signal > 0:
                        divergence_filter = True  # Block long trades during strong bearish divergence
                    elif divergence_type == 'BULLISH_DIV' and current_qqe_signal < 0:
                        divergence_filter = True  # Block short trades during strong bullish divergence"""
    
    new_code = """                # COMMENTED OUT: Only apply filter if divergence is strong enough
                # if not pd.isna(divergence_strength) and divergence_strength >= self.divergence_strength_threshold:
                #     if divergence_type == 'BEARISH_DIV' and current_qqe_signal > 0:
                #         divergence_filter = True  # Block long trades during strong bearish divergence
                #     elif divergence_type == 'BULLISH_DIV' and current_qqe_signal < 0:
                #         divergence_filter = True  # Block short trades during strong bullish divergence"""
    
    if old_code in content:
        content = content.replace(old_code, new_code)
        
        # Also ensure divergence_filter is always False
        content = content.replace(
            "divergence_filter = False",
            "# DIVERGENCE FILTER TEMPORARILY DISABLED - Always set to False\n                divergence_filter = False"
        )
        
        # Write back
        with open('qqe_indicator.py', 'w') as f:
            f.write(content)
        
        print("✅ Volume divergence filter DISABLED")
        return True
    else:
        print("⚠️ Filter already appears to be disabled")
        return False

def enable_divergence_filter():
    """Enable volume divergence filtering"""
    print("🔧 Enabling volume divergence filter...")
    
    # Read the file
    with open('qqe_indicator.py', 'r') as f:
        content = f.read()
    
    # Replace disabled version with active filtering
    old_code = """                # COMMENTED OUT: Only apply filter if divergence is strong enough
                # if not pd.isna(divergence_strength) and divergence_strength >= self.divergence_strength_threshold:
                #     if divergence_type == 'BEARISH_DIV' and current_qqe_signal > 0:
                #         divergence_filter = True  # Block long trades during strong bearish divergence
                #     elif divergence_type == 'BULLISH_DIV' and current_qqe_signal < 0:
                #         divergence_filter = True  # Block short trades during strong bullish divergence"""
    
    new_code = """                # Only apply filter if divergence is strong enough
                if not pd.isna(divergence_strength) and divergence_strength >= self.divergence_strength_threshold:
                    if divergence_type == 'BEARISH_DIV' and current_qqe_signal > 0:
                        divergence_filter = True  # Block long trades during strong bearish divergence
                    elif divergence_type == 'BULLISH_DIV' and current_qqe_signal < 0:
                        divergence_filter = True  # Block short trades during strong bullish divergence"""
    
    if old_code in content:
        content = content.replace(old_code, new_code)
        
        # Remove the disabled comment
        content = content.replace(
            "# DIVERGENCE FILTER TEMPORARILY DISABLED - Always set to False\n                divergence_filter = False",
            "divergence_filter = False"
        )
        
        # Write back
        with open('qqe_indicator.py', 'w') as f:
            f.write(content)
        
        print("✅ Volume divergence filter ENABLED")
        return True
    else:
        print("⚠️ Filter already appears to be enabled")
        return False

def check_filter_status():
    """Check current status of divergence filter"""
    print("🔍 Checking divergence filter status...")
    
    with open('qqe_indicator.py', 'r') as f:
        content = f.read()
    
    if "# DIVERGENCE FILTER TEMPORARILY DISABLED" in content:
        print("📊 Status: DISABLED ❌")
        return "disabled"
    elif "Only apply filter if divergence is strong enough" in content and "# COMMENTED OUT" not in content:
        print("📊 Status: ENABLED ✅")
        return "enabled"
    else:
        print("📊 Status: UNKNOWN ❓")
        return "unknown"

def main():
    """Main function"""
    print("🎛️ VOLUME DIVERGENCE FILTER TOGGLE")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python toggle_divergence_filter.py status   - Check current status")
        print("  python toggle_divergence_filter.py disable  - Disable filtering")
        print("  python toggle_divergence_filter.py enable   - Enable filtering")
        return
    
    command = sys.argv[1].lower()
    
    if command == "status":
        check_filter_status()
    elif command == "disable":
        if disable_divergence_filter():
            print("\n🎯 RESULT: All trades will now be allowed regardless of divergence")
            print("💡 To re-enable: python toggle_divergence_filter.py enable")
    elif command == "enable":
        if enable_divergence_filter():
            print("\n🎯 RESULT: Divergence filtering is now active")
            print("💡 Strong divergences will block conflicting trades")
    else:
        print(f"❌ Unknown command: {command}")
        print("Valid commands: status, disable, enable")

if __name__ == "__main__":
    main()
