"""
Risk Management Module
Implements comprehensive risk management for trading system
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass

from config.config import *

# Set up logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

@dataclass
class RiskMetrics:
    """Risk metrics for portfolio monitoring"""
    current_risk: float
    max_risk: float
    daily_pnl: float
    weekly_pnl: float
    monthly_pnl: float
    max_drawdown: float
    current_drawdown: float
    var_95: float  # Value at Risk 95%
    sharpe_ratio: float
    win_rate: float
    profit_factor: float
    risk_reward_ratio: float

class RiskManager:
    """
    Comprehensive risk management system
    """
    
    def __init__(self):
        self.max_daily_loss = 0.1  # 10% of account
        self.max_weekly_loss = 0.15  # 15% of account
        self.max_monthly_loss = 0.2  # 20% of account
        self.max_drawdown_limit = 0.25  # 25% maximum drawdown
        self.max_positions = MAX_POSITIONS
        self.risk_per_trade = RISK_PERCENT / 100
        
        # Track performance
        self.trade_history = []
        self.daily_pnl = []
        self.equity_curve = []
        self.peak_equity = 0
        self.current_drawdown = 0
        
        # Risk limits
        self.trading_halted = False
        self.halt_reason = ""
        
    def validate_trade_risk(self, account_info: Dict, position_size: float, 
                          entry_price: float, stop_loss: float) -> Tuple[bool, str]:
        """
        Validate if a trade meets risk management criteria
        
        Args:
            account_info: Account information
            position_size: Proposed position size
            entry_price: Entry price
            stop_loss: Stop loss price
            
        Returns:
            Tuple of (is_valid, reason)
        """
        try:
            account_balance = account_info.get('balance', 0)
            account_equity = account_info.get('equity', 0)
            
            if account_balance <= 0:
                return False, "Invalid account balance"
            
            # Check minimum account balance
            if account_balance < MIN_ACCOUNT_BALANCE:
                return False, f"Account balance below minimum: {account_balance} < {MIN_ACCOUNT_BALANCE}"
            
            # Check if trading is halted
            if self.trading_halted:
                return False, f"Trading halted: {self.halt_reason}"
            
            # Calculate trade risk
            risk_per_unit = abs(entry_price - stop_loss)
            total_risk = risk_per_unit * position_size * 100000  # Assuming standard lot size
            risk_percentage = total_risk / account_balance
            
            # Check risk per trade limit
            if risk_percentage > self.risk_per_trade:
                return False, f"Trade risk {risk_percentage:.2%} exceeds limit {self.risk_per_trade:.2%}"
            
            # Check maximum positions
            current_positions = self._get_current_position_count()
            if current_positions >= self.max_positions:
                return False, f"Maximum positions reached: {current_positions}/{self.max_positions}"
            
            # Check daily loss limit
            daily_pnl = self._calculate_daily_pnl(account_info)
            if daily_pnl < -self.max_daily_loss * account_balance:
                return False, f"Daily loss limit reached: {daily_pnl:.2f}"
            
            # Check weekly loss limit
            weekly_pnl = self._calculate_weekly_pnl(account_info)
            if weekly_pnl < -self.max_weekly_loss * account_balance:
                return False, f"Weekly loss limit reached: {weekly_pnl:.2f}"
            
            # Check monthly loss limit
            monthly_pnl = self._calculate_monthly_pnl(account_info)
            if monthly_pnl < -self.max_monthly_loss * account_balance:
                return False, f"Monthly loss limit reached: {monthly_pnl:.2f}"
            
            # Check drawdown limit
            current_drawdown = self._calculate_current_drawdown(account_equity)
            if current_drawdown > self.max_drawdown_limit:
                return False, f"Drawdown limit exceeded: {current_drawdown:.2%}"
            
            # Check margin requirements
            margin_required = self._calculate_margin_required(position_size, entry_price)
            free_margin = account_info.get('free_margin', 0)
            
            if margin_required > free_margin * 0.8:  # Use max 80% of free margin
                return False, f"Insufficient margin: required {margin_required}, available {free_margin}"
            
            return True, "Trade risk validated"
            
        except Exception as e:
            logger.error(f"Error validating trade risk: {e}")
            return False, f"Risk validation error: {e}"
    
    def calculate_optimal_position_size(self, account_balance: float, entry_price: float,
                                      stop_loss: float, confidence: float = 1.0) -> float:
        """
        Calculate optimal position size based on risk management
        
        Args:
            account_balance: Account balance
            entry_price: Entry price
            stop_loss: Stop loss price
            confidence: Model confidence (0-1)
            
        Returns:
            Optimal position size
        """
        try:
            # Base risk amount
            base_risk_amount = account_balance * self.risk_per_trade
            
            # Adjust risk based on confidence
            confidence_multiplier = 0.5 + (confidence * 0.5)  # Range: 0.5 to 1.0
            adjusted_risk_amount = base_risk_amount * confidence_multiplier
            
            # Calculate position size
            risk_per_unit = abs(entry_price - stop_loss)
            if risk_per_unit <= 0:
                return 0.0
            
            # For forex, assuming standard lot calculation
            contract_size = 100000  # Standard lot
            position_size = adjusted_risk_amount / (risk_per_unit * contract_size)
            
            # Apply additional safety factor
            position_size *= 0.9  # Use 90% of calculated size for safety
            
            # Round to appropriate lot size
            min_lot = 0.01
            lot_step = 0.01
            position_size = max(min_lot, round(position_size / lot_step) * lot_step)
            
            logger.debug(f"Calculated position size: {position_size} lots")
            logger.debug(f"Risk amount: {adjusted_risk_amount:.2f}, Risk per unit: {risk_per_unit:.5f}")
            
            return position_size
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.0
    
    def update_trade_history(self, trade_info: Dict):
        """
        Update trade history for risk monitoring
        
        Args:
            trade_info: Trade information dictionary
        """
        try:
            trade_record = {
                'timestamp': datetime.now(),
                'type': trade_info.get('type', 'UNKNOWN'),
                'entry_price': trade_info.get('entry_price', 0),
                'exit_price': trade_info.get('exit_price', 0),
                'position_size': trade_info.get('position_size', 0),
                'pnl': trade_info.get('pnl', 0),
                'duration': trade_info.get('duration', 0),
                'confidence': trade_info.get('confidence', 0)
            }
            
            self.trade_history.append(trade_record)
            
            # Update performance metrics
            self._update_performance_metrics(trade_record)
            
            logger.debug(f"Trade history updated: {trade_record}")
            
        except Exception as e:
            logger.error(f"Error updating trade history: {e}")
    
    def _update_performance_metrics(self, trade_record: Dict):
        """Update performance metrics with new trade"""
        try:
            pnl = trade_record.get('pnl', 0)
            
            # Update daily PnL
            today = datetime.now().date()
            if not self.daily_pnl or self.daily_pnl[-1]['date'] != today:
                self.daily_pnl.append({'date': today, 'pnl': pnl})
            else:
                self.daily_pnl[-1]['pnl'] += pnl
            
            # Update equity curve
            current_equity = self.equity_curve[-1]['equity'] + pnl if self.equity_curve else 10000 + pnl
            self.equity_curve.append({
                'timestamp': trade_record['timestamp'],
                'equity': current_equity,
                'pnl': pnl
            })
            
            # Update peak equity and drawdown
            if current_equity > self.peak_equity:
                self.peak_equity = current_equity
                self.current_drawdown = 0
            else:
                self.current_drawdown = (self.peak_equity - current_equity) / self.peak_equity
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
    
    def _get_current_position_count(self) -> int:
        """Get current number of open positions"""
        # This would typically query MT5 for current positions
        # For now, return 0 as placeholder
        return 0
    
    def _calculate_daily_pnl(self, account_info: Dict) -> float:
        """Calculate daily PnL"""
        try:
            if not self.daily_pnl:
                return 0.0
            
            today = datetime.now().date()
            for daily_record in reversed(self.daily_pnl):
                if daily_record['date'] == today:
                    return daily_record['pnl']
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error calculating daily PnL: {e}")
            return 0.0
    
    def _calculate_weekly_pnl(self, account_info: Dict) -> float:
        """Calculate weekly PnL"""
        try:
            if not self.daily_pnl:
                return 0.0
            
            week_start = datetime.now().date() - timedelta(days=7)
            weekly_pnl = 0.0
            
            for daily_record in self.daily_pnl:
                if daily_record['date'] >= week_start:
                    weekly_pnl += daily_record['pnl']
            
            return weekly_pnl
            
        except Exception as e:
            logger.error(f"Error calculating weekly PnL: {e}")
            return 0.0
    
    def _calculate_monthly_pnl(self, account_info: Dict) -> float:
        """Calculate monthly PnL"""
        try:
            if not self.daily_pnl:
                return 0.0
            
            month_start = datetime.now().date() - timedelta(days=30)
            monthly_pnl = 0.0
            
            for daily_record in self.daily_pnl:
                if daily_record['date'] >= month_start:
                    monthly_pnl += daily_record['pnl']
            
            return monthly_pnl
            
        except Exception as e:
            logger.error(f"Error calculating monthly PnL: {e}")
            return 0.0
    
    def _calculate_current_drawdown(self, current_equity: float) -> float:
        """Calculate current drawdown percentage"""
        try:
            if self.peak_equity <= 0:
                self.peak_equity = current_equity
                return 0.0
            
            if current_equity > self.peak_equity:
                self.peak_equity = current_equity
                return 0.0
            
            drawdown = (self.peak_equity - current_equity) / self.peak_equity
            return drawdown
            
        except Exception as e:
            logger.error(f"Error calculating drawdown: {e}")
            return 0.0
    
    def _calculate_margin_required(self, position_size: float, entry_price: float) -> float:
        """Calculate margin required for position"""
        try:
            # Simplified margin calculation for forex
            # Actual calculation would depend on broker and leverage
            leverage = 100  # Assuming 1:100 leverage
            contract_size = 100000  # Standard lot
            
            margin_required = (position_size * contract_size * entry_price) / leverage
            return margin_required
            
        except Exception as e:
            logger.error(f"Error calculating margin: {e}")
            return 0.0
    
    def get_risk_metrics(self, account_info: Dict) -> RiskMetrics:
        """
        Calculate comprehensive risk metrics
        
        Args:
            account_info: Current account information
            
        Returns:
            RiskMetrics object
        """
        try:
            account_balance = account_info.get('balance', 0)
            account_equity = account_info.get('equity', 0)
            
            # Calculate PnL metrics
            daily_pnl = self._calculate_daily_pnl(account_info)
            weekly_pnl = self._calculate_weekly_pnl(account_info)
            monthly_pnl = self._calculate_monthly_pnl(account_info)
            
            # Calculate drawdown
            current_drawdown = self._calculate_current_drawdown(account_equity)
            max_drawdown = self._calculate_max_drawdown()
            
            # Calculate trading statistics
            win_rate = self._calculate_win_rate()
            profit_factor = self._calculate_profit_factor()
            sharpe_ratio = self._calculate_sharpe_ratio()
            risk_reward_ratio = self._calculate_risk_reward_ratio()
            
            # Calculate VaR
            var_95 = self._calculate_var_95()
            
            metrics = RiskMetrics(
                current_risk=self.risk_per_trade,
                max_risk=self.risk_per_trade,
                daily_pnl=daily_pnl,
                weekly_pnl=weekly_pnl,
                monthly_pnl=monthly_pnl,
                max_drawdown=max_drawdown,
                current_drawdown=current_drawdown,
                var_95=var_95,
                sharpe_ratio=sharpe_ratio,
                win_rate=win_rate,
                profit_factor=profit_factor,
                risk_reward_ratio=risk_reward_ratio
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return RiskMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
    
    def _calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown from equity curve"""
        try:
            if not self.equity_curve:
                return 0.0
            
            equity_values = [record['equity'] for record in self.equity_curve]
            peak = equity_values[0]
            max_dd = 0.0
            
            for equity in equity_values:
                if equity > peak:
                    peak = equity
                else:
                    drawdown = (peak - equity) / peak
                    max_dd = max(max_dd, drawdown)
            
            return max_dd
            
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0.0
    
    def _calculate_win_rate(self) -> float:
        """Calculate win rate from trade history"""
        try:
            if not self.trade_history:
                return 0.0
            
            winning_trades = sum(1 for trade in self.trade_history if trade.get('pnl', 0) > 0)
            total_trades = len(self.trade_history)
            
            return winning_trades / total_trades if total_trades > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating win rate: {e}")
            return 0.0
    
    def _calculate_profit_factor(self) -> float:
        """Calculate profit factor"""
        try:
            if not self.trade_history:
                return 0.0
            
            gross_profit = sum(trade.get('pnl', 0) for trade in self.trade_history if trade.get('pnl', 0) > 0)
            gross_loss = abs(sum(trade.get('pnl', 0) for trade in self.trade_history if trade.get('pnl', 0) < 0))
            
            return gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
        except Exception as e:
            logger.error(f"Error calculating profit factor: {e}")
            return 0.0
    
    def _calculate_sharpe_ratio(self) -> float:
        """Calculate Sharpe ratio"""
        try:
            if not self.daily_pnl or len(self.daily_pnl) < 2:
                return 0.0
            
            returns = [record['pnl'] for record in self.daily_pnl]
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            
            return mean_return / std_return if std_return > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0
    
    def _calculate_risk_reward_ratio(self) -> float:
        """Calculate average risk-reward ratio"""
        try:
            if not self.trade_history:
                return 0.0
            
            winning_trades = [trade.get('pnl', 0) for trade in self.trade_history if trade.get('pnl', 0) > 0]
            losing_trades = [abs(trade.get('pnl', 0)) for trade in self.trade_history if trade.get('pnl', 0) < 0]
            
            if not winning_trades or not losing_trades:
                return 0.0
            
            avg_win = np.mean(winning_trades)
            avg_loss = np.mean(losing_trades)
            
            return avg_win / avg_loss if avg_loss > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating risk-reward ratio: {e}")
            return 0.0
    
    def _calculate_var_95(self) -> float:
        """Calculate Value at Risk at 95% confidence level"""
        try:
            if not self.daily_pnl or len(self.daily_pnl) < 20:
                return 0.0
            
            returns = [record['pnl'] for record in self.daily_pnl]
            return np.percentile(returns, 5)  # 5th percentile for 95% VaR
            
        except Exception as e:
            logger.error(f"Error calculating VaR: {e}")
            return 0.0
    
    def check_emergency_stop(self, account_info: Dict) -> bool:
        """
        Check if emergency stop conditions are met
        
        Args:
            account_info: Current account information
            
        Returns:
            bool: True if trading should be halted
        """
        try:
            account_balance = account_info.get('balance', 0)
            account_equity = account_info.get('equity', 0)
            
            # Check drawdown limit
            current_drawdown = self._calculate_current_drawdown(account_equity)
            if current_drawdown > self.max_drawdown_limit:
                self.trading_halted = True
                self.halt_reason = f"Maximum drawdown exceeded: {current_drawdown:.2%}"
                return True
            
            # Check daily loss limit
            daily_pnl = self._calculate_daily_pnl(account_info)
            if daily_pnl < -self.max_daily_loss * account_balance:
                self.trading_halted = True
                self.halt_reason = f"Daily loss limit exceeded: {daily_pnl:.2f}"
                return True
            
            # Check margin level
            margin_level = account_info.get('margin_level', 1000)
            if margin_level < 200:  # 200% margin level minimum
                self.trading_halted = True
                self.halt_reason = f"Low margin level: {margin_level:.1f}%"
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking emergency stop: {e}")
            return True  # Halt trading on error for safety
    
    def reset_trading_halt(self):
        """Reset trading halt status"""
        self.trading_halted = False
        self.halt_reason = ""
        logger.info("Trading halt reset")
