#!/usr/bin/env python3
"""
Comprehensive Trading System Visualization
Shows last 200 candles with all indicators, channels, regimes, and signals
Uses EXACT same settings as the live trading system
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Import all trading system components
from mt5_integration import MT5Manager
from fixed_live_trader import FixedFeatureEngineer, RegimeDetector
from simple_regression_channel import SimpleRegressionChannel
from qqe_indicator import QQEIndicator

class ComprehensiveTradingSystemPlot:
    def __init__(self):
        self.mt5_manager = MT5Manager()
        self.feature_engineer = FixedFeatureEngineer()
        self.regime_detector = RegimeDetector()
        self.qqe_indicator = QQEIndicator(
            rsi_period=7,  # USER'S TRADINGVIEW SETTING: 7
            rsi_smoothing=5,
            qqe_factor=1.0,  # USER'S TRADINGVIEW SETTING: 1
            threshold=10
        )
        
        # Initialize simple regression channel
        self.simple_regression = SimpleRegressionChannel(
            periods=20,                       # Fixed 20-period lookback
            std_multiplier=2.0               # 2 standard deviations for channel bounds
        )

        # Pass simple regression to regime detector
        self.regime_detector.set_simple_regression(self.simple_regression)
        
        self.symbol = "XAUUSD!"
        self.timeframe = "M5"
        
    def get_data_and_indicators(self):
        """Get data and calculate all indicators with exact same settings"""
        print("🔍 Connecting to MT5 and getting data...")
        
        if not self.mt5_manager.connect():
            raise Exception("Failed to connect to MT5")
        
        # Get 500 candles to ensure indicators have enough historical data
        df = self.mt5_manager.get_latest_data(self.symbol, self.timeframe, 500)
        if df is None or len(df) < 400:
            raise Exception("Failed to get sufficient data")

        print(f"📊 Got {len(df)} candles, calculating indicators with full history")

        # Calculate all technical indicators using EXACT same settings with full history
        df = self.feature_engineer.create_technical_indicators(df)

        # Calculate QQE indicator with full history
        df = self.qqe_indicator.calculate_qqe_bands(df)

        # Calculate regime indicators (includes atr_percentile) with full history
        df = self.regime_detector.calculate_regime_indicators(df)

        print(f"📊 Using last 200 candles for analysis (indicators now have {len(df)-200} candles of lookback)")

        # Use last 200 candles for plotting (but keep full history for indicator calculations)
        analysis_df = df.iloc[-200:].copy()
        analysis_df.reset_index(drop=True, inplace=True)
        
        return analysis_df, df  # Return both analysis data and full history
    
    def calculate_adaptive_channels(self, analysis_df, full_df):
        """Calculate adaptive regression channels for each candle"""
        print("🔧 Calculating adaptive regression channels...")

        channel_history = []
        regime_history = []

        # Calculate starting index in full_df for analysis_df
        start_idx = len(full_df) - len(analysis_df)

        for i in range(len(analysis_df)):
            # Get data up to current candle from full history
            full_idx = start_idx + i
            current_df = full_df.iloc[:full_idx+1].copy()

            if len(current_df) >= 50:  # Need sufficient data for indicators
                # Update channel state
                channel_info = self.simple_regression.update_channel_state(current_df)

                # Get regime detection
                regime, confidence, details, trend_dir, accurate_trend = self.regime_detector.detect_regime(current_df)

                channel_history.append({
                    'index': i,
                    'channel_info': channel_info,
                    'regime': regime,
                    'confidence': confidence
                })

                regime_history.append(regime)
            else:
                channel_history.append({
                    'index': i,
                    'channel_info': None,
                    'regime': 'INSUFFICIENT_DATA',
                    'confidence': 0.0
                })
                regime_history.append('INSUFFICIENT_DATA')

        return channel_history, regime_history
    
    def calculate_complete_trades(self, analysis_df, full_df, regime_history):
        """Calculate complete trades with entries and exits using exact same logic as live trader"""
        print("⚡ Calculating complete trades with entries and exits...")

        trades = []
        active_trade = None

        # Calculate starting index in full_df for analysis_df
        start_idx = len(full_df) - len(analysis_df)

        for i in range(len(analysis_df)):
            if i < 50:  # Need enough data for signals
                continue

            # Get data up to current candle from full history
            full_idx = start_idx + i
            current_df = full_df.iloc[:full_idx+1].copy()
            current_candle = analysis_df.iloc[i]

            # Check for exit conditions first if we have an active trade
            if active_trade:
                exit_reason = None
                exit_price = None

                # Stop loss check
                if active_trade['direction'] == 'BUY':
                    if current_candle['low'] <= active_trade['stop_loss']:
                        exit_reason = 'STOP_LOSS'
                        exit_price = active_trade['stop_loss']
                elif active_trade['direction'] == 'SELL':
                    if current_candle['high'] >= active_trade['stop_loss']:
                        exit_reason = 'STOP_LOSS'
                        exit_price = active_trade['stop_loss']

                # Take profit check (only for ranging markets)
                if not exit_reason and active_trade['take_profit']:
                    if active_trade['direction'] == 'BUY':
                        if current_candle['high'] >= active_trade['take_profit']:
                            exit_reason = 'TAKE_PROFIT'
                            exit_price = active_trade['take_profit']
                    elif active_trade['direction'] == 'SELL':
                        if current_candle['low'] <= active_trade['take_profit']:
                            exit_reason = 'TAKE_PROFIT'
                            exit_price = active_trade['take_profit']

                # Trailing stop logic (simplified - check if we should trail)
                if not exit_reason:
                    atr = current_candle['atr']
                    if active_trade['direction'] == 'BUY':
                        new_stop = current_candle['close'] - (1.5 * atr)
                        if new_stop > active_trade['stop_loss']:
                            # Trail the stop
                            active_trade['stop_loss'] = new_stop
                            active_trade['trailing_count'] += 1
                    elif active_trade['direction'] == 'SELL':
                        new_stop = current_candle['close'] + (1.5 * atr)
                        if new_stop < active_trade['stop_loss']:
                            # Trail the stop
                            active_trade['stop_loss'] = new_stop
                            active_trade['trailing_count'] += 1

                # Exit the trade if conditions met
                if exit_reason:
                    active_trade['exit_bar'] = i
                    active_trade['exit_price'] = exit_price
                    active_trade['exit_reason'] = exit_reason

                    # Calculate P&L
                    if active_trade['direction'] == 'BUY':
                        pnl_pips = (exit_price - active_trade['entry_price']) * 100  # Convert to pips for Gold
                    else:
                        pnl_pips = (active_trade['entry_price'] - exit_price) * 100

                    active_trade['pnl_pips'] = pnl_pips
                    trades.append(active_trade)
                    active_trade = None

            # Check for new entry signals if no active trade
            if not active_trade and len(current_df) >= 50:
                # Calculate candle strength (8-candle lookback, excluding current)
                lookback_candles = current_df.iloc[-9:-1]  # Exclude current candle

                bullish_strength = 0
                bearish_strength = 0

                for _, candle in lookback_candles.iterrows():
                    candle_range = candle['high'] - candle['low']
                    if candle_range > 0:
                        close_position = (candle['close'] - candle['low']) / candle_range

                        if candle['close'] > candle['open']:  # Bullish candle
                            body_strength = (candle['close'] - candle['open']) / candle_range
                            bullish_strength += body_strength * close_position
                        else:  # Bearish candle
                            body_strength = (candle['open'] - candle['close']) / candle_range
                            bearish_strength += body_strength * (1 - close_position)

                total_strength = bullish_strength + bearish_strength
                if total_strength > 0:
                    net_strength = (bullish_strength - bearish_strength) / total_strength
                else:
                    net_strength = 0

                # Get QQE signal
                qqe_trend = current_candle.get('qqe_trend_signal', 0)  # Use correct column name
                qqe_signal = current_candle.get('qqe_signal', 0)
                qqe_strength = abs(current_candle.get('qqe_signal_strength', 0))

                # Signal generation logic (OPTIMIZED)
                signal = None
                if qqe_trend > 0 and qqe_signal > 0 and net_strength > 0.15:  # Lowered threshold
                    signal = 'BUY'
                elif qqe_trend < 0 and qqe_signal < 0 and net_strength < -0.15:  # Lowered threshold
                    signal = 'SELL'
                elif qqe_strength > 0.5 and qqe_trend > 0 and qqe_signal > 0:  # High QQE strength override
                    signal = 'BUY'
                elif qqe_strength > 0.5 and qqe_trend < 0 and qqe_signal < 0:  # High QQE strength override
                    signal = 'SELL'
                elif abs(net_strength) > 0.5:  # Strong candle strength override
                    if net_strength > 0.5:
                        signal = 'BUY'
                    elif net_strength < -0.5:
                        signal = 'SELL'

                # Enter trade if signal generated
                if signal:
                    atr = current_candle['atr']
                    entry_price = current_candle['close']

                    # Calculate stop loss (1.5 ATR)
                    if signal == 'BUY':
                        stop_loss = entry_price - (1.5 * atr)
                    else:
                        stop_loss = entry_price + (1.5 * atr)

                    # Calculate take profit (0.75 ATR for ranging, None for trending)
                    regime = regime_history[i] if i < len(regime_history) else 'RANGING'
                    take_profit = None
                    if regime == 'RANGING':
                        if signal == 'BUY':
                            take_profit = entry_price + (0.75 * atr)
                        else:
                            take_profit = entry_price - (0.75 * atr)

                    active_trade = {
                        'entry_bar': i,
                        'entry_price': entry_price,
                        'direction': signal,
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'atr': atr,
                        'regime': regime,
                        'trailing_count': 0,
                        'net_strength': net_strength,
                        'qqe_trend': qqe_trend,
                        'qqe_signal': qqe_signal
                    }

        # Close any remaining active trade at the end
        if active_trade:
            last_candle = analysis_df.iloc[-1]
            active_trade['exit_bar'] = len(analysis_df) - 1
            active_trade['exit_price'] = last_candle['close']
            active_trade['exit_reason'] = 'END_OF_DATA'

            # Calculate P&L
            if active_trade['direction'] == 'BUY':
                pnl_pips = (active_trade['exit_price'] - active_trade['entry_price']) * 100
            else:
                pnl_pips = (active_trade['entry_price'] - active_trade['exit_price']) * 100

            active_trade['pnl_pips'] = pnl_pips
            trades.append(active_trade)

        return trades

    def create_comprehensive_plot(self, df, channel_history, regime_history, trades):
        """Create comprehensive multi-panel plot"""
        print("📈 Creating comprehensive visualization...")

        # Create figure with subplots
        fig = plt.figure(figsize=(20, 16))

        # Define subplot layout
        gs = fig.add_gridspec(5, 1, height_ratios=[3, 1, 1, 1, 0.5], hspace=0.3)

        # Main price chart
        ax1 = fig.add_subplot(gs[0])

        # Plot candlesticks
        for i in range(len(df)):
            candle = df.iloc[i]
            color = 'green' if candle['close'] > candle['open'] else 'red'

            # Candle body
            body_height = abs(candle['close'] - candle['open'])
            body_bottom = min(candle['open'], candle['close'])
            ax1.bar(i, body_height, bottom=body_bottom, width=0.6, color=color, alpha=0.8)

            # Wicks
            ax1.plot([i, i], [candle['low'], candle['high']], color='black', linewidth=0.5)

        # Plot EMAs
        ax1.plot(df.index, df['ema_fast'], label='EMA Fast (21)', color='blue', linewidth=1)
        ax1.plot(df.index, df['ema_slow'], label='EMA Slow (34)', color='orange', linewidth=1)

        # Plot adaptive regression channels
        colors = ['red', 'blue', 'green', 'purple', 'brown', 'pink', 'cyan', 'magenta',
                 'yellow', 'lime', 'indigo', 'teal', 'coral', 'navy', 'maroon', 'olive']

        channel_count = 0
        for i, history in enumerate(channel_history):
            channel_info = history.get('channel_info')
            if channel_info and channel_info['channel_data']:
                channel_data = channel_info['channel_data']
                homogeneous_indices = channel_info.get('homogeneous_indices', [])

                if homogeneous_indices:
                    color = colors[channel_count % len(colors)]
                    slope = channel_data['slope']
                    channel_type = channel_data.get('channel_type', 'UNKNOWN')

                    # Calculate sloped lines
                    upper_values = []
                    lower_values = []
                    center_values = []

                    for j, idx in enumerate(homogeneous_indices):
                        if idx < len(df):
                            center_val = channel_data['center_line'] + slope * (j - (len(homogeneous_indices) - 1))
                            width = channel_data['width']

                            upper_values.append(center_val + width)
                            lower_values.append(center_val - width)
                            center_values.append(center_val)

                    # Plot channel
                    channel_indices = [idx for idx in homogeneous_indices if idx < len(df)]
                    if len(channel_indices) == len(upper_values):
                        ax1.plot(channel_indices, upper_values, color=color, linewidth=2, alpha=0.8,
                                label=f'Ch{channel_count+1}: {channel_type} ({slope:+.3f})')
                        ax1.plot(channel_indices, lower_values, color=color, linewidth=2, alpha=0.8)
                        ax1.plot(channel_indices, center_values, color=color, linewidth=1, alpha=0.6, linestyle='--')
                        ax1.fill_between(channel_indices, upper_values, lower_values, color=color, alpha=0.1)

                        channel_count += 1

        # Plot complete trades with entries, exits, stop losses, and take profits
        for trade in trades:
            entry_bar = trade['entry_bar']
            entry_price = trade['entry_price']
            direction = trade['direction']

            # Plot entry signal
            if direction == 'BUY':
                entry_y = df.iloc[entry_bar]['low'] - (df.iloc[entry_bar]['high'] - df.iloc[entry_bar]['low']) * 0.1
                ax1.scatter(entry_bar, entry_y, marker='^', color='green', s=150, label='BUY Entry' if entry_bar == trades[0]['entry_bar'] else '', zorder=5)
            else:
                entry_y = df.iloc[entry_bar]['high'] + (df.iloc[entry_bar]['high'] - df.iloc[entry_bar]['low']) * 0.1
                ax1.scatter(entry_bar, entry_y, marker='v', color='red', s=150, label='SELL Entry' if entry_bar == trades[0]['entry_bar'] else '', zorder=5)

            # Plot stop loss line
            if 'exit_bar' in trade:
                exit_bar = trade['exit_bar']
                stop_loss = trade['stop_loss']

                # Draw stop loss line from entry to exit
                ax1.plot([entry_bar, exit_bar], [stop_loss, stop_loss],
                        color='red', linestyle='--', linewidth=2, alpha=0.7,
                        label='Stop Loss' if entry_bar == trades[0]['entry_bar'] else '')

                # Plot take profit line if exists
                if trade['take_profit']:
                    take_profit = trade['take_profit']
                    ax1.plot([entry_bar, exit_bar], [take_profit, take_profit],
                            color='green', linestyle='--', linewidth=2, alpha=0.7,
                            label='Take Profit' if entry_bar == trades[0]['entry_bar'] else '')

                # Plot exit signal
                exit_price = trade['exit_price']
                exit_reason = trade['exit_reason']

                if direction == 'BUY':
                    exit_y = df.iloc[exit_bar]['high'] + (df.iloc[exit_bar]['high'] - df.iloc[exit_bar]['low']) * 0.1
                    marker = 'X' if exit_reason == 'STOP_LOSS' else 'o'
                    color = 'red' if exit_reason == 'STOP_LOSS' else 'green'
                else:
                    exit_y = df.iloc[exit_bar]['low'] - (df.iloc[exit_bar]['high'] - df.iloc[exit_bar]['low']) * 0.1
                    marker = 'X' if exit_reason == 'STOP_LOSS' else 'o'
                    color = 'red' if exit_reason == 'STOP_LOSS' else 'green'

                ax1.scatter(exit_bar, exit_y, marker=marker, color=color, s=150,
                           label=f'{exit_reason} Exit' if entry_bar == trades[0]['entry_bar'] else '', zorder=5)

                # Draw trade line from entry to exit
                pnl_color = 'green' if trade['pnl_pips'] > 0 else 'red'
                ax1.plot([entry_bar, exit_bar], [entry_price, exit_price],
                        color=pnl_color, linewidth=3, alpha=0.6, zorder=4)

                # Add P&L text
                mid_bar = (entry_bar + exit_bar) // 2
                mid_price = (entry_price + exit_price) / 2
                ax1.text(mid_bar, mid_price, f'{trade["pnl_pips"]:+.1f}p',
                        fontsize=8, ha='center', va='center',
                        bbox=dict(boxstyle='round,pad=0.3', facecolor=pnl_color, alpha=0.7, edgecolor='none'),
                        color='white', fontweight='bold')

        ax1.set_title(f'XAUUSD M5 - Comprehensive Trading System Analysis (Last 200 Candles)', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Price')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
        ax1.grid(True, alpha=0.3)

        # QQE Indicator subplot
        ax2 = fig.add_subplot(gs[1])
        ax2.plot(df.index, df['rsi'], label='RSI', color='purple', linewidth=1)
        ax2.plot(df.index, df['rsi_ma'], label='RSI MA', color='blue', linewidth=1)
        ax2.plot(df.index, df['fast_atr_rsi_tl'], label='Fast ATR RSI TL', color='red', linewidth=1)
        ax2.axhline(y=50, color='gray', linestyle='--', alpha=0.5)
        ax2.set_ylabel('QQE')
        ax2.legend(fontsize=8)
        ax2.grid(True, alpha=0.3)

        # ATR subplot
        ax3 = fig.add_subplot(gs[2])
        ax3.plot(df.index, df['atr'], label='ATR (14)', color='orange', linewidth=1)
        if 'atr_percentile' in df.columns:
            ax3.plot(df.index, df['atr_percentile'], label='ATR Percentile', color='red', linewidth=1)
        ax3.set_ylabel('ATR')
        ax3.legend(fontsize=8)
        ax3.grid(True, alpha=0.3)

        # Market Regime subplot
        ax4 = fig.add_subplot(gs[3])
        regime_colors = {'RANGING': 'blue', 'TRENDING': 'red', 'TRANSITIONAL': 'orange'}
        regime_values = {'RANGING': 0, 'TRENDING': 1, 'TRANSITIONAL': 0.5}

        regime_y = [regime_values.get(regime, 0.5) for regime in regime_history]
        regime_color_list = [regime_colors.get(regime, 'gray') for regime in regime_history]

        for i in range(len(regime_y)):
            ax4.bar(i, 1, color=regime_color_list[i], alpha=0.7, width=1)

        ax4.set_ylabel('Market Regime')
        ax4.set_ylim(0, 1)
        ax4.set_yticks([0, 0.5, 1])
        ax4.set_yticklabels(['RANGING', 'TRANSITIONAL', 'TRENDING'])
        ax4.grid(True, alpha=0.3)

        # Channel state subplot
        ax5 = fig.add_subplot(gs[4])
        state_colors = {'ACTIVE_CHANNEL': 'green', 'BUILDING': 'orange', 'TRANSITION': 'red'}
        state_values = {'ACTIVE_CHANNEL': 1, 'BUILDING': 0.5, 'TRANSITION': 0}

        states = []
        for history in channel_history:
            channel_info = history.get('channel_info')
            if channel_info:
                state = channel_info['state']
                states.append(state)
            else:
                states.append('BUILDING')

        state_y = [state_values.get(state, 0.5) for state in states]
        state_color_list = [state_colors.get(state, 'gray') for state in states]

        for i in range(len(state_y)):
            ax5.bar(i, 1, color=state_color_list[i], alpha=0.7, width=1)

        ax5.set_ylabel('Channel State')
        ax5.set_xlabel('Candle Index')
        ax5.set_ylim(0, 1)
        ax5.set_yticks([0, 0.5, 1])
        ax5.set_yticklabels(['TRANSITION', 'BUILDING', 'ACTIVE'])
        ax5.grid(True, alpha=0.3)

        plt.tight_layout()

        # Save plot
        filename = f'comprehensive_trading_system_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 Comprehensive plot saved as '{filename}'")

        return filename

    def run_comprehensive_analysis(self):
        """Run complete analysis and create visualization"""
        try:
            print("🚀 COMPREHENSIVE TRADING SYSTEM VISUALIZATION")
            print("=" * 60)
            print("📊 Analyzing last 200 candles with:")
            print("   • Adaptive Regression Channels (16+ channels)")
            print("   • QQE Indicator (RSI:14, SF:5, QQE:1.0, Wilders:27)")
            print("   • EMAs (Fast:21, Slow:34)")
            print("   • ATR (14 periods)")
            print("   • Market Regime Detection")
            print("   • Entry Signals (QQE + Candle Strength)")
            print("=" * 60)

            # Get data and calculate indicators
            analysis_df, full_df = self.get_data_and_indicators()

            # Calculate adaptive channels
            channel_history, regime_history = self.calculate_adaptive_channels(analysis_df, full_df)

            # Calculate complete trades with entries and exits
            trades = self.calculate_complete_trades(analysis_df, full_df, regime_history)

            # Create comprehensive plot
            filename = self.create_comprehensive_plot(analysis_df, channel_history, regime_history, trades)

            # Print summary statistics
            print("\n📊 ANALYSIS SUMMARY:")
            print(f"   • Total Candles Analyzed: {len(analysis_df)}")
            print(f"   • Historical Data Used: {len(full_df)} candles")
            print(f"   • Channels Generated: {sum(1 for h in channel_history if h['channel_info'] and h['channel_info']['channel_data'])}")
            print(f"   • Total Trades: {len(trades)}")

            if trades:
                winning_trades = [t for t in trades if t['pnl_pips'] > 0]
                losing_trades = [t for t in trades if t['pnl_pips'] < 0]
                total_pnl = sum(t['pnl_pips'] for t in trades)

                print(f"   • Winning Trades: {len(winning_trades)}")
                print(f"   • Losing Trades: {len(losing_trades)}")
                print(f"   • Win Rate: {len(winning_trades)/len(trades)*100:.1f}%")
                print(f"   • Total P&L: {total_pnl:+.1f} pips")
                print(f"   • Average P&L per Trade: {total_pnl/len(trades):+.1f} pips")

                # Trade breakdown by exit reason
                exit_reasons = {}
                for trade in trades:
                    reason = trade.get('exit_reason', 'UNKNOWN')
                    exit_reasons[reason] = exit_reasons.get(reason, 0) + 1

                print(f"   • Exit Reasons:")
                for reason, count in exit_reasons.items():
                    print(f"     - {reason}: {count} trades")

            regime_counts = {}
            for regime in regime_history:
                regime_counts[regime] = regime_counts.get(regime, 0) + 1

            print(f"   • Market Regimes:")
            for regime, count in regime_counts.items():
                percentage = (count / len(regime_history)) * 100
                print(f"     - {regime}: {count} candles ({percentage:.1f}%)")

            print(f"\n✅ Comprehensive analysis complete!")
            print(f"📈 Visualization saved as: {filename}")

            return filename

        except Exception as e:
            print(f"❌ Error in comprehensive analysis: {e}")
            return None
        finally:
            if self.mt5_manager.connected:
                self.mt5_manager.disconnect()

def main():
    """Main function"""
    plotter = ComprehensiveTradingSystemPlot()
    plotter.run_comprehensive_analysis()

if __name__ == "__main__":
    main()
