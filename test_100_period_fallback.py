#!/usr/bin/env python3
"""
Test 100-Period Regression Fallback Logic

Tests the new fallback mechanism where 100-period regression is used for signal generation
when 10 and 20-period regressions don't align but 100-period supports the signal.
"""

import pandas as pd
import numpy as np
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader

def create_test_data():
    """Create test data with different regression scenarios"""
    
    # Create base DataFrame with required columns
    dates = pd.date_range('2024-01-01', periods=150, freq='5min')
    
    # Scenario 1: 10/20-period disagree, 100-period supports BUY
    df1 = pd.DataFrame({
        'open': np.random.uniform(4300, 4310, 150),
        'high': np.random.uniform(4305, 4315, 150),
        'low': np.random.uniform(4295, 4305, 150),
        'close': np.random.uniform(4300, 4310, 150),
        'volume': np.random.uniform(100, 1000, 150),
        
        # 20-period: Weak/flat trend
        'regression_slope': 0.002,  # Very weak UP
        'regression_trend': 'UP',
        'regression_position': 0.5,
        
        # 10-period: Opposite trend
        'regression_slope_short': -0.003,  # Weak DOWN
        'regression_trend_short': 'DOWN', 
        'regression_position_short': 0.6,
        
        # 100-period: Strong UP trend (supports BUY)
        'regression_slope_regime': 0.015,  # Strong UP
        'regression_trend_regime': 'UP',
        'regression_position_regime': 0.3,  # Low in channel (good BUY entry)
        
        # Other required columns
        'ema_10': 4305.0,
        'ema_20': 4300.0,
        'regression_upper': 4320.0,
        'regression_lower': 4280.0,
        'regression_upper_short': 4315.0,
        'regression_lower_short': 4285.0,
        'regression_upper_regime': 4350.0,
        'regression_lower_regime': 4250.0,
    }, index=dates)
    
    # Scenario 2: 10/20-period disagree, 100-period supports SELL
    df2 = df1.copy()
    df2['regression_slope'] = 0.001  # Very weak UP
    df2['regression_trend'] = 'UP'
    df2['regression_slope_short'] = 0.004  # Weak UP
    df2['regression_trend_short'] = 'UP'
    df2['regression_slope_regime'] = -0.020  # Strong DOWN
    df2['regression_trend_regime'] = 'DOWN'
    df2['regression_position_regime'] = 0.7  # High in channel (good SELL entry)
    
    # Scenario 3: 10/20-period agree, no fallback needed
    df3 = df1.copy()
    df3['regression_slope'] = 0.015  # Strong UP
    df3['regression_trend'] = 'UP'
    df3['regression_slope_short'] = 0.012  # Strong UP
    df3['regression_trend_short'] = 'UP'
    df3['regression_slope_regime'] = 0.018  # Strong UP
    df3['regression_trend_regime'] = 'UP'
    df3['regression_position_regime'] = 0.4
    
    return df1, df2, df3

def test_100_period_fallback():
    """Test the 100-period regression fallback logic"""
    
    print("🧪 Testing 100-Period Regression Fallback Logic")
    print("=" * 60)
    
    # Create test trader instance
    trader = FixedLiveTrader("XAUUSD!")
    
    # Create test scenarios
    df_buy_fallback, df_sell_fallback, df_no_fallback = create_test_data()
    
    # Test Scenario 1: BUY Fallback
    print("\n📊 Scenario 1: 10/20-period disagree, 100-period supports BUY")
    print("-" * 50)
    
    # Create weak initial confluence
    initial_confluence = {'buy_confluence': 0.2, 'sell_confluence': 0.1}
    current_price = 4305.0
    
    result1 = trader._apply_100_period_fallback(
        initial_confluence, df_buy_fallback, current_price, "UP"
    )
    
    print(f"Initial BUY confluence: {initial_confluence['buy_confluence']:.3f}")
    print(f"Final BUY confluence:   {result1['buy_confluence']:.3f}")
    print(f"Fallback applied: {'✅ YES' if result1['buy_confluence'] > initial_confluence['buy_confluence'] else '❌ NO'}")
    
    # Test Scenario 2: SELL Fallback
    print("\n📊 Scenario 2: 10/20-period disagree, 100-period supports SELL")
    print("-" * 50)
    
    initial_confluence = {'buy_confluence': 0.1, 'sell_confluence': 0.25}
    
    result2 = trader._apply_100_period_fallback(
        initial_confluence, df_sell_fallback, current_price, "DOWN"
    )
    
    print(f"Initial SELL confluence: {initial_confluence['sell_confluence']:.3f}")
    print(f"Final SELL confluence:   {result2['sell_confluence']:.3f}")
    print(f"Fallback applied: {'✅ YES' if result2['sell_confluence'] > initial_confluence['sell_confluence'] else '❌ NO'}")
    
    # Test Scenario 3: No Fallback Needed
    print("\n📊 Scenario 3: 10/20-period agree, no fallback needed")
    print("-" * 50)
    
    initial_confluence = {'buy_confluence': 0.3, 'sell_confluence': 0.2}
    
    result3 = trader._apply_100_period_fallback(
        initial_confluence, df_no_fallback, current_price, "UP"
    )
    
    print(f"Initial BUY confluence: {initial_confluence['buy_confluence']:.3f}")
    print(f"Final BUY confluence:   {result3['buy_confluence']:.3f}")
    print(f"Fallback applied: {'✅ YES' if result3['buy_confluence'] > initial_confluence['buy_confluence'] else '❌ NO (expected)'}")
    
    # Summary
    print("\n🎯 Test Summary")
    print("=" * 30)
    
    buy_fallback_worked = result1['buy_confluence'] > 0.4
    sell_fallback_worked = result2['sell_confluence'] > 0.4
    no_fallback_correct = abs(result3['buy_confluence'] - initial_confluence['buy_confluence']) < 0.01
    
    print(f"✅ BUY Fallback Test:  {'PASSED' if buy_fallback_worked else 'FAILED'}")
    print(f"✅ SELL Fallback Test: {'PASSED' if sell_fallback_worked else 'FAILED'}")
    print(f"✅ No Fallback Test:   {'PASSED' if no_fallback_correct else 'FAILED'}")
    
    all_passed = buy_fallback_worked and sell_fallback_worked and no_fallback_correct
    print(f"\n🎉 Overall Result: {'ALL TESTS PASSED!' if all_passed else 'SOME TESTS FAILED'}")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = test_100_period_fallback()
        exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
