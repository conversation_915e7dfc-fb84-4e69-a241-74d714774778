#!/usr/bin/env python3
"""
Adaptive Regression Channel System
Implements break-aware, homogeneous candle-based regression channels
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from enum import Enum
from sklearn.linear_model import LinearRegression
import logging

class ChannelState(Enum):
    ACTIVE_CHANNEL = "ACTIVE_CHANNEL"
    TRANSITION = "TRANSITION" 
    BUILDING = "BUILDING"

class AdaptiveRegressionChannel:
    """
    Adaptive Regression Channel System
    
    Features:
    - Break detection with confirmation
    - Homogeneous candle selection (ATR similarity + behavioral consistency)
    - Transition mode handling
    - Dynamic lookback (10-40 candles)
    - Regime detection integration
    """
    
    def __init__(self, 
                 break_buffer_multiplier: float = 2.0,
                 break_confirmation_candles: int = 2,
                 transition_duration_min: int = 15,
                 transition_duration_max: int = 25,
                 atr_similarity_threshold: float = 0.30,
                 min_channel_candles: int = 10,
                 max_channel_candles: int = 40,
                 regime_transition_weight: float = 2.0,
                 max_channel_age: int = 20):  # NEW: Force channel renewal after N candles
        
        self.break_buffer_multiplier = break_buffer_multiplier
        self.break_confirmation_candles = break_confirmation_candles
        self.transition_duration_min = transition_duration_min
        self.transition_duration_max = transition_duration_max
        self.atr_similarity_threshold = atr_similarity_threshold
        self.min_channel_candles = min_channel_candles
        self.max_channel_candles = max_channel_candles
        self.regime_transition_weight = regime_transition_weight
        self.max_channel_age = max_channel_age

        # State tracking
        self.current_state = ChannelState.ACTIVE_CHANNEL
        self.transition_start_candle = None
        self.transition_duration = None
        self.last_channel_break = None
        self.current_channel_data = None
        self.homogeneous_candles_indices = []
        self.channel_start_candle = None  # NEW: Track when channel was established
        self.channel_count = 0  # Track total channels created
        
        self.logger = logging.getLogger(__name__)
        
    def detect_channel_break(self, df: pd.DataFrame, current_channel: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Detect if price has broken out of the current regression channel
        
        Args:
            df: DataFrame with OHLC data
            current_channel: Current channel data with upper/lower bounds
            
        Returns:
            Tuple of (is_broken: bool, reason: str)
        """
        if current_channel is None or len(df) < self.break_confirmation_candles + 1:
            return False, "Insufficient data"
            
        # Get recent candles for break confirmation
        recent_candles = df.iloc[-(self.break_confirmation_candles + 1):]
        
        upper_bound = current_channel.get('upper_bound')
        lower_bound = current_channel.get('lower_bound')
        channel_width = current_channel.get('width', 0)
        
        if upper_bound is None or lower_bound is None:
            return False, "Invalid channel data"
            
        # ULTRA AGGRESSIVE: Any close beyond channel bounds = break
        break_upper = upper_bound
        break_lower = lower_bound
        
        # Check for sustained breaks
        upper_breaks = 0
        lower_breaks = 0
        
        for _, candle in recent_candles.iterrows():
            if candle['close'] > break_upper:
                upper_breaks += 1
            elif candle['close'] < break_lower:
                lower_breaks += 1
                
        # Require confirmation over multiple candles
        if upper_breaks >= self.break_confirmation_candles:
            return True, f"Upper break confirmed ({upper_breaks}/{self.break_confirmation_candles} candles above {break_upper:.5f})"
        elif lower_breaks >= self.break_confirmation_candles:
            return True, f"Lower break confirmed ({lower_breaks}/{self.break_confirmation_candles} candles below {break_lower:.5f})"
            
        return False, "No sustained break detected"
    
    def calculate_candle_atr(self, candle: pd.Series, prev_close: float) -> float:
        """Calculate True Range for a single candle"""
        high_low = candle['high'] - candle['low']
        high_prev_close = abs(candle['high'] - prev_close)
        low_prev_close = abs(candle['low'] - prev_close)
        return max(high_low, high_prev_close, low_prev_close)

    def detect_market_regime(self, df: pd.DataFrame) -> str:
        """
        Detect if market is currently TRENDING or RANGING

        Args:
            df: DataFrame with OHLC data

        Returns:
            'TRENDING' or 'RANGING'
        """
        if len(df) < 20:
            return 'RANGING'

        # Calculate momentum over last 20 candles
        recent_data = df.iloc[-20:]

        # Method 1: Price momentum
        price_start = recent_data.iloc[0]['close']
        price_end = recent_data.iloc[-1]['close']
        price_change_pct = abs(price_end - price_start) / price_start

        # Method 2: Directional consistency
        up_moves = 0
        down_moves = 0
        for i in range(1, len(recent_data)):
            if recent_data.iloc[i]['close'] > recent_data.iloc[i-1]['close']:
                up_moves += 1
            else:
                down_moves += 1

        directional_consistency = max(up_moves, down_moves) / len(recent_data)

        # Method 3: ATR-normalized momentum
        atr_values = []
        for i in range(1, len(recent_data)):
            prev_close = recent_data.iloc[i-1]['close']
            current_candle = recent_data.iloc[i]
            atr = self.calculate_candle_atr(current_candle, prev_close)
            atr_values.append(atr)

        avg_atr = np.mean(atr_values)
        momentum_strength = abs(price_end - price_start) / (avg_atr * len(recent_data))

        # Trending criteria: Strong momentum OR high directional consistency
        is_trending = (price_change_pct > 0.015 or  # 1.5% price move
                      directional_consistency > 0.65 or  # 65% directional consistency
                      momentum_strength > 1.5)  # Strong ATR-normalized momentum

        regime = 'TRENDING' if is_trending else 'RANGING'
        self.logger.info(f"📊 MARKET REGIME: {regime} (momentum: {momentum_strength:.2f}, consistency: {directional_consistency:.2f})")

        return regime

    def find_homogeneous_candles(self, df: pd.DataFrame) -> List[int]:
        """
        Find homogeneous candles based on market regime and behavioral consistency

        Args:
            df: DataFrame with OHLC data

        Returns:
            List of indices for homogeneous candles
        """
        if len(df) < self.min_channel_candles:
            return list(range(len(df)))

        # Detect market regime first
        regime = self.detect_market_regime(df)

        # Regime-specific parameters
        if regime == 'TRENDING':
            # TRENDING: Stricter similarity, larger channels
            min_candles = max(10, self.min_channel_candles)
            max_candles = min(25, self.max_channel_candles)
            atr_threshold = 0.30  # Stricter ATR similarity
        else:
            # RANGING: More lenient similarity, smaller channels
            min_candles = max(5, self.min_channel_candles)
            max_candles = min(15, self.max_channel_candles)
            atr_threshold = 0.60  # More lenient ATR similarity

        # Start with minimum required candles from the end
        homogeneous_indices = list(range(len(df) - min_candles, len(df)))

        # Calculate ATR for recent candles
        atr_values = []
        for i in range(1, len(df)):
            prev_close = df.iloc[i-1]['close']
            current_candle = df.iloc[i]
            atr = self.calculate_candle_atr(current_candle, prev_close)
            atr_values.append(atr)

        if len(atr_values) < min_candles:
            return homogeneous_indices

        # Calculate reference ATR from the minimum required candles
        reference_atr = np.mean(atr_values[-min_candles:])
        atr_lower_bound = reference_atr * (1 - atr_threshold)
        atr_upper_bound = reference_atr * (1 + atr_threshold)

        # Extend backwards while candles remain similar
        max_lookback = min(max_candles, len(df))

        for i in range(len(df) - min_candles - 1, len(df) - max_lookback - 1, -1):
            if i < 1:  # Need previous candle for ATR calculation
                break

            candle_atr = atr_values[i-1]  # ATR values are offset by 1

            # Check ATR similarity
            if atr_lower_bound <= candle_atr <= atr_upper_bound:
                homogeneous_indices.insert(0, i)
            else:
                # Stop extending if candle is not similar
                break

        self.logger.info(f"🔍 HOMOGENEOUS CANDLES ({regime}): Found {len(homogeneous_indices)} similar candles")
        self.logger.info(f"   Reference ATR: {reference_atr:.5f}")
        self.logger.info(f"   ATR Range: {atr_lower_bound:.5f} - {atr_upper_bound:.5f}")

        return homogeneous_indices
    
    def build_adaptive_channel(self, df: pd.DataFrame, homogeneous_indices: List[int]) -> Dict[str, Any]:
        """
        Build regression channel using only homogeneous candles
        
        Args:
            df: DataFrame with OHLC data
            homogeneous_indices: Indices of homogeneous candles to use
            
        Returns:
            Dictionary with channel data
        """
        if len(homogeneous_indices) < 3:
            return None
            
        # Extract homogeneous candles
        homogeneous_candles = df.iloc[homogeneous_indices]
        
        # Prepare data for regression
        X = np.array(range(len(homogeneous_candles))).reshape(-1, 1)
        y_close = homogeneous_candles['close'].values
        y_high = homogeneous_candles['high'].values
        y_low = homogeneous_candles['low'].values
        
        # Fit regression lines
        reg_close = LinearRegression().fit(X, y_close)
        reg_high = LinearRegression().fit(X, y_high)
        reg_low = LinearRegression().fit(X, y_low)
        
        # Calculate channel bounds
        latest_x = len(homogeneous_candles) - 1
        center_line = reg_close.predict([[latest_x]])[0]

        # Get slope and classify channel strength
        slope = reg_close.coef_[0]
        slope_abs = abs(slope)

        # Classify channel by slope strength
        if slope_abs > 0.5:
            channel_type = "STRONG_TREND"
            slope_bonus = 1.0
        elif slope_abs > 0.2:
            channel_type = "MEDIUM_TREND"
            slope_bonus = 0.7
        elif slope_abs > 0.05:
            channel_type = "WEAK_TREND"
            slope_bonus = 0.3
        else:
            channel_type = "FLAT_RANGING"
            slope_bonus = 0.1
        upper_bound = reg_high.predict([[latest_x]])[0]
        lower_bound = reg_low.predict([[latest_x]])[0]
        
        # Calculate channel width and position
        channel_width = (upper_bound - lower_bound) / 2
        current_price = df.iloc[-1]['close']
        
        if channel_width > 0:
            position = (current_price - lower_bound) / (upper_bound - lower_bound)
        else:
            position = 0.5
            
        # Calculate quality score (R² with slope bonus)
        r_squared = reg_close.score(X, y_close)
        quality_score = r_squared * (1 + slope_bonus)

        channel_data = {
            'center_line': center_line,
            'upper_bound': upper_bound,
            'lower_bound': lower_bound,
            'width': channel_width,
            'position': position,
            'candle_count': len(homogeneous_indices),
            'homogeneous_indices': homogeneous_indices,
            'slope': reg_close.coef_[0],
            'r_squared': r_squared,
            'quality_score': quality_score,
            'channel_type': channel_type,
            'slope_strength': slope_abs
        }
        
        self.logger.info(f"📊 ADAPTIVE CHANNEL BUILT ({channel_type}):")
        self.logger.info(f"   Candles Used: {len(homogeneous_indices)}")
        self.logger.info(f"   Upper: {upper_bound:.5f}")
        self.logger.info(f"   Center: {center_line:.5f}")
        self.logger.info(f"   Lower: {lower_bound:.5f}")
        self.logger.info(f"   Width: {channel_width:.5f}")
        self.logger.info(f"   Position: {position:.3f}")
        self.logger.info(f"   R²: {r_squared:.3f} | Quality: {quality_score:.3f}")
        self.logger.info(f"   Slope: {slope:+.6f} ({channel_type})")
        
        return channel_data

    def update_channel_state(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Update the adaptive channel state and return current channel information

        Args:
            df: DataFrame with OHLC data

        Returns:
            Dictionary with current channel state and data
        """
        current_candle_index = len(df) - 1

        # State machine logic
        if self.current_state == ChannelState.ACTIVE_CHANNEL:
            # Check if we have an active channel
            if self.current_channel_data is not None:
                # NEW: Check channel age - force renewal if too old
                channel_age = current_candle_index - (self.channel_start_candle or 0)
                if channel_age >= self.max_channel_age:
                    self.logger.info(f"🕐 CHANNEL AGE LIMIT REACHED: {channel_age} candles (max: {self.max_channel_age})")
                    self.current_state = ChannelState.TRANSITION
                    self.transition_start_candle = current_candle_index
                    self.transition_duration = self.transition_duration_min  # Minimal transition for age renewal
                    self.logger.info(f"🔄 ENTERING TRANSITION MODE (AGE RENEWAL): Duration {self.transition_duration} candles")
                    self.last_channel_break = current_candle_index
                    self.current_channel_data = None
                    self.channel_start_candle = None
                else:
                    # Check for channel break
                    is_broken, break_reason = self.detect_channel_break(df, self.current_channel_data)

                    if is_broken:
                        self.logger.info(f"🚨 CHANNEL BREAK DETECTED: {break_reason}")
                        self.current_state = ChannelState.TRANSITION
                        self.transition_start_candle = current_candle_index

                        # Calculate adaptive transition duration based on volatility
                        recent_atr = self._calculate_recent_volatility(df)
                        volatility_factor = min(2.0, max(0.5, recent_atr / 2.0))  # Scale factor
                        base_duration = (self.transition_duration_min + self.transition_duration_max) / 2
                        self.transition_duration = int(base_duration * volatility_factor)

                        self.logger.info(f"🔄 ENTERING TRANSITION MODE: Duration {self.transition_duration} candles")
                        self.last_channel_break = current_candle_index
                        self.current_channel_data = None
                        self.channel_start_candle = None
            else:
                # No active channel data - try to build initial channel
                self.logger.info(f"🔄 NO ACTIVE CHANNEL - ATTEMPTING TO BUILD INITIAL CHANNEL")
                self.current_state = ChannelState.BUILDING

        elif self.current_state == ChannelState.TRANSITION:
            # Check if transition period is over
            candles_in_transition = current_candle_index - self.transition_start_candle

            # Handle zero transition duration (instant transition)
            if self.transition_duration == 0 or candles_in_transition >= self.transition_duration:
                self.logger.info(f"🔄 TRANSITION COMPLETE: Starting channel building")
                self.current_state = ChannelState.BUILDING

        elif self.current_state == ChannelState.BUILDING:
            # Try to build new channel with homogeneous candles
            homogeneous_indices = self.find_homogeneous_candles(df)

            if len(homogeneous_indices) >= self.min_channel_candles:
                new_channel = self.build_adaptive_channel(df, homogeneous_indices)

                if new_channel is not None:
                    # Regime-specific quality thresholds
                    regime = self.detect_market_regime(df)
                    channel_type = new_channel.get('channel_type', 'UNKNOWN')
                    quality_score = new_channel.get('quality_score', 0)
                    r_squared = new_channel['r_squared']

                    # EXTREME LENIENT quality thresholds - accept almost any channel
                    if regime == 'TRENDING':
                        # TRENDING: Accept any trending channel
                        if channel_type in ['STRONG_TREND', 'MEDIUM_TREND', 'WEAK_TREND']:
                            accept_channel = True
                        elif r_squared > 0.01:  # Extremely low threshold
                            accept_channel = True
                        else:
                            accept_channel = False
                    else:
                        # RANGING: Accept almost any channel
                        if quality_score > 0.01 or r_squared > 0.01:  # Extremely low thresholds
                            accept_channel = True
                        else:
                            accept_channel = False

                    if accept_channel:
                        self.current_channel_data = new_channel
                        self.homogeneous_candles_indices = homogeneous_indices
                        self.current_state = ChannelState.ACTIVE_CHANNEL
                        self.channel_start_candle = current_candle_index  # NEW: Track channel start time
                        self.channel_count += 1  # Increment channel counter
                        self.logger.info(f"✅ NEW ADAPTIVE CHANNEL #{self.channel_count} ESTABLISHED ({channel_type})")
                    else:
                        self.logger.info(f"⚠️ Channel quality insufficient for {regime} regime: {channel_type} (Q:{quality_score:.3f}, R²:{r_squared:.3f})")
                else:
                    self.logger.info(f"⚠️ Channel quality insufficient, remaining in BUILDING state")
            else:
                self.logger.info(f"⚠️ Not enough homogeneous candles ({len(homogeneous_indices)}/{self.min_channel_candles})")

        # Return current state information
        return {
            'state': self.current_state.value,
            'channel_data': self.current_channel_data,
            'homogeneous_indices': self.homogeneous_candles_indices,
            'transition_progress': self._get_transition_progress(current_candle_index),
            'regime_contribution': self._calculate_regime_contribution()
        }

    def _calculate_recent_volatility(self, df: pd.DataFrame, periods: int = 10) -> float:
        """Calculate recent volatility for adaptive transition duration"""
        if len(df) < periods + 1:
            return 1.0

        recent_data = df.iloc[-periods-1:]
        atr_values = []

        for i in range(1, len(recent_data)):
            prev_close = recent_data.iloc[i-1]['close']
            current = recent_data.iloc[i]
            atr = self.calculate_candle_atr(current, prev_close)
            atr_values.append(atr)

        return np.mean(atr_values) if atr_values else 1.0

    def _get_transition_progress(self, current_candle_index: int) -> Optional[float]:
        """Get transition progress as percentage (0.0 to 1.0)"""
        if self.current_state != ChannelState.TRANSITION or self.transition_start_candle is None:
            return None

        # Handle zero transition duration (instant transition)
        if self.transition_duration == 0:
            return 1.0

        candles_elapsed = current_candle_index - self.transition_start_candle
        return min(1.0, candles_elapsed / self.transition_duration)

    def _calculate_regime_contribution(self) -> float:
        """
        Calculate contribution to regime detection based on channel state and slope

        Logic:
        - FLAT channel (|slope| <= 0.05) → RANGING contribution (negative weight)
        - TRENDING channel → No contribution (let other indicators decide)
        - BUILDING/TRANSITION states → TRANSITIONAL contribution (positive weight)
        """
        if self.current_state == ChannelState.TRANSITION:
            # Transitional state - contribute to TRANSITIONAL regime
            return self.regime_transition_weight
        elif self.current_state == ChannelState.BUILDING:
            # Building state (no channel yet) - contribute to TRANSITIONAL regime
            return self.regime_transition_weight * 0.5
        elif self.current_state == ChannelState.ACTIVE_CHANNEL and self.current_channel_data:
            # Active channel - check if it's flat (RANGING) or trending
            channel_slope = abs(self.current_channel_data.get('slope', 0))
            channel_type = self.current_channel_data.get('channel_type', 'UNKNOWN')

            if channel_type == 'FLAT_RANGING' or channel_slope <= 0.05:
                # Flat channel - contribute to RANGING regime (negative weight)
                return -self.regime_transition_weight  # Negative to push toward RANGING
            else:
                # Trending channel - no contribution, let other indicators decide
                return 0.0
        else:
            # Fallback - no contribution
            return 0.0

    def get_channel_position(self, current_price: float) -> Optional[float]:
        """
        Get current price position within the adaptive channel

        Args:
            current_price: Current market price

        Returns:
            Position (0.0 = lower bound, 1.0 = upper bound) or None if no active channel
        """
        if self.current_state != ChannelState.ACTIVE_CHANNEL or self.current_channel_data is None:
            return None

        upper = self.current_channel_data['upper_bound']
        lower = self.current_channel_data['lower_bound']

        if upper == lower:
            return 0.5

        position = (current_price - lower) / (upper - lower)
        return max(0.0, min(1.0, position))  # Clamp to [0, 1]

    def get_status_summary(self) -> Dict[str, Any]:
        """Get comprehensive status summary for logging"""
        summary = {
            'state': self.current_state.value,
            'has_active_channel': self.current_state == ChannelState.ACTIVE_CHANNEL,
            'regime_contribution': self._calculate_regime_contribution()
        }

        if self.current_channel_data:
            summary.update({
                'channel_candles': self.current_channel_data['candle_count'],
                'channel_width': self.current_channel_data['width'],
                'channel_r_squared': self.current_channel_data['r_squared'],
                'channel_slope': self.current_channel_data['slope']
            })

        if self.current_state == ChannelState.TRANSITION:
            summary['transition_progress'] = self._get_transition_progress(0)  # Will be updated by caller

        return summary
