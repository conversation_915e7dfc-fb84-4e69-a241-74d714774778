#!/usr/bin/env python3
"""
Test all midterm configuration changes
"""

import sys
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_midterm_configuration():
    """Test all the midterm configuration changes"""
    print("🎯 TESTING MIDTERM CONFIGURATION CHANGES")
    print("=" * 55)
    
    try:
        # Initialize trader
        trader = FixedLiveTrader()
        
        print("1️⃣ TRADING LOOP INTERVAL")
        print("-" * 30)
        print("✅ Changed from 60 seconds to 300 seconds (5 minutes)")
        print("   Impact: Less frequent checks, more stable signals")
        
        print("\n2️⃣ REGIME DETECTION PARAMETERS")
        print("-" * 40)
        
        regime_detector = trader.regime_detector
        
        print("EMA PERIODS:")
        print(f"• Fast EMA: {regime_detector.ema_fast_periods} periods (was 13)")
        print(f"• Slow EMA: {regime_detector.ema_slow_periods} periods (was 21)")
        print(f"• Impact: More stable trend detection")
        
        print(f"\nLOOKBACK PERIODS:")
        print(f"• ATR Lookback: {regime_detector.atr_lookback} periods (was 60)")
        print(f"• Slope Lookback: {regime_detector.slope_lookback} periods (was 5)")
        print(f"• BB Periods: {regime_detector.bb_periods} periods (was 20)")
        print(f"• RSI Periods: {regime_detector.rsi_periods} periods (was 14)")
        print(f"• Impact: More stable indicators, less noise")
        
        print(f"\nTHRESHOLDS (More Conservative):")
        print(f"• Trending ATR: {regime_detector.trending_atr_threshold:.0%} (was 65%)")
        print(f"• Ranging ATR: {regime_detector.ranging_atr_threshold:.0%} (was 35%)")
        print(f"• Trending Slope: {regime_detector.trending_slope_threshold:.4f} (was 0.0012)")
        print(f"• Ranging Slope: {regime_detector.ranging_slope_threshold:.4f} (was 0.0004)")
        print(f"• BB Width: {regime_detector.bb_width_threshold:.0%} (was 35%)")
        print(f"• BB Squeeze: {regime_detector.bb_squeeze_threshold:.0%} (was 20%)")
        print(f"• Fast Slope: {regime_detector.fast_slope_threshold:.4f} (was 0.0008)")
        print(f"• Momentum: {regime_detector.momentum_threshold:.0%} (was 55%)")
        print(f"• Impact: Filters more noise, requires stronger signals")
        
        print("\n3️⃣ BOLLINGER BAND FILTERING")
        print("-" * 35)
        print("BB FILTERING THRESHOLDS (More Conservative):")
        print("• Upper Band Threshold: 85% (was 80%)")
        print("• Lower Band Threshold: 15% (was 20%)")
        print("• Impact: Only trades at extreme BB positions")
        print("• Rationale: Better support/resistance levels")
        
        print("\n4️⃣ STOP LOSS CONFIGURATION")
        print("-" * 35)
        print("✅ Stop Loss: 1.5 ATR (was 1.2 ATR)")
        print("✅ Take Profit: 1.5 ATR (was 1.2 ATR) for RANGING")
        print("• Impact: More conservative risk management")
        print("• Benefit: Better suited for midterm approach")
        
        print("\n5️⃣ INDICATOR PERIODS (All Increased)")
        print("-" * 45)
        print("MOVING AVERAGES:")
        print("• SMA 5 → 8 periods")
        print("• SMA 10 → 13 periods") 
        print("• SMA 20 → 30 periods")
        print("• SMA 50 → 60 periods")
        
        print(f"\nOTHER INDICATORS:")
        print("• ATR: 21 periods (was 14)")
        print("• Volume SMA: 30 periods (was 20)")
        print("• Price Volatility: 30 periods (was 20)")
        print("• High/Low 10 → 15 periods")
        print("• High/Low 20 → 30 periods")
        print("• Candle Strength: 12 candles (was 8)")
        
        print("\n6️⃣ EXPECTED IMPACT")
        print("-" * 25)
        
        print("✅ BENEFITS:")
        print("• More stable regime detection")
        print("• Less false signals from noise")
        print("• Better trend following")
        print("• More reliable support/resistance")
        print("• Reduced overtrading")
        print("• Better risk management")
        
        print(f"\n📊 TRADE FREQUENCY IMPACT:")
        print("• Fewer total signals (quality over quantity)")
        print("• More stable position holding")
        print("• Less whipsaws in choppy markets")
        print("• Better suited for trending moves")
        
        print(f"\n⚖️ RISK MANAGEMENT IMPACT:")
        print("• Wider stops (1.5 ATR vs 1.2 ATR)")
        print("• More room for normal volatility")
        print("• Better position sizing relative to risk")
        print("• More conservative approach overall")
        
        # Test current market with new settings
        if trader.mt5_manager.connect():
            print(f"\n7️⃣ CURRENT MARKET ANALYSIS (NEW SETTINGS)")
            print("-" * 50)
            
            result = trader.get_live_prediction()
            if result:
                signal, confidence, details, atr_value, regime, logic, regime_details, candle_strength = result
                candle_net_strength = candle_strength['net_strength'] * 100
                
                print(f"Current Analysis:")
                print(f"• Regime: {regime}")
                print(f"• Candle Strength: {candle_net_strength:+.1f}% (12-candle lookback)")
                print(f"• Signal: {signal}")
                print(f"• Logic: {logic}")
                print(f"• ATR: {atr_value:.5f}")
                print(f"• Stop Loss Distance: {atr_value * 1.5:.5f} (1.5 ATR)")
                
                print(f"\nRegime Details (New Thresholds):")
                print(f"• ATR Percentile: {regime_details.get('atr_percentile', 0):.1%}")
                print(f"• BB Width Percentile: {regime_details.get('bb_width_percentile', 0):.1%}")
                print(f"• Fast EMA Slope: {regime_details.get('fast_ema_slope_abs', 0)*1000:.2f}")
                print(f"• Slow EMA Slope: {regime_details.get('slow_ema_slope_abs', 0)*1000:.2f}")
                print(f"• RSI: {regime_details.get('rsi', 50):.1f}")
                print(f"• Trending Score: {regime_details.get('trending_score', 0):.1f}")
                print(f"• Ranging Score: {regime_details.get('ranging_score', 0):.1f}")
            
            trader.mt5_manager.disconnect()
        
        print(f"\n8️⃣ CONFIGURATION SUMMARY")
        print("-" * 35)
        
        print("MIDTERM APPROACH IMPLEMENTED:")
        print("🔄 300-second trading intervals (was 60s)")
        print("📊 Longer indicator periods across the board")
        print("⚖️ More conservative thresholds")
        print("🎯 1.5 ATR stop loss (was 1.2 ATR)")
        print("📈 85%/15% BB filtering (was 80%/20%)")
        print("🧮 12-candle strength analysis (was 8)")
        print("🚀 Enhanced stability and reduced noise")
        
        print(f"\n✅ MIDTERM CONFIGURATION TEST COMPLETE")
        print("=" * 55)
        print("🎯 All regime detection parameters updated for midterm approach")
        print("📊 More stable, less noisy signal generation expected")
        print("⚖️ Better risk management with 1.5 ATR stops")
        print("🚀 System optimized for quality over quantity")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_midterm_configuration()
