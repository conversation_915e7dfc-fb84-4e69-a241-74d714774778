#!/usr/bin/env python3
"""
Test script to verify Enhanced Regime Detector timezone fixes
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timezone
import logging

# Add src to path
sys.path.append('src')

from enhanced_regime_detector import EnhancedRegimeDetector
from mt5_integration import MT<PERSON><PERSON><PERSON><PERSON>

def test_session_timing():
    """Test session timing fixes"""
    print("🧪 TESTING SESSION TIMING FIXES")
    print("=" * 50)
    
    # Initialize detector
    detector = EnhancedRegimeDetector("XAUUSD!", "M5", mtf_mode=False)
    
    # Check session definitions
    print("📊 Session Definitions (UTC):")
    for session, times in detector.sessions.items():
        print(f"   {session:6}: {times['start']:02d}:00 - {times['end']:02d}:00 UTC")
    
    # Test current session detection
    current_time = datetime.now(timezone.utc)
    current_hour = current_time.hour
    
    print(f"\n⏰ Current Time: {current_time.strftime('%H:%M UTC')}")
    print(f"📊 Current Hour: {current_hour}")
    
    # Determine active session
    active_sessions = []
    
    # London session
    if detector.sessions['LONDON']['start'] <= current_hour <= detector.sessions['LONDON']['end']:
        active_sessions.append('LONDON')
    
    # NY session  
    if detector.sessions['NY']['start'] <= current_hour <= detector.sessions['NY']['end']:
        active_sessions.append('NY')
    
    # Asian session (crosses midnight)
    if current_hour >= detector.sessions['ASIAN']['start'] or current_hour <= detector.sessions['ASIAN']['end']:
        active_sessions.append('ASIAN')
    
    if active_sessions:
        print(f"🕐 Active Sessions: {', '.join(active_sessions)}")
    else:
        print("🕐 No major sessions currently active")
    
    # Expected sessions at 15:45 UTC
    expected_sessions = ['LONDON', 'NY']  # Both should be active
    
    if set(active_sessions) == set(expected_sessions):
        print("✅ Session detection CORRECT!")
    else:
        print(f"❌ Session detection WRONG! Expected: {expected_sessions}, Got: {active_sessions}")
    
    return len(active_sessions) == 2  # Should have 2 active sessions

def test_timezone_consistency():
    """Test timezone consistency fixes"""
    print("\n🧪 TESTING TIMEZONE CONSISTENCY")
    print("=" * 50)
    
    try:
        # Initialize detector
        detector = EnhancedRegimeDetector("XAUUSD!", "M5", mtf_mode=False)
        
        # Test economic calendar timing
        if hasattr(detector, 'economic_calendar'):
            next_event = detector.economic_calendar.get_next_event()
            if next_event:
                print(f"📅 Next Event: {next_event.get('event', 'Unknown')}")
                print(f"🕐 Event Time: {next_event.get('time', 'Unknown')}")
                print("✅ Economic calendar access working")
            else:
                print("📅 No upcoming events found")
        
        print("✅ Timezone consistency test passed")
        return True
        
    except Exception as e:
        print(f"❌ Timezone consistency test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🎯 ENHANCED REGIME DETECTOR - TIMEZONE FIXES TEST")
    print("Testing session timing and timezone consistency fixes")
    print("=" * 70)
    
    # Configure logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise
    
    # Test 1: Session timing
    session_test_passed = test_session_timing()
    
    # Test 2: Timezone consistency
    timezone_test_passed = test_timezone_consistency()
    
    # Summary
    print(f"\n🎯 TEST SUMMARY")
    print("=" * 30)
    print(f"Session Timing: {'✅ PASSED' if session_test_passed else '❌ FAILED'}")
    print(f"Timezone Consistency: {'✅ PASSED' if timezone_test_passed else '❌ FAILED'}")
    
    if session_test_passed and timezone_test_passed:
        print("\n🚀 ALL TESTS PASSED! Enhanced Regime Detector fixes are working correctly.")
        return True
    else:
        print("\n⚠️ SOME TESTS FAILED! Please check the fixes.")
        return False

if __name__ == "__main__":
    main()
