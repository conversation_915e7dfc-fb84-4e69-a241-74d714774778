#!/usr/bin/env python3
"""
ANALYZE CONVERSION FACTOR
Find the correct conversion factor for XAUUSD
"""

def analyze_conversion_factor():
    """Analyze what the correct conversion factor should be"""
    print("🔍 ANALYZING CONVERSION FACTOR")
    print("=" * 50)
    
    # User's reported values
    user_sl_distance = 890  # points
    user_profit = 900       # points
    
    # System calculated values (raw)
    system_sl_raw = 19.40   # price difference
    system_profit_raw = 9.68  # price difference
    
    print(f"📊 USER'S VALUES:")
    print(f"SL Distance: {user_sl_distance} points")
    print(f"Profit: {user_profit} points")
    
    print(f"\n📊 SYSTEM RAW VALUES:")
    print(f"SL Distance: {system_sl_raw} (price)")
    print(f"Profit: {system_profit_raw} (price)")
    
    # Calculate conversion factors
    sl_conversion = user_sl_distance / system_sl_raw
    profit_conversion = user_profit / system_profit_raw
    
    print(f"\n🧮 CONVERSION FACTORS:")
    print(f"SL Conversion: {user_sl_distance} ÷ {system_sl_raw} = {sl_conversion:.2f}")
    print(f"Profit Conversion: {user_profit} ÷ {system_profit_raw} = {profit_conversion:.2f}")
    
    # Test different conversion factors
    print(f"\n🧪 TESTING CONVERSION FACTORS:")
    
    factors_to_test = [45.9, 46, 50, 92.9, 93, 100]
    
    for factor in factors_to_test:
        sl_converted = system_sl_raw * factor
        profit_converted = system_profit_raw * factor
        sl_units = profit_converted / sl_converted if sl_converted > 0 else 0
        
        print(f"\nFactor {factor}:")
        print(f"  SL: {sl_converted:.1f} points")
        print(f"  Profit: {profit_converted:.1f} points")
        print(f"  SL Units: {sl_units:.4f}")
        print(f"  Triggers: {'✅' if sl_units >= 1.0 else '❌'}")
        
        # Check how close to user's values
        sl_match = abs(sl_converted - user_sl_distance) < 50
        profit_match = abs(profit_converted - user_profit) < 50
        print(f"  Match: SL {'✅' if sl_match else '❌'}, Profit {'✅' if profit_match else '❌'}")
    
    # Find the factor that makes SL units = 1.01 (user's expected)
    print(f"\n🎯 FINDING CORRECT FACTOR:")
    target_sl_units = 1.01
    
    # If profit_converted / sl_converted = 1.01
    # And we want both to be close to user values
    # Then factor should make: (9.68 * factor) / (19.40 * factor) = 1.01
    # Which simplifies to: 9.68 / 19.40 = 0.499
    # But we want 1.01, so we need: 900 / 890 = 1.011
    
    # This means the raw ratio is wrong, let's check
    raw_ratio = system_profit_raw / system_sl_raw
    user_ratio = user_profit / user_sl_distance
    
    print(f"Raw ratio: {system_profit_raw} ÷ {system_sl_raw} = {raw_ratio:.4f}")
    print(f"User ratio: {user_profit} ÷ {user_sl_distance} = {user_ratio:.4f}")
    
    # The issue might be that the SL distance and profit have different conversion factors
    print(f"\n💡 HYPOTHESIS:")
    print(f"Maybe SL distance needs factor {sl_conversion:.1f}")
    print(f"And profit needs factor {profit_conversion:.1f}")
    
    # Test this hypothesis
    sl_with_correct_factor = system_sl_raw * sl_conversion
    profit_with_correct_factor = system_profit_raw * profit_conversion
    correct_sl_units = profit_with_correct_factor / sl_with_correct_factor
    
    print(f"\n🧪 TESTING DIFFERENT FACTORS:")
    print(f"SL: {system_sl_raw} × {sl_conversion:.1f} = {sl_with_correct_factor:.1f}")
    print(f"Profit: {system_profit_raw} × {profit_conversion:.1f} = {profit_with_correct_factor:.1f}")
    print(f"SL Units: {correct_sl_units:.4f}")
    print(f"Should trigger: {'✅' if correct_sl_units >= 1.0 else '❌'}")

if __name__ == "__main__":
    analyze_conversion_factor()
