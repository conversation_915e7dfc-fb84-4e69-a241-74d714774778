#!/usr/bin/env python3
"""
LSTM Sequence Preparation for Multi-target Prediction
Creates sequences for training LSTM model on XAUUSD data
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
import joblib
import os

class LSTMSequencePreparator:
    """Prepare sequences for LSTM training with multi-target prediction"""
    
    def __init__(self, sequence_length=60, prediction_steps=[1, 3, 5]):
        self.sequence_length = sequence_length
        self.prediction_steps = prediction_steps
        self.feature_scaler = StandardScaler()
        self.target_scalers = {}
        
    def load_prepared_data(self, filepath='data/lstm_training_data_12m.csv'):
        """Load the prepared training data"""
        print(f"Loading prepared data from {filepath}...")
        
        df = pd.read_csv(filepath)
        df['datetime'] = pd.to_datetime(df['datetime'])
        
        print(f"Loaded {len(df):,} rows with {len(df.columns)} columns")
        return df
    
    def get_feature_target_columns(self, df):
        """Separate feature and target columns"""
        
        # Load feature columns
        with open('data/feature_columns_12m.txt', 'r') as f:
            feature_cols = [line.strip() for line in f if line.strip()]
        
        # Get target columns
        target_cols = [col for col in df.columns if col.startswith('target_')]
        
        # Verify features exist in dataframe
        available_features = [col for col in feature_cols if col in df.columns]
        missing_features = set(feature_cols) - set(available_features)
        
        if missing_features:
            print(f"⚠️ Missing features: {missing_features}")
        
        print(f"Features: {len(available_features)}")
        print(f"Targets: {len(target_cols)}")
        
        return available_features, target_cols
    
    def create_sequences(self, df, feature_cols, target_cols):
        """Create sequences for LSTM training"""
        print(f"Creating sequences with length {self.sequence_length}...")
        
        # Prepare feature data
        feature_data = df[feature_cols].values
        target_data = df[target_cols].values
        
        # Handle NaN values
        feature_data = np.nan_to_num(feature_data, nan=0.0, posinf=0.0, neginf=0.0)
        target_data = np.nan_to_num(target_data, nan=0.0, posinf=0.0, neginf=0.0)
        
        # Scale features
        print("Scaling features...")
        feature_data_scaled = self.feature_scaler.fit_transform(feature_data)
        
        # Scale targets (each target type separately)
        target_data_scaled = target_data.copy()
        target_col_groups = self.group_target_columns(target_cols)
        
        for group_name, cols in target_col_groups.items():
            if cols:  # Only if columns exist
                col_indices = [target_cols.index(col) for col in cols if col in target_cols]
                if col_indices:
                    scaler = StandardScaler()
                    target_data_scaled[:, col_indices] = scaler.fit_transform(target_data[:, col_indices])
                    self.target_scalers[group_name] = scaler
                    print(f"Scaled {len(col_indices)} {group_name} targets")
        
        # Create sequences
        X, y = [], []
        
        for i in range(self.sequence_length, len(feature_data_scaled)):
            # Features: sequence of past data
            X.append(feature_data_scaled[i-self.sequence_length:i])
            
            # Targets: future values
            y.append(target_data_scaled[i])
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"Created {len(X):,} sequences")
        print(f"X shape: {X.shape} (samples, timesteps, features)")
        print(f"y shape: {y.shape} (samples, targets)")
        
        return X, y, feature_cols, target_cols
    
    def group_target_columns(self, target_cols):
        """Group target columns by type for separate scaling"""
        groups = {
            'price': [],
            'direction': [],
            'change_pct': []
        }
        
        for col in target_cols:
            if 'direction' in col:
                groups['direction'].append(col)
            elif 'change_pct' in col:
                groups['change_pct'].append(col)
            elif any(price_type in col for price_type in ['open_pct', 'high_pct', 'low_pct', 'close_pct']):
                groups['price'].append(col)
        
        return groups
    
    def split_data(self, X, y, train_ratio=0.7, val_ratio=0.2):
        """Split data into train/validation/test sets"""
        n_samples = len(X)
        
        train_end = int(n_samples * train_ratio)
        val_end = int(n_samples * (train_ratio + val_ratio))
        
        X_train = X[:train_end]
        y_train = y[:train_end]
        
        X_val = X[train_end:val_end]
        y_val = y[train_end:val_end]
        
        X_test = X[val_end:]
        y_test = y[val_end:]
        
        print(f"Data split:")
        print(f"  Train: {len(X_train):,} samples ({len(X_train)/n_samples:.1%})")
        print(f"  Val:   {len(X_val):,} samples ({len(X_val)/n_samples:.1%})")
        print(f"  Test:  {len(X_test):,} samples ({len(X_test)/n_samples:.1%})")
        
        return (X_train, y_train), (X_val, y_val), (X_test, y_test)
    
    def save_sequences(self, X_train, y_train, X_val, y_val, X_test, y_test, 
                      feature_cols, target_cols, output_dir='data/lstm_sequences'):
        """Save prepared sequences and metadata"""
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Save sequences
        np.save(f'{output_dir}/X_train.npy', X_train)
        np.save(f'{output_dir}/y_train.npy', y_train)
        np.save(f'{output_dir}/X_val.npy', X_val)
        np.save(f'{output_dir}/y_val.npy', y_val)
        np.save(f'{output_dir}/X_test.npy', X_test)
        np.save(f'{output_dir}/y_test.npy', y_test)
        
        # Save scalers
        joblib.dump(self.feature_scaler, f'{output_dir}/feature_scaler.pkl')
        joblib.dump(self.target_scalers, f'{output_dir}/target_scalers.pkl')
        
        # Save metadata
        metadata = {
            'sequence_length': self.sequence_length,
            'prediction_steps': self.prediction_steps,
            'n_features': len(feature_cols),
            'n_targets': len(target_cols),
            'feature_columns': feature_cols,
            'target_columns': target_cols,
            'train_samples': len(X_train),
            'val_samples': len(X_val),
            'test_samples': len(X_test)
        }
        
        import json
        with open(f'{output_dir}/metadata.json', 'w') as f:
            json.dump(metadata, f, indent=2)
        
        print(f"Saved sequences and metadata to {output_dir}/")
        
        return metadata
    
    def prepare_complete_dataset(self):
        """Complete pipeline to prepare LSTM sequences"""
        print("=== LSTM SEQUENCE PREPARATION ===")
        
        # Load data
        df = self.load_prepared_data()
        
        # Get feature and target columns
        feature_cols, target_cols = self.get_feature_target_columns(df)
        
        # Create sequences
        X, y, feature_cols, target_cols = self.create_sequences(df, feature_cols, target_cols)
        
        # Split data
        (X_train, y_train), (X_val, y_val), (X_test, y_test) = self.split_data(X, y)
        
        # Save sequences
        metadata = self.save_sequences(X_train, y_train, X_val, y_val, X_test, y_test, 
                                     feature_cols, target_cols)
        
        print("✅ LSTM sequence preparation completed!")
        return metadata

if __name__ == "__main__":
    preparator = LSTMSequencePreparator(sequence_length=60, prediction_steps=[1, 3, 5])
    metadata = preparator.prepare_complete_dataset()
