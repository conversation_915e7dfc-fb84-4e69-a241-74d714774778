#!/usr/bin/env python3
"""
Position Sizing Fix Test

Tests that position sizing correctly calculates risk based on ACTUAL stop loss distance
instead of fixed 1.50 points, ensuring proper 4% risk management.
"""

import pandas as pd
import numpy as np
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader

def create_test_data_with_signal_candle():
    """Create test data with a specific signal candle scenario"""
    
    # Create 10 candles
    dates = pd.date_range('2024-01-01', periods=10, freq='5min')
    
    # Create realistic OHLC data
    data = {
        'open': [4300.0, 4302.0, 4305.0, 4303.0, 4301.0, 4299.0, 4297.0, 4295.0, 4298.0, 4300.0],
        'high': [4305.0, 4307.0, 4308.0, 4306.0, 4304.0, 4302.0, 4300.0, 4298.0, 4302.0, 4305.0],
        'low':  [4298.0, 4300.0, 4302.0, 4301.0, 4299.0, 4296.0, 4294.0, 4293.0, 4296.0, 4298.0],
        'close':[4302.0, 4305.0, 4303.0, 4301.0, 4299.0, 4297.0, 4295.0, 4298.0, 4300.0, 4302.0],
        'volume': [500] * 10,
    }
    
    df = pd.DataFrame(data, index=dates)
    return df

def test_position_sizing_scenarios():
    """Test position sizing with different signal candle scenarios"""
    
    print("🧪 Position Sizing Fix Test")
    print("=" * 50)
    
    # Create test trader
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test parameters
    balance = 1000.0
    atr_value = 2.0
    
    print(f"📋 Test Setup:")
    print(f"  - Balance: ${balance:.2f}")
    print(f"  - Expected Risk: 4% = ${balance * 0.04:.2f}")
    print(f"  - ATR: {atr_value:.2f}")
    
    # Create test data
    df = create_test_data_with_signal_candle()
    
    # Mock the get_latest_data_safe method to return our test data
    trader.get_latest_data_safe = lambda: df
    
    test_results = []
    
    # Test 1: BUY signal with signal candle low at 4296.0
    print(f"\n📊 Test 1: BUY Signal")
    print("-" * 30)
    
    current_price = 4300.0  # Entry price
    signal = "BUY"
    signal_candle = df.iloc[-2]  # Second to last candle
    expected_sl = signal_candle['low'] - 1.50  # 4296.0 - 1.50 = 4294.50
    expected_sl_distance = abs(current_price - expected_sl)  # 4300.0 - 4294.50 = 5.50
    expected_risk_per_lot = expected_sl_distance * 100  # 5.50 * 100 = $550
    expected_lot_size = (balance * 0.04) / expected_risk_per_lot  # $40 / $550 = 0.073
    
    print(f"  Current Price: {current_price:.2f}")
    print(f"  Signal Candle Low: {signal_candle['low']:.2f}")
    print(f"  Expected SL: {expected_sl:.2f}")
    print(f"  Expected SL Distance: {expected_sl_distance:.2f} points")
    print(f"  Expected Risk per Lot: ${expected_risk_per_lot:.2f}")
    print(f"  Expected Lot Size: {expected_lot_size:.3f}")
    
    # Calculate actual lot size
    actual_lot_size = trader.calculate_position_size(
        balance=balance,
        current_price=current_price,
        atr_value=atr_value,
        half_size=False,
        swing_size_factor=1.0,
        signal=signal
    )
    
    actual_risk = actual_lot_size * expected_risk_per_lot
    actual_risk_percent = (actual_risk / balance) * 100
    
    print(f"  Actual Lot Size: {actual_lot_size:.3f}")
    print(f"  Actual Risk: ${actual_risk:.2f}")
    print(f"  Actual Risk %: {actual_risk_percent:.2f}%")
    
    # Check if risk is approximately 4%
    risk_ok = abs(actual_risk_percent - 4.0) < 0.5  # Allow 0.5% tolerance
    lot_size_reasonable = 0.01 <= actual_lot_size <= 1.0  # Reasonable lot size range
    
    test1_passed = risk_ok and lot_size_reasonable
    print(f"  ✅ Risk Check: {'PASSED' if risk_ok else 'FAILED'} (target: 4.0%, actual: {actual_risk_percent:.2f}%)")
    print(f"  ✅ Lot Size Check: {'PASSED' if lot_size_reasonable else 'FAILED'} (range: 0.01-1.0)")
    
    test_results.append(("BUY Signal", test1_passed))
    
    # Test 2: SELL signal with signal candle high at 4302.0
    print(f"\n📊 Test 2: SELL Signal")
    print("-" * 30)
    
    current_price = 4300.0  # Entry price
    signal = "SELL"
    signal_candle = df.iloc[-2]  # Second to last candle
    expected_sl = signal_candle['high'] + 1.50  # 4302.0 + 1.50 = 4303.50
    expected_sl_distance = abs(expected_sl - current_price)  # 4303.50 - 4300.0 = 3.50
    expected_risk_per_lot = expected_sl_distance * 100  # 3.50 * 100 = $350
    expected_lot_size = (balance * 0.04) / expected_risk_per_lot  # $40 / $350 = 0.114
    
    print(f"  Current Price: {current_price:.2f}")
    print(f"  Signal Candle High: {signal_candle['high']:.2f}")
    print(f"  Expected SL: {expected_sl:.2f}")
    print(f"  Expected SL Distance: {expected_sl_distance:.2f} points")
    print(f"  Expected Risk per Lot: ${expected_risk_per_lot:.2f}")
    print(f"  Expected Lot Size: {expected_lot_size:.3f}")
    
    # Calculate actual lot size
    actual_lot_size = trader.calculate_position_size(
        balance=balance,
        current_price=current_price,
        atr_value=atr_value,
        half_size=False,
        swing_size_factor=1.0,
        signal=signal
    )
    
    actual_risk = actual_lot_size * expected_risk_per_lot
    actual_risk_percent = (actual_risk / balance) * 100
    
    print(f"  Actual Lot Size: {actual_lot_size:.3f}")
    print(f"  Actual Risk: ${actual_risk:.2f}")
    print(f"  Actual Risk %: {actual_risk_percent:.2f}%")
    
    # Check if risk is approximately 4%
    risk_ok = abs(actual_risk_percent - 4.0) < 0.5  # Allow 0.5% tolerance
    lot_size_reasonable = 0.01 <= actual_lot_size <= 1.0  # Reasonable lot size range
    
    test2_passed = risk_ok and lot_size_reasonable
    print(f"  ✅ Risk Check: {'PASSED' if risk_ok else 'FAILED'} (target: 4.0%, actual: {actual_risk_percent:.2f}%)")
    print(f"  ✅ Lot Size Check: {'PASSED' if lot_size_reasonable else 'FAILED'} (range: 0.01-1.0)")
    
    test_results.append(("SELL Signal", test2_passed))
    
    # Test 3: Half-size scenario
    print(f"\n📊 Test 3: Half-Size BUY Signal")
    print("-" * 30)
    
    current_price = 4300.0
    signal = "BUY"
    half_size = True
    expected_risk_percent = 2.0  # Half of 4%
    
    actual_lot_size = trader.calculate_position_size(
        balance=balance,
        current_price=current_price,
        atr_value=atr_value,
        half_size=half_size,
        swing_size_factor=1.0,
        signal=signal
    )
    
    signal_candle = df.iloc[-2]
    expected_sl = signal_candle['low'] - 1.50
    expected_sl_distance = abs(current_price - expected_sl)
    expected_risk_per_lot = expected_sl_distance * 100
    actual_risk = actual_lot_size * expected_risk_per_lot
    actual_risk_percent = (actual_risk / balance) * 100
    
    print(f"  Half-Size: {half_size}")
    print(f"  Expected Risk %: {expected_risk_percent:.2f}%")
    print(f"  Actual Risk %: {actual_risk_percent:.2f}%")
    print(f"  Actual Lot Size: {actual_lot_size:.3f}")
    
    risk_ok = abs(actual_risk_percent - expected_risk_percent) < 0.5
    test3_passed = risk_ok
    print(f"  ✅ Half-Size Risk Check: {'PASSED' if risk_ok else 'FAILED'}")
    
    test_results.append(("Half-Size BUY", test3_passed))
    
    # Summary
    print(f"\n🎯 Test Summary")
    print("=" * 30)
    
    passed_tests = sum(1 for _, passed in test_results if passed)
    total_tests = len(test_results)
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed Tests: {passed_tests}")
    print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
    
    for test_name, passed in test_results:
        print(f"  {test_name}: {'✅ PASSED' if passed else '❌ FAILED'}")
    
    overall_success = passed_tests == total_tests
    
    if overall_success:
        print(f"\n🎉 SUCCESS: Position sizing now uses ACTUAL stop loss distance!")
        print("✅ Risk management fixed - no more $240 SL on $1000 balance")
        print("✅ Proper 4% risk calculation implemented")
        print("✅ Half-size and scaling factors working correctly")
    else:
        print(f"\n❌ ISSUES DETECTED: Some position sizing tests failed")
    
    return overall_success

if __name__ == "__main__":
    try:
        success = test_position_sizing_scenarios()
        exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
