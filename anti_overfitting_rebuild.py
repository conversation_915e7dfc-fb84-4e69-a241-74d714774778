#!/usr/bin/env python3
"""
Anti-Overfitting System Rebuild
Fix the severe overfitting issue with proper regularization
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager

# Import ML libraries
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.model_selection import train_test_split, cross_val_score, TimeSeriesSplit
from sklearn.metrics import classification_report, roc_auc_score, roc_curve
from sklearn.linear_model import LogisticRegression
import xgboost as xgb
import joblib

class AntiOverfittingSystem:
    def __init__(self):
        self.mt5_manager = MT5Manager()
        self.scaler = RobustScaler()  # More robust to outliers
        self.model = None
        self.selected_features = None
        
    def create_simple_features(self, df):
        """Create simple, robust features to prevent overfitting"""
        df = df.copy()
        
        # Simple price-based features only
        df['return_1'] = df['close'].pct_change(1)
        df['return_3'] = df['close'].pct_change(3)
        df['return_5'] = df['close'].pct_change(5)
        df['return_10'] = df['close'].pct_change(10)
        
        # Simple moving averages
        df['sma_5'] = df['close'].rolling(5).mean()
        df['sma_10'] = df['close'].rolling(10).mean()
        df['sma_20'] = df['close'].rolling(20).mean()
        
        # Price ratios (normalized features)
        df['price_sma5_ratio'] = df['close'] / df['sma_5'] - 1
        df['price_sma10_ratio'] = df['close'] / df['sma_10'] - 1
        df['price_sma20_ratio'] = df['close'] / df['sma_20'] - 1
        
        # Simple volatility
        df['volatility_5'] = df['close'].rolling(5).std() / df['close']
        df['volatility_20'] = df['close'].rolling(20).std() / df['close']
        
        # High-low range
        df['hl_ratio'] = (df['high'] - df['low']) / df['close']
        
        # Simple RSI (14 period only)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi_normalized'] = (df['rsi'] - 50) / 50  # Normalize RSI
        
        # Simple MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = (exp1 - exp2) / df['close']  # Normalized MACD
        
        return df
    
    def get_recent_data(self, bars=3000):
        """Get recent data - smaller dataset to prevent overfitting"""
        print("📊 Getting recent data for anti-overfitting model...")
        
        if not self.mt5_manager.connect():
            print("❌ Failed to connect to MT5")
            return None
        
        df = self.mt5_manager.get_latest_data("XAUUSD!", "M5", bars)
        if df is None or len(df) < 1000:
            print("❌ Failed to get sufficient data")
            return None
        
        print(f"✅ Retrieved {len(df)} bars")
        print(f"📅 Date range: {df.index[0]} to {df.index[-1]}")
        
        return df
    
    def create_conservative_targets(self, df):
        """Create more conservative targets to prevent overfitting"""
        print("🎯 Creating conservative targets...")
        
        # Use longer forward period to reduce noise
        forward_periods = 5  # Increased from 3
        df['future_return'] = df['close'].shift(-forward_periods) / df['close'] - 1
        
        # Use more conservative thresholds
        returns_std = df['future_return'].std()
        up_threshold = returns_std * 0.75  # Increased threshold
        down_threshold = -returns_std * 0.75
        
        print(f"📊 Conservative thresholds:")
        print(f"   Returns std: {returns_std:.6f}")
        print(f"   Up threshold: {up_threshold:.6f}")
        print(f"   Down threshold: {down_threshold:.6f}")
        
        # Create targets
        df['target'] = np.where(df['future_return'] > up_threshold, 1,
                               np.where(df['future_return'] < down_threshold, 0, np.nan))
        
        # Remove NaN targets
        clean_df = df.dropna(subset=['target', 'future_return']).copy()
        clean_df['target'] = clean_df['target'].astype(int)
        
        print(f"🎯 Target distribution:")
        print(f"   Total samples: {len(clean_df):,}")
        print(f"   Up: {sum(clean_df['target'] == 1):,} ({sum(clean_df['target'] == 1)/len(clean_df)*100:.1f}%)")
        print(f"   Down: {sum(clean_df['target'] == 0):,} ({sum(clean_df['target'] == 0)/len(clean_df)*100:.1f}%)")
        
        return clean_df
    
    def select_features_conservatively(self, df):
        """Conservative feature selection to prevent overfitting"""
        print("🔍 Conservative feature selection...")
        
        # Get feature columns
        feature_columns = [col for col in df.columns 
                          if col not in ['target', 'future_return', 'open', 'high', 'low', 'close', 'volume']]
        
        X = df[feature_columns].copy()
        y = df['target'].copy()
        
        print(f"📊 Initial features: {len(feature_columns)}")
        
        # Remove high-NaN features
        nan_ratios = X.isnull().sum() / len(X)
        valid_features = nan_ratios[nan_ratios < 0.05].index.tolist()  # Stricter NaN threshold
        X_clean = X[valid_features].copy()
        X_clean = X_clean.fillna(X_clean.median())
        
        print(f"📊 After NaN filtering: {len(valid_features)} features")
        
        # Remove low-variance features
        feature_vars = X_clean.var()
        high_var_features = feature_vars[feature_vars > 1e-6].index.tolist()
        X_clean = X_clean[high_var_features]
        
        print(f"📊 After variance filtering: {len(high_var_features)} features")
        
        # Conservative feature selection - only top 8 features
        selector = SelectKBest(score_func=f_classif, k=8)
        X_selected = selector.fit_transform(X_clean, y)
        
        selected_feature_names = X_clean.columns[selector.get_support()].tolist()
        self.selected_features = selected_feature_names
        
        print(f"🎯 Selected {len(self.selected_features)} features:")
        feature_scores = selector.scores_[selector.get_support()]
        for i, (feature, score) in enumerate(zip(self.selected_features, feature_scores)):
            print(f"   {i+1:2d}. {feature:20s}: {score:.2f}")
        
        X_selected_df = pd.DataFrame(X_selected, columns=self.selected_features, index=X_clean.index)
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X_selected_df)
        X_scaled_df = pd.DataFrame(X_scaled, columns=self.selected_features, index=X_selected_df.index)
        
        return X_scaled_df, y
    
    def train_anti_overfitting_model(self, X, y):
        """Train model with strong anti-overfitting measures"""
        print("🤖 Training anti-overfitting model...")
        
        # Time series split for proper validation
        tscv = TimeSeriesSplit(n_splits=5)
        
        # Test multiple models with strong regularization
        models = {
            'LogisticRegression': LogisticRegression(
                C=0.1,  # Strong regularization
                random_state=42,
                max_iter=1000
            ),
            'RandomForest': RandomForestClassifier(
                n_estimators=50,  # Fewer trees
                max_depth=4,      # Shallow trees
                min_samples_split=20,  # Conservative splits
                min_samples_leaf=10,   # Conservative leaves
                random_state=42,
                n_jobs=-1
            ),
            'XGBoost': xgb.XGBClassifier(
                n_estimators=50,   # Fewer estimators
                max_depth=3,       # Very shallow
                learning_rate=0.05, # Slower learning
                subsample=0.7,     # More regularization
                colsample_bytree=0.7,
                reg_alpha=1.0,     # L1 regularization
                reg_lambda=1.0,    # L2 regularization
                random_state=42,
                n_jobs=-1,
                eval_metric='logloss'
            )
        }
        
        best_model = None
        best_score = 0
        best_name = ""
        
        for name, model in models.items():
            print(f"\n🔄 Testing {name}...")
            
            # Cross-validation with time series splits
            cv_scores = cross_val_score(model, X, y, cv=tscv, scoring='roc_auc')
            mean_score = cv_scores.mean()
            std_score = cv_scores.std()
            
            print(f"   CV AUC: {mean_score:.4f} ± {std_score:.4f}")
            print(f"   CV Scores: {[f'{s:.3f}' for s in cv_scores]}")
            
            # Train on full data for final test
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.3, random_state=42, stratify=y
            )
            
            model.fit(X_train, y_train)
            
            train_pred = model.predict_proba(X_train)[:, 1]
            test_pred = model.predict_proba(X_test)[:, 1]
            
            train_auc = roc_auc_score(y_train, train_pred)
            test_auc = roc_auc_score(y_test, test_pred)
            gap = train_auc - test_auc
            
            print(f"   Train AUC: {train_auc:.4f}")
            print(f"   Test AUC: {test_auc:.4f}")
            print(f"   Gap: {gap:.4f}")
            
            # Check for overfitting
            if gap > 0.05:
                print(f"   ⚠️  Overfitting detected (gap > 0.05)")
            else:
                print(f"   ✅ Good generalization (gap ≤ 0.05)")
            
            # Select best model based on test AUC and low overfitting
            if test_auc > best_score and gap < 0.08:  # Strict overfitting threshold
                best_score = test_auc
                best_model = model
                best_name = name
        
        if best_model is None:
            print("❌ No model passed overfitting test!")
            return False
        
        self.model = best_model
        
        print(f"\n🏆 Best model: {best_name}")
        print(f"   Test AUC: {best_score:.4f}")
        print(f"   Overfitting gap: < 0.08")
        
        return True
    
    def validate_final_model(self, X, y):
        """Final validation to ensure no overfitting"""
        print("\n🔍 Final anti-overfitting validation...")
        
        if self.model is None:
            return False
        
        # Multiple random splits to test consistency
        aucs = []
        gaps = []
        
        for i in range(5):
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.3, random_state=42+i, stratify=y
            )
            
            self.model.fit(X_train, y_train)
            
            train_pred = self.model.predict_proba(X_train)[:, 1]
            test_pred = self.model.predict_proba(X_test)[:, 1]
            
            train_auc = roc_auc_score(y_train, train_pred)
            test_auc = roc_auc_score(y_test, test_pred)
            gap = train_auc - test_auc
            
            aucs.append(test_auc)
            gaps.append(gap)
        
        mean_auc = np.mean(aucs)
        mean_gap = np.mean(gaps)
        std_auc = np.std(aucs)
        std_gap = np.std(gaps)
        
        print(f"📊 Final Validation Results:")
        print(f"   Mean Test AUC: {mean_auc:.4f} ± {std_auc:.4f}")
        print(f"   Mean Overfitting Gap: {mean_gap:.4f} ± {std_gap:.4f}")
        print(f"   Individual AUCs: {[f'{a:.3f}' for a in aucs]}")
        print(f"   Individual Gaps: {[f'{g:.3f}' for g in gaps]}")
        
        # Final checks
        if mean_auc < 0.55:
            print("❌ Model performance too low (AUC < 0.55)")
            return False
        
        if mean_gap > 0.05:
            print("❌ Overfitting detected (mean gap > 0.05)")
            return False
        
        if std_gap > 0.03:
            print("❌ Inconsistent overfitting (gap std > 0.03)")
            return False
        
        print("✅ Model passes all anti-overfitting tests!")
        return True
    
    def test_live_predictions(self):
        """Test live predictions"""
        print("\n🔍 Testing live predictions...")
        
        predictions = []
        
        for i in range(3):
            df = self.mt5_manager.get_latest_data("XAUUSD!", "M5", 200)
            if df is None:
                continue
            
            features_df = self.create_simple_features(df)
            latest_features = features_df[self.selected_features].iloc[-1]
            latest_features = latest_features.fillna(latest_features.median())
            
            features_scaled = self.scaler.transform(latest_features.values.reshape(1, -1))
            pred_proba = self.model.predict_proba(features_scaled)[0, 1]
            
            predictions.append(pred_proba)
            print(f"   Test {i+1}: {pred_proba:.6f}")
            
            if i < 2:
                import time
                time.sleep(5)
        
        unique_preds = len(set(predictions))
        print(f"   Unique predictions: {unique_preds}")
        
        return unique_preds > 1
    
    def save_model(self):
        """Save the anti-overfitting model"""
        os.makedirs('models_anti_overfit', exist_ok=True)
        
        joblib.dump(self.model, 'models_anti_overfit/model.pkl')
        joblib.dump(self.scaler, 'models_anti_overfit/scaler.pkl')
        joblib.dump(self.selected_features, 'models_anti_overfit/features.pkl')
        
        print("💾 Anti-overfitting model saved!")
    
    def run_anti_overfitting_rebuild(self):
        """Run complete anti-overfitting rebuild"""
        print("🚀 ANTI-OVERFITTING SYSTEM REBUILD")
        print("=" * 60)
        
        # Get data
        df = self.get_recent_data(3000)
        if df is None:
            return False
        
        # Create simple features
        features_df = self.create_simple_features(df)
        
        # Create conservative targets
        clean_df = self.create_conservative_targets(features_df)
        if len(clean_df) < 500:
            print("❌ Insufficient data")
            return False
        
        # Select features conservatively
        X, y = self.select_features_conservatively(clean_df)
        
        # Train anti-overfitting model
        if not self.train_anti_overfitting_model(X, y):
            return False
        
        # Final validation
        if not self.validate_final_model(X, y):
            return False
        
        # Test live predictions
        if not self.test_live_predictions():
            print("❌ Live prediction test failed")
            return False
        
        # Save model
        self.save_model()
        
        print("\n🎉 ANTI-OVERFITTING REBUILD COMPLETED!")
        self.mt5_manager.disconnect()
        
        return True

def main():
    """Main function"""
    print("🚨 ANTI-OVERFITTING SYSTEM REBUILD")
    print("Fixing the severe overfitting issue with proper regularization")
    print()
    
    system = AntiOverfittingSystem()
    success = system.run_anti_overfitting_rebuild()
    
    if success:
        print("\n✅ Anti-overfitting system ready!")
    else:
        print("\n❌ Anti-overfitting rebuild failed")

if __name__ == "__main__":
    main()
