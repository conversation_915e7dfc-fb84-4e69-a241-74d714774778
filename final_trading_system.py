#!/usr/bin/env python3
"""
FINAL OPTIMIZED TRADING SYSTEM
No overfitting - Pure technical analysis with optimized parameters
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager

class OptimizedTechnicalAnalyzer:
    """Optimized technical indicators"""
    
    @staticmethod
    def calculate_ema(data, period):
        return data.ewm(span=period).mean()
    
    @staticmethod
    def calculate_rsi(data, period=14):
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    @staticmethod
    def calculate_atr(high, low, close, period=14):
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window=period).mean()

class OptimizedSignalGenerator:
    """Generate high-quality trading signals"""
    
    def __init__(self):
        self.tech = OptimizedTechnicalAnalyzer()
    
    def generate_signals(self, df):
        """Generate optimized trading signals"""
        # Calculate indicators
        df['ema_10'] = self.tech.calculate_ema(df['close'], 10)
        df['ema_21'] = self.tech.calculate_ema(df['close'], 21)
        df['ema_50'] = self.tech.calculate_ema(df['close'], 50)
        df['rsi'] = self.tech.calculate_rsi(df['close'])
        df['atr'] = self.tech.calculate_atr(df['high'], df['low'], df['close'])
        
        # Trend analysis
        df['trend'] = np.where(df['ema_21'] > df['ema_50'], 1, -1)
        df['momentum'] = np.where(df['ema_10'] > df['ema_21'], 1, -1)
        
        # Price position relative to EMAs
        df['price_above_ema21'] = df['close'] > df['ema_21']
        df['price_above_ema50'] = df['close'] > df['ema_50']
        
        # RSI conditions
        df['rsi_bullish'] = (df['rsi'] > 45) & (df['rsi'] < 65)
        df['rsi_bearish'] = (df['rsi'] > 35) & (df['rsi'] < 55)
        
        # Initialize signals
        df['signal'] = 0
        df['signal_strength'] = 0.0
        
        # OPTIMIZED LONG SIGNALS
        long_conditions = (
            (df['trend'] == 1) &  # Main trend up
            (df['momentum'] == 1) &  # Short-term momentum up
            (df['price_above_ema21']) &  # Price above key EMA
            (df['price_above_ema50']) &  # Price above trend EMA
            (df['rsi_bullish']) &  # RSI in good range
            (df['close'] > df['close'].shift(1))  # Current bar closing higher
        )
        
        # OPTIMIZED SHORT SIGNALS
        short_conditions = (
            (df['trend'] == -1) &  # Main trend down
            (df['momentum'] == -1) &  # Short-term momentum down
            (~df['price_above_ema21']) &  # Price below key EMA
            (~df['price_above_ema50']) &  # Price below trend EMA
            (df['rsi_bearish']) &  # RSI in good range
            (df['close'] < df['close'].shift(1))  # Current bar closing lower
        )
        
        df.loc[long_conditions, 'signal'] = 1
        df.loc[short_conditions, 'signal'] = -1
        
        # Calculate signal strength
        df.loc[df['signal'] != 0, 'signal_strength'] = (
            abs(df['ema_21'] - df['ema_50']) / df['close'] * 1000 +  # Trend strength
            abs(50 - df['rsi']) / 50 * 30 +  # RSI momentum
            abs(df['ema_10'] - df['ema_21']) / df['close'] * 500  # Short-term momentum
        )
        
        return df

class OptimizedRiskManager:
    """Optimized risk management"""
    
    def __init__(self, account_balance, risk_percent=3.0, atr_multiplier=1.2):
        self.account_balance = account_balance
        self.risk_percent = risk_percent  # Reduced risk
        self.atr_multiplier = atr_multiplier  # Tighter stops
    
    def calculate_position_size(self, entry_price, atr_value):
        risk_amount = self.account_balance * (self.risk_percent / 100)
        stop_distance = atr_value * self.atr_multiplier
        position_size = risk_amount / stop_distance
        lot_size = max(0.01, round(position_size / 100, 2))
        return lot_size
    
    def calculate_stops_targets(self, entry_price, atr_value, signal_direction):
        stop_distance = atr_value * self.atr_multiplier
        target_distance = atr_value * 2.5  # Better risk:reward 1:2.08
        
        if signal_direction == 1:  # Long
            stop_loss = entry_price - stop_distance
            take_profit = entry_price + target_distance
        else:  # Short
            stop_loss = entry_price + stop_distance
            take_profit = entry_price - target_distance
        
        return stop_loss, take_profit

class FinalTradingSystem:
    """Final optimized trading system"""
    
    def __init__(self):
        self.mt5_manager = MT5Manager()
        self.signal_generator = OptimizedSignalGenerator()
        self.risk_manager = None
        self.last_signal_time = None
        self.min_signal_interval = 900  # 15 minutes between signals
    
    def initialize(self):
        print("🚀 FINAL OPTIMIZED TRADING SYSTEM")
        print("=" * 60)
        print("✅ Optimized technical analysis")
        print("✅ Enhanced risk management (3% risk)")
        print("✅ Better risk:reward ratio (1:2.08)")
        print("✅ Quality over quantity approach")
        print("=" * 60)
        
        if not self.mt5_manager.connect():
            return False
        
        account_info = self.mt5_manager.get_account_info()
        if account_info:
            balance = account_info['balance']
            self.risk_manager = OptimizedRiskManager(balance)
            print(f"💰 Account Balance: ${balance:.2f}")
            print(f"🎯 Risk per trade: 3% (${balance * 0.03:.2f})")
            return True
        
        return False
    
    def analyze_market(self):
        df = self.mt5_manager.get_latest_data("XAUUSD!", "M5", 200)
        if df is None or len(df) < 100:
            return None, None
        
        df = self.signal_generator.generate_signals(df)
        latest = df.iloc[-1]
        
        if latest['signal'] == 0:
            return None, df
        
        # Check minimum time between signals
        current_time = datetime.now()
        if (self.last_signal_time and 
            (current_time - self.last_signal_time).seconds < self.min_signal_interval):
            return None, df
        
        # Only trade high-strength signals
        if latest['signal_strength'] < 20:  # Minimum strength threshold
            return None, df
        
        return latest, df
    
    def execute_trade(self, signal_data):
        try:
            tick = self.mt5_manager.get_symbol_info_tick("XAUUSD!")
            if not tick:
                return False
            
            entry_price = tick['ask'] if signal_data['signal'] == 1 else tick['bid']
            atr_value = signal_data['atr']
            
            lot_size = self.risk_manager.calculate_position_size(entry_price, atr_value)
            stop_loss, take_profit = self.risk_manager.calculate_stops_targets(
                entry_price, atr_value, signal_data['signal']
            )
            
            order_type = "buy" if signal_data['signal'] == 1 else "sell"
            
            print(f"\n🎯 HIGH-QUALITY SIGNAL DETECTED!")
            print(f"   Direction: {'LONG' if signal_data['signal'] == 1 else 'SHORT'}")
            print(f"   Entry Price: {entry_price:.2f}")
            print(f"   Position Size: {lot_size:.2f} lots")
            print(f"   Stop Loss: {stop_loss:.2f}")
            print(f"   Take Profit: {take_profit:.2f}")
            print(f"   Signal Strength: {signal_data['signal_strength']:.1f}")
            print(f"   Risk:Reward: 1:2.08")
            
            success = self.mt5_manager.place_order(
                symbol="XAUUSD!",
                order_type=order_type,
                lot_size=lot_size,
                price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                comment=f"Final-{signal_data['signal_strength']:.0f}"
            )
            
            if success:
                self.last_signal_time = datetime.now()
                print("✅ Trade executed successfully!")
                return True
            else:
                print("❌ Trade execution failed!")
                return False
                
        except Exception as e:
            print(f"❌ Error executing trade: {e}")
            return False
    
    def run_live_trading(self):
        if not self.initialize():
            print("❌ Failed to initialize trading system")
            return
        
        print("\n🔄 Starting optimized live trading...")
        print("Press Ctrl+C to stop")
        
        try:
            while True:
                signal, market_data = self.analyze_market()
                
                if signal is not None:
                    self.execute_trade(signal)
                else:
                    if market_data is not None:
                        latest = market_data.iloc[-1]
                        print(f"\n📊 Market Analysis ({datetime.now().strftime('%H:%M:%S')})")
                        print(f"   Price: {latest['close']:.2f}")
                        print(f"   Trend: {'UP' if latest['trend'] == 1 else 'DOWN'}")
                        print(f"   RSI: {latest['rsi']:.1f}")
                        print(f"   Signal: None (waiting for high-quality setup)")
                
                import time
                time.sleep(900)  # Wait 15 minutes
                
        except KeyboardInterrupt:
            print("\n🛑 Trading stopped by user")
        except Exception as e:
            print(f"\n❌ Error in live trading: {e}")
        finally:
            self.mt5_manager.disconnect()

def main():
    system = FinalTradingSystem()
    system.run_live_trading()

if __name__ == "__main__":
    main()
