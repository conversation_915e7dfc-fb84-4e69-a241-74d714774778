#!/usr/bin/env python3
"""
Fixed System Rebuild - Address All Critical Issues
1. Fix feature engineering bugs
2. Use recent data matching current price levels
3. Add proper feature scaling
4. Ensure model actually learns
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager

# Import ML libraries
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import mutual_info_classif
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, roc_auc_score
import xgboost as xgb
import joblib

class FixedFeatureEngineer:
    """Fixed feature engineering with proper calculations"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        
    def create_technical_indicators(self, df):
        """Create technical indicators with proper calculations"""
        df = df.copy()
        
        # Basic price features
        df['return_1'] = df['close'].pct_change(1)
        df['return_3'] = df['close'].pct_change(3)
        df['return_5'] = df['close'].pct_change(5)
        
        # Volatility
        df['high_low_pct'] = (df['high'] - df['low']) / df['close']
        df['close_open_pct'] = (df['close'] - df['open']) / df['open']
        
        # Moving averages
        df['sma_5'] = df['close'].rolling(5).mean()
        df['sma_10'] = df['close'].rolling(10).mean()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['sma_50'] = df['close'].rolling(50).mean()
        
        # Price relative to moving averages
        df['price_sma5_ratio'] = df['close'] / df['sma_5']
        df['price_sma20_ratio'] = df['close'] / df['sma_20']
        df['price_sma50_ratio'] = df['close'] / df['sma_50']
        
        # Bollinger Bands
        bb_period = 20
        bb_std = 2
        df['bb_middle'] = df['close'].rolling(bb_period).mean()
        bb_rolling_std = df['close'].rolling(bb_period).std()
        df['bb_upper'] = df['bb_middle'] + (bb_rolling_std * bb_std)
        df['bb_lower'] = df['bb_middle'] - (bb_rolling_std * bb_std)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # RSI (Fixed calculation)
        def calculate_rsi(prices, period=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        df['rsi'] = calculate_rsi(df['close'])
        
        # MACD (Fixed calculation)
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Rolling min/max (FIXED - these were broken before)
        df['highest_high_5'] = df['high'].rolling(5).max()
        df['lowest_low_5'] = df['low'].rolling(5).min()
        df['highest_high_10'] = df['high'].rolling(10).max()
        df['lowest_low_10'] = df['low'].rolling(10).min()
        df['highest_high_20'] = df['high'].rolling(20).max()
        df['lowest_low_20'] = df['low'].rolling(20).min()
        
        # Price position in recent range
        df['price_position_5'] = (df['close'] - df['lowest_low_5']) / (df['highest_high_5'] - df['lowest_low_5'])
        df['price_position_20'] = (df['close'] - df['lowest_low_20']) / (df['highest_high_20'] - df['lowest_low_20'])
        
        # Volume features (if available)
        if 'volume' in df.columns:
            df['volume_sma'] = df['volume'].rolling(20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # Momentum indicators
        df['momentum_3'] = df['close'] / df['close'].shift(3)
        df['momentum_5'] = df['close'] / df['close'].shift(5)
        df['momentum_10'] = df['close'] / df['close'].shift(10)
        
        # Stochastic %K (Fixed calculation)
        def calculate_stochastic(high, low, close, k_period=14):
            lowest_low = low.rolling(k_period).min()
            highest_high = high.rolling(k_period).max()
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            return k_percent
        
        df['stoch_k'] = calculate_stochastic(df['high'], df['low'], df['close'])
        df['stoch_d'] = df['stoch_k'].rolling(3).mean()
        
        # Williams %R
        df['williams_r'] = -100 * ((df['highest_high_20'] - df['close']) / (df['highest_high_20'] - df['lowest_low_20']))
        
        # Average True Range (ATR)
        df['tr1'] = df['high'] - df['low']
        df['tr2'] = abs(df['high'] - df['close'].shift(1))
        df['tr3'] = abs(df['low'] - df['close'].shift(1))
        df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
        df['atr'] = df['true_range'].rolling(14).mean()
        df['atr_ratio'] = df['atr'] / df['close']
        
        # Clean up temporary columns
        df = df.drop(['tr1', 'tr2', 'tr3'], axis=1, errors='ignore')
        
        return df

class FixedTradingSystem:
    def __init__(self):
        self.mt5_manager = MT5Manager()
        self.feature_engineer = FixedFeatureEngineer()
        self.model = None
        self.scaler = StandardScaler()
        self.selected_features = None
        
    def get_recent_live_data(self, bars=5000):
        """Get recent live data that matches current price levels"""
        print("📊 Getting recent live data...")
        
        if not self.mt5_manager.connect():
            print("❌ Failed to connect to MT5")
            return None
        
        # Get recent data
        df = self.mt5_manager.get_latest_data("XAUUSD!", "M5", bars)
        if df is None or len(df) < 1000:
            print("❌ Failed to get sufficient live data")
            return None
        
        print(f"✅ Retrieved {len(df)} recent bars")
        print(f"📅 Date range: {df.index[0]} to {df.index[-1]}")
        print(f"💰 Price range: {df['close'].min():.2f} to {df['close'].max():.2f}")
        
        return df
    
    def create_targets(self, df, forward_periods=3):
        """Create balanced targets using recent data"""
        print("🎯 Creating targets...")
        
        # Calculate forward returns
        df['future_return'] = df['close'].shift(-forward_periods) / df['close'] - 1
        
        # Use dynamic thresholds based on recent volatility
        returns_std = df['future_return'].std()
        up_threshold = returns_std * 0.5  # 0.5 standard deviations
        down_threshold = -returns_std * 0.5
        
        print(f"📊 Volatility-based thresholds:")
        print(f"   Returns std: {returns_std:.6f}")
        print(f"   Up threshold: {up_threshold:.6f}")
        print(f"   Down threshold: {down_threshold:.6f}")
        
        # Create targets
        df['target'] = np.where(df['future_return'] > up_threshold, 1,
                               np.where(df['future_return'] < down_threshold, 0, np.nan))
        
        # Remove NaN targets
        clean_df = df.dropna(subset=['target', 'future_return']).copy()
        clean_df['target'] = clean_df['target'].astype(int)
        
        print(f"🎯 Target distribution:")
        print(f"   Total samples: {len(clean_df):,}")
        print(f"   Up: {sum(clean_df['target'] == 1):,} ({sum(clean_df['target'] == 1)/len(clean_df)*100:.1f}%)")
        print(f"   Down: {sum(clean_df['target'] == 0):,} ({sum(clean_df['target'] == 0)/len(clean_df)*100:.1f}%)")
        
        return clean_df
    
    def select_and_scale_features(self, df):
        """Select best features and apply scaling"""
        print("🔍 Selecting and scaling features...")
        
        # Get feature columns (exclude target and price columns)
        feature_columns = [col for col in df.columns 
                          if col not in ['target', 'future_return', 'open', 'high', 'low', 'close', 'volume']]
        
        X = df[feature_columns].copy()
        y = df['target'].copy()
        
        print(f"📊 Initial features: {len(feature_columns)}")
        
        # Remove features with too many NaN values
        nan_threshold = 0.1
        nan_ratios = X.isnull().sum() / len(X)
        valid_features = nan_ratios[nan_ratios < nan_threshold].index.tolist()
        X_clean = X[valid_features].copy()
        
        print(f"📊 After NaN filtering: {len(valid_features)} features")
        
        # Fill remaining NaN values
        X_clean = X_clean.fillna(X_clean.median())
        
        # Remove features with zero variance
        feature_vars = X_clean.var()
        non_zero_var_features = feature_vars[feature_vars > 1e-8].index.tolist()
        X_clean = X_clean[non_zero_var_features]
        
        print(f"📊 After variance filtering: {len(non_zero_var_features)} features")
        
        # Feature selection using mutual information
        mi_scores = mutual_info_classif(X_clean, y, random_state=42)
        feature_scores = pd.Series(mi_scores, index=X_clean.columns).sort_values(ascending=False)
        
        # Select top features
        n_features = min(15, len(feature_scores))
        self.selected_features = feature_scores.head(n_features).index.tolist()
        
        print(f"🎯 Top {n_features} selected features:")
        for i, (feature, score) in enumerate(feature_scores.head(n_features).items()):
            print(f"   {i+1:2d}. {feature:20s}: {score:.6f}")
        
        X_selected = X_clean[self.selected_features].copy()
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X_selected)
        X_scaled_df = pd.DataFrame(X_scaled, columns=self.selected_features, index=X_selected.index)
        
        print(f"✅ Features scaled successfully")
        
        return X_scaled_df, y
    
    def train_model(self, X, y):
        """Train model with proper validation"""
        print("🤖 Training model...")
        
        # Split data for validation
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        print(f"📊 Training set: {len(X_train):,} samples")
        print(f"📊 Test set: {len(X_test):,} samples")
        
        # Train XGBoost with proper parameters
        self.model = xgb.XGBClassifier(
            n_estimators=200,
            max_depth=6,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            n_jobs=-1,
            eval_metric='logloss'
        )
        
        # Fit model
        self.model.fit(X_train, y_train)
        
        # Validate model
        train_pred = self.model.predict_proba(X_train)[:, 1]
        test_pred = self.model.predict_proba(X_test)[:, 1]
        
        train_auc = roc_auc_score(y_train, train_pred)
        test_auc = roc_auc_score(y_test, test_pred)
        
        print(f"📊 Model Performance:")
        print(f"   Training AUC: {train_auc:.4f}")
        print(f"   Test AUC: {test_auc:.4f}")
        
        # Check for overfitting
        if train_auc - test_auc > 0.1:
            print(f"⚠️  Warning: Possible overfitting (AUC diff: {train_auc - test_auc:.4f})")
        else:
            print(f"✅ Good generalization (AUC diff: {train_auc - test_auc:.4f})")
        
        # Test predictions diversity
        unique_preds = len(np.unique(test_pred))
        print(f"📊 Prediction diversity: {unique_preds:,} unique values out of {len(test_pred):,}")
        
        if unique_preds < 100:
            print(f"⚠️  Warning: Low prediction diversity")
        else:
            print(f"✅ Good prediction diversity")
        
        # Classification report
        test_pred_binary = (test_pred > 0.5).astype(int)
        print(f"\n📊 Classification Report:")
        print(classification_report(y_test, test_pred_binary))
        
        return train_auc, test_auc
    
    def test_live_predictions(self):
        """Test live predictions to ensure they vary"""
        print("🔍 Testing live predictions...")
        
        if self.model is None or self.selected_features is None:
            print("❌ Model not trained")
            return False
        
        predictions = []
        
        for i in range(3):
            print(f"   Test {i+1}/3:")
            
            # Get live data
            df = self.mt5_manager.get_latest_data("XAUUSD!", "M5", 200)
            if df is None:
                print("   ❌ Failed to get live data")
                continue
            
            # Calculate features
            features_df = self.feature_engineer.create_technical_indicators(df)
            
            # Get latest features
            latest_features = features_df[self.selected_features].iloc[-1]
            latest_features = latest_features.fillna(latest_features.median())
            
            # Scale features
            features_scaled = self.scaler.transform(latest_features.values.reshape(1, -1))
            
            # Make prediction
            pred_proba = self.model.predict_proba(features_scaled)[0, 1]
            confidence = abs(pred_proba - 0.5) * 2
            
            print(f"   Prediction: {pred_proba:.6f} (Confidence: {confidence:.6f})")
            predictions.append(pred_proba)
            
            if i < 2:
                import time
                time.sleep(10)  # Short wait
        
        # Check prediction diversity
        unique_preds = len(set(predictions))
        print(f"\n📊 Live Prediction Analysis:")
        print(f"   Predictions: {[f'{p:.6f}' for p in predictions]}")
        print(f"   Unique predictions: {unique_preds}")
        
        if unique_preds == 1:
            print(f"❌ All predictions identical - model still stuck!")
            return False
        else:
            print(f"✅ Predictions vary - model working correctly!")
            return True
    
    def save_model(self):
        """Save the trained model and scaler"""
        print("💾 Saving model...")
        
        os.makedirs('models_fixed', exist_ok=True)
        
        # Save model
        joblib.dump(self.model, 'models_fixed/xgboost_model.pkl')
        joblib.dump(self.scaler, 'models_fixed/feature_scaler.pkl')
        joblib.dump(self.selected_features, 'models_fixed/selected_features.pkl')
        
        print("✅ Model saved successfully!")
    
    def run_complete_rebuild(self):
        """Run complete system rebuild"""
        print("🚀 COMPLETE SYSTEM REBUILD")
        print("=" * 60)
        
        # Step 1: Get recent live data
        df = self.get_recent_live_data(5000)
        if df is None:
            return False
        
        # Step 2: Create features
        print("\n🔧 Creating features...")
        features_df = self.feature_engineer.create_technical_indicators(df)
        print(f"✅ Created {features_df.shape[1]} features")
        
        # Step 3: Create targets
        clean_df = self.create_targets(features_df)
        if len(clean_df) < 1000:
            print("❌ Insufficient clean data")
            return False
        
        # Step 4: Select and scale features
        X_scaled, y = self.select_and_scale_features(clean_df)
        
        # Step 5: Train model
        train_auc, test_auc = self.train_model(X_scaled, y)
        
        # Step 6: Test live predictions
        if not self.test_live_predictions():
            print("❌ Live prediction test failed")
            return False
        
        # Step 7: Save model
        self.save_model()
        
        print(f"\n🎉 SYSTEM REBUILD COMPLETED SUCCESSFULLY!")
        print(f"   Training AUC: {train_auc:.4f}")
        print(f"   Test AUC: {test_auc:.4f}")
        print(f"   Features: {len(self.selected_features)}")
        
        # Disconnect
        self.mt5_manager.disconnect()
        
        return True

def main():
    """Main function"""
    print("🔧 FIXED SYSTEM REBUILD")
    print("This will fix all critical issues and rebuild the system properly")
    print()
    
    system = FixedTradingSystem()
    success = system.run_complete_rebuild()
    
    if success:
        print("\n✅ System rebuild completed successfully!")
        print("The trading system is now properly fixed and ready for use.")
    else:
        print("\n❌ System rebuild failed - see errors above")

if __name__ == "__main__":
    main()
