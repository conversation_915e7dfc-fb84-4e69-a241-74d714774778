"""
Demo script for LSTM Prediction Plotter
Shows how to use the prediction system for trade entry analysis
"""

import pandas as pd
import sys
import os

# Add src to path
sys.path.append('src')

from prediction_plotter import PredictionPlotter

def main():
    """Demo the LSTM prediction plotter"""
    print("🚀 LSTM Market Prediction Demo")
    print("=" * 50)
    
    # Load recent market data
    print("Loading XAUUSD market data...")
    df = pd.read_csv('data/XAU_5m_data.csv')
    df['datetime'] = pd.to_datetime(df['datetime'])
    df.set_index('datetime', inplace=True)
    
    # Use recent data for prediction
    df_recent = df.tail(200).copy()  # Last 200 candles for context
    
    print(f"✅ Loaded {len(df_recent)} recent candles")
    print(f"Date range: {df_recent.index[0]} to {df_recent.index[-1]}")
    print(f"Current price: ${df_recent.iloc[-1]['close']:.2f}")
    
    # Initialize prediction plotter
    print("\nInitializing LSTM Prediction System...")
    plotter = PredictionPlotter()
    
    # Make predictions and create plot
    print("\nMaking predictions...")
    price_predictions, plot_path = plotter.predict_and_plot(
        df_recent,
        save_path='plots/demo_prediction.png',
        show_candles=100
    )
    
    if price_predictions:
        print("\n🎯 PREDICTION ANALYSIS")
        print("=" * 30)
        
        current_close = df_recent.iloc[-1]['close']
        
        for step in [1, 3, 5]:
            step_key = f'step_{step}'
            if step_key in price_predictions:
                pred = price_predictions[step_key]
                direction = "BULLISH" if price_predictions['directions'][step_key] == 1 else "BEARISH"
                confidence = price_predictions['direction_probs'][step_key]
                change_pct = price_predictions['change_percentages'][step_key] * 100
                
                print(f"\n📊 {step} Candle(s) Ahead:")
                print(f"   Direction: {direction} ({confidence:.1%} confidence)")
                print(f"   Expected Change: {change_pct:+.2f}%")
                print(f"   Predicted Close: ${pred['close']:.2f}")
                
                # Risk/Reward Analysis
                if direction == "BULLISH":
                    potential_gain = pred['close'] - current_close
                    print(f"   Potential Gain: ${potential_gain:+.2f}")
                else:
                    potential_loss = current_close - pred['close']
                    print(f"   Potential Loss: ${potential_loss:+.2f}")
        
        # Trading Recommendation
        print("\n💡 TRADING RECOMMENDATION")
        print("=" * 25)
        
        # Get step 1 prediction (most reliable)
        step1_direction = price_predictions['directions']['step_1']
        step1_confidence = price_predictions['direction_probs']['step_1']
        step1_change = price_predictions['change_percentages']['step_1'] * 100
        
        if step1_confidence > 0.6:  # High confidence threshold
            if step1_direction == 1:
                print("🟢 STRONG BUY SIGNAL")
                print(f"   Confidence: {step1_confidence:.1%}")
                print(f"   Expected move: +{abs(step1_change):.2f}%")
            else:
                print("🔴 STRONG SELL SIGNAL")
                print(f"   Confidence: {step1_confidence:.1%}")
                print(f"   Expected move: -{abs(step1_change):.2f}%")
        elif step1_confidence > 0.55:  # Medium confidence
            action = "BUY" if step1_direction == 1 else "SELL"
            print(f"🟡 MODERATE {action} SIGNAL")
            print(f"   Confidence: {step1_confidence:.1%}")
            print(f"   Expected move: {step1_change:+.2f}%")
        else:
            print("⚪ NEUTRAL - Low confidence prediction")
            print(f"   Confidence: {step1_confidence:.1%}")
            print("   Consider waiting for clearer signals")
        
        if plot_path:
            print(f"\n📈 Prediction chart saved: {plot_path}")
        else:
            print("\n⚠️  Chart not created (matplotlib not available)")
        
        print("\n" + "=" * 50)
        print("✅ Demo completed successfully!")
        print("🔄 Run this script again to get updated predictions")
        
    else:
        print("❌ Prediction failed")

if __name__ == "__main__":
    # Create plots directory
    os.makedirs('plots', exist_ok=True)
    
    try:
        main()
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
