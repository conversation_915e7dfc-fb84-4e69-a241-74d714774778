#!/usr/bin/env python3
"""
Ultra Conservative Model - Minimal Features, Maximum Regularization
Focus on simplicity and generalization over performance
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager

# Import ML libraries
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, TimeSeriesSplit, cross_val_score
from sklearn.metrics import roc_auc_score, classification_report
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
import joblib

class UltraConservativeModel:
    def __init__(self):
        self.mt5_manager = MT5Manager()
        self.scaler = StandardScaler()
        self.model = None
        self.selected_features = ['return_5', 'return_10', 'price_sma20_ratio']  # Only 3 features!
        
    def create_minimal_features(self, df):
        """Create only the most basic, robust features"""
        df = df.copy()
        
        # Only the most basic features
        df['return_1'] = df['close'].pct_change(1)
        df['return_5'] = df['close'].pct_change(5)
        df['return_10'] = df['close'].pct_change(10)
        
        # Simple moving average
        df['sma_20'] = df['close'].rolling(20).mean()
        df['price_sma20_ratio'] = (df['close'] / df['sma_20']) - 1
        
        return df
    
    def get_data_and_targets(self):
        """Get data and create very conservative targets"""
        print("📊 Getting data for ultra-conservative model...")
        
        if not self.mt5_manager.connect():
            return None, None
        
        # Use even less data to prevent overfitting
        df = self.mt5_manager.get_latest_data("XAUUSD!", "M5", 2000)
        if df is None or len(df) < 500:
            return None, None
        
        print(f"✅ Retrieved {len(df)} bars")
        
        # Create minimal features
        df = self.create_minimal_features(df)
        
        # Very conservative targets - only strong moves
        df['future_return'] = df['close'].shift(-10) / df['close'] - 1  # 10-period forward
        
        # Use very high thresholds - only trade obvious moves
        returns_std = df['future_return'].std()
        up_threshold = returns_std * 1.0    # 1 standard deviation
        down_threshold = -returns_std * 1.0
        
        print(f"📊 Ultra-conservative thresholds:")
        print(f"   Up threshold: {up_threshold:.6f}")
        print(f"   Down threshold: {down_threshold:.6f}")
        
        # Create targets
        df['target'] = np.where(df['future_return'] > up_threshold, 1,
                               np.where(df['future_return'] < down_threshold, 0, np.nan))
        
        # Clean data
        clean_df = df.dropna(subset=['target'] + self.selected_features).copy()
        clean_df['target'] = clean_df['target'].astype(int)
        
        print(f"🎯 Target distribution:")
        print(f"   Total samples: {len(clean_df):,}")
        print(f"   Up: {sum(clean_df['target'] == 1):,} ({sum(clean_df['target'] == 1)/len(clean_df)*100:.1f}%)")
        print(f"   Down: {sum(clean_df['target'] == 0):,} ({sum(clean_df['target'] == 0)/len(clean_df)*100:.1f}%)")
        
        if len(clean_df) < 200:
            print("❌ Insufficient data after conservative filtering")
            return None, None
        
        X = clean_df[self.selected_features].copy()
        y = clean_df['target'].copy()
        
        return X, y
    
    def train_ultra_conservative_model(self, X, y):
        """Train with maximum regularization"""
        print("🤖 Training ultra-conservative model...")
        print(f"📊 Using only {len(self.selected_features)} features: {self.selected_features}")
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        X_scaled_df = pd.DataFrame(X_scaled, columns=self.selected_features, index=X.index)
        
        # Test only the most conservative models
        models = {
            'LogisticRegression_C0.01': LogisticRegression(
                C=0.01,  # Very strong regularization
                random_state=42,
                max_iter=1000,
                penalty='l2'
            ),
            'LogisticRegression_C0.1': LogisticRegression(
                C=0.1,   # Strong regularization
                random_state=42,
                max_iter=1000,
                penalty='l2'
            ),
            'RandomForest_Conservative': RandomForestClassifier(
                n_estimators=20,      # Very few trees
                max_depth=2,          # Very shallow
                min_samples_split=50, # Very conservative
                min_samples_leaf=25,  # Very conservative
                random_state=42,
                n_jobs=-1
            )
        }
        
        best_model = None
        best_score = 0
        best_name = ""
        best_gap = float('inf')
        
        # Time series cross-validation
        tscv = TimeSeriesSplit(n_splits=3)  # Fewer splits
        
        for name, model in models.items():
            print(f"\n🔄 Testing {name}...")
            
            # Cross-validation
            try:
                cv_scores = cross_val_score(model, X_scaled_df, y, cv=tscv, scoring='roc_auc')
                mean_cv = cv_scores.mean()
                std_cv = cv_scores.std()
                
                print(f"   CV AUC: {mean_cv:.4f} ± {std_cv:.4f}")
                
                # Train-test split validation
                X_train, X_test, y_train, y_test = train_test_split(
                    X_scaled_df, y, test_size=0.4, random_state=42, stratify=y  # Larger test set
                )
                
                model.fit(X_train, y_train)
                
                train_pred = model.predict_proba(X_train)[:, 1]
                test_pred = model.predict_proba(X_test)[:, 1]
                
                train_auc = roc_auc_score(y_train, train_pred)
                test_auc = roc_auc_score(y_test, test_pred)
                gap = train_auc - test_auc
                
                print(f"   Train AUC: {train_auc:.4f}")
                print(f"   Test AUC: {test_auc:.4f}")
                print(f"   Gap: {gap:.4f}")
                
                # Very strict criteria
                if gap > 0.03:
                    print(f"   ❌ Overfitting (gap > 0.03)")
                    continue
                
                if test_auc < 0.52:
                    print(f"   ❌ Performance too low (AUC < 0.52)")
                    continue
                
                print(f"   ✅ Passes overfitting test")
                
                # Select model with best test performance and lowest gap
                if test_auc > best_score or (test_auc >= best_score * 0.98 and gap < best_gap):
                    best_score = test_auc
                    best_model = model
                    best_name = name
                    best_gap = gap
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
                continue
        
        if best_model is None:
            print("❌ No model passed ultra-conservative tests!")
            return False
        
        self.model = best_model
        
        print(f"\n🏆 Best ultra-conservative model: {best_name}")
        print(f"   Test AUC: {best_score:.4f}")
        print(f"   Overfitting gap: {best_gap:.4f}")
        
        return True
    
    def final_validation(self, X, y):
        """Ultra-strict final validation"""
        print("\n🔍 Ultra-strict final validation...")
        
        X_scaled = self.scaler.transform(X)
        
        # Multiple random validations
        test_aucs = []
        gaps = []
        
        for seed in [42, 123, 456, 789, 999]:
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=0.4, random_state=seed, stratify=y
            )
            
            self.model.fit(X_train, y_train)
            
            train_pred = self.model.predict_proba(X_train)[:, 1]
            test_pred = self.model.predict_proba(X_test)[:, 1]
            
            train_auc = roc_auc_score(y_train, train_pred)
            test_auc = roc_auc_score(y_test, test_pred)
            gap = train_auc - test_auc
            
            test_aucs.append(test_auc)
            gaps.append(gap)
        
        mean_auc = np.mean(test_aucs)
        mean_gap = np.mean(gaps)
        std_auc = np.std(test_aucs)
        std_gap = np.std(gaps)
        max_gap = np.max(gaps)
        
        print(f"📊 Final Validation (5 random seeds):")
        print(f"   Mean Test AUC: {mean_auc:.4f} ± {std_auc:.4f}")
        print(f"   Mean Gap: {mean_gap:.4f} ± {std_gap:.4f}")
        print(f"   Max Gap: {max_gap:.4f}")
        print(f"   Individual AUCs: {[f'{a:.3f}' for a in test_aucs]}")
        print(f"   Individual Gaps: {[f'{g:.3f}' for g in gaps]}")
        
        # Ultra-strict criteria
        if mean_auc < 0.52:
            print("❌ Mean AUC too low (< 0.52)")
            return False
        
        if max_gap > 0.05:
            print("❌ Maximum gap too high (> 0.05)")
            return False
        
        if std_gap > 0.02:
            print("❌ Gap inconsistency too high (std > 0.02)")
            return False
        
        print("✅ Passes all ultra-strict validation tests!")
        return True
    
    def test_live_predictions(self):
        """Test live predictions"""
        print("\n🔍 Testing live predictions...")
        
        predictions = []
        
        for i in range(3):
            df = self.mt5_manager.get_latest_data("XAUUSD!", "M5", 100)
            if df is None:
                continue
            
            df = self.create_minimal_features(df)
            latest_features = df[self.selected_features].iloc[-1]
            
            if latest_features.isnull().any():
                print(f"   Test {i+1}: NaN features, skipping")
                continue
            
            features_scaled = self.scaler.transform(latest_features.values.reshape(1, -1))
            pred_proba = self.model.predict_proba(features_scaled)[0, 1]
            confidence = abs(pred_proba - 0.5) * 2
            
            predictions.append(pred_proba)
            print(f"   Test {i+1}: Prob={pred_proba:.4f}, Conf={confidence:.4f}")
            
            if i < 2:
                import time
                time.sleep(5)
        
        if len(predictions) < 2:
            print("❌ Insufficient live predictions")
            return False
        
        unique_preds = len(set([round(p, 3) for p in predictions]))
        print(f"   Unique predictions (3 decimals): {unique_preds}")
        
        return unique_preds >= 2
    
    def save_model(self):
        """Save ultra-conservative model"""
        os.makedirs('models_ultra_conservative', exist_ok=True)
        
        joblib.dump(self.model, 'models_ultra_conservative/model.pkl')
        joblib.dump(self.scaler, 'models_ultra_conservative/scaler.pkl')
        joblib.dump(self.selected_features, 'models_ultra_conservative/features.pkl')
        
        print("💾 Ultra-conservative model saved!")
    
    def run_ultra_conservative_build(self):
        """Run ultra-conservative model build"""
        print("🚀 ULTRA-CONSERVATIVE MODEL BUILD")
        print("=" * 60)
        print("Maximum regularization, minimal features, strict validation")
        print("=" * 60)
        
        # Get data
        X, y = self.get_data_and_targets()
        if X is None:
            return False
        
        # Train model
        if not self.train_ultra_conservative_model(X, y):
            return False
        
        # Final validation
        if not self.final_validation(X, y):
            return False
        
        # Test live predictions
        if not self.test_live_predictions():
            return False
        
        # Save model
        self.save_model()
        
        print("\n🎉 ULTRA-CONSERVATIVE MODEL READY!")
        print("This model prioritizes reliability over performance")
        
        self.mt5_manager.disconnect()
        return True

def main():
    """Main function"""
    print("🚨 ULTRA-CONSERVATIVE MODEL BUILD")
    print("Extreme regularization to eliminate overfitting")
    print()
    
    model = UltraConservativeModel()
    success = model.run_ultra_conservative_build()
    
    if success:
        print("\n✅ Ultra-conservative model ready for live trading!")
    else:
        print("\n❌ Ultra-conservative build failed")

if __name__ == "__main__":
    main()
