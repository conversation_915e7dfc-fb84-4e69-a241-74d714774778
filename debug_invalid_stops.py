#!/usr/bin/env python3
"""
Debug tool for MT5 "Invalid stops" error (10016)
Helps identify why stop loss 4328.99 is being rejected
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

import MetaTrader5 as mt5
from datetime import datetime

def debug_invalid_stops_error():
    """Debug the specific Invalid stops error with SL 4328.99"""
    
    print("🔍 DEBUGGING MT5 'Invalid stops' Error (10016)")
    print("=" * 60)
    
    # Initialize MT5
    if not mt5.initialize():
        print("❌ Failed to initialize MT5")
        return
    
    try:
        # Get current XAUUSD market info
        symbol = "XAUUSD!"
        
        # Get current tick
        tick = mt5.symbol_info_tick(symbol)
        if not tick:
            print(f"❌ Failed to get tick for {symbol}")
            return
            
        print(f"📊 CURRENT MARKET DATA:")
        print(f"   Symbol: {symbol}")
        print(f"   Bid: {tick.bid:.5f}")
        print(f"   Ask: {tick.ask:.5f}")
        print(f"   Spread: {tick.ask - tick.bid:.5f}")
        print(f"   Time: {datetime.fromtimestamp(tick.time)}")
        
        # Get symbol info for stop level requirements
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info:
            print(f"\n📋 SYMBOL REQUIREMENTS:")
            print(f"   Trade Stops Level: {symbol_info.trade_stops_level} points")
            print(f"   Point Value: {symbol_info.point:.5f}")
            print(f"   Minimum Distance: {symbol_info.trade_stops_level * symbol_info.point:.5f}")
            print(f"   Digits: {symbol_info.digits}")
            print(f"   Tick Size: {symbol_info.trade_tick_size:.5f}")
            print(f"   Tick Value: {symbol_info.trade_tick_value:.5f}")
        
        # Check current positions
        positions = mt5.positions_get(symbol=symbol)
        if positions:
            print(f"\n🎯 CURRENT POSITIONS ({len(positions)}):")
            for i, pos in enumerate(positions):
                print(f"   Position {i+1}:")
                print(f"      Ticket: {pos.ticket}")
                print(f"      Type: {'BUY' if pos.type == 0 else 'SELL'}")
                print(f"      Volume: {pos.volume}")
                print(f"      Entry Price: {pos.price_open:.5f}")
                print(f"      Current SL: {pos.sl:.5f}")
                print(f"      Current TP: {pos.tp:.5f}")
                print(f"      Current Profit: {pos.profit:.2f}")
                
                # Analyze the problematic SL 4328.99
                problematic_sl = 4328.99
                current_price = tick.bid if pos.type == 1 else tick.ask
                
                print(f"\n   🚨 ANALYZING PROBLEMATIC SL: {problematic_sl:.5f}")
                print(f"      Current Market Price: {current_price:.5f}")
                print(f"      Distance to SL: {abs(current_price - problematic_sl):.5f}")
                
                if symbol_info:
                    min_distance = symbol_info.trade_stops_level * symbol_info.point
                    print(f"      Required Min Distance: {min_distance:.5f}")
                    
                    if abs(current_price - problematic_sl) < min_distance:
                        print(f"      ❌ PROBLEM: SL too close to market!")
                        print(f"         Distance: {abs(current_price - problematic_sl):.5f}")
                        print(f"         Required: {min_distance:.5f}")
                        print(f"         Shortfall: {min_distance - abs(current_price - problematic_sl):.5f}")
                    else:
                        print(f"      ✅ Distance OK: {abs(current_price - problematic_sl):.5f} >= {min_distance:.5f}")
                
                # Check if SL is on correct side
                if pos.type == 0:  # BUY position
                    if problematic_sl >= current_price:
                        print(f"      ❌ PROBLEM: BUY position SL {problematic_sl:.5f} >= current price {current_price:.5f}")
                        print(f"         BUY stop loss must be BELOW current price")
                    else:
                        print(f"      ✅ SL Side OK: BUY SL {problematic_sl:.5f} < current price {current_price:.5f}")
                else:  # SELL position
                    if problematic_sl <= current_price:
                        print(f"      ❌ PROBLEM: SELL position SL {problematic_sl:.5f} <= current price {current_price:.5f}")
                        print(f"         SELL stop loss must be ABOVE current price")
                    else:
                        print(f"      ✅ SL Side OK: SELL SL {problematic_sl:.5f} > current price {current_price:.5f}")
                
                # Test the actual modification with our MT5Manager
                print(f"\n   🧪 TESTING WITH FIXED MT5 INTEGRATION:")
                try:
                    from mt5_integration import MT5Manager
                    mt5_manager = MT5Manager()

                    # Test the validation (this should now catch the error)
                    print(f"      Testing modify_position with SL {problematic_sl:.5f}...")
                    success = mt5_manager.modify_position(
                        ticket=pos.ticket,
                        stop_loss=problematic_sl
                    )

                    if success:
                        print(f"      ✅ Modification succeeded (unexpected)")
                    else:
                        print(f"      ❌ Modification blocked by validation (expected)")

                except Exception as e:
                    print(f"      ⚠️ Error testing modification: {e}")
                    print(f"      This is expected if validation is working")
                
        else:
            print(f"\n📭 No current positions found for {symbol}")
            
        # Additional checks
        print(f"\n🔍 ADDITIONAL DIAGNOSTICS:")
        
        # Check account info
        account_info = mt5.account_info()
        if account_info:
            print(f"   Account: {account_info.login}")
            print(f"   Server: {account_info.server}")
            print(f"   Balance: {account_info.balance:.2f}")
            print(f"   Equity: {account_info.equity:.2f}")
            print(f"   Margin: {account_info.margin:.2f}")
            print(f"   Free Margin: {account_info.margin_free:.2f}")
            print(f"   Margin Level: {account_info.margin_level:.2f}%")
        
        # Check terminal info
        terminal_info = mt5.terminal_info()
        if terminal_info:
            print(f"   Terminal Connected: {terminal_info.connected}")
            print(f"   Trade Allowed: {terminal_info.trade_allowed}")
            print(f"   Expert Advisors: {terminal_info.tradeapi_disabled}")
            
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        mt5.shutdown()

def suggest_fixes():
    """Suggest potential fixes for the Invalid stops error"""
    
    print(f"\n💡 POTENTIAL FIXES FOR 'Invalid stops' ERROR:")
    print("=" * 50)
    
    fixes = [
        {
            'issue': 'Stop Loss Too Close to Market',
            'fix': 'Increase minimum distance validation',
            'code': '''
# In modify_position function, add stricter validation:
min_distance = stops_level * point * 1.2  # Add 20% buffer
if sl_distance < min_distance:
    logger.error(f"SL too close: {sl_distance:.5f} < {min_distance:.5f}")
    return False
'''
        },
        {
            'issue': 'Stop Loss on Wrong Side',
            'fix': 'Add side validation before sending request',
            'code': '''
# Validate SL is on correct side:
if position.type == 0 and stop_loss >= current_price:  # BUY
    logger.error("BUY SL must be below current price")
    return False
elif position.type == 1 and stop_loss <= current_price:  # SELL
    logger.error("SELL SL must be above current price") 
    return False
'''
        },
        {
            'issue': 'Precision Issues',
            'fix': 'Round SL to symbol digits',
            'code': '''
# Round to symbol-specific precision:
if symbol_info:
    digits = symbol_info.digits
    rounded_sl = round(float(stop_loss), digits)
    request["sl"] = rounded_sl
'''
        },
        {
            'issue': 'Market Closed/Weekend',
            'fix': 'Check market hours before modification',
            'code': '''
# Check if market is open:
if not symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_FULL:
    logger.error("Market not available for trading")
    return False
'''
        }
    ]
    
    for i, fix in enumerate(fixes, 1):
        print(f"{i}. {fix['issue']}:")
        print(f"   Solution: {fix['fix']}")
        print(f"   Code:")
        for line in fix['code'].strip().split('\n'):
            print(f"     {line}")
        print()

if __name__ == "__main__":
    debug_invalid_stops_error()
    suggest_fixes()
