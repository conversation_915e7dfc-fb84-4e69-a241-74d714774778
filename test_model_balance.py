#!/usr/bin/env python3
"""
Test script to check if model can generate SELL signals by manipulating features
"""

import sys
import pandas as pd
import numpy as np
import joblib
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager
from fixed_live_trader import FixedFeatureEngineer, RegimeDetector

def test_model_balance():
    """Test if model can generate SELL signals with different feature values"""
    print("🧪 TESTING MODEL BALANCE...")
    
    # Load model components
    try:
        model = joblib.load('models_fixed/xgboost_model.pkl')
        scaler = joblib.load('models_fixed/feature_scaler.pkl')
        selected_features = joblib.load('models_fixed/selected_features.pkl')
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return
    
    # Initialize components
    mt5_manager = MT5Manager()
    feature_engineer = FixedFeatureEngineer()
    regime_detector = RegimeDetector()
    
    # Connect to MT5
    if not mt5_manager.connect():
        print("❌ Cannot connect to MT5")
        return
    
    try:
        df = mt5_manager.get_latest_data("XAUUSD!", "M5", 200)
        if df is None or len(df) < 100:
            print("❌ Cannot get sufficient data")
            return
        
        # Create features
        features_df = feature_engineer.create_technical_indicators(df)
        features_df = regime_detector.calculate_regime_indicators(features_df)
        
        # Get base features
        base_features = features_df[selected_features].iloc[-1]
        base_features = base_features.fillna(base_features.median())
        
        # Apply price normalization
        current_price = features_df['close'].iloc[-1]
        price_features = [
            'lowest_low_20', 'lowest_low_10', 'highest_high_10', 'highest_high_5', 
            'lowest_low_5', 'sma_50', 'highest_high_20', 'bb_upper', 'bb_lower',
            'sma_10', 'sma_5', 'sma_20', 'bb_middle'
        ]
        
        for feature in price_features:
            if feature in base_features.index:
                base_features[feature] = base_features[feature] / current_price
        
        print(f"🎯 TESTING DIFFERENT FEATURE SCENARIOS:")
        print("=" * 80)
        
        # Test 1: Original features
        features_scaled = scaler.transform(base_features.values.reshape(1, -1))
        prob = model.predict_proba(features_scaled)[0, 1]
        signal = "BUY" if prob > 0.5 else "SELL"
        print(f"Original features:     Prob={prob:.3f} | Signal={signal}")
        
        # Test 2: Flip MACD signal (make it more negative)
        test_features = base_features.copy()
        test_features['macd_signal'] = -abs(test_features['macd_signal']) * 3  # More bearish
        features_scaled = scaler.transform(test_features.values.reshape(1, -1))
        prob = model.predict_proba(features_scaled)[0, 1]
        signal = "BUY" if prob > 0.5 else "SELL"
        print(f"Bearish MACD (x3):     Prob={prob:.3f} | Signal={signal}")
        
        # Test 3: Negative momentum
        test_features = base_features.copy()
        test_features['momentum_3'] = -0.01  # Strong negative momentum
        features_scaled = scaler.transform(test_features.values.reshape(1, -1))
        prob = model.predict_proba(features_scaled)[0, 1]
        signal = "BUY" if prob > 0.5 else "SELL"
        print(f"Negative momentum:     Prob={prob:.3f} | Signal={signal}")
        
        # Test 4: Lower price relative to MAs
        test_features = base_features.copy()
        # Make price appear lower relative to moving averages
        for feature in ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'bb_middle', 'bb_upper']:
            if feature in test_features.index:
                test_features[feature] = test_features[feature] * 1.01  # MAs 1% higher than price
        features_scaled = scaler.transform(test_features.values.reshape(1, -1))
        prob = model.predict_proba(features_scaled)[0, 1]
        signal = "BUY" if prob > 0.5 else "SELL"
        print(f"Price below MAs:       Prob={prob:.3f} | Signal={signal}")
        
        # Test 5: Price near lows
        test_features = base_features.copy()
        # Make recent lows higher (price near bottom of range)
        for feature in ['lowest_low_5', 'lowest_low_10', 'lowest_low_20']:
            if feature in test_features.index:
                test_features[feature] = test_features[feature] * 1.005  # Lows closer to current price
        features_scaled = scaler.transform(test_features.values.reshape(1, -1))
        prob = model.predict_proba(features_scaled)[0, 1]
        signal = "BUY" if prob > 0.5 else "SELL"
        print(f"Price near lows:       Prob={prob:.3f} | Signal={signal}")
        
        # Test 6: Extreme bearish scenario
        test_features = base_features.copy()
        test_features['macd_signal'] = -2.0  # Very bearish MACD
        test_features['momentum_3'] = -0.02  # Strong negative momentum
        # Price well below all MAs
        for feature in ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'bb_middle', 'bb_upper']:
            if feature in test_features.index:
                test_features[feature] = test_features[feature] * 1.02  # MAs 2% higher
        # Price near lows
        for feature in ['lowest_low_5', 'lowest_low_10', 'lowest_low_20']:
            if feature in test_features.index:
                test_features[feature] = test_features[feature] * 1.01
        features_scaled = scaler.transform(test_features.values.reshape(1, -1))
        prob = model.predict_proba(features_scaled)[0, 1]
        signal = "BUY" if prob > 0.5 else "SELL"
        print(f"EXTREME BEARISH:       Prob={prob:.3f} | Signal={signal}")
        
        # Test 7: Try to find the threshold
        print(f"\n🔍 SEARCHING FOR SELL THRESHOLD:")
        print("=" * 80)
        
        multipliers = [1.5, 2.0, 3.0, 5.0, 10.0]
        for mult in multipliers:
            test_features = base_features.copy()
            test_features['macd_signal'] = -abs(test_features['macd_signal']) * mult
            test_features['momentum_3'] = -0.005 * mult
            
            # Increase MA distance
            for feature in ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'bb_middle', 'bb_upper']:
                if feature in test_features.index:
                    test_features[feature] = test_features[feature] * (1 + 0.005 * mult)
            
            features_scaled = scaler.transform(test_features.values.reshape(1, -1))
            prob = model.predict_proba(features_scaled)[0, 1]
            signal = "BUY" if prob > 0.5 else "SELL"
            print(f"Multiplier {mult:4.1f}x:       Prob={prob:.3f} | Signal={signal}")
            
            if prob < 0.5:
                print(f"🎉 FOUND SELL SIGNAL at multiplier {mult}x!")
                break
        
        print(f"\n📊 FEATURE IMPORTANCE TEST:")
        print("=" * 80)
        
        # Test each feature individually by making it extreme
        for feature in selected_features:
            test_features = base_features.copy()
            
            if feature in ['macd_signal', 'momentum_3']:
                # Make negative indicators more negative
                test_features[feature] = -abs(test_features[feature]) * 10
            elif feature in price_features:
                # For price features, try making them higher (bearish if price below)
                test_features[feature] = test_features[feature] * 1.05
            
            features_scaled = scaler.transform(test_features.values.reshape(1, -1))
            prob = model.predict_proba(features_scaled)[0, 1]
            signal = "BUY" if prob > 0.5 else "SELL"
            
            prob_change = prob - model.predict_proba(scaler.transform(base_features.values.reshape(1, -1)))[0, 1]
            print(f"{feature:20s}: Prob={prob:.3f} | Change={prob_change:+.3f} | Signal={signal}")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        mt5_manager.disconnect()

if __name__ == "__main__":
    test_model_balance()
