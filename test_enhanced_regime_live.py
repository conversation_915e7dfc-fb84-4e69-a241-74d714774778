#!/usr/bin/env python3
"""
Test Enhanced Regime Detection with Live MT5 Data
Demonstrates how to use the enhanced regime detector with real market data
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# Add src to path
sys.path.append('src')
from mt5_integration import MT5Manager
from enhanced_regime_detector import EnhancedRegimeDetector

def test_with_live_data():
    """Test enhanced regime detector with live MT5 data"""
    print("🚀 ENHANCED REGIME DETECTOR - LIVE DATA TEST")
    print("=" * 70)
    
    # Initialize MT5 connection
    mt5_manager = MT5Manager()
    if not mt5_manager.connect():
        print("❌ Failed to connect to MT5")
        return False
    
    # Initialize enhanced regime detector
    detector = EnhancedRegimeDetector("XAUUSD!", "M5")
    
    try:
        # Get recent data
        print("📊 Fetching recent XAUUSD M5 data...")
        df = mt5_manager.get_latest_data("XAUUSD!", "M5", 200)
        
        if df is None or len(df) < 100:
            print("❌ Insufficient data received")
            return False
        
        print(f"✅ Received {len(df)} candles")
        print(f"📈 Data Range: {df.index[0]} to {df.index[-1]}")
        print(f"💰 Price Range: {df['low'].min():.2f} - {df['high'].max():.2f}")
        
        # Run regime detection
        print("\n🔍 RUNNING ENHANCED REGIME ANALYSIS...")
        print("-" * 50)
        
        regime, confidence, details = detector.detect_regime(df)
        
        # Display results
        print(f"\n🎯 CURRENT MARKET REGIME:")
        print("=" * 40)
        print(f"📊 Regime: {regime}")
        print(f"🎯 Confidence: {confidence:.2f}%")
        print(f"📈 Trending Score: {details['trending_score']}/98 ({details['trending_score']/98*100:.1f}%)")
        print(f"📉 Ranging Score: {details['ranging_score']}/98 ({details['ranging_score']/98*100:.1f}%)")
        
        print(f"\n📊 DETAILED TIER BREAKDOWN:")
        print("-" * 40)
        print(f"🔥 TIER 1 (Price Action - 60 pts):")
        print(f"   Trending: {details['tier1_scores']['trending']}/60")
        print(f"   Ranging:  {details['tier1_scores']['ranging']}/60")
        
        print(f"\n✅ TIER 2 (Confirmation - 30 pts):")
        print(f"   Trending: {details['tier2_scores']['trending']}/30")
        print(f"   Ranging:  {details['tier2_scores']['ranging']}/30")
        
        print(f"\n🎛️ TIER 3 (Context - 8 pts):")
        print(f"   Trending: {details['tier3_scores']['trending']}/8")
        print(f"   Ranging:  {details['tier3_scores']['ranging']}/8")
        
        # Current market context
        current_price = df['close'].iloc[-1]
        atr_current = df['atr'].iloc[-1] if 'atr' in df.columns else 0
        
        print(f"\n💹 CURRENT MARKET CONTEXT:")
        print("-" * 40)
        print(f"💰 Current Price: {current_price:.2f}")
        print(f"📊 Current ATR: {atr_current:.2f}")
        print(f"⏰ Last Update: {df.index[-1]}")
        
        # Trading recommendations based on regime
        print(f"\n🎯 TRADING IMPLICATIONS:")
        print("-" * 40)
        
        if regime == "STRONG_TRENDING":
            print("🔥 STRONG TRENDING MARKET")
            print("   • Follow trend direction")
            print("   • Use momentum strategies")
            print("   • Wider stop losses")
            print("   • Trail stops aggressively")
            
        elif regime == "STRONG_RANGING":
            print("📊 STRONG RANGING MARKET")
            print("   • Trade range boundaries")
            print("   • Use mean reversion strategies")
            print("   • Tighter stop losses")
            print("   • Take profits at resistance/support")
            
        elif regime == "TRENDING":
            print("📈 TRENDING MARKET (Moderate)")
            print("   • Cautious trend following")
            print("   • Wait for pullbacks")
            print("   • Moderate position sizes")
            
        elif regime == "RANGING":
            print("📉 RANGING MARKET (Moderate)")
            print("   • Range trading opportunities")
            print("   • Counter-trend strategies")
            print("   • Quick profit taking")
            
        else:  # TRANSITIONAL
            print("⚠️ TRANSITIONAL MARKET")
            print("   • AVOID TRADING")
            print("   • Wait for clear regime")
            print("   • Conflicting signals present")
            print("   • High risk environment")
        
        # Risk assessment
        risk_level = "HIGH" if regime == "TRANSITIONAL" else "MEDIUM" if confidence < 70 else "LOW"
        print(f"\n⚠️ RISK ASSESSMENT: {risk_level}")
        
        if confidence < 60:
            print("   • Low confidence - reduce position sizes")
        elif confidence > 80:
            print("   • High confidence - normal position sizes")
        
        print(f"\n✅ ANALYSIS COMPLETED SUCCESSFULLY!")
        print(f"🕐 Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        return False
    
    finally:
        mt5_manager.disconnect()

def run_continuous_monitoring():
    """Run continuous regime monitoring (demo)"""
    print("\n🔄 CONTINUOUS MONITORING MODE")
    print("=" * 50)
    print("This would run continuous monitoring every 5 minutes...")
    print("(Implementation would go here for live trading)")
    
    # Demo of how continuous monitoring would work
    detector = EnhancedRegimeDetector("XAUUSD!", "M5")
    
    print("\n📋 MONITORING FEATURES:")
    print("• Real-time regime detection")
    print("• Economic calendar integration")
    print("• Timezone-aware processing")
    print("• Persistence tracking")
    print("• Multi-timeframe analysis")
    print("• Session-based filtering")

def main():
    """Main function"""
    try:
        # Test with live data
        success = test_with_live_data()
        
        if success:
            # Show continuous monitoring demo
            run_continuous_monitoring()
        
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
