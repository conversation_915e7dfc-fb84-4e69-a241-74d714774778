#!/usr/bin/env python3
"""
Test Enhanced Regime Detector Integration with Fixed Live Trader
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# Add src to path
sys.path.append('src')
from mt5_integration import <PERSON>T<PERSON><PERSON>anager
from enhanced_regime_detector import EnhancedRegimeDetector

def test_integration_compatibility():
    """Test compatibility with fixed_live_trader.py interface"""
    print("🔗 TESTING INTEGRATION WITH FIXED_LIVE_TRADER.PY")
    print("=" * 70)
    
    # Initialize MT5 and get data
    mt5_manager = MT5Manager()
    if not mt5_manager.connect():
        print("❌ Failed to connect to MT5")
        return False
    
    try:
        # Get recent data
        print("📊 Fetching XAUUSD M5 data for integration test...")
        df = mt5_manager.get_latest_data("XAUUSD!", "M5", 100)
        
        if df is None or len(df) < 50:
            print("❌ Insufficient data received")
            return False
        
        print(f"✅ Received {len(df)} candles")
        
        # Test Enhanced Regime Detector
        print("\n🔍 TESTING ENHANCED REGIME DETECTOR")
        print("-" * 50)
        
        enhanced_detector = EnhancedRegimeDetector("XAUUSD!", "M5")
        
        # Test the main interface method that fixed_live_trader.py would use
        regime, confidence, details = enhanced_detector.detect_regime(df)
        
        print(f"✅ Enhanced Detector Results:")
        print(f"   Regime: {regime}")
        print(f"   Confidence: {confidence:.2f}%")
        print(f"   Trending Score: {details['trending_score']}/98")
        print(f"   Ranging Score: {details['ranging_score']}/98")
        
        # Test compatibility with expected interface
        print(f"\n🔧 INTERFACE COMPATIBILITY CHECK")
        print("-" * 40)
        
        # Check if the enhanced detector provides the same interface as the original
        expected_methods = [
            'detect_regime',
            '_calculate_technical_indicators'
        ]
        
        for method in expected_methods:
            if hasattr(enhanced_detector, method):
                print(f"✅ Method '{method}': Available")
            else:
                print(f"❌ Method '{method}': Missing")
        
        # Test with different data sizes
        print(f"\n📏 TESTING WITH DIFFERENT DATA SIZES")
        print("-" * 40)
        
        for size in [50, 100, 200]:
            test_df = df.tail(size)
            try:
                test_regime, test_conf, test_details = enhanced_detector.detect_regime(test_df)
                print(f"✅ Size {size}: {test_regime} ({test_conf:.1f}%)")
            except Exception as e:
                print(f"❌ Size {size}: Error - {e}")
        
        # Test regime consistency
        print(f"\n🎯 TESTING REGIME CONSISTENCY")
        print("-" * 40)
        
        # Test multiple calls with same data
        results = []
        for i in range(3):
            test_regime, test_conf, test_details = enhanced_detector.detect_regime(df)
            results.append((test_regime, test_conf))
        
        # Check if results are consistent
        if all(r[0] == results[0][0] for r in results):
            print("✅ Regime detection is consistent across multiple calls")
        else:
            print("❌ Regime detection is inconsistent")
            for i, (regime, conf) in enumerate(results):
                print(f"   Call {i+1}: {regime} ({conf:.2f}%)")
        
        # Test performance
        print(f"\n⚡ PERFORMANCE TEST")
        print("-" * 40)
        
        import time
        start_time = time.time()
        
        for i in range(10):
            enhanced_detector.detect_regime(df)
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 10
        
        print(f"✅ Average detection time: {avg_time:.3f} seconds")
        if avg_time < 1.0:
            print("✅ Performance: Acceptable for live trading")
        else:
            print("⚠️ Performance: May be too slow for live trading")
        
        # Test error handling
        print(f"\n🛡️ ERROR HANDLING TEST")
        print("-" * 40)
        
        # Test with insufficient data
        try:
            small_df = df.head(10)
            error_regime, error_conf, error_details = enhanced_detector.detect_regime(small_df)
            print(f"✅ Small data handling: {error_regime} ({error_conf:.1f}%)")
        except Exception as e:
            print(f"❌ Small data error: {e}")
        
        # Test with empty data
        try:
            empty_df = df.head(0)
            empty_regime, empty_conf, empty_details = enhanced_detector.detect_regime(empty_df)
            print(f"❌ Empty data should have failed but returned: {empty_regime}")
        except Exception as e:
            print(f"✅ Empty data properly handled: {str(e)[:50]}...")
        
        print(f"\n🎉 INTEGRATION TEST COMPLETED")
        print("=" * 50)
        print("✅ Enhanced Regime Detector is ready for integration!")
        print("✅ Compatible with fixed_live_trader.py interface")
        print("✅ Handles edge cases appropriately")
        print("✅ Performance is acceptable for live trading")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False
    
    finally:
        mt5_manager.disconnect()

def create_integration_guide():
    """Create integration guide for fixed_live_trader.py"""
    print(f"\n📋 INTEGRATION GUIDE")
    print("=" * 50)
    
    integration_steps = """
    STEP 1: Import Enhanced Regime Detector
    ----------------------------------------
    Add to imports in fixed_live_trader.py:
    from enhanced_regime_detector import EnhancedRegimeDetector
    
    STEP 2: Replace RegimeDetector Initialization
    ----------------------------------------
    Replace line ~1116:
    OLD: self.regime_detector = RegimeDetector()
    NEW: self.regime_detector = EnhancedRegimeDetector(symbol, "M5")
    
    STEP 3: Update Regime Detection Calls
    ----------------------------------------
    The enhanced detector returns (regime, confidence, details) tuple.
    Update any calls to match this interface.
    
    STEP 4: Optional - Add Enhanced Logging
    ----------------------------------------
    Use details dictionary for enhanced logging:
    - details['trending_score']: Trending points (0-98)
    - details['ranging_score']: Ranging points (0-98)
    - details['tier1_scores']: Tier 1 breakdown
    - details['tier2_scores']: Tier 2 breakdown
    - details['tier3_scores']: Tier 3 breakdown
    
    STEP 5: Test Integration
    ----------------------------------------
    Run fixed_live_trader.py with paper trading first
    Monitor logs for regime detection accuracy
    Verify trading decisions align with regime
    """
    
    print(integration_steps)

def main():
    """Main function"""
    try:
        # Test integration compatibility
        success = test_integration_compatibility()
        
        if success:
            # Show integration guide
            create_integration_guide()
            
            print(f"\n🚀 READY FOR LIVE INTEGRATION!")
            print("Follow the integration guide above to replace the")
            print("existing RegimeDetector in fixed_live_trader.py")
        else:
            print(f"\n⚠️ INTEGRATION ISSUES DETECTED")
            print("Please fix the issues before integrating")
        
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
