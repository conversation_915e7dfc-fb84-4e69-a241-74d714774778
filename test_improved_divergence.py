#!/usr/bin/env python3
"""
Test Improved Divergence Detection with Less Strict Filtering
"""

import sys
import pandas as pd
import numpy as np
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_clear_bullish_div_scenario():
    """Create a very clear BULLISH_DIV scenario"""
    logger.info("📊 Creating CLEAR BULLISH_DIV scenario...")
    
    data = []
    
    # Create 20 periods for better lookback
    for i in range(20):
        if i < 8:
            # First 8 periods: High prices, Low volume
            price = 2020 - i * 0.5  # Prices declining significantly
            volume = 800 + i * 10   # Volume very low
        elif i < 12:
            # Middle periods: Continuing decline, still low volume
            price = 2016 - (i-8) * 0.3  # Continue declining
            volume = 880 + (i-8) * 5   # Volume still low
        else:
            # Last 8 periods: Lower prices, MUCH higher volume (BULLISH DIVERGENCE)
            price = 2014.8 - (i-12) * 0.2  # Prices continue down slightly
            volume = 900 + (i-12) * 300     # Volume EXPLODING upward
        
        data.append({
            'datetime': pd.Timestamp('2024-01-01') + pd.Timedelta(minutes=5*i),
            'open': price + 0.1,
            'high': price + 0.2,
            'low': price - 0.2,
            'close': price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('datetime', inplace=True)
    
    logger.info(f"✅ Created {len(df)} periods")
    logger.info(f"   Price: {df.iloc[0]['close']:.2f} → {df.iloc[-1]['close']:.2f} (DOWN {((df.iloc[-1]['close']/df.iloc[0]['close']-1)*100):+.2f}%)")
    logger.info(f"   Volume: {df.iloc[0]['volume']:.0f} → {df.iloc[-1]['volume']:.0f} (UP {((df.iloc[-1]['volume']/df.iloc[0]['volume']-1)*100):+.2f}%)")
    logger.info(f"   Expected: BULLISH_DIV (price down, volume up)")
    
    return df

def test_improved_divergence():
    """Test the improved divergence detection"""
    logger.info("\n🔧 TESTING IMPROVED DIVERGENCE DETECTION...")
    
    try:
        from qqe_indicator import QQEIndicator
        
        # Test with improved settings
        logger.info("   📊 Testing with improved settings:")
        logger.info("      - Lookback period: 8 (reduced from 10)")
        logger.info("      - Strength threshold: 0.02 (new)")
        
        # Create QQE indicator with improved settings
        qqe = QQEIndicator(
            volume_divergence_lookback=8,  # Reduced lookback
            divergence_strength_threshold=0.02  # Strength threshold
        )
        
        # Test BULLISH_DIV scenario
        df = create_clear_bullish_div_scenario()
        df_with_qqe = qqe.calculate_qqe_bands(df)
        
        # Analyze results
        last_row = df_with_qqe.iloc[-1]
        divergence_type = last_row.get('divergence_type', 'NONE')
        price_direction = last_row.get('price_direction', 0)
        volume_direction = last_row.get('volume_direction', 0)
        divergence_strength = last_row.get('divergence_strength', 0)
        
        logger.info(f"\n   📊 RESULTS:")
        logger.info(f"      Divergence Type: {divergence_type}")
        logger.info(f"      Price Direction: {int(price_direction):+d}")
        logger.info(f"      Volume Direction: {int(volume_direction):+d}")
        logger.info(f"      Divergence Strength: {divergence_strength:.4f}")
        
        # Manual verification
        logger.info(f"\n   🔍 MANUAL VERIFICATION:")
        
        # Check volume SMA calculation
        volume_sma_current = df.iloc[-8:]['volume'].mean()  # Last 8 periods
        volume_sma_lookback = df.iloc[-16:-8]['volume'].mean()  # 8 periods before that
        
        logger.info(f"      Current volume SMA (last 8): {volume_sma_current:.1f}")
        logger.info(f"      Previous volume SMA (8 periods ago): {volume_sma_lookback:.1f}")
        logger.info(f"      Volume direction should be: {'UP' if volume_sma_current > volume_sma_lookback else 'DOWN'}")
        
        # Check price
        current_close = df.iloc[-1]['close']
        lookback_close = df.iloc[-9]['close']  # 8 periods ago
        logger.info(f"      Current close: {current_close:.2f}")
        logger.info(f"      Close 8 periods ago: {lookback_close:.2f}")
        logger.info(f"      Price direction should be: {'UP' if current_close > lookback_close else 'DOWN'}")
        
        # Verify BULLISH_DIV
        expected_bullish = (current_close < lookback_close) and (volume_sma_current > volume_sma_lookback)
        logger.info(f"      Expected BULLISH_DIV: {expected_bullish}")
        
        success = divergence_type == 'BULLISH_DIV'
        if success:
            logger.info(f"      ✅ BULLISH_DIV correctly detected!")
        else:
            logger.error(f"      ❌ Expected BULLISH_DIV, got {divergence_type}")
        
        return success, divergence_strength
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False, 0

def test_filtering_strictness():
    """Test the new filtering strictness"""
    logger.info("\n🔧 TESTING FILTERING STRICTNESS...")
    
    try:
        from qqe_indicator import QQEIndicator
        
        # Test scenarios with different strengths
        scenarios = [
            ("WEAK", 0.01, 0.005),    # Very weak divergence
            ("MODERATE", 0.05, 0.03), # Moderate divergence  
            ("STRONG", 0.15, 0.10),   # Strong divergence
        ]
        
        results = {}
        
        for scenario_name, price_change, volume_change in scenarios:
            logger.info(f"\n   📊 Testing {scenario_name} divergence:")
            logger.info(f"      Price change: {price_change*100:+.1f}%")
            logger.info(f"      Volume change: {volume_change*100:+.1f}%")
            
            # Create test data
            data = []
            for i in range(15):
                if i < 8:
                    price = 2000 + i * (price_change/8)
                    volume = 1000 - i * (volume_change*1000/8)
                else:
                    price = 2000 + price_change + (i-8) * (price_change/7)
                    volume = 1000 - volume_change*1000 + (i-8) * (volume_change*1000/7)
                
                data.append({
                    'datetime': pd.Timestamp('2024-01-01') + pd.Timedelta(minutes=5*i),
                    'open': price,
                    'high': price + 0.02,
                    'low': price - 0.02,
                    'close': price,
                    'volume': volume
                })
            
            df = pd.DataFrame(data)
            df.set_index('datetime', inplace=True)
            
            # Test with QQE
            qqe = QQEIndicator(
                volume_divergence_lookback=8,
                divergence_strength_threshold=0.02
            )
            
            df_with_qqe = qqe.calculate_qqe_bands(df)
            last_row = df_with_qqe.iloc[-1]
            
            divergence_type = last_row.get('divergence_type', 'NONE')
            divergence_strength = last_row.get('divergence_strength', 0)
            
            logger.info(f"      Result: {divergence_type}")
            logger.info(f"      Strength: {divergence_strength:.4f}")
            logger.info(f"      Above threshold (0.02): {'Yes' if divergence_strength >= 0.02 else 'No'}")
            
            results[scenario_name] = {
                'type': divergence_type,
                'strength': divergence_strength,
                'filtered': divergence_strength >= 0.02
            }
        
        # Analyze results
        logger.info(f"\n   📊 FILTERING ANALYSIS:")
        weak_filtered = results['WEAK']['filtered']
        strong_filtered = results['STRONG']['filtered']
        
        logger.info(f"      WEAK divergence filtered: {'Yes' if weak_filtered else 'No'} ✅")
        logger.info(f"      STRONG divergence filtered: {'Yes' if strong_filtered else 'No'} ✅")
        
        if not weak_filtered and strong_filtered:
            logger.info(f"      ✅ Filtering working correctly - ignores weak, catches strong")
            return True
        else:
            logger.error(f"      ❌ Filtering issue - check threshold logic")
            return False
        
    except Exception as e:
        logger.error(f"❌ Filtering test failed: {e}")
        return False

def main():
    """Run improved divergence tests"""
    logger.info("🧪 TESTING IMPROVED DIVERGENCE DETECTION")
    logger.info("=" * 70)
    
    # Test BULLISH_DIV detection
    bullish_success, strength = test_improved_divergence()
    
    # Test filtering strictness
    filtering_success = test_filtering_strictness()
    
    logger.info("\n" + "=" * 70)
    logger.info("🏁 IMPROVEMENT SUMMARY")
    
    logger.info(f"\n   ✅ IMPROVEMENTS MADE:")
    logger.info(f"      1. Reduced lookback period: 10 → 8 periods")
    logger.info(f"      2. Added strength threshold: 0.02 minimum")
    logger.info(f"      3. Only strong divergences trigger filtering")
    
    logger.info(f"\n   📊 TEST RESULTS:")
    logger.info(f"      BULLISH_DIV Detection: {'✅ Working' if bullish_success else '❌ Issue'}")
    logger.info(f"      Filtering Strictness: {'✅ Improved' if filtering_success else '❌ Issue'}")
    
    if bullish_success and filtering_success:
        logger.info(f"\n   🎯 EXPECTED BENEFITS:")
        logger.info(f"      - You should now see BULLISH_DIV in appropriate market conditions")
        logger.info(f"      - Weak divergences won't block trades anymore")
        logger.info(f"      - Only significant divergences will trigger filtering")
        logger.info(f"      - More responsive to recent market changes")
        
        return True
    else:
        logger.error(f"\n   ❌ Some issues remain - check implementation")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
