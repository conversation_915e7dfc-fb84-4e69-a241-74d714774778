#!/usr/bin/env python3
"""
Test System Startup
Verify the fixed_live_trader.py can start up correctly with volume integration
"""

import os
import sys
import logging
import warnings
warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_system_startup():
    """Test that the system can start up correctly"""
    logger.info("🚀 TESTING SYSTEM STARTUP...")
    
    try:
        # Import the main trading system
        from fixed_live_trader import FixedLiveTrader
        
        logger.info("   ✅ Successfully imported FixedLiveTrader")
        
        # Test initialization for different symbols
        symbols = ["XAUUSD!", "EURUSD!", "BTCUSD"]
        
        for symbol in symbols:
            logger.info(f"   🔄 Testing {symbol} initialization...")
            
            try:
                trader = FixedLiveTrader(symbol=symbol)
                logger.info(f"   ✅ {symbol}: Initialized successfully")
                
                # Check QQE indicator has volume parameters
                qqe = trader.qqe_indicator
                if hasattr(qqe, 'volume_lookback') and hasattr(qqe, 'volume_divergence_lookback'):
                    logger.info(f"      📊 Volume parameters: lookback={qqe.volume_lookback}, divergence={qqe.volume_divergence_lookback}")
                else:
                    logger.error(f"      ❌ Missing volume parameters!")
                    return False
                
                # Test volume confirmation method
                test_confirmation = qqe._calculate_volume_confirmation(1.5, 'HIGH')
                logger.info(f"      🔧 Volume confirmation test: {test_confirmation:.2f}x")
                
            except Exception as e:
                logger.error(f"   ❌ {symbol}: Failed to initialize - {e}")
                return False
        
        logger.info("✅ System Startup Test: PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ System Startup Test: FAILED - {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

def test_component_integration():
    """Test that all components integrate correctly"""
    logger.info("\n🔧 TESTING COMPONENT INTEGRATION...")
    
    try:
        # Test individual components
        from qqe_indicator import QQEIndicator
        from fixed_live_trader import FixedFeatureEngineer, RegimeDetector
        
        logger.info("   ✅ All components imported successfully")
        
        # Test QQE with volume parameters
        qqe = QQEIndicator(volume_lookback=20, volume_divergence_lookback=10)
        logger.info("   ✅ QQE indicator with volume parameters created")
        
        # Test feature engineer
        feature_engineer = FixedFeatureEngineer()
        logger.info("   ✅ Feature engineer created")
        
        # Test regime detector
        regime_detector = RegimeDetector()
        logger.info("   ✅ Regime detector created")
        
        # Test that regime detector can accept simple regression
        from simple_regression_channel import SimpleRegressionChannel
        simple_regression = SimpleRegressionChannel()
        regime_detector.set_simple_regression(simple_regression)
        logger.info("   ✅ Simple regression channel integration working")
        
        logger.info("✅ Component Integration Test: PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Component Integration Test: FAILED - {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run startup tests"""
    logger.info("🧪 STARTING SYSTEM STARTUP TESTS")
    logger.info("=" * 60)
    
    tests = [
        ("System Startup", test_system_startup),
        ("Component Integration", test_component_integration)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            logger.error(f"❌ {test_name}: FAILED with exception - {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info("🏁 STARTUP TEST SUMMARY")
    logger.info(f"   ✅ Passed: {passed}")
    logger.info(f"   ❌ Failed: {failed}")
    logger.info(f"   📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        logger.info("🎉 ALL STARTUP TESTS PASSED! System is ready for trading.")
        return True
    else:
        logger.error(f"⚠️ {failed} tests failed. System may have issues.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
