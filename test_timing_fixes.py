#!/usr/bin/env python3
"""
Test script to verify timing fixes
"""

import sys
import time
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_timing_precision():
    """Test the timing precision of candle close detection"""
    print("🧪 TIMING PRECISION TEST")
    print("=" * 50)
    
    trader = FixedLiveTrader("XAUUSD!")
    
    print("🕐 Testing candle close timing logic...")
    
    # Test multiple scenarios
    test_times = [
        datetime(2025, 10, 15, 14, 23, 45),  # 14:23:45 - should wait for 14:25:00
        datetime(2025, 10, 15, 14, 25, 0),   # 14:25:00 - exact boundary
        datetime(2025, 10, 15, 14, 25, 5),   # 14:25:05 - just after boundary
        datetime(2025, 10, 15, 14, 29, 58),  # 14:29:58 - should wait for 14:30:00
        datetime(2025, 10, 15, 14, 59, 30),  # 14:59:30 - should wait for 15:00:00
    ]
    
    for test_time in test_times:
        print(f"\n📊 Test Time: {test_time.strftime('%H:%M:%S')}")
        
        # Mock the current time
        original_datetime = datetime
        
        class MockDateTime(datetime):
            @classmethod
            def now(cls):
                return test_time
        
        # Temporarily replace datetime.now
        import builtins
        builtins.datetime = MockDateTime
        
        try:
            wait_seconds, next_candle_close = trader.wait_for_candle_close()
            
            print(f"   Next candle close: {next_candle_close.strftime('%H:%M:%S')}")
            print(f"   Wait time: {wait_seconds:.1f} seconds")
            
            # Verify the next candle close is at a 5-minute boundary
            if next_candle_close.minute % 5 == 0 and next_candle_close.second == 0:
                print("   ✅ Correct 5-minute boundary")
            else:
                print("   ❌ Incorrect boundary")
                
        finally:
            # Restore original datetime
            builtins.datetime = original_datetime

def test_boundary_detection():
    """Test 5-minute boundary detection"""
    print("\n🎯 BOUNDARY DETECTION TEST")
    print("=" * 40)
    
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test boundary detection logic
    boundary_times = [
        (datetime(2025, 10, 15, 14, 25, 0), True),   # Exact boundary
        (datetime(2025, 10, 15, 14, 25, 1), False),  # 1 second after
        (datetime(2025, 10, 15, 14, 30, 0), True),   # Another boundary
        (datetime(2025, 10, 15, 14, 24, 59), False), # 1 second before
        (datetime(2025, 10, 15, 15, 0, 0), True),    # Hour boundary
    ]
    
    for test_time, expected in boundary_times:
        # Check if time is at 5-minute boundary
        is_boundary = (test_time.minute % 5 == 0 and test_time.second == 0)
        
        print(f"📊 {test_time.strftime('%H:%M:%S')} -> Boundary: {is_boundary} (Expected: {expected})")
        
        if is_boundary == expected:
            print("   ✅ Correct detection")
        else:
            print("   ❌ Incorrect detection")

def simulate_timing_sequence():
    """Simulate a sequence of candle close timings"""
    print("\n⏰ TIMING SEQUENCE SIMULATION")
    print("=" * 45)
    
    print("🎬 Simulating how the system should behave:")
    print()
    
    # Simulate starting at 14:23:45
    current_time = datetime(2025, 10, 15, 14, 23, 45)
    
    for i in range(5):  # Simulate 5 iterations
        print(f"📊 Iteration {i+1}:")
        print(f"   Current time: {current_time.strftime('%H:%M:%S')}")
        
        # Calculate next 5-minute boundary
        current_5min = (current_time.minute // 5) * 5
        next_5min = current_5min + 5
        
        if next_5min >= 60:
            next_boundary = current_time.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
        else:
            next_boundary = current_time.replace(minute=next_5min, second=0, microsecond=0)
        
        wait_time = (next_boundary - current_time).total_seconds()
        
        print(f"   Next boundary: {next_boundary.strftime('%H:%M:%S')}")
        print(f"   Wait time: {wait_time:.1f} seconds")
        print(f"   🎯 ANALYSIS would run at: {next_boundary.strftime('%H:%M:%S')}")
        
        # Simulate analysis taking 2-3 seconds
        analysis_duration = 2.5
        after_analysis = next_boundary + timedelta(seconds=analysis_duration)
        print(f"   📊 Analysis complete at: {after_analysis.strftime('%H:%M:%S')}")
        
        # Move to next iteration (next boundary)
        current_time = next_boundary + timedelta(minutes=5)
        print()

def main():
    """Run all timing tests"""
    print("🚀 TIMING FIXES VERIFICATION")
    print("=" * 60)
    print(f"⏰ Test Time: {datetime.now()}")
    print()
    
    # Test 1: Timing precision
    test_timing_precision()
    
    # Test 2: Boundary detection
    test_boundary_detection()
    
    # Test 3: Timing sequence simulation
    simulate_timing_sequence()
    
    print("📊 SUMMARY OF FIXES")
    print("=" * 30)
    print("✅ REMOVED: 4-second broker delay")
    print("✅ FIXED: Timing drift issue")
    print("✅ IMPROVED: Precise candle boundary synchronization")
    print()
    print("🎯 Expected behavior:")
    print("   • Analysis runs exactly at XX:X5:00 (no delay)")
    print("   • After analysis, waits for next exact boundary")
    print("   • No more timing drift between iterations")
    print("   • Consistent 300-second intervals")
    print()
    print("📝 Expected log changes:")
    print("   OLD: '🎯 CANDLE CLOSE ANALYSIS: Running at 14:25:05 (305s since last)'")
    print("   NEW: '🎯 CANDLE CLOSE ANALYSIS: Running at 14:25:00 (300s since last)'")
    print()
    print("   OLD: '⏳ Waiting 4 seconds for broker to process candle data...'")
    print("   NEW: (No delay message - analysis starts immediately)")

if __name__ == "__main__":
    main()
