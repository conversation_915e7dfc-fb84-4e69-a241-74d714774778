#!/usr/bin/env python3
"""
Test the recency-first swing detection approach
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_recency_first_selection():
    """Test that the algorithm prioritizes the most recent swing low"""
    print("🧪 TESTING RECENCY-FIRST SWING SELECTION")
    print("=" * 60)
    
    # Create trader instance
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test Scenario: Multiple swing lows where we want the MOST RECENT, not the lowest
    print("\n📊 SCENARIO: Recent Swing Low vs Older Lower Swing Low")
    dates = pd.date_range(start='2024-01-01', periods=20, freq='5min')
    
    # Create data that matches your XAUUSD situation:
    # - Older lower swing low at 4204.22 (9 candles ago)
    # - More recent swing low at 4208.74 (1-2 candles ago) - SHOULD BE SELECTED
    
    highs = [
        4210.0, 4212.0, 4214.0, 4216.0, 4218.0,   # Rising
        4220.0, 4222.0, 4220.0, 4218.0, 4216.0,   # Peak and decline
        4214.0, 4206.22, 4208.0, 4210.0, 4212.0,  # Older swing low at index 11 (4204.22)
        4214.0, 4216.0, 4218.0, 4210.74, 4212.0   # Recent swing low at index 18 (4208.74)
    ]
    
    lows = [
        4207.0, 4209.0, 4211.0, 4213.0, 4215.0,   # Rising
        4217.0, 4219.0, 4217.0, 4215.0, 4213.0,   # Peak and decline
        4211.0, 4204.22, 4205.0, 4207.0, 4209.0,  # OLDER LOWER swing low at index 11
        4211.0, 4213.0, 4215.0, 4208.74, 4209.0   # RECENT swing low at index 18
    ]
    
    closes = [(h + l) / 2 for h, l in zip(highs, lows)]
    opens = closes.copy()
    
    # Add ATR column
    atr_values = [2.5] * 20  # Typical XAUUSD ATR
    
    df = pd.DataFrame({
        'time': dates,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'tick_volume': [100] * 20,
        'atr': atr_values
    }).set_index('time')
    
    print("📈 DATA ANALYSIS:")
    print("Swing Low Candidates:")
    print(f"  📊 Older Lower Low: 4204.22 (index 11, ~8 candles ago)")
    print(f"  📊 Recent Higher Low: 4208.74 (index 18, ~1 candle ago)")
    print(f"  📊 Price Difference: {4208.74 - 4204.22:.2f} points")
    print()
    
    print("Expected Logic (RECENCY-FIRST):")
    print(f"  🎯 Should select: 4208.74 (most recent swing low)")
    print(f"  🎯 Should NOT select: 4204.22 (older, even though lower)")
    print()
    
    print("Last 5 candles:")
    for i in range(5):
        idx = -(5-i)
        candle = df.iloc[idx]
        marker = ""
        if len(df) + idx == 18:
            marker = " ← Expected Recent Low (4208.74)"
        elif len(df) + idx == 11:
            marker = " ← Older Lower Low (4204.22)"
        print(f"  {len(df) + idx:2d}: H={candle['high']:8.2f} L={candle['low']:8.2f}{marker}")
    
    # Run swing detection
    print(f"\n🔍 RUNNING RECENCY-FIRST SWING DETECTION:")
    swing_points = trader.find_recent_swing_points(df)
    
    print(f"\n📋 RESULTS:")
    success = True
    
    if swing_points['recent_low']:
        detected_low = swing_points['recent_low']
        expected_low = 4208.74  # Recent swing low
        older_low = 4204.22     # Older lower swing low
        candles_ago = swing_points['recent_low_candles_ago']
        
        print(f"✅ Detected Swing Low: {detected_low:.2f}")
        print(f"   📅 Candles ago: {candles_ago}")
        
        if abs(detected_low - expected_low) < 0.01:
            print(f"   🎉 CORRECT: Selected most recent swing low (4208.74)")
            print(f"   ✅ Properly prioritized recency over absolute price")
            success = True
        elif abs(detected_low - older_low) < 0.01:
            print(f"   ❌ INCORRECT: Selected older lower swing low (4204.22)")
            print(f"   ⚠️  Should prioritize recent swing low (4208.74)")
            success = False
        else:
            print(f"   ❓ UNEXPECTED: Selected {detected_low:.2f}")
            success = False
    else:
        print("❌ No swing low detected")
        success = False
    
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}: Recency-first selection test")
    print("=" * 60)
    
    if success:
        print("🎉 The algorithm correctly prioritizes RECENT swing points!")
        print("📊 It selects the most recent valid swing low, not just the lowest price")
        print("🔍 This should fix your XAUUSD issue: 4208.74 instead of 4204.22")
    else:
        print("⚠️  The algorithm still needs adjustment")
        print("🔧 The recency-first logic may need refinement")
    
    return success

if __name__ == "__main__":
    test_recency_first_selection()
