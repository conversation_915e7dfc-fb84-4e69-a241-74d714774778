"""
Main execution script for XAUUSD LSTM Trading System
"""
import argparse
import logging
import sys
from pathlib import Path
import pandas as pd

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from config.config import *
from src.data_manager import DataManager
from src.data_preprocessing import DataPreprocessor
from src.training_pipeline import TrainingPipeline
from src.model_evaluation import ModelEvaluator
from src.live_trading_system import LiveTradingSystem

# Set up logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

def train_model():
    """Train the LSTM model"""
    try:
        logger.info("Starting model training pipeline...")
        
        # Step 1: Load and update data
        logger.info("Loading and updating data...")
        data_manager = DataManager()
        
        # Load historical data
        historical_data = data_manager.load_historical_data()
        if historical_data is None:
            logger.error("Failed to load historical data")
            return False
        
        # Update with latest MT5 data (optional - requires MT5 credentials)
        # data_manager.update_data_from_mt5()
        
        # Get training data
        training_data = data_manager.get_data_for_training()
        if training_data is None:
            logger.error("Failed to get training data")
            return False
        
        logger.info(f"Training data shape: {training_data.shape}")
        
        # Step 2: Prepare dataset
        logger.info("Preparing dataset...")
        preprocessor = DataPreprocessor()
        dataset = preprocessor.prepare_dataset(training_data)
        
        if not dataset:
            logger.error("Failed to prepare dataset")
            return False
        
        # Step 3: Train model
        logger.info("Training LSTM model...")
        trainer = TrainingPipeline()
        results = trainer.train_model(dataset)
        
        if not results:
            logger.error("Model training failed")
            return False
        
        # Step 4: Evaluate model
        logger.info("Evaluating model...")
        evaluator = ModelEvaluator()
        evaluation_report = evaluator.generate_comprehensive_report(results)
        
        logger.info("Model training completed successfully!")
        logger.info(f"Test Accuracy: {results.get('test_results', {}).get('accuracy', 0):.4f}")
        logger.info(f"Test F1-Score: {results.get('test_results', {}).get('f1_score', 0):.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in model training: {e}")
        return False

def run_live_trading(model_path: str, preprocessor_path: str, 
                    mt5_login: int = None, mt5_password: str = None, mt5_server: str = None):
    """Run live trading system"""
    try:
        logger.info("Starting live trading system...")
        
        # Initialize trading system
        trading_system = LiveTradingSystem()
        
        if not trading_system.initialize(model_path, preprocessor_path, 
                                       mt5_login, mt5_password, mt5_server):
            logger.error("Failed to initialize trading system")
            return False
        
        # Start trading
        if not trading_system.start_trading():
            logger.error("Failed to start trading")
            return False
        
        logger.info("Live trading system is running. Press Ctrl+C to stop.")
        
        # Keep running until interrupted
        try:
            while trading_system.is_running:
                import time
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Shutdown signal received")
        
        # Stop trading
        trading_system.stop_trading()
        logger.info("Live trading system stopped")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in live trading: {e}")
        return False

def test_system():
    """Test system components"""
    try:
        logger.info("Running system tests...")
        
        # Test 1: Data loading
        logger.info("Testing data loading...")
        data_manager = DataManager()
        data = data_manager.load_historical_data()
        
        if data is None:
            logger.error("Data loading test failed")
            return False
        
        logger.info(f"Data loading test passed. Loaded {len(data)} records")
        
        # Test 2: Feature engineering
        logger.info("Testing feature engineering...")
        from src.feature_engineering import FeatureEngineer
        
        feature_engineer = FeatureEngineer()
        sample_data = data.tail(1000)  # Use last 1000 records for testing
        
        features_data = feature_engineer.create_technical_indicators(sample_data)
        if features_data is None or len(features_data.columns) <= len(sample_data.columns):
            logger.error("Feature engineering test failed")
            return False
        
        logger.info(f"Feature engineering test passed. Created {len(features_data.columns) - len(sample_data.columns)} features")
        
        # Test 3: Data preprocessing
        logger.info("Testing data preprocessing...")
        preprocessor = DataPreprocessor()
        
        # Create a small dataset for testing
        test_data = features_data.tail(5000)
        dataset = preprocessor.prepare_dataset(test_data)
        
        if not dataset or 'train' not in dataset:
            logger.error("Data preprocessing test failed")
            return False
        
        X_train, y_train = dataset['train']
        logger.info(f"Data preprocessing test passed. Train shape: {X_train.shape}")
        
        # Test 4: Model architecture
        logger.info("Testing model architecture...")
        from src.lstm_model import LSTMTradingModel
        
        input_shape = (X_train.shape[1], X_train.shape[2])
        model = LSTMTradingModel(input_shape, num_classes=3)
        
        keras_model = model.build_model()
        if keras_model is None:
            logger.error("Model architecture test failed")
            return False
        
        logger.info("Model architecture test passed")
        
        # Test 5: Risk management
        logger.info("Testing risk management...")
        from src.risk_management import RiskManager
        
        risk_manager = RiskManager()
        
        # Test position size calculation
        position_size = risk_manager.calculate_optimal_position_size(
            account_balance=10000,
            entry_price=2000.0,
            stop_loss=1990.0,
            confidence=0.8
        )
        
        if position_size <= 0:
            logger.error("Risk management test failed")
            return False
        
        logger.info(f"Risk management test passed. Position size: {position_size}")
        
        logger.info("All system tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"Error in system testing: {e}")
        return False

def update_data():
    """Update data from MT5"""
    try:
        logger.info("Updating data from MT5...")
        
        # Get MT5 credentials (you would need to provide these)
        mt5_login = input("Enter MT5 Login: ")
        mt5_password = input("Enter MT5 Password: ")
        mt5_server = input("Enter MT5 Server: ")
        
        if not all([mt5_login, mt5_password, mt5_server]):
            logger.error("MT5 credentials required")
            return False
        
        data_manager = DataManager()
        
        # Load existing data
        data_manager.load_historical_data()
        
        # Update with latest data
        success = data_manager.update_data_from_mt5(
            int(mt5_login), mt5_password, mt5_server
        )
        
        if success:
            logger.info("Data updated successfully")
            return True
        else:
            logger.error("Data update failed")
            return False
        
    except Exception as e:
        logger.error(f"Error updating data: {e}")
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="XAUUSD LSTM Trading System")
    parser.add_argument("command", choices=["train", "trade", "test", "update"], 
                       help="Command to execute")
    parser.add_argument("--model-path", default="models/lstm_trading_model.h5",
                       help="Path to trained model")
    parser.add_argument("--preprocessor-path", default="models/preprocessor.joblib",
                       help="Path to fitted preprocessor")
    parser.add_argument("--mt5-login", type=int, help="MT5 login")
    parser.add_argument("--mt5-password", help="MT5 password")
    parser.add_argument("--mt5-server", help="MT5 server")
    
    args = parser.parse_args()
    
    try:
        if args.command == "train":
            success = train_model()
        elif args.command == "trade":
            if not Path(args.model_path).exists():
                logger.error(f"Model file not found: {args.model_path}")
                logger.info("Please train the model first using: python main.py train")
                return False

            # Use logged-in MT5 account if no credentials provided
            success = run_live_trading(
                args.model_path, args.preprocessor_path,
                args.mt5_login, args.mt5_password, args.mt5_server
            )
        elif args.command == "test":
            success = test_system()
        elif args.command == "update":
            success = update_data()
        else:
            logger.error(f"Unknown command: {args.command}")
            return False
        
        if success:
            logger.info(f"Command '{args.command}' completed successfully")
            return True
        else:
            logger.error(f"Command '{args.command}' failed")
            return False
            
    except KeyboardInterrupt:
        logger.info("Operation interrupted by user")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
