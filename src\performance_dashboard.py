"""
Performance Monitoring Dashboard
Real-time monitoring and visualization of trading system performance
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import streamlit as st
from typing import Dict, List, Optional
import logging
from datetime import datetime, timedelta
import json
from pathlib import Path

from config.config import *

# Set up logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

class PerformanceDashboard:
    """
    Real-time performance monitoring dashboard
    """
    
    def __init__(self):
        self.performance_data = []
        self.trade_history = []
        self.risk_metrics = {}
        
    def load_performance_data(self) -> bool:
        """Load performance data from files"""
        try:
            # Load performance logs
            performance_files = list(REPORTS_DIR.glob("performance_log_*.json"))
            
            if performance_files:
                latest_file = max(performance_files, key=lambda x: x.stat().st_mtime)
                
                with open(latest_file, 'r') as f:
                    self.performance_data = json.load(f)
                
                logger.info(f"Loaded {len(self.performance_data)} performance records")
            
            # Load trading session reports
            session_files = list(REPORTS_DIR.glob("trading_session_report_*.json"))
            
            if session_files:
                latest_session = max(session_files, key=lambda x: x.stat().st_mtime)
                
                with open(latest_session, 'r') as f:
                    session_data = json.load(f)
                    self.trade_history = session_data.get('trading_statistics', {})
                    self.risk_metrics = session_data.get('risk_metrics', {})
                
                logger.info("Loaded latest trading session data")
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading performance data: {e}")
            return False
    
    def create_equity_curve(self) -> go.Figure:
        """Create equity curve visualization"""
        try:
            if not self.performance_data:
                return go.Figure()
            
            df = pd.DataFrame(self.performance_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            fig = go.Figure()
            
            # Equity curve
            fig.add_trace(go.Scatter(
                x=df['timestamp'],
                y=df['equity'],
                mode='lines',
                name='Account Equity',
                line=dict(color='blue', width=2)
            ))
            
            # Balance line
            fig.add_trace(go.Scatter(
                x=df['timestamp'],
                y=df['balance'],
                mode='lines',
                name='Account Balance',
                line=dict(color='green', width=1, dash='dash')
            ))
            
            fig.update_layout(
                title='Account Equity Curve',
                xaxis_title='Time',
                yaxis_title='Account Value',
                hovermode='x unified',
                showlegend=True
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"Error creating equity curve: {e}")
            return go.Figure()
    
    def create_drawdown_chart(self) -> go.Figure:
        """Create drawdown visualization"""
        try:
            if not self.performance_data:
                return go.Figure()
            
            df = pd.DataFrame(self.performance_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Calculate drawdown
            df['peak'] = df['equity'].expanding().max()
            df['drawdown'] = (df['equity'] - df['peak']) / df['peak'] * 100
            
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=df['timestamp'],
                y=df['drawdown'],
                mode='lines',
                name='Drawdown %',
                fill='tonexty',
                line=dict(color='red', width=1),
                fillcolor='rgba(255, 0, 0, 0.3)'
            ))
            
            fig.update_layout(
                title='Account Drawdown',
                xaxis_title='Time',
                yaxis_title='Drawdown (%)',
                hovermode='x unified',
                yaxis=dict(range=[df['drawdown'].min() * 1.1, 5])
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"Error creating drawdown chart: {e}")
            return go.Figure()
    
    def create_performance_metrics_table(self) -> pd.DataFrame:
        """Create performance metrics summary table"""
        try:
            if not self.performance_data:
                return pd.DataFrame()
            
            df = pd.DataFrame(self.performance_data)
            
            # Calculate metrics
            initial_balance = df['balance'].iloc[0] if len(df) > 0 else 0
            current_balance = df['balance'].iloc[-1] if len(df) > 0 else 0
            
            total_return = (current_balance - initial_balance) / initial_balance * 100 if initial_balance > 0 else 0
            
            # Calculate other metrics
            equity_values = df['equity'].values
            peak = np.maximum.accumulate(equity_values)
            drawdowns = (equity_values - peak) / peak * 100
            max_drawdown = np.min(drawdowns)
            
            # Daily returns
            daily_returns = df['equity'].pct_change().dropna()
            sharpe_ratio = np.mean(daily_returns) / np.std(daily_returns) * np.sqrt(252) if len(daily_returns) > 1 else 0
            
            metrics_data = {
                'Metric': [
                    'Total Return (%)',
                    'Max Drawdown (%)',
                    'Sharpe Ratio',
                    'Total Predictions',
                    'Total Trades',
                    'Current Balance',
                    'Current Equity',
                    'Free Margin'
                ],
                'Value': [
                    f"{total_return:.2f}%",
                    f"{max_drawdown:.2f}%",
                    f"{sharpe_ratio:.2f}",
                    df['total_predictions'].iloc[-1] if len(df) > 0 else 0,
                    df['total_trades'].iloc[-1] if len(df) > 0 else 0,
                    f"${current_balance:.2f}",
                    f"${df['equity'].iloc[-1]:.2f}" if len(df) > 0 else "$0.00",
                    f"${df['free_margin'].iloc[-1]:.2f}" if len(df) > 0 else "$0.00"
                ]
            }
            
            return pd.DataFrame(metrics_data)
            
        except Exception as e:
            logger.error(f"Error creating performance metrics table: {e}")
            return pd.DataFrame()
    
    def create_risk_metrics_chart(self) -> go.Figure:
        """Create risk metrics visualization"""
        try:
            if not self.risk_metrics:
                return go.Figure()
            
            # Create gauge charts for key risk metrics
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('Win Rate', 'Profit Factor', 'Sharpe Ratio', 'Current Drawdown'),
                specs=[[{"type": "indicator"}, {"type": "indicator"}],
                       [{"type": "indicator"}, {"type": "indicator"}]]
            )
            
            # Win Rate
            win_rate = self.risk_metrics.get('win_rate', 0) * 100
            fig.add_trace(go.Indicator(
                mode="gauge+number",
                value=win_rate,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Win Rate (%)"},
                gauge={'axis': {'range': [None, 100]},
                       'bar': {'color': "darkblue"},
                       'steps': [{'range': [0, 50], 'color': "lightgray"},
                                {'range': [50, 80], 'color': "gray"}],
                       'threshold': {'line': {'color': "red", 'width': 4},
                                   'thickness': 0.75, 'value': 90}}
            ), row=1, col=1)
            
            # Profit Factor
            profit_factor = self.risk_metrics.get('profit_factor', 0)
            fig.add_trace(go.Indicator(
                mode="gauge+number",
                value=profit_factor,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Profit Factor"},
                gauge={'axis': {'range': [0, 3]},
                       'bar': {'color': "darkgreen"},
                       'steps': [{'range': [0, 1], 'color': "lightgray"},
                                {'range': [1, 2], 'color': "gray"}],
                       'threshold': {'line': {'color': "red", 'width': 4},
                                   'thickness': 0.75, 'value': 2.5}}
            ), row=1, col=2)
            
            # Sharpe Ratio
            sharpe_ratio = self.risk_metrics.get('sharpe_ratio', 0)
            fig.add_trace(go.Indicator(
                mode="gauge+number",
                value=sharpe_ratio,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Sharpe Ratio"},
                gauge={'axis': {'range': [-2, 3]},
                       'bar': {'color': "darkblue"},
                       'steps': [{'range': [-2, 0], 'color': "lightgray"},
                                {'range': [0, 1], 'color': "gray"}],
                       'threshold': {'line': {'color': "red", 'width': 4},
                                   'thickness': 0.75, 'value': 2}}
            ), row=2, col=1)
            
            # Current Drawdown
            current_drawdown = self.risk_metrics.get('current_drawdown', 0) * 100
            fig.add_trace(go.Indicator(
                mode="gauge+number",
                value=current_drawdown,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Current Drawdown (%)"},
                gauge={'axis': {'range': [0, 25]},
                       'bar': {'color': "darkred"},
                       'steps': [{'range': [0, 10], 'color': "lightgray"},
                                {'range': [10, 20], 'color': "gray"}],
                       'threshold': {'line': {'color': "red", 'width': 4},
                                   'thickness': 0.75, 'value': 20}}
            ), row=2, col=2)
            
            fig.update_layout(height=600, title_text="Risk Metrics Dashboard")
            
            return fig
            
        except Exception as e:
            logger.error(f"Error creating risk metrics chart: {e}")
            return go.Figure()
    
    def create_trade_distribution_chart(self) -> go.Figure:
        """Create trade distribution visualization"""
        try:
            if not self.trade_history:
                return go.Figure()
            
            signal_dist = self.trade_history.get('signal_distribution', {})
            
            if not signal_dist:
                return go.Figure()
            
            fig = go.Figure(data=[
                go.Bar(
                    x=list(signal_dist.keys()),
                    y=list(signal_dist.values()),
                    marker_color=['blue', 'green', 'red']
                )
            ])
            
            fig.update_layout(
                title='Trade Signal Distribution',
                xaxis_title='Signal Type',
                yaxis_title='Number of Trades'
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"Error creating trade distribution chart: {e}")
            return go.Figure()
    
    def generate_html_report(self, output_path: str = None) -> str:
        """Generate HTML performance report"""
        try:
            if output_path is None:
                output_path = REPORTS_DIR / f"performance_dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            
            # Create visualizations
            equity_fig = self.create_equity_curve()
            drawdown_fig = self.create_drawdown_chart()
            risk_fig = self.create_risk_metrics_chart()
            trade_fig = self.create_trade_distribution_chart()
            metrics_df = self.create_performance_metrics_table()
            
            # Generate HTML
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>XAUUSD LSTM Trading System - Performance Dashboard</title>
                <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ text-align: center; color: #333; }}
                    .section {{ margin: 30px 0; }}
                    .metrics-table {{ margin: 20px auto; border-collapse: collapse; }}
                    .metrics-table th, .metrics-table td {{ 
                        border: 1px solid #ddd; padding: 8px; text-align: left; 
                    }}
                    .metrics-table th {{ background-color: #f2f2f2; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>XAUUSD LSTM Trading System</h1>
                    <h2>Performance Dashboard</h2>
                    <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                
                <div class="section">
                    <h3>Performance Metrics</h3>
                    <table class="metrics-table">
                        <tr><th>Metric</th><th>Value</th></tr>
            """
            
            # Add metrics table
            for _, row in metrics_df.iterrows():
                html_content += f"<tr><td>{row['Metric']}</td><td>{row['Value']}</td></tr>"
            
            html_content += """
                    </table>
                </div>
                
                <div class="section">
                    <h3>Account Equity Curve</h3>
                    <div id="equity-chart"></div>
                </div>
                
                <div class="section">
                    <h3>Drawdown Analysis</h3>
                    <div id="drawdown-chart"></div>
                </div>
                
                <div class="section">
                    <h3>Risk Metrics</h3>
                    <div id="risk-chart"></div>
                </div>
                
                <div class="section">
                    <h3>Trade Distribution</h3>
                    <div id="trade-chart"></div>
                </div>
                
                <script>
            """
            
            # Add Plotly charts
            html_content += f"""
                Plotly.newPlot('equity-chart', {equity_fig.to_json()});
                Plotly.newPlot('drawdown-chart', {drawdown_fig.to_json()});
                Plotly.newPlot('risk-chart', {risk_fig.to_json()});
                Plotly.newPlot('trade-chart', {trade_fig.to_json()});
            """
            
            html_content += """
                </script>
            </body>
            </html>
            """
            
            # Save HTML file
            with open(output_path, 'w') as f:
                f.write(html_content)
            
            logger.info(f"HTML performance report generated: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error generating HTML report: {e}")
            return ""
    
    def get_real_time_status(self) -> Dict:
        """Get real-time system status"""
        try:
            status = {
                'timestamp': datetime.now().isoformat(),
                'system_health': 'UNKNOWN',
                'last_update': None,
                'performance_summary': {},
                'alerts': []
            }
            
            if self.performance_data:
                latest_data = self.performance_data[-1]
                status['last_update'] = latest_data.get('timestamp')
                status['system_health'] = 'HEALTHY' if latest_data.get('error_count', 0) < 5 else 'WARNING'
                
                status['performance_summary'] = {
                    'current_balance': latest_data.get('balance', 0),
                    'current_equity': latest_data.get('equity', 0),
                    'total_trades': latest_data.get('total_trades', 0),
                    'total_predictions': latest_data.get('total_predictions', 0)
                }
                
                # Check for alerts
                if latest_data.get('equity', 0) < latest_data.get('balance', 0) * 0.9:
                    status['alerts'].append('High drawdown detected')
                
                if latest_data.get('free_margin', 0) < 1000:
                    status['alerts'].append('Low free margin')
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting real-time status: {e}")
            return {'error': str(e)}

def main():
    """Main function for standalone dashboard"""
    dashboard = PerformanceDashboard()
    
    if dashboard.load_performance_data():
        # Generate HTML report
        report_path = dashboard.generate_html_report()
        print(f"Performance dashboard generated: {report_path}")
        
        # Print real-time status
        status = dashboard.get_real_time_status()
        print(f"System Status: {status}")
    else:
        print("No performance data available")

if __name__ == "__main__":
    main()
