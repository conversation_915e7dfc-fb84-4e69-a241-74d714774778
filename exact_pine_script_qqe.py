#!/usr/bin/env python3
"""
EXACT Pine Script QQE Implementation - Line by Line
"""

import sys
import os
sys.path.append('src')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Import MT5
from mt5_integration import MT5Manager

def rsi_pine_script(src, length):
    """RSI calculation exactly like Pine Script"""
    delta = src.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    # Pine Script uses RMA (Running Moving Average) which is EWM with alpha = 1/length
    alpha = 1.0 / length
    avg_gain = gain.ewm(alpha=alpha, adjust=False).mean()
    avg_loss = loss.ewm(alpha=alpha, adjust=False).mean()
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def ema_pine_script(src, length):
    """EMA calculation exactly like Pine Script"""
    return src.ewm(span=length, adjust=False).mean()

def exact_pine_script_qqe():
    """Implement QQE exactly as Pine Script line by line"""
    print("🔬 EXACT Pine Script QQE Implementation")
    print("=" * 60)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Connect to MT5
    mt5_manager = MT5Manager()
    if not mt5_manager.connect():
        print("❌ Failed to connect to MT5")
        return
    
    # Get recent data
    df = mt5_manager.get_latest_data("XAUUSD!", "M5", 100)
    if df is None or len(df) < 50:
        print("❌ Failed to get market data")
        return
    
    print(f"📊 Got {len(df)} periods of XAUUSD M5 data")
    print(f"   Latest close: {df['close'].iloc[-1]:.2f}")
    
    # Pine Script exact parameters - FIXED: Match user's TradingView settings
    RSI_Period = 7  # USER'S TRADINGVIEW SETTING: 7
    SF = 5  # RSI Smoothing
    QQE = 1.0  # Fast QQE Factor - USER'S TRADINGVIEW SETTING: 1
    ThreshHold = 10
    
    src = df['close']
    Wilders_Period = RSI_Period * 2 - 1  # 27
    
    print(f"\n🔧 Pine Script Parameters:")
    print(f"   RSI_Period: {RSI_Period}")
    print(f"   SF (RSI Smoothing): {SF}")
    print(f"   QQE (Fast QQE Factor): {QQE}")
    print(f"   ThreshHold: {ThreshHold}")
    print(f"   Wilders_Period: {Wilders_Period}")
    
    # Line by line Pine Script implementation
    print(f"\n🔄 Pine Script Line by Line:")
    
    # Rsi = rsi(src, RSI_Period)
    Rsi = rsi_pine_script(src, RSI_Period)
    print(f"   Rsi calculated: {Rsi.iloc[-1]:.1f}")
    
    # RsiMa = ema(Rsi, SF)
    RsiMa = ema_pine_script(Rsi, SF)
    print(f"   RsiMa calculated: {RsiMa.iloc[-1]:.1f}")
    
    # AtrRsi = abs(RsiMa[1] - RsiMa)
    AtrRsi = abs(RsiMa.shift(1) - RsiMa)
    print(f"   AtrRsi calculated: {AtrRsi.iloc[-1]:.3f}")
    
    # MaAtrRsi = ema(AtrRsi, Wilders_Period)
    MaAtrRsi = ema_pine_script(AtrRsi, Wilders_Period)
    print(f"   MaAtrRsi calculated: {MaAtrRsi.iloc[-1]:.3f}")
    
    # dar = ema(MaAtrRsi, Wilders_Period) * QQE
    dar = ema_pine_script(MaAtrRsi, Wilders_Period) * QQE
    print(f"   dar calculated: {dar.iloc[-1]:.3f}")
    
    # Initialize arrays
    longband = pd.Series(0.0, index=df.index)
    shortband = pd.Series(0.0, index=df.index)
    trend = pd.Series(1, index=df.index)  # Default to 1
    FastAtrRsiTL = pd.Series(0.0, index=df.index)
    
    # Pine Script band calculation loop
    for i in range(1, len(df)):
        if pd.isna(dar.iloc[i]) or pd.isna(RsiMa.iloc[i]):
            trend.iloc[i] = trend.iloc[i-1]
            continue
            
        RSIndex = RsiMa.iloc[i]
        DeltaFastAtrRsi = dar.iloc[i]
        RSIndex_prev = RsiMa.iloc[i-1]
        
        # newshortband = RSIndex + DeltaFastAtrRsi
        # newlongband = RSIndex - DeltaFastAtrRsi
        newlongband = RSIndex - DeltaFastAtrRsi
        newshortband = RSIndex + DeltaFastAtrRsi
        
        # longband := RSIndex[1] > longband[1] and RSIndex > longband[1] ? max(longband[1], newlongband) : newlongband
        longband_prev = longband.iloc[i-1]
        if RSIndex_prev > longband_prev and RSIndex > longband_prev:
            longband.iloc[i] = max(longband_prev, newlongband)
        else:
            longband.iloc[i] = newlongband
        
        # shortband := RSIndex[1] < shortband[1] and RSIndex < shortband[1] ? min(shortband[1], newshortband) : newshortband
        shortband_prev = shortband.iloc[i-1]
        if RSIndex_prev < shortband_prev and RSIndex < shortband_prev:
            shortband.iloc[i] = min(shortband_prev, newshortband)
        else:
            shortband.iloc[i] = newshortband
        
        # cross_1 = cross(longband[1], RSIndex)
        # trend := cross(RSIndex, shortband[1]) ? 1 : cross_1 ? -1 : nz(trend[1], 1)
        cross_rsi_above_shortband = (RSIndex > shortband_prev and RSIndex_prev <= shortband_prev)
        cross_longband_above_rsi = (longband_prev > RSIndex_prev and longband.iloc[i] <= RSIndex)
        
        if cross_rsi_above_shortband:
            trend.iloc[i] = 1
        elif cross_longband_above_rsi:
            trend.iloc[i] = -1
        else:
            trend.iloc[i] = trend.iloc[i-1] if not pd.isna(trend.iloc[i-1]) else 1
        
        # FastAtrRsiTL = trend == 1 ? longband : shortband
        if trend.iloc[i] == 1:
            FastAtrRsiTL.iloc[i] = longband.iloc[i]
        else:
            FastAtrRsiTL.iloc[i] = shortband.iloc[i]
    
    # QQE signal calculation
    QQExlong = pd.Series(0, index=df.index)
    QQExshort = pd.Series(0, index=df.index)
    qqeLong = pd.Series(np.nan, index=df.index)
    qqeShort = pd.Series(np.nan, index=df.index)
    
    for i in range(1, len(df)):
        if pd.isna(FastAtrRsiTL.iloc[i]) or pd.isna(RsiMa.iloc[i]):
            QQExlong.iloc[i] = QQExlong.iloc[i-1]
            QQExshort.iloc[i] = QQExshort.iloc[i-1]
            continue
            
        FastAtrRsiTL_val = FastAtrRsiTL.iloc[i]
        RSIndex = RsiMa.iloc[i]
        
        # QQExlong := FastAtrRsiTL < RSIndex ? QQExlong + 1 : 0
        if FastAtrRsiTL_val < RSIndex:
            QQExlong.iloc[i] = QQExlong.iloc[i-1] + 1
            QQExshort.iloc[i] = 0
        else:
            QQExlong.iloc[i] = 0
            QQExshort.iloc[i] = QQExshort.iloc[i-1] + 1
        
        # qqeLong = QQExlong == 1 ? FastAtrRsiTL[1] - 50 : na
        if QQExlong.iloc[i] == 1:
            qqeLong.iloc[i] = FastAtrRsiTL.iloc[i-1] - 50
        
        # qqeShort = QQExshort == 1 ? FastAtrRsiTL[1] - 50 : na
        if QQExshort.iloc[i] == 1:
            qqeShort.iloc[i] = FastAtrRsiTL.iloc[i-1] - 50
    
    # Show results for last 10 periods
    print(f"\n📈 Last 10 Periods - EXACT Pine Script:")
    print("Time                 | RSI   | RsiMa | Long_B | Short_B | TL    | Trend | QQELong | QQEShort | Signal")
    print("-" * 120)
    
    recent_indices = df.index[-10:]
    for idx in recent_indices:
        i = df.index.get_loc(idx)
        time_str = idx.strftime('%m-%d %H:%M')
        rsi = Rsi.iloc[i]
        rsi_ma = RsiMa.iloc[i]
        longband_val = longband.iloc[i]
        shortband_val = shortband.iloc[i]
        tl = FastAtrRsiTL.iloc[i]
        trend_val = int(trend.iloc[i])
        qqe_long = int(QQExlong.iloc[i])
        qqe_short = int(QQExshort.iloc[i])
        
        signal = ""
        if not pd.isna(qqeLong.iloc[i]):
            signal = f"LONG({qqeLong.iloc[i]:.1f})"
        elif not pd.isna(qqeShort.iloc[i]):
            signal = f"SHORT({qqeShort.iloc[i]:.1f})"
        else:
            signal = "----"
        
        print(f"{time_str} | {rsi:5.1f} | {rsi_ma:5.1f} | {longband_val:6.1f} | {shortband_val:7.1f} | {tl:5.1f} | {trend_val:5d} | {qqe_long:7d} | {qqe_short:8d} | {signal}")
    
    # Check last closed candle (second to last)
    last_closed_idx = -2  # Last closed candle
    last_closed_row_idx = df.index[last_closed_idx]
    i = df.index.get_loc(last_closed_row_idx)
    
    print(f"\n🎯 Last CLOSED Candle Analysis:")
    print(f"   Time: {last_closed_row_idx.strftime('%m-%d %H:%M')}")
    print(f"   RSI: {Rsi.iloc[i]:.1f}")
    print(f"   RSI MA: {RsiMa.iloc[i]:.1f}")
    print(f"   Fast ATR RSI TL: {FastAtrRsiTL.iloc[i]:.1f}")
    print(f"   Trend: {int(trend.iloc[i])}")
    print(f"   Condition: TL ({FastAtrRsiTL.iloc[i]:.1f}) {'<' if FastAtrRsiTL.iloc[i] < RsiMa.iloc[i] else '>'} RSI_MA ({RsiMa.iloc[i]:.1f}) = {'LONG' if FastAtrRsiTL.iloc[i] < RsiMa.iloc[i] else 'SHORT'}")
    print(f"   QQExlong: {int(QQExlong.iloc[i])}")
    print(f"   QQExshort: {int(QQExshort.iloc[i])}")
    
    if not pd.isna(qqeLong.iloc[i]):
        print(f"   ✅ PINE SCRIPT LONG SIGNAL: {qqeLong.iloc[i]:.1f}")
    elif not pd.isna(qqeShort.iloc[i]):
        print(f"   ✅ PINE SCRIPT SHORT SIGNAL: {qqeShort.iloc[i]:.1f}")
    else:
        print(f"   ❌ NO PINE SCRIPT SIGNAL")
    
    # Also check current forming candle
    current_idx = -1  # Current forming candle
    current_row_idx = df.index[current_idx]
    i = df.index.get_loc(current_row_idx)
    
    print(f"\n🔄 Current FORMING Candle (for reference):")
    print(f"   Time: {current_row_idx.strftime('%m-%d %H:%M')}")
    print(f"   RSI: {Rsi.iloc[i]:.1f}")
    print(f"   RSI MA: {RsiMa.iloc[i]:.1f}")
    print(f"   Fast ATR RSI TL: {FastAtrRsiTL.iloc[i]:.1f}")
    print(f"   Trend: {int(trend.iloc[i])}")
    print(f"   Condition: TL ({FastAtrRsiTL.iloc[i]:.1f}) {'<' if FastAtrRsiTL.iloc[i] < RsiMa.iloc[i] else '>'} RSI_MA ({RsiMa.iloc[i]:.1f}) = {'LONG' if FastAtrRsiTL.iloc[i] < RsiMa.iloc[i] else 'SHORT'}")
    
    mt5_manager.disconnect()
    print(f"\n✅ EXACT Pine Script QQE Complete!")

if __name__ == "__main__":
    try:
        exact_pine_script_qqe()
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
