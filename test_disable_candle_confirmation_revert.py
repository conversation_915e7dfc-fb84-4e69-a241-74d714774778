#!/usr/bin/env python3
"""
Test Disable Candle Confirmation Revert Functionality

Tests that candle confirmation reverting can be disabled and that
the system properly handles this disabled state.
"""

import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader

def test_disable_candle_confirmation_revert():
    """Test that candle confirmation reverting can be disabled"""
    
    print("🧪 Disable Candle Confirmation Revert Test")
    print("=" * 50)
    
    # Create test trader
    trader = FixedLiveTrader("XAUUSD!")
    
    test_results = []
    
    # Test 1: Verify flag is set to disabled by default
    print(f"\n📊 Test 1: Default Disable Flag State")
    print("-" * 40)
    
    disable_flag = trader.disable_candle_confirmation_revert
    print(f"  Disable Flag: {disable_flag}")
    print(f"  Expected: True (disabled by default)")
    
    flag_correct = disable_flag == True
    test_results.append(("Default Disable Flag", flag_correct))
    print(f"  Result: {'✅ PASSED' if flag_correct else '❌ FAILED'}")
    
    # Test 2: Test disabled revert behavior
    print(f"\n📊 Test 2: Disabled Revert Behavior")
    print("-" * 40)
    
    # Set up a mock candle confirmation stop (as if it was set earlier)
    trader.candle_confirmation_stop = {
        'confirmation_sl': 4295.0,
        'original_sl': 4285.0,
        'candle_close_time': datetime.now() - timedelta(minutes=6),  # 6 minutes ago (past threshold)
        'position_type': 'BUY',
        'confirmation_candle': {'high': 4290.0, 'low': 4280.0}
    }
    
    # Set up a mock position
    trader.current_position = {
        'type': 'BUY',
        'ticket': 12345,
        'time': datetime.now(),
        'volume': 0.10,
        'remaining_volume': 0.10,
        'price': 4290.0,
        'stop_loss': 4295.0,  # Current confirmation SL
        'take_profit': 0.0
    }
    
    print(f"  Setup: Candle confirmation stop active (6 minutes old)")
    print(f"  Original SL: 4285.0")
    print(f"  Confirmation SL: 4295.0")
    print(f"  Disable Flag: {trader.disable_candle_confirmation_revert}")
    
    # Test the revert check (should be disabled)
    revert_result = trader.check_candle_confirmation_stop_revert()
    confirmation_stop_after = trader.candle_confirmation_stop
    
    print(f"  Revert Result: {revert_result}")
    print(f"  Expected: False (no revert due to disabled)")
    print(f"  Confirmation Stop After: {confirmation_stop_after}")
    print(f"  Expected: None (cleared when disabled)")
    
    revert_correct = revert_result == False
    cleared_correct = confirmation_stop_after is None
    
    print(f"  Revert Correct: {'✅' if revert_correct else '❌'}")
    print(f"  Cleared Correct: {'✅' if cleared_correct else '❌'}")
    
    disabled_behavior_correct = revert_correct and cleared_correct
    test_results.append(("Disabled Revert Behavior", disabled_behavior_correct))
    print(f"  Result: {'✅ PASSED' if disabled_behavior_correct else '❌ FAILED'}")
    
    # Test 3: Test enabled revert behavior (for comparison)
    print(f"\n📊 Test 3: Enabled Revert Behavior (Comparison)")
    print("-" * 40)
    
    # Create a new trader instance and enable reverting
    trader_enabled = FixedLiveTrader("XAUUSD!")
    trader_enabled.disable_candle_confirmation_revert = False  # Enable reverting
    
    # Set up the same mock data
    trader_enabled.candle_confirmation_stop = {
        'confirmation_sl': 4295.0,
        'original_sl': 4285.0,
        'candle_close_time': datetime.now() - timedelta(minutes=6),  # 6 minutes ago
        'position_type': 'BUY',
        'confirmation_candle': {'high': 4290.0, 'low': 4280.0}
    }
    
    trader_enabled.current_position = {
        'type': 'BUY',
        'ticket': 12345,
        'time': datetime.now(),
        'volume': 0.10,
        'remaining_volume': 0.10,
        'price': 4290.0,
        'stop_loss': 4295.0,
        'take_profit': 0.0
    }
    
    print(f"  Setup: Same configuration but reverting ENABLED")
    print(f"  Disable Flag: {trader_enabled.disable_candle_confirmation_revert}")
    
    # Mock the MT5 operations for the enabled test
    with patch.object(trader_enabled.mt5_manager, 'get_positions', return_value=[trader_enabled.current_position]), \
         patch.object(trader_enabled.mt5_manager, 'get_symbol_info_tick', return_value={'bid': 4292.0, 'ask': 4292.5}), \
         patch.object(trader_enabled.mt5_manager, 'modify_position', return_value=True):
        
        # Test the revert check (should proceed with revert logic)
        revert_result_enabled = trader_enabled.check_candle_confirmation_stop_revert()
        confirmation_stop_after_enabled = trader_enabled.candle_confirmation_stop
    
    print(f"  Revert Result: {revert_result_enabled}")
    print(f"  Expected: True (revert logic executed)")
    print(f"  Confirmation Stop After: {confirmation_stop_after_enabled}")
    print(f"  Expected: None (cleared after revert)")
    
    enabled_revert_correct = revert_result_enabled == True
    enabled_cleared_correct = confirmation_stop_after_enabled is None
    
    print(f"  Revert Correct: {'✅' if enabled_revert_correct else '❌'}")
    print(f"  Cleared Correct: {'✅' if enabled_cleared_correct else '❌'}")
    
    enabled_behavior_correct = enabled_revert_correct and enabled_cleared_correct
    test_results.append(("Enabled Revert Behavior", enabled_behavior_correct))
    print(f"  Result: {'✅ PASSED' if enabled_behavior_correct else '❌ FAILED'}")
    
    # Test 4: Test no confirmation stop scenario
    print(f"\n📊 Test 4: No Confirmation Stop Scenario")
    print("-" * 40)
    
    # Create trader with no confirmation stop
    trader_no_stop = FixedLiveTrader("XAUUSD!")
    trader_no_stop.candle_confirmation_stop = None
    
    print(f"  Setup: No candle confirmation stop active")
    print(f"  Disable Flag: {trader_no_stop.disable_candle_confirmation_revert}")
    
    revert_result_no_stop = trader_no_stop.check_candle_confirmation_stop_revert()
    
    print(f"  Revert Result: {revert_result_no_stop}")
    print(f"  Expected: False (no confirmation stop to revert)")
    
    no_stop_correct = revert_result_no_stop == False
    test_results.append(("No Confirmation Stop", no_stop_correct))
    print(f"  Result: {'✅ PASSED' if no_stop_correct else '❌ FAILED'}")
    
    # Summary
    print(f"\n🎯 Test Summary")
    print("=" * 30)
    
    passed_tests = sum(1 for _, passed in test_results if passed)
    total_tests = len(test_results)
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed Tests: {passed_tests}")
    print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
    
    for test_name, passed in test_results:
        print(f"  {test_name}: {'✅ PASSED' if passed else '❌ FAILED'}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 DISABLE CANDLE CONFIRMATION REVERT SUCCESSFUL!")
        print("✅ Reverting is disabled by default")
        print("✅ Disabled state prevents revert analysis and execution")
        print("✅ Confirmation stop data is properly cleared when disabled")
        print("✅ Enabled state still works for comparison")
        print("✅ No confirmation stop scenario handled correctly")
        
        print(f"\n📊 EXPECTED BEHAVIOR IN LIVE TRADING:")
        print("  • Candle confirmation stops will be set normally")
        print("  • After 5+ minutes, revert check will be called")
        print("  • Revert check will see disabled flag and skip revert")
        print("  • Confirmation stop data will be cleared")
        print("  • No 'REVERT ANALYSIS' logs will appear")
        print("  • Position will keep the confirmation stop level")
        
    else:
        print(f"\n⚠️ SOME ISSUES REMAIN:")
        for test_name, passed in test_results:
            if not passed:
                print(f"  • {test_name} needs attention")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    try:
        success = test_disable_candle_confirmation_revert()
        exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
