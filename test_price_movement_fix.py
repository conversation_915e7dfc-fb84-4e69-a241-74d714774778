#!/usr/bin/env python3
"""
Test script to verify the price movement fix for pending orders
Tests scenarios where price moves during analysis delay
"""

import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader

def test_price_movement_scenarios():
    """Test pending order placement when price moves during analysis delay"""
    print("🧪 TESTING PRICE MOVEMENT FIX FOR PENDING ORDERS")
    print("=" * 70)
    
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test scenarios
    test_scenarios = [
        {
            'name': 'SELL STOP - Price moved UP (your exact scenario)',
            'signal': 'SELL',
            'confirmation_candle': {'high': 4295.00, 'low': 4293.20, 'close': 4294.00},
            'current_bid': 4294.50,  # Price moved above intended entry (4293.19)
            'current_ask': 4294.60,
            'expected_behavior': 'Adjust entry price below current bid'
        },
        {
            'name': 'SELL STOP - Normal scenario (no adjustment needed)',
            'signal': 'SELL',
            'confirmation_candle': {'high': 4295.00, 'low': 4293.20, 'close': 4294.00},
            'current_bid': 4292.50,  # Price below intended entry (4293.19) - OK
            'current_ask': 4292.60,
            'expected_behavior': 'Use original entry price'
        },
        {
            'name': 'BUY STOP - Price moved DOWN (opposite scenario)',
            'signal': 'BUY',
            'confirmation_candle': {'high': 4295.00, 'low': 4293.20, 'close': 4294.00},
            'current_bid': 4294.50,  # Price below intended entry (4295.01) - Invalid
            'current_ask': 4294.60,
            'expected_behavior': 'Adjust entry price above current ask'
        },
        {
            'name': 'BUY STOP - Normal scenario (no adjustment needed)',
            'signal': 'BUY',
            'confirmation_candle': {'high': 4295.00, 'low': 4293.20, 'close': 4294.00},
            'current_bid': 4296.50,  # Price above intended entry (4295.01) - OK
            'current_ask': 4296.60,
            'expected_behavior': 'Use original entry price'
        }
    ]
    
    all_passed = True
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"🧪 TEST {i}: {scenario['name']}")
        print("-" * 60)
        
        # Mock MT5 manager for this scenario
        class MockMT5Manager:
            def __init__(self, bid, ask):
                self.bid = bid
                self.ask = ask
            
            def get_symbol_info_tick(self, symbol):
                return {
                    'bid': self.bid,
                    'ask': self.ask
                }
            
            def get_pip_size(self, symbol):
                return 0.01  # XAUUSD pip size
            
            def place_order(self, symbol, order_type, volume, price, sl, tp, comment):
                print(f"   📝 MT5 ORDER PLACED:")
                print(f"      Type: {order_type}")
                print(f"      Entry Price: {price:.5f}")
                print(f"      Stop Loss: {sl:.5f}")
                print(f"      Comment: {comment}")
                return 12345  # Mock ticket
        
        trader.mt5_manager = MockMT5Manager(scenario['current_bid'], scenario['current_ask'])
        
        # Calculate expected prices
        pip_size = 0.01
        if scenario['signal'] == 'SELL':
            original_entry = scenario['confirmation_candle']['low'] - pip_size
            print(f"   📊 Original SELL STOP entry: {original_entry:.5f}")
            print(f"   📊 Current bid: {scenario['current_bid']:.5f}")
            if original_entry >= scenario['current_bid']:
                expected_entry = scenario['current_bid'] - (pip_size * 2)
                print(f"   ⚠️ Price moved - Expected adjusted entry: {expected_entry:.5f}")
            else:
                expected_entry = original_entry
                print(f"   ✅ No adjustment needed - Expected entry: {expected_entry:.5f}")
        else:  # BUY
            original_entry = scenario['confirmation_candle']['high'] + pip_size
            print(f"   📊 Original BUY STOP entry: {original_entry:.5f}")
            print(f"   📊 Current ask: {scenario['current_ask']:.5f}")
            if original_entry <= scenario['current_ask']:
                expected_entry = scenario['current_ask'] + (pip_size * 2)
                print(f"   ⚠️ Price moved - Expected adjusted entry: {expected_entry:.5f}")
            else:
                expected_entry = original_entry
                print(f"   ✅ No adjustment needed - Expected entry: {expected_entry:.5f}")
        
        print(f"   🎯 Expected behavior: {scenario['expected_behavior']}")
        print()
        
        # Test the function
        try:
            result = trader.place_pending_order_from_confirmation_candle(
                scenario['signal'], 
                scenario['confirmation_candle'], 
                0.01,  # volume
                10.0   # atr_value
            )
            
            if result:
                print(f"   ✅ Order placed successfully")
                # Check if pending order was added
                if trader.pending_orders:
                    actual_entry = trader.pending_orders[-1]['price']
                    print(f"   📋 Actual entry price: {actual_entry:.5f}")
                    
                    # Verify the price adjustment worked
                    if abs(actual_entry - expected_entry) < 0.00001:
                        print(f"   ✅ Price adjustment correct!")
                    else:
                        print(f"   ❌ Price adjustment incorrect - Expected: {expected_entry:.5f}, Got: {actual_entry:.5f}")
                        all_passed = False
                else:
                    print(f"   ❌ No pending order created")
                    all_passed = False
            else:
                print(f"   ❌ Order placement failed")
                all_passed = False
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            all_passed = False
        
        # Clear pending orders for next test
        trader.pending_orders.clear()
        print()
    
    print("=" * 70)
    print("🏁 FINAL RESULTS:")
    if all_passed:
        print("🎯 ALL TESTS PASSED! ✅")
        print("\n📋 PRICE MOVEMENT FIX SUMMARY:")
        print("• ✅ SELL STOP orders: Adjusted below current bid when price moves up")
        print("• ✅ BUY STOP orders: Adjusted above current ask when price moves down")
        print("• ✅ Normal scenarios: Use original entry prices when valid")
        print("• ✅ Real-time validation: Prevents 'Invalid price' errors")
        print("\n🚀 Your system will now handle analysis delays gracefully!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("⚠️ Please review the implementation.")
    
    return all_passed

if __name__ == "__main__":
    test_price_movement_scenarios()
