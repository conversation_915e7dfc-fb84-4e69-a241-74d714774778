#!/usr/bin/env python3
"""
Test the partial confirmation logic specifically
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_partial_confirmation_logic():
    """Test the partial confirmation logic with specific scenarios"""
    print("🧪 TESTING PARTIAL CONFIRMATION LOGIC")
    print("=" * 60)
    
    # Create trader instance
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test Scenario 1: Most recent candle is clearly a swing high
    print("\n📈 SCENARIO 1: Clear Swing High at Most Recent Candle")
    dates = pd.date_range(start='2024-01-01', periods=10, freq='5min')
    
    # Create data where most recent candle is highest
    highs = [2000, 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2020]  # Last is highest
    lows = [h - 3 for h in highs]
    closes = [h - 1.5 for h in highs]
    
    df1 = pd.DataFrame({
        'time': dates,
        'open': closes,
        'high': highs,
        'low': lows,
        'close': closes,
        'tick_volume': [100] * 10
    }).set_index('time')
    
    print("Data (last 5 candles):")
    for i in range(5):
        idx = -(5-i)
        candle = df1.iloc[idx]
        print(f"  {len(df1) + idx:2d}: H={candle['high']:4.0f} L={candle['low']:4.0f}")
    
    # Test left-side strength calculation
    strength = trader.calculate_left_side_strength_high(df1.iloc[:-1], len(df1.iloc[:-1]) - 1)
    print(f"\nLeft-side confirmation strength: {strength}")
    print(f"Required strength: 2")
    print(f"Qualifies for partial confirmation: {strength >= 2}")
    
    # Run swing detection
    swing_points = trader.find_recent_swing_points(df1)
    print(f"\nSwing detection result:")
    if swing_points['recent_high']:
        print(f"✅ Found swing high: {swing_points['recent_high']:.0f} ({swing_points['recent_high_candles_ago']} candles ago)")
    else:
        print("❌ No swing high found")
    
    # Test Scenario 2: Most recent candle is clearly a swing low
    print("\n📉 SCENARIO 2: Clear Swing Low at Most Recent Candle")
    
    # Create data where most recent candle is lowest
    highs2 = [2020, 2019, 2018, 2017, 2016, 2015, 2014, 2013, 2012, 2000]  # Last is lowest
    lows2 = [h - 3 for h in highs2]
    lows2[-1] = 1990  # Make last candle much lower
    closes2 = [h - 1.5 for h in highs2]
    
    df2 = pd.DataFrame({
        'time': dates,
        'open': closes2,
        'high': highs2,
        'low': lows2,
        'close': closes2,
        'tick_volume': [100] * 10
    }).set_index('time')
    
    print("Data (last 5 candles):")
    for i in range(5):
        idx = -(5-i)
        candle = df2.iloc[idx]
        print(f"  {len(df2) + idx:2d}: H={candle['high']:4.0f} L={candle['low']:4.0f}")
    
    # Test left-side strength calculation
    strength_low = trader.calculate_left_side_strength_low(df2.iloc[:-1], len(df2.iloc[:-1]) - 1)
    print(f"\nLeft-side confirmation strength: {strength_low}")
    print(f"Required strength: 2")
    print(f"Qualifies for partial confirmation: {strength_low >= 2}")
    
    # Run swing detection
    swing_points2 = trader.find_recent_swing_points(df2)
    print(f"\nSwing detection result:")
    if swing_points2['recent_low']:
        print(f"✅ Found swing low: {swing_points2['recent_low']:.0f} ({swing_points2['recent_low_candles_ago']} candles ago)")
    else:
        print("❌ No swing low found")
    
    # Test Scenario 3: Most recent candle doesn't qualify (weak left-side)
    print("\n➡️ SCENARIO 3: Weak Left-Side Confirmation (Should NOT Qualify)")
    
    # Create data where most recent candle is only slightly higher
    highs3 = [2000, 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009]  # Gradual rise
    lows3 = [h - 3 for h in highs3]
    closes3 = [h - 1.5 for h in highs3]
    
    df3 = pd.DataFrame({
        'time': dates,
        'open': closes3,
        'high': highs3,
        'low': lows3,
        'close': closes3,
        'tick_volume': [100] * 10
    }).set_index('time')
    
    print("Data (last 5 candles):")
    for i in range(5):
        idx = -(5-i)
        candle = df3.iloc[idx]
        print(f"  {len(df3) + idx:2d}: H={candle['high']:4.0f} L={candle['low']:4.0f}")
    
    # Test left-side strength calculation
    strength_weak = trader.calculate_left_side_strength_high(df3.iloc[:-1], len(df3.iloc[:-1]) - 1)
    print(f"\nLeft-side confirmation strength: {strength_weak}")
    print(f"Required strength: 2")
    print(f"Qualifies for partial confirmation: {strength_weak >= 2}")
    
    # Run swing detection
    swing_points3 = trader.find_recent_swing_points(df3)
    print(f"\nSwing detection result:")
    if swing_points3['recent_high']:
        print(f"Found swing high: {swing_points3['recent_high']:.0f} ({swing_points3['recent_high_candles_ago']} candles ago)")
    else:
        print("✅ Correctly rejected weak swing high")
    
    print(f"\n✅ PARTIAL CONFIRMATION TESTS COMPLETE")
    print("=" * 60)
    print("🎉 The improved swing detection can now identify swing points at the most recent candle!")
    print("📊 It requires strong left-side confirmation (≥2 candles) to avoid false signals")
    print("🔍 This allows catching swing points that traditional methods would miss")

if __name__ == "__main__":
    test_partial_confirmation_logic()
