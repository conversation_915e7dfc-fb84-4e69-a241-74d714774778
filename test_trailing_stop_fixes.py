#!/usr/bin/env python3
"""
Test script to verify trailing stop and partial exit fixes
"""

import sys
from datetime import datetime

# Add src to path
sys.path.append('src')
from fixed_live_trader import FixedLiveTrader

def test_trailing_stop_validation():
    """Test the new validation logic to prevent 'No changes' errors"""
    print("🧪 TRAILING STOP VALIDATION TEST")
    print("=" * 45)
    
    trader = FixedLiveTrader("XAUUSD!")
    
    # Mock pip size (XAUUSD typically 0.01)
    pip_size = 0.01
    
    # Test scenarios for validation
    scenarios = [
        {
            'name': 'Same SL (should skip)',
            'current_sl': 4200.00,
            'new_sl': 4200.00,
            'should_skip': True
        },
        {
            'name': 'Very close SL (should skip)',
            'current_sl': 4200.00,
            'new_sl': 4200.005,  # 0.5 pip difference
            'should_skip': True
        },
        {
            'name': 'Valid SL difference (should proceed)',
            'current_sl': 4200.00,
            'new_sl': 4200.50,  # 50 pip difference
            'should_skip': False
        },
        {
            'name': 'Large SL difference (should proceed)',
            'current_sl': 4200.00,
            'new_sl': 4203.00,  # 300 pip difference
            'should_skip': False
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📊 {scenario['name']}:")
        current_sl = scenario['current_sl']
        new_sl = scenario['new_sl']
        
        # Test the validation logic
        difference = abs(new_sl - current_sl)
        threshold = pip_size * 0.1  # 0.1 pip threshold
        should_skip = difference < threshold
        
        print(f"   Current SL: {current_sl:.5f}")
        print(f"   New SL: {new_sl:.5f}")
        print(f"   Difference: {difference:.5f}")
        print(f"   Threshold: {threshold:.5f}")
        print(f"   Should Skip: {should_skip}")
        print(f"   Expected: {scenario['should_skip']}")
        
        if should_skip == scenario['should_skip']:
            print(f"   ✅ Validation logic correct")
        else:
            print(f"   ❌ Validation logic incorrect")

def test_candle_confirmation_logging():
    """Test the improved logging for candle confirmation trailing stops"""
    print("\n🎯 CANDLE CONFIRMATION LOGGING TEST")
    print("=" * 45)
    
    # Test data
    scenarios = [
        {
            'position_type': 'BUY',
            'current_sl': 4195.00,
            'candle_low': 4210.00,
            'pip_size': 0.01,
            'expected_new_sl': 4210.00 - (0.01 * 10)  # 10 pips below
        },
        {
            'position_type': 'SELL',
            'current_sl': 4215.00,
            'candle_high': 4212.44,
            'pip_size': 0.01,
            'expected_new_sl': 4212.44 + (0.01 * 10)  # 10 pips above
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📊 {scenario['position_type']} Position:")
        current_sl = scenario['current_sl']
        pip_size = scenario['pip_size']
        
        if scenario['position_type'] == 'BUY':
            candle_low = scenario['candle_low']
            new_sl = candle_low - (pip_size * 10)
            print(f"   Current SL: {current_sl:.5f}")
            print(f"   Candle Low: {candle_low:.5f}")
            print(f"   New SL: {new_sl:.5f} (10 pips below candle low)")
        else:
            candle_high = scenario['candle_high']
            new_sl = candle_high + (pip_size * 10)
            print(f"   Current SL: {current_sl:.5f}")
            print(f"   Candle High: {candle_high:.5f}")
            print(f"   New SL: {new_sl:.5f} (10 pips above candle high)")
        
        expected_new_sl = scenario['expected_new_sl']
        if abs(new_sl - expected_new_sl) < 0.00001:
            print(f"   ✅ Calculation correct")
        else:
            print(f"   ❌ Calculation incorrect (expected {expected_new_sl:.5f})")

def test_atr_trailing_logic():
    """Test the ATR-based trailing stop logic"""
    print("\n🔄 ATR TRAILING STOP LOGIC TEST")
    print("=" * 40)
    
    # Test scenarios
    scenarios = [
        {
            'name': 'BUY - 1.5 ATR profit (should trail)',
            'position_type': 'BUY',
            'entry_price': 4200.00,
            'current_price': 4204.50,
            'current_sl': 4195.00,
            'atr_value': 3.0,
            'profit_atr_count': 0,
            'should_trail': True
        },
        {
            'name': 'SELL - 2.2 ATR profit (should trail twice)',
            'position_type': 'SELL',
            'entry_price': 4200.00,
            'current_price': 4193.40,
            'current_sl': 4204.50,
            'atr_value': 3.0,
            'profit_atr_count': 0,
            'should_trail': True,
            'expected_trails': 2
        },
        {
            'name': 'BUY - 0.8 ATR profit (should not trail)',
            'position_type': 'BUY',
            'entry_price': 4200.00,
            'current_price': 4202.40,
            'current_sl': 4195.00,
            'atr_value': 3.0,
            'profit_atr_count': 0,
            'should_trail': False
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📊 {scenario['name']}:")
        
        position_type = scenario['position_type']
        entry_price = scenario['entry_price']
        current_price = scenario['current_price']
        current_sl = scenario['current_sl']
        atr_value = scenario['atr_value']
        profit_atr_count = scenario['profit_atr_count']
        
        # Calculate profit in ATR units
        if position_type == 'BUY':
            profit_points = current_price - entry_price
        else:
            profit_points = entry_price - current_price
        
        profit_atr = profit_points / atr_value
        
        print(f"   Entry: {entry_price}")
        print(f"   Current: {current_price}")
        print(f"   Profit: {profit_points:.2f} points ({profit_atr:.2f} ATR)")
        print(f"   Current SL: {current_sl:.5f}")
        print(f"   ATR Count: {profit_atr_count}")
        
        # Check if should trail
        should_trail = profit_atr >= (profit_atr_count + 1)
        print(f"   Should Trail: {should_trail}")
        print(f"   Expected: {scenario['should_trail']}")
        
        if should_trail == scenario['should_trail']:
            print(f"   ✅ Trailing logic correct")
            
            if should_trail:
                # Calculate new SL
                if position_type == 'BUY':
                    new_sl = current_sl + atr_value
                else:
                    new_sl = current_sl - atr_value
                
                print(f"   New SL: {new_sl:.5f}")
                
                # Check for multiple trails
                if 'expected_trails' in scenario:
                    total_trails = int(profit_atr)
                    print(f"   Total Trails: {total_trails}")
                    print(f"   Expected Trails: {scenario['expected_trails']}")
                    
                    if total_trails == scenario['expected_trails']:
                        print(f"   ✅ Multiple trail calculation correct")
                    else:
                        print(f"   ❌ Multiple trail calculation incorrect")
        else:
            print(f"   ❌ Trailing logic incorrect")

def main():
    """Run all trailing stop fix tests"""
    print("🚀 TRAILING STOP & PARTIAL EXIT FIXES VERIFICATION")
    print("=" * 65)
    print(f"⏰ Test Time: {datetime.now()}")
    print()
    
    # Test 1: Validation logic
    test_trailing_stop_validation()
    
    # Test 2: Candle confirmation logging
    test_candle_confirmation_logging()
    
    # Test 3: ATR trailing logic
    test_atr_trailing_logic()
    
    print("\n📊 SUMMARY OF FIXES APPLIED")
    print("=" * 35)
    print("✅ FIXED: Added current SL logging to candle confirmation trailing")
    print("✅ FIXED: Added validation to prevent 'No changes' MT5 errors")
    print("✅ FIXED: Improved error handling in MT5 integration")
    print("✅ FIXED: Added validation to ATR-based trailing stops")
    print("✅ FIXED: Enhanced logging for better debugging")
    print()
    print("🎯 Key improvements:")
    print("   • Current SL vs New SL comparison in logs")
    print("   • Skip modification if difference < 0.1 pip")
    print("   • Better MT5 error messages for 'No changes' (10025)")
    print("   • Validation prevents unnecessary MT5 calls")
    print("   • Trailing stop data updated before MT5 modification")
    print()
    print("📝 Expected behavior:")
    print("   • No more 'No changes' errors from MT5")
    print("   • Clear logging of SL modifications")
    print("   • Partial exits trigger when trailing stops succeed")
    print("   • Enhanced revert logic works with proper trailing initialization")

if __name__ == "__main__":
    main()
