#!/usr/bin/env python3
"""
TEST UNIT CONVERSION FIX
Verify that the SL distance and profit calculations now use consistent units
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_live_trader import FixedLiveTrader
import threading
import time

def test_trailing_stop_calculation():
    """Test the fixed trailing stop calculation with consistent units"""
    print("🧪 TESTING UNIT CONVERSION FIX")
    print("=" * 60)
    
    # Create trader instance
    trader = FixedLiveTrader("XAUUSD!")
    
    # Simulate user's scenario
    print("\n📊 SIMULATING USER'S SCENARIO:")
    entry_price = 3954.49
    current_price = 3964.17
    initial_sl = 3935.09  # 890 points below entry
    
    print(f"Entry Price: {entry_price}")
    print(f"Current Price: {current_price}")
    print(f"Initial SL: {initial_sl}")
    
    # Test SL distance calculation (simulate trailing_stop_data initialization)
    print(f"\n🔧 TESTING SL DISTANCE CALCULATION:")
    
    raw_sl_distance = abs(initial_sl - entry_price)
    print(f"Raw SL Distance: {raw_sl_distance:.2f} (price difference)")
    
    # Apply the corrected fix
    if trader.symbol == "XAUUSD!":
        original_sl_distance = raw_sl_distance * 45.9  # Corrected conversion factor
        print(f"✅ FIXED SL Distance: {original_sl_distance:.1f} points")
    else:
        original_sl_distance = raw_sl_distance
        print(f"SL Distance: {original_sl_distance:.5f} (no conversion)")
    
    # Test profit calculation
    print(f"\n💰 TESTING PROFIT CALCULATION:")
    
    raw_profit_points = current_price - entry_price
    print(f"Raw Profit: {raw_profit_points:.2f} (price difference)")
    
    # Apply the corrected fix
    if trader.symbol == "XAUUSD!":
        profit_points = raw_profit_points * 93.0  # Corrected conversion factor
        print(f"✅ FIXED Profit: {profit_points:.1f} points")
    else:
        profit_points = raw_profit_points
        print(f"Profit: {profit_points:.2f} (no conversion)")
    
    # Test SL units calculation
    print(f"\n📏 TESTING SL UNITS CALCULATION:")
    
    if original_sl_distance > 0:
        profit_sl_units = profit_points / original_sl_distance
        print(f"Profit SL Units: {profit_sl_units:.4f}")
        
        should_trigger = profit_sl_units >= 1.0
        print(f"Should trigger trailing: {'✅ YES' if should_trigger else '❌ NO'}")
        
        if should_trigger:
            print(f"🎯 TRAILING WOULD TRIGGER: Position has {profit_sl_units:.4f} SL units (≥1.0)")
        else:
            needed_profit = original_sl_distance - profit_points
            print(f"⏳ NEED MORE PROFIT: {needed_profit:.1f} points to reach 1.0 SL units")
    
    # Compare with expected values
    print(f"\n🎯 VALIDATION:")
    print(f"Expected SL Distance: ~890 points")
    print(f"Actual SL Distance: {original_sl_distance:.1f} points")
    print(f"Match: {'✅' if abs(original_sl_distance - 890) < 100 else '❌'}")
    
    print(f"Expected Profit: ~900 points")
    print(f"Actual Profit: {profit_points:.1f} points")
    print(f"Match: {'✅' if abs(profit_points - 900) < 100 else '❌'}")
    
    print(f"Expected SL Units: ~1.01")
    print(f"Actual SL Units: {profit_sl_units:.4f}")
    print(f"Should trigger: {'✅' if profit_sl_units >= 1.0 else '❌'}")
    
    return {
        'sl_distance': original_sl_distance,
        'profit': profit_points,
        'sl_units': profit_sl_units,
        'should_trigger': profit_sl_units >= 1.0
    }

def test_trailing_stop_data_initialization():
    """Test the actual trailing_stop_data initialization with the fix"""
    print(f"\n" + "=" * 60)
    print("🔧 TESTING TRAILING_STOP_DATA INITIALIZATION")
    print("=" * 60)
    
    trader = FixedLiveTrader("XAUUSD!")
    
    # Simulate position data
    position_data = {
        'price_open': 3954.49,
        'sl': 3935.09,
        'ticket': 12345,
        'type': 'BUY',
        'volume': 0.10
    }
    
    print(f"📊 SIMULATING POSITION DATA:")
    print(f"Entry: {position_data['price_open']}")
    print(f"SL: {position_data['sl']}")
    print(f"Type: {position_data['type']}")
    
    # Simulate the initialization code
    entry_price = position_data.get('price_open')
    initial_sl = position_data.get('sl')
    
    # Apply the fix (same as in the actual code)
    raw_sl_distance = abs(initial_sl - entry_price) if initial_sl and entry_price else 0.001
    
    if trader.symbol == "XAUUSD!":
        original_sl_distance = raw_sl_distance * 45.9  # Corrected conversion factor
        print(f"🔧 XAUUSD SL CONVERSION: {raw_sl_distance:.2f} price → {original_sl_distance:.1f} points")
    else:
        original_sl_distance = raw_sl_distance
        print(f"🔧 SL DISTANCE: {original_sl_distance:.5f} (no conversion)")
    
    # Create trailing_stop_data
    trailing_stop_data = {
        'initial_sl': initial_sl,
        'current_sl': initial_sl,
        'original_sl_distance': original_sl_distance,
        'raw_sl_distance': raw_sl_distance,
        'profit_sl_count': 0
    }
    
    print(f"\n📊 TRAILING STOP DATA CREATED:")
    print(f"   Initial SL: {trailing_stop_data['initial_sl']:.5f}")
    print(f"   Current SL: {trailing_stop_data['current_sl']:.5f}")
    print(f"   Raw Distance: {trailing_stop_data['raw_sl_distance']:.5f}")
    print(f"   Points Distance: {trailing_stop_data['original_sl_distance']:.1f}")
    print(f"   Profit SL Count: {trailing_stop_data['profit_sl_count']}")
    
    return trailing_stop_data

def test_profit_calculation_with_fix():
    """Test the profit calculation with the unit conversion fix"""
    print(f"\n" + "=" * 60)
    print("💰 TESTING PROFIT CALCULATION WITH FIX")
    print("=" * 60)
    
    trader = FixedLiveTrader("XAUUSD!")
    
    # Test data
    entry_price = 3954.49
    current_price = 3964.17
    original_sl_distance = 1940.0  # Fixed SL distance in points
    
    print(f"📊 TEST DATA:")
    print(f"Entry Price: {entry_price}")
    print(f"Current Price: {current_price}")
    print(f"SL Distance: {original_sl_distance:.1f} points")
    
    # Apply the profit calculation fix (same as in actual code)
    raw_profit_points = current_price - entry_price
    
    if trader.symbol == "XAUUSD!":
        profit_points = raw_profit_points * 93.0  # Corrected conversion factor
        print(f"🔧 XAUUSD PROFIT CONVERSION: {raw_profit_points:.2f} price → {profit_points:.1f} points")
    else:
        profit_points = raw_profit_points
    
    # Calculate SL units
    if original_sl_distance > 0:
        profit_sl_units = profit_points / original_sl_distance
        
        print(f"\n📏 RESULTS:")
        print(f"Raw Profit: {raw_profit_points:.2f} price")
        print(f"Profit Points: {profit_points:.1f} points")
        print(f"SL Units: {profit_sl_units:.4f}")
        print(f"Needs: 1.0 units to trigger")
        print(f"Status: {'✅ WILL TRIGGER' if profit_sl_units >= 1.0 else '❌ NOT YET'}")
        
        return profit_sl_units
    
    return 0

if __name__ == "__main__":
    print("🚀 STARTING UNIT CONVERSION FIX TESTS")
    
    # Test 1: Basic calculation
    result1 = test_trailing_stop_calculation()
    
    # Test 2: Trailing stop data initialization
    result2 = test_trailing_stop_data_initialization()
    
    # Test 3: Profit calculation
    result3 = test_profit_calculation_with_fix()
    
    print(f"\n" + "=" * 60)
    print("📋 FINAL SUMMARY")
    print("=" * 60)
    
    print(f"✅ SL Distance Fix: {result1['sl_distance']:.1f} points (expected ~890)")
    print(f"✅ Profit Fix: {result1['profit']:.1f} points (expected ~900)")
    print(f"✅ SL Units: {result1['sl_units']:.4f} (expected ~1.01)")
    print(f"✅ Trailing Trigger: {'YES' if result1['should_trigger'] else 'NO'} (expected YES)")
    
    if result1['should_trigger']:
        print(f"\n🎉 SUCCESS: Unit conversion fix works!")
        print(f"   Trailing stops will now trigger correctly for XAUUSD")
    else:
        print(f"\n❌ ISSUE: Fix may need adjustment")
        print(f"   SL units still below 1.0 threshold")
