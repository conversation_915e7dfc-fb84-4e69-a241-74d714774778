"""
Data Management Module
Handles data loading, updating, validation, and preprocessing
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Optional, Tuple, List, Dict
import os
from pathlib import Path

from config.config import *
from src.mt5_integration import MT5Manager

# Set up logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

class DataManager:
    """
    Manages all data operations including loading, updating, validation, and preprocessing
    """
    
    def __init__(self):
        self.mt5_manager = MT5Manager()
        self.data = None
        self.updated_data = None
        
    def load_historical_data(self) -> Optional[pd.DataFrame]:
        """
        Load historical data from CSV file
        
        Returns:
            DataFrame with historical OHLCV data
        """
        try:
            if not DATA_FILE.exists():
                logger.error(f"Data file not found: {DATA_FILE}")
                return None
            
            # Load data
            df = pd.read_csv(DATA_FILE)
            
            # Convert datetime column
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)
            
            # Sort by datetime
            df.sort_index(inplace=True)
            
            # Validate data structure
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_columns):
                logger.error(f"Missing required columns. Expected: {required_columns}")
                return None
            
            # Remove duplicates
            df = df[~df.index.duplicated(keep='last')]
            
            # Basic data validation
            df = self._validate_ohlc_data(df)
            
            logger.info(f"Loaded {len(df)} historical records from {df.index.min()} to {df.index.max()}")
            self.data = df
            return df
            
        except Exception as e:
            logger.error(f"Error loading historical data: {e}")
            return None
    
    def update_data_from_mt5(self, login: Optional[int] = None, 
                           password: Optional[str] = None, 
                           server: Optional[str] = None) -> bool:
        """
        Update data with latest bars from MT5
        
        Args:
            login: MT5 login
            password: MT5 password
            server: MT5 server
            
        Returns:
            bool: True if update successful
        """
        try:
            # Connect to MT5
            if not self.mt5_manager.connect(login, password, server):
                logger.error("Failed to connect to MT5")
                return False
            
            # Load existing data
            if self.data is None:
                self.load_historical_data()
            
            if self.data is None or len(self.data) == 0:
                logger.error("No historical data available")
                return False
            
            # Get last datetime from existing data
            last_datetime = self.data.index.max()
            current_time = datetime.now()
            
            logger.info(f"Updating data from {last_datetime} to {current_time}")
            
            # Get new data from MT5
            new_data = self.mt5_manager.get_historical_data(
                SYMBOL, TIMEFRAME, last_datetime, current_time
            )
            
            if new_data is None or len(new_data) == 0:
                logger.info("No new data available from MT5")
                self.mt5_manager.disconnect()
                return True
            
            # Remove the last bar from existing data to avoid overlap
            existing_data = self.data[self.data.index < last_datetime]
            
            # Combine data
            combined_data = pd.concat([existing_data, new_data])
            combined_data = combined_data[~combined_data.index.duplicated(keep='last')]
            combined_data.sort_index(inplace=True)
            
            # Validate combined data
            combined_data = self._validate_ohlc_data(combined_data)
            
            # Save updated data
            self.updated_data = combined_data
            self._save_updated_data()
            
            logger.info(f"Data updated successfully. Total records: {len(combined_data)}")
            logger.info(f"Added {len(new_data)} new records")
            
            self.mt5_manager.disconnect()
            return True
            
        except Exception as e:
            logger.error(f"Error updating data from MT5: {e}")
            self.mt5_manager.disconnect()
            return False
    
    def _validate_ohlc_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Validate and clean OHLC data
        
        Args:
            df: DataFrame with OHLC data
            
        Returns:
            Cleaned DataFrame
        """
        try:
            # Remove rows with missing values
            initial_count = len(df)
            df = df.dropna()
            
            if len(df) < initial_count:
                logger.warning(f"Removed {initial_count - len(df)} rows with missing values")
            
            # Validate OHLC relationships
            invalid_ohlc = (
                (df['high'] < df['low']) |
                (df['high'] < df['open']) |
                (df['high'] < df['close']) |
                (df['low'] > df['open']) |
                (df['low'] > df['close'])
            )
            
            if invalid_ohlc.any():
                logger.warning(f"Found {invalid_ohlc.sum()} rows with invalid OHLC relationships")
                df = df[~invalid_ohlc]
            
            # Remove outliers (prices that are too far from median)
            for col in ['open', 'high', 'low', 'close']:
                median_price = df[col].median()
                std_price = df[col].std()
                outlier_threshold = OUTLIER_THRESHOLD * std_price
                
                outliers = (
                    (df[col] > median_price + outlier_threshold) |
                    (df[col] < median_price - outlier_threshold)
                )
                
                if outliers.any():
                    logger.warning(f"Found {outliers.sum()} outliers in {col} column")
                    df = df[~outliers]
            
            # Ensure positive volume
            df = df[df['volume'] > 0]
            
            return df
            
        except Exception as e:
            logger.error(f"Error validating OHLC data: {e}")
            return df
    
    def _save_updated_data(self):
        """Save updated data to CSV file"""
        try:
            if self.updated_data is not None:
                # Reset index to save datetime as column
                df_to_save = self.updated_data.reset_index()
                df_to_save.to_csv(UPDATED_DATA_FILE, index=False)
                logger.info(f"Updated data saved to {UPDATED_DATA_FILE}")
                
        except Exception as e:
            logger.error(f"Error saving updated data: {e}")
    
    def get_data_for_training(self, years: int = MAX_DATA_YEARS) -> Optional[pd.DataFrame]:
        """
        Get data suitable for training (last N years)
        
        Args:
            years: Number of years of data to use
            
        Returns:
            DataFrame with training data
        """
        try:
            # Use updated data if available, otherwise historical data
            data = self.updated_data if self.updated_data is not None else self.data
            
            if data is None:
                logger.error("No data available")
                return None
            
            # Get data from last N years
            end_date = data.index.max()
            start_date = end_date - timedelta(days=years * 365)
            
            training_data = data[data.index >= start_date].copy()
            
            if len(training_data) < MIN_DATA_POINTS:
                logger.warning(f"Insufficient data points: {len(training_data)} < {MIN_DATA_POINTS}")
                logger.info("Using all available data")
                training_data = data.copy()
            
            logger.info(f"Training data: {len(training_data)} records from {training_data.index.min()} to {training_data.index.max()}")
            
            return training_data
            
        except Exception as e:
            logger.error(f"Error getting training data: {e}")
            return None
    
    def check_data_gaps(self, df: pd.DataFrame) -> List[Tuple[datetime, datetime]]:
        """
        Check for gaps in time series data
        
        Args:
            df: DataFrame with datetime index
            
        Returns:
            List of tuples with gap start and end times
        """
        try:
            gaps = []
            
            # Expected time difference (5 minutes for M5 data)
            expected_diff = timedelta(minutes=5)
            
            # Find gaps larger than expected
            time_diffs = df.index.to_series().diff()
            large_gaps = time_diffs > expected_diff * 2  # Allow for some flexibility
            
            gap_starts = df.index[large_gaps]
            
            for gap_start in gap_starts:
                gap_start_idx = df.index.get_loc(gap_start)
                if gap_start_idx > 0:
                    gap_end = df.index[gap_start_idx - 1]
                    gaps.append((gap_end, gap_start))
            
            if gaps:
                logger.info(f"Found {len(gaps)} data gaps")
                for start, end in gaps:
                    duration = end - start
                    logger.info(f"Gap from {start} to {end} (duration: {duration})")
            
            return gaps
            
        except Exception as e:
            logger.error(f"Error checking data gaps: {e}")
            return []
    
    def get_latest_data_for_prediction(self, count: int = SEQUENCE_LENGTH) -> Optional[pd.DataFrame]:
        """
        Get latest data for live prediction
        
        Args:
            count: Number of latest bars to get
            
        Returns:
            DataFrame with latest data
        """
        try:
            if not self.mt5_manager.connected:
                logger.error("MT5 not connected")
                return None
            
            latest_data = self.mt5_manager.get_latest_data(SYMBOL, TIMEFRAME, count)
            
            if latest_data is None:
                logger.error("Failed to get latest data from MT5")
                return None
            
            # Validate data
            latest_data = self._validate_ohlc_data(latest_data)
            
            return latest_data
            
        except Exception as e:
            logger.error(f"Error getting latest data for prediction: {e}")
            return None
    
    def get_data_summary(self, df: pd.DataFrame) -> Dict:
        """
        Get summary statistics of the data
        
        Args:
            df: DataFrame to analyze
            
        Returns:
            Dictionary with summary statistics
        """
        try:
            summary = {
                'total_records': len(df),
                'date_range': {
                    'start': df.index.min(),
                    'end': df.index.max(),
                    'duration_days': (df.index.max() - df.index.min()).days
                },
                'price_stats': {
                    'min_price': df[['open', 'high', 'low', 'close']].min().min(),
                    'max_price': df[['open', 'high', 'low', 'close']].max().max(),
                    'avg_close': df['close'].mean(),
                    'price_volatility': df['close'].std()
                },
                'volume_stats': {
                    'avg_volume': df['volume'].mean(),
                    'max_volume': df['volume'].max(),
                    'min_volume': df['volume'].min()
                },
                'data_quality': {
                    'missing_values': df.isnull().sum().sum(),
                    'duplicate_timestamps': df.index.duplicated().sum(),
                    'gaps_count': len(self.check_data_gaps(df))
                }
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting data summary: {e}")
            return {}
