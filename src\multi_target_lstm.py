#!/usr/bin/env python3
"""
Multi-target LSTM Model for Market Prediction
Predicts price, direction, and percentage changes for next N candles
"""

import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import (
    Input, LSTM, Dense, Dropout, BatchNormalization, 
    Concatenate, Lambda, Multiply, Add
)
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
import json
import os

class MultiTargetLSTM:
    """Multi-target LSTM model for market prediction"""
    
    def __init__(self, sequence_length=60, n_features=78, n_targets=18):
        self.sequence_length = sequence_length
        self.n_features = n_features
        self.n_targets = n_targets
        self.model = None
        
        # Target groups (from metadata) - NOW PERCENTAGE-BASED
        self.target_groups = {
            'price': 12,      # OHLC PERCENTAGES for steps 1,3,5 = 4*3 = 12
            'direction': 3,   # Direction for steps 1,3,5 = 3
            'change_pct': 3   # Change % for steps 1,3,5 = 3
        }
        
    def build_model(self):
        """Build multi-target LSTM architecture"""
        print("Building multi-target LSTM model...")
        
        # Input layer
        inputs = Input(shape=(self.sequence_length, self.n_features), name='market_data')
        
        # Shared LSTM layers
        lstm1 = LSTM(128, return_sequences=True, dropout=0.2, recurrent_dropout=0.2, name='lstm_1')(inputs)
        lstm1_norm = BatchNormalization(name='lstm1_norm')(lstm1)
        
        lstm2 = LSTM(64, return_sequences=True, dropout=0.2, recurrent_dropout=0.2, name='lstm_2')(lstm1_norm)
        lstm2_norm = BatchNormalization(name='lstm2_norm')(lstm2)
        
        lstm3 = LSTM(32, return_sequences=False, dropout=0.2, recurrent_dropout=0.2, name='lstm_3')(lstm2_norm)
        lstm3_norm = BatchNormalization(name='lstm3_norm')(lstm3)
        
        # Shared dense layer
        shared_dense = Dense(64, activation='relu', name='shared_dense')(lstm3_norm)
        shared_dropout = Dropout(0.3, name='shared_dropout')(shared_dense)
        
        # Price prediction branch (regression)
        price_dense1 = Dense(32, activation='relu', name='price_dense1')(shared_dropout)
        price_dropout = Dropout(0.2, name='price_dropout')(price_dense1)
        price_output = Dense(self.target_groups['price'], activation='linear', name='price_predictions')(price_dropout)
        
        # Direction prediction branch (classification)
        direction_dense1 = Dense(16, activation='relu', name='direction_dense1')(shared_dropout)
        direction_dropout = Dropout(0.2, name='direction_dropout')(direction_dense1)
        direction_output = Dense(self.target_groups['direction'], activation='sigmoid', name='direction_predictions')(direction_dropout)
        
        # Change percentage prediction branch (regression)
        change_dense1 = Dense(16, activation='relu', name='change_dense1')(shared_dropout)
        change_dropout = Dropout(0.2, name='change_dropout')(change_dense1)
        change_output = Dense(self.target_groups['change_pct'], activation='tanh', name='change_predictions')(change_dropout)
        
        # Create model
        self.model = Model(
            inputs=inputs,
            outputs=[price_output, direction_output, change_output],
            name='MultiTargetLSTM'
        )
        
        # Compile with different losses for different outputs
        self.model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss={
                'price_predictions': 'mse',
                'direction_predictions': 'binary_crossentropy',
                'change_predictions': 'mse'
            },
            loss_weights={
                'price_predictions': 1.0,
                'direction_predictions': 2.0,  # Higher weight for direction accuracy
                'change_predictions': 1.5
            },
            metrics={
                'price_predictions': ['mae'],
                'direction_predictions': ['accuracy'],
                'change_predictions': ['mae']
            }
        )
        
        print(f"Model built with {self.model.count_params():,} parameters")
        return self.model
    
    def prepare_targets(self, y):
        """Split targets into different groups for multi-output training"""
        # Assuming targets are ordered as: price(12), direction(3), change_pct(3)
        price_targets = y[:, :12]  # First 12 columns
        direction_targets = y[:, 12:15]  # Next 3 columns
        change_targets = y[:, 15:18]  # Last 3 columns
        
        return [price_targets, direction_targets, change_targets]
    
    def train_model(self, X_train, y_train, X_val, y_val, epochs=100, batch_size=32):
        """Train the multi-target LSTM model"""
        print("Training multi-target LSTM model...")
        
        if self.model is None:
            self.build_model()
        
        # Prepare targets
        y_train_split = self.prepare_targets(y_train)
        y_val_split = self.prepare_targets(y_val)
        
        # Callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=8,
                min_lr=1e-6,
                verbose=1
            ),
            ModelCheckpoint(
                'models/best_lstm_model.h5',
                monitor='val_loss',
                save_best_only=True,
                verbose=1
            )
        ]
        
        # Create models directory
        os.makedirs('models', exist_ok=True)
        
        # Train model
        history = self.model.fit(
            X_train,
            y_train_split,
            validation_data=(X_val, y_val_split),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1
        )
        
        print("Training completed!")
        return history
    
    def predict(self, X):
        """Make predictions with the trained model"""
        if self.model is None:
            raise ValueError("Model not built or loaded")
        
        predictions = self.model.predict(X)
        
        # Combine predictions back into single array
        price_pred, direction_pred, change_pred = predictions
        combined_pred = np.concatenate([price_pred, direction_pred, change_pred], axis=1)
        
        return combined_pred, predictions
    
    def evaluate_model(self, X_test, y_test):
        """Evaluate model performance"""
        if self.model is None:
            raise ValueError("Model not built or loaded")
        
        y_test_split = self.prepare_targets(y_test)
        
        # Evaluate
        results = self.model.evaluate(X_test, y_test_split, verbose=0)
        
        # Get predictions for detailed analysis
        predictions = self.model.predict(X_test)
        
        # Calculate additional metrics
        price_pred, direction_pred, change_pred = predictions
        
        # Direction accuracy
        direction_accuracy = np.mean((direction_pred > 0.5) == (y_test_split[1] > 0.5))
        
        # Price MAE
        price_mae = np.mean(np.abs(price_pred - y_test_split[0]))
        
        # Change MAE
        change_mae = np.mean(np.abs(change_pred - y_test_split[2]))
        
        evaluation_results = {
            'total_loss': results[0],
            'price_loss': results[1],
            'direction_loss': results[2],
            'change_loss': results[3],
            'price_mae': price_mae,
            'direction_accuracy': direction_accuracy,
            'change_mae': change_mae
        }
        
        print("=== MODEL EVALUATION ===")
        for metric, value in evaluation_results.items():
            print(f"{metric}: {value:.6f}")
        
        return evaluation_results, predictions
    
    def save_model(self, filepath='models/lstm_market_predictor.h5'):
        """Save the trained model"""
        if self.model is None:
            raise ValueError("No model to save")
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        self.model.save(filepath)
        
        # Save model configuration
        config = {
            'sequence_length': self.sequence_length,
            'n_features': self.n_features,
            'n_targets': self.n_targets,
            'target_groups': self.target_groups
        }
        
        config_path = filepath.replace('.h5', '_config.json')
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"Model saved to {filepath}")
        print(f"Config saved to {config_path}")
    
    def load_model(self, filepath='models/lstm_market_predictor.h5'):
        """Load a trained model"""
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Model file not found: {filepath}")
        
        self.model = tf.keras.models.load_model(filepath)
        
        # Load configuration
        config_path = filepath.replace('.h5', '_config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            self.sequence_length = config['sequence_length']
            self.n_features = config['n_features']
            self.n_targets = config['n_targets']
            self.target_groups = config['target_groups']
        
        print(f"Model loaded from {filepath}")
    
    def get_model_summary(self):
        """Get model architecture summary"""
        if self.model is None:
            self.build_model()
        
        return self.model.summary()

if __name__ == "__main__":
    # Test model creation
    model = MultiTargetLSTM()
    model.build_model()
    print(model.get_model_summary())
